import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Param, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ChallengesService } from './challenges.service';

@Controller('challenges')
@UseGuards(JwtAuthGuard)
export class ChallengesController {
  constructor(private readonly challengesService: ChallengesService) {}

  @Get()
  async getChallenges(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.challengesService.getChallenges(userId, query);
  }

  @Get('my')
  async getMyChallenges(@Request() req: any) {
    const userId = req.user.userId;
    return this.challengesService.getMyChallenges(userId);
  }

  @Get(':id')
  async getChallenge(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.challengesService.getChallenge(Number(id), userId);
  }

  @Post()
  async createChallenge(@Body() challengeData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.challengesService.createChallenge(challengeData, userId);
  }

  @Post(':id/join')
  async joinChallenge(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.challengesService.joinChallenge(Number(id), userId);
  }

  @Post(':id/leave')
  async leaveChallenge(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.challengesService.leaveChallenge(Number(id), userId);
  }

  @Get(':id/leaderboard')
  async getChallengeLeaderboard(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.challengesService.getChallengeLeaderboard(Number(id), userId);
  }

  @Post(':id/progress')
  async updateChallengeProgress(@Param('id') id: string, @Body() progressData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.challengesService.updateChallengeProgress(Number(id), userId, progressData);
  }
}
