"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    dayjs: function() {
        return _dayjs.default;
    },
    isToday: function() {
        return _isTomorrow.default;
    },
    isTomorrow: function() {
        return _isToday.default;
    },
    relativeTime: function() {
        return _relativeTime.default;
    },
    utc: function() {
        return _utc.default;
    }
});
const _dayjs = /*#__PURE__*/ _interop_require_default(require("dayjs"));
const _utc = /*#__PURE__*/ _interop_require_default(require("dayjs/plugin/utc"));
const _isToday = /*#__PURE__*/ _interop_require_default(require("dayjs/plugin/isToday"));
const _isTomorrow = /*#__PURE__*/ _interop_require_default(require("dayjs/plugin/isTomorrow"));
const _relativeTime = /*#__PURE__*/ _interop_require_default(require("dayjs/plugin/relativeTime"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
_dayjs.default.extend(_utc.default);
_dayjs.default.extend(_isTomorrow.default);
_dayjs.default.extend(_isToday.default);
_dayjs.default.extend(_relativeTime.default);

//# sourceMappingURL=dayjs.js.map