// src/admin/dto/get-all-users-query.dto.ts
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsInt } from 'class-validator';

export class GetAllUsersQueryDto {
  @IsOptional()
  @IsString()
  q?: string; // Nome ou email

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  role_id?: number; // Filtro por role_id

  @IsOptional()
  @Type(() => Number)
  page?: number;

  @IsOptional()
  @Type(() => Number)
  limit?: number;
}