"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "NutritionistController", {
    enumerable: true,
    get: function() {
        return NutritionistController;
    }
});
const _common = require("@nestjs/common");
const _nutritionistservice = require("./nutritionist.service");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _getnutritionistclientsquerydto = require("../admin/dto/get-nutritionist-clients-query.dto");
const _createnutritionistprotocoldto = require("../dto/create-nutritionist-protocol.dto");
const _createnutritionistclientprotocoldto = require("../dto/create-nutritionist-client-protocol.dto");
const _importnutritionistclientprotocoldto = require("../dto/import-nutritionist-client-protocol.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let NutritionistController = class NutritionistController {
    getStats() {
        return {
            status: 'success',
            data: {
                total_clients: 99,
                active_protocols: 99,
                completion_rate: 99
            }
        };
    }
    getClients(query, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.nutritionistService.getClients(query, userId);
    }
    getClient(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.nutritionistService.getClient(id, userId);
    }
    createProtocol(createNutritionistProtocolDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.nutritionistService.createProtocol(createNutritionistProtocolDto, userId);
    }
    getProtocols(req) {
        const userId = req.user?.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.nutritionistService.getProtocols(userId);
    }
    getClientProtocols(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.nutritionistService.clientProtocols(id, userId);
    }
    createClientProtocol(id, createNutritionistClientProtocolDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.nutritionistService.createClientProtocol(id, createNutritionistClientProtocolDto, userId);
    }
    // import protocol
    importClientProtocol(id, importNutritionistClientProtocolDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.nutritionistService.importClientProtocol(id, importNutritionistClientProtocolDto, userId);
    }
    constructor(nutritionistService){
        this.nutritionistService = nutritionistService;
    }
};
_ts_decorate([
    (0, _common.Get)('stats'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "getStats", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('clients'),
    _ts_param(0, (0, _common.Query)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _getnutritionistclientsquerydto.GetNutritionistClientsQueryDto === "undefined" ? Object : _getnutritionistclientsquerydto.GetNutritionistClientsQueryDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "getClients", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('clients/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "getClient", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createnutritionistprotocoldto.CreateNutritionistProtocolDto === "undefined" ? Object : _createnutritionistprotocoldto.CreateNutritionistProtocolDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "createProtocol", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "getProtocols", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('clients/:id/protocols'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "getClientProtocols", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('clients/:id/protocols'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        typeof _createnutritionistclientprotocoldto.CreateNutritionistClientProtocolDto === "undefined" ? Object : _createnutritionistclientprotocoldto.CreateNutritionistClientProtocolDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "createClientProtocol", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('clients/:id/protocols/import'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        typeof _importnutritionistclientprotocoldto.ImportNutritionistClientProtocolDto === "undefined" ? Object : _importnutritionistclientprotocoldto.ImportNutritionistClientProtocolDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], NutritionistController.prototype, "importClientProtocol", null);
NutritionistController = _ts_decorate([
    (0, _common.Controller)('nutritionist'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _nutritionistservice.NutritionistService === "undefined" ? Object : _nutritionistservice.NutritionistService
    ])
], NutritionistController);

//# sourceMappingURL=nutritionist.controller.js.map