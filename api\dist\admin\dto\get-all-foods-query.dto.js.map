{"version": 3, "sources": ["../../../src/admin/dto/get-all-foods-query.dto.ts"], "sourcesContent": ["// src/admin/dto/get-all-foods-query.dto.ts\r\nimport { IsOptional, IsString, IsInt } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class GetAllFoodsQueryDto {\r\n  @IsOptional()\r\n  @IsString()\r\n  name?: string; // Filtro por nome do alimento\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  category_id?: number; // Filtro por categoria\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  page?: number; // Página atual\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  limit?: number; // Limite de itens por página\r\n}"], "names": ["GetAllFoodsQueryDto", "Number"], "mappings": "AAAA,2CAA2C;;;;;+BAI9BA;;;eAAAA;;;gCAH+B;kCACvB;;;;;;;;;;AAEd,IAAA,AAAMA,sBAAN,MAAMA;AAmBb;;;;;;;;oCAbcC;;;;;;oCAKAA;;;;;;oCAKAA"}