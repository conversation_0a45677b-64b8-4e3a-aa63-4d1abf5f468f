import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { db } from '../database';

@Injectable()
export class RewardsService {

  async getAvailableRewards(userId: number, query: any) {
    try {
      const { category, minCost, maxCost, page = 1, limit = 20 } = query;
      const offset = (page - 1) * limit;

      // Get user's current points
      const [userPoints] = await db.execute(
        'SELECT total_points FROM user_points WHERE user_id = ?',
        [userId]
      );

      const currentPoints = userPoints[0]?.total_points || 0;

      let sql = `
        SELECT 
          r.id,
          r.title,
          r.description,
          r.category,
          r.cost_points as costPoints,
          r.image_url as imageUrl,
          r.terms_conditions as termsConditions,
          r.expiry_days as expiryDays,
          r.max_redemptions as maxRedemptions,
          r.status,
          CASE WHEN r.cost_points <= ? THEN true ELSE false END as canAfford,
          COUNT(ur.id) as timesRedeemed
        FROM rewards r
        LEFT JOIN user_rewards ur ON r.id = ur.reward_id AND ur.user_id = ?
        WHERE r.status = 'active'
      `;

      const params = [currentPoints, userId];

      if (category) {
        sql += ` AND r.category = ?`;
        params.push(category);
      }

      if (minCost) {
        sql += ` AND r.cost_points >= ?`;
        params.push(minCost);
      }

      if (maxCost) {
        sql += ` AND r.cost_points <= ?`;
        params.push(maxCost);
      }

      sql += ` GROUP BY r.id ORDER BY r.cost_points ASC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const [rewards] = await db.execute(sql, params);

      // Get total count
      let countSql = `SELECT COUNT(*) as total FROM rewards WHERE status = 'active'`;
      const countParams: any[] = [];

      if (category) {
        countSql += ` AND category = ?`;
        countParams.push(category);
      }

      if (minCost) {
        countSql += ` AND cost_points >= ?`;
        countParams.push(minCost);
      }

      if (maxCost) {
        countSql += ` AND cost_points <= ?`;
        countParams.push(maxCost);
      }

      const [countResult] = await db.execute(countSql, countParams);
      const total = countResult[0]?.total || 0;

      return {
        status: 'success',
        data: {
          rewards,
          userPoints: currentPoints,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      };
    } catch (error) {
      console.error('Error getting available rewards:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get available rewards'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getMyRewards(userId: number) {
    try {
      const sql = `
        SELECT 
          ur.id,
          ur.redeemed_at as redeemedAt,
          ur.used_at as usedAt,
          ur.expires_at as expiresAt,
          ur.status,
          ur.redemption_code as redemptionCode,
          r.title,
          r.description,
          r.category,
          r.cost_points as costPoints,
          r.image_url as imageUrl,
          r.terms_conditions as termsConditions,
          CASE 
            WHEN ur.expires_at < NOW() THEN 'expired'
            WHEN ur.used_at IS NOT NULL THEN 'used'
            ELSE 'active'
          END as currentStatus
        FROM user_rewards ur
        INNER JOIN rewards r ON ur.reward_id = r.id
        WHERE ur.user_id = ?
        ORDER BY ur.redeemed_at DESC
      `;

      const [rewards] = await db.execute(sql, [userId]);

      // Group by status
      const groupedRewards = {
        active: rewards.filter(r => r.currentStatus === 'active'),
        used: rewards.filter(r => r.currentStatus === 'used'),
        expired: rewards.filter(r => r.currentStatus === 'expired')
      };

      return {
        status: 'success',
        data: {
          rewards,
          grouped: groupedRewards,
          summary: {
            total: rewards.length,
            active: groupedRewards.active.length,
            used: groupedRewards.used.length,
            expired: groupedRewards.expired.length
          }
        }
      };
    } catch (error) {
      console.error('Error getting my rewards:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get my rewards'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async redeemReward(rewardId: number, userId: number) {
    try {
      // Get reward details
      const [reward] = await db.execute(
        'SELECT id, title, cost_points, max_redemptions, expiry_days, status FROM rewards WHERE id = ?',
        [rewardId]
      );

      if (!reward[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Reward not found'
        }, HttpStatus.NOT_FOUND);
      }

      if (reward[0].status !== 'active') {
        throw new HttpException({
          status: 'error',
          message: 'Reward is not available'
        }, HttpStatus.BAD_REQUEST);
      }

      // Check user's points
      const [userPoints] = await db.execute(
        'SELECT total_points FROM user_points WHERE user_id = ?',
        [userId]
      );

      const currentPoints = userPoints[0]?.total_points || 0;

      if (currentPoints < reward[0].cost_points) {
        throw new HttpException({
          status: 'error',
          message: 'Insufficient points'
        }, HttpStatus.BAD_REQUEST);
      }

      // Check redemption limit
      if (reward[0].max_redemptions > 0) {
        const [redemptionCount] = await db.execute(
          'SELECT COUNT(*) as count FROM user_rewards WHERE user_id = ? AND reward_id = ?',
          [userId, rewardId]
        );

        if (redemptionCount[0].count >= reward[0].max_redemptions) {
          throw new HttpException({
            status: 'error',
            message: 'Redemption limit reached for this reward'
          }, HttpStatus.BAD_REQUEST);
        }
      }

      // Calculate expiry date
      const expiresAt = reward[0].expiry_days > 0 ? 
        new Date(Date.now() + (reward[0].expiry_days * 24 * 60 * 60 * 1000)) : null;

      // Generate redemption code
      const redemptionCode = this.generateRedemptionCode();

      // Start transaction
      await db.execute('START TRANSACTION');

      try {
        // Deduct points
        await db.execute(
          'UPDATE user_points SET total_points = total_points - ?, updated_at = NOW() WHERE user_id = ?',
          [reward[0].cost_points, userId]
        );

        // Log points transaction
        await db.execute(
          `INSERT INTO points_transactions (user_id, points, type, reason, created_at)
           VALUES (?, ?, 'spent', ?, NOW())`,
          [userId, reward[0].cost_points, `Redeemed: ${reward[0].title}`]
        );

        // Create user reward
        const [userRewardResult] = await db.execute(
          `INSERT INTO user_rewards (
            user_id, reward_id, redeemed_at, expires_at, 
            status, redemption_code, created_at
          ) VALUES (?, ?, NOW(), ?, 'redeemed', ?, NOW())`,
          [userId, rewardId, expiresAt?.toISOString() || null, redemptionCode]
        );

        await db.execute('COMMIT');

        // Create notification
        await this.createRewardNotification(userId, reward[0].title, redemptionCode);

        return {
          status: 'success',
          message: 'Reward redeemed successfully',
          data: {
            id: userRewardResult.insertId,
            rewardTitle: reward[0].title,
            costPoints: reward[0].cost_points,
            redemptionCode,
            expiresAt,
            remainingPoints: currentPoints - reward[0].cost_points
          }
        };
      } catch (error) {
        await db.execute('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('Error redeeming reward:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to redeem reward'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getRewardCategories() {
    try {
      const sql = `
        SELECT 
          category,
          COUNT(*) as rewardCount,
          MIN(cost_points) as minCost,
          MAX(cost_points) as maxCost,
          AVG(cost_points) as avgCost
        FROM rewards 
        WHERE status = 'active'
        GROUP BY category
        ORDER BY category ASC
      `;

      const [categories] = await db.execute(sql);

      return {
        status: 'success',
        data: categories
      };
    } catch (error) {
      console.error('Error getting reward categories:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get reward categories'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private generateRedemptionCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private async createRewardNotification(userId: number, rewardTitle: string, redemptionCode: string) {
    try {
      await db.execute(
        `INSERT INTO notifications (user_id, type, title, message, data, created_at)
         VALUES (?, 'reward_redeemed', 'Recompensa Resgatada!', ?, ?, NOW())`,
        [
          userId,
          `Você resgatou: ${rewardTitle}. Código: ${redemptionCode}`,
          JSON.stringify({ 
            rewardTitle, 
            redemptionCode, 
            type: 'reward_redeemed' 
          })
        ]
      );
    } catch (error) {
      console.error('Error creating reward notification:', error);
    }
  }

  // Admin methods for managing rewards
  async createReward(rewardData: any) {
    try {
      const {
        title,
        description,
        category,
        costPoints,
        imageUrl,
        termsConditions,
        expiryDays,
        maxRedemptions
      } = rewardData;

      const [result] = await db.execute(
        `INSERT INTO rewards (
          title, description, category, cost_points, image_url,
          terms_conditions, expiry_days, max_redemptions, status,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
        [
          title, description, category, costPoints, imageUrl,
          termsConditions, expiryDays, maxRedemptions
        ]
      );

      return {
        status: 'success',
        message: 'Reward created successfully',
        data: {
          id: result.insertId,
          ...rewardData
        }
      };
    } catch (error) {
      console.error('Error creating reward:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to create reward'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateReward(rewardId: number, updateData: any) {
    try {
      const updates: string[] = [];
      const params: any[] = [];

      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          const dbKey = key === 'costPoints' ? 'cost_points' :
                       key === 'imageUrl' ? 'image_url' :
                       key === 'termsConditions' ? 'terms_conditions' :
                       key === 'expiryDays' ? 'expiry_days' :
                       key === 'maxRedemptions' ? 'max_redemptions' : key;
          updates.push(`${dbKey} = ?`);
          params.push(updateData[key]);
        }
      });

      if (updates.length === 0) {
        throw new HttpException({
          status: 'error',
          message: 'No fields to update'
        }, HttpStatus.BAD_REQUEST);
      }

      params.push(rewardId);

      await db.execute(
        `UPDATE rewards SET ${updates.join(', ')}, updated_at = NOW() WHERE id = ?`,
        params
      );

      return {
        status: 'success',
        message: 'Reward updated successfully'
      };
    } catch (error) {
      console.error('Error updating reward:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to update reward'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async deleteReward(rewardId: number) {
    try {
      await db.execute(
        'UPDATE rewards SET status = "inactive", updated_at = NOW() WHERE id = ?',
        [rewardId]
      );

      return {
        status: 'success',
        message: 'Reward deactivated successfully'
      };
    } catch (error) {
      console.error('Error deleting reward:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to delete reward'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // Method to expire old rewards (to be called by cron job)
  async expireOldRewards() {
    try {
      await db.execute(
        `UPDATE user_rewards 
         SET status = 'expired' 
         WHERE expires_at < NOW() AND status = 'redeemed'`
      );

      console.log('Expired old rewards');
      return true;
    } catch (error) {
      console.error('Error expiring old rewards:', error);
      return false;
    }
  }
}
