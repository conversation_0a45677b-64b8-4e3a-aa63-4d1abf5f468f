"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AffiliatesController", {
    enumerable: true,
    get: function() {
        return AffiliatesController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _affiliatesservice = require("./affiliates.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let AffiliatesController = class AffiliatesController {
    async getMe(req) {
        const userId = req.user.userId;
        return this.affiliatesService.getAffiliateById(userId);
    }
    async checkPaymentEligibility(req) {
        const affiliateId = req.user.userId;
        return this.affiliatesService.checkPaymentEligibilityByAffiliateId(affiliateId);
    }
    async startOnboarding(req) {
        const affiliateId = req.user.userId;
        return this.affiliatesService.startOnboarding(affiliateId);
    }
    async getLinks(req) {
        const userId = req.user.userId;
        return this.affiliatesService.getLinks(userId);
    }
    // hit this endpoint when a user clicks on a link
    async registerLinkVisit(id) {
        return this.affiliatesService.registerLinkVisit(id);
    }
    async createLink(req, body) {
        const userId = req.user.userId;
        return this.affiliatesService.createLink(userId, body);
    }
    async getDashboardStats(req) {
        const userId = req.user.userId;
        return this.affiliatesService.getDashboardStats(userId);
    }
    async getDetailedStats(req, startDate, endDate) {
        const userId = req.user.userId;
        return this.affiliatesService.getDetailedStats(userId, startDate, endDate);
    }
    async getPaymentHistory(req, page, limit) {
        const userId = req.user.userId;
        const pageNumber = page ? parseInt(page, 10) : 1;
        const limitNumber = limit ? parseInt(limit, 10) : 10;
        return this.affiliatesService.getPaymentHistory(userId, pageNumber, limitNumber);
    }
    async getRecentTransactions(req, page, limit) {
        const userId = req.user.userId;
        const pageNumber = page ? parseInt(page, 10) : 1;
        const limitNumber = limit ? parseInt(limit, 10) : 10;
        return this.affiliatesService.getRecentTransactions(userId, pageNumber, limitNumber);
    }
    constructor(affiliatesService){
        this.affiliatesService = affiliatesService;
    }
};
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('me'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "getMe", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('onboarding'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "checkPaymentEligibility", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('onboarding'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "startOnboarding", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('links'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "getLinks", null);
_ts_decorate([
    (0, _common.Get)('links/visit/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "registerLinkVisit", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('links'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "createLink", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('dashboard'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "getDashboardStats", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('stats'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)('start_date')),
    _ts_param(2, (0, _common.Query)('end_date')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "getDetailedStats", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('payments'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)('page')),
    _ts_param(2, (0, _common.Query)('limit')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "getPaymentHistory", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('transactions'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)('page')),
    _ts_param(2, (0, _common.Query)('limit')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AffiliatesController.prototype, "getRecentTransactions", null);
AffiliatesController = _ts_decorate([
    (0, _common.Controller)('affiliates'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _affiliatesservice.AffiliatesService === "undefined" ? Object : _affiliatesservice.AffiliatesService
    ])
], AffiliatesController);

//# sourceMappingURL=affiliates.controller.js.map