import { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateCoachProtocolDto {
  @IsString()
  name: string;

  @IsNumber()
  @Type(() => Number)
  type_id: number;

  @IsString()
  split: string;

  @IsNumber()
  @Type(() => Number)
  frequency: number;

  @IsString()
  objective: string;    

  @IsOptional()
  @IsString()
  notes?: string;

  @ArrayNotEmpty()
  @IsArray()
  workouts: {
    exercises: {
    exercise_id: number,
    split_group: number,
    sets: number,
    reps: number,
    rpe: number,
    rest_seconds: number,
    notes?: string
    }[],
  }[];
}