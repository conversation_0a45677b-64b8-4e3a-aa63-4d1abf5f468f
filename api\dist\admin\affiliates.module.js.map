{"version": 3, "sources": ["../../src/admin/affiliates.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { AffiliatesController } from './affiliates.controller';\r\nimport { AffiliatesService } from './affiliates.service';\r\n\r\n@Module({\r\n  imports: [\r\n  ],\r\n  controllers: [AffiliatesController],\r\n  providers: [AffiliatesService],\r\n})\r\nexport class AffiliatesModule {}\r\n"], "names": ["AffiliatesModule", "imports", "controllers", "AffiliatesController", "providers", "AffiliatesService"], "mappings": ";;;;+BAUaA;;;eAAAA;;;wBAVU;sCACc;mCACH;;;;;;;AAQ3B,IAAA,AAAMA,mBAAN,MAAMA;AAAkB;;;QAL7BC,SAAS,EACR;QACDC,aAAa;YAACC,0CAAoB;SAAC;QACnCC,WAAW;YAACC,oCAAiB;SAAC"}