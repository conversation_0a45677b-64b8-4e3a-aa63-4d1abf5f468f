import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Plan } from './plan.entity';

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  @Column()
  userId: string;

  @ManyToOne(() => Plan)
  plan: Plan;

  @Column()
  planId: string;

  @Column()
  status: 'active' | 'cancelled' | 'expired' | 'pending';

  @Column({ type: 'timestamptz' })
  currentPeriodStart: Date;

  @Column({ type: 'timestamptz' })
  currentPeriodEnd: Date;

  @Column({ default: false })
  cancelAtPeriodEnd: boolean;

  @Column({ type: 'timestamptz', nullable: true })
  canceledAt?: Date;

  @Column({ type: 'timestamptz', nullable: true })
  endedAt?: Date;

  @Column({ type: 'timestamptz', nullable: true })
  trialStart?: Date;

  @Column({ type: 'timestamptz', nullable: true })
  trialEnd?: Date;

  @Column('jsonb', { nullable: true })
  paymentMethod?: {
    id: string;
    type: 'credit_card' | 'debit_card' | 'pix' | 'bank_slip';
    last4?: string;
    brand?: string;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}