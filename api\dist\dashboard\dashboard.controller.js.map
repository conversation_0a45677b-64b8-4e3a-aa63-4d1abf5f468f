{"version": 3, "sources": ["../../src/dashboard/dashboard.controller.ts"], "sourcesContent": ["import { Controller, Get, UseGuards, Request, Query, HttpException, HttpStatus } from '@nestjs/common';\r\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\r\nimport { DashboardService } from './dashboard.service';\r\n\r\n@Controller('dashboard')\r\nexport class DashboardController {\r\n    constructor(private readonly dashboardService: DashboardService) {}\r\n\r\n    private validateRequest(userId: number, period: string) {\r\n        if (!userId) {\r\n            throw new HttpException('User ID not found', HttpStatus.UNAUTHORIZED);\r\n        }\r\n\r\n        const validPeriods = ['week', 'month', 'semester', 'year', 'all'];\r\n        if (!validPeriods.includes(period)) {\r\n            throw new HttpException('Invalid period parameter', HttpStatus.BAD_REQUEST);\r\n        }\r\n    }\r\n    @Get('stats')\r\n    getStats() {\r\n        return {\r\n            totalUsers: 0,\r\n            totalProfessionals: 0,\r\n            activeSubscriptions: 0,\r\n            monthlyRevenue: 0\r\n          };\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workout')\r\n    async getWorkoutData(@Request() req: any) {\r\n        const userId = req.user.userId;\r\n        return await this.dashboardService.getWorkoutDashboardData(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/weight')\r\n    async getProgressWeight(@Request() req: any, @Query() query: any) {\r\n        try {\r\n            const userId = req.user.userId;\r\n            const period = query.period || 'month';\r\n            this.validateRequest(userId, period);\r\n            return await this.dashboardService.getWeightProgress(userId, period);\r\n        } catch (error) {\r\n            if (error instanceof HttpException) {\r\n                throw error;\r\n            }\r\n            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/analytics')\r\n    async getProgressAnalytics(@Request() req: any, @Query() query: any) {\r\n        try {\r\n            const userId = req.user.userId;\r\n            const period = query.period || 'month';\r\n            this.validateRequest(userId, period);\r\n            return await this.dashboardService.getAnalyticsOverview(userId, period);\r\n        } catch (error) {\r\n            if (error instanceof HttpException) {\r\n                throw error;\r\n            }\r\n            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('nutritional-summary')\r\n    async getNutritionalSummary(@Request() req: any, @Query() query: any) {\r\n        const userId = req.user.userId;\r\n        if (!userId) {\r\n            throw new Error('User ID is required');\r\n        }\r\n\r\n        return this.dashboardService.getNutritionalSummary(userId, query);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('ai-suggestions')\r\n    getAISuggestions(@Request() req: any, @Query() query: any) {\r\n        const userId = req.user.userId;\r\n\r\n        // Mock AI suggestions\r\n        const suggestions = [\r\n            'Considere adicionar mais proteína na próxima refeição',\r\n            'Você está próximo da sua meta de hidratação hoje!',\r\n            'Que tal um treino de força hoje?',\r\n            'Tente incluir mais vegetais na sua dieta',\r\n            'Mantenha a consistência nos treinos'\r\n        ];\r\n\r\n        return {\r\n            status: 'success',\r\n            data: suggestions.slice(0, 3) // Return 3 random suggestions\r\n        };\r\n    }\r\n\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workout-analytics')\r\n    async getWorkoutAnalytics(@Request() req: any, @Query() query: any) {\r\n        try {\r\n            const userId = req.user.userId;\r\n            const period = query.period || 'week';\r\n            this.validateRequest(userId, period);\r\n            return await this.dashboardService.getWorkoutAnalytics(userId, period);\r\n        } catch (error) {\r\n            if (error instanceof HttpException) {\r\n                throw error;\r\n            }\r\n            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('nutrition-analytics')\r\n    async getNutritionAnalytics(@Request() req: any, @Query() query: any) {\r\n        try {\r\n            const userId = req.user.userId;\r\n            const period = query.period || 'week';\r\n            this.validateRequest(userId, period);\r\n            return await this.dashboardService.getNutritionAnalytics(userId, period);\r\n        } catch (error) {\r\n            if (error instanceof HttpException) {\r\n                throw error;\r\n            }\r\n            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('caloric-balance')\r\n    async getCaloricBalance(@Request() req: any, @Query() query: any) {\r\n        try {\r\n            const userId = req.user.userId;\r\n            const period = query.period || 'week';\r\n            this.validateRequest(userId, period);\r\n            return await this.dashboardService.getCaloricBalance(userId, period);\r\n        } catch (error) {\r\n            if (error instanceof HttpException) {\r\n                throw error;\r\n            }\r\n            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('strength-progress')\r\n    async getStrengthProgress(@Request() req: any, @Query() query: any) {\r\n        try {\r\n            const userId = req.user.userId;\r\n            const period = query.period || 'month';\r\n            this.validateRequest(userId, period);\r\n            return await this.dashboardService.getStrengthProgress(userId, period);\r\n        } catch (error) {\r\n            if (error instanceof HttpException) {\r\n                throw error;\r\n            }\r\n            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('body-composition')\r\n    async getBodyComposition(@Request() req: any, @Query() query: any) {\r\n        try {\r\n            const userId = req.user.userId;\r\n            const period = query.period || 'month';\r\n            this.validateRequest(userId, period);\r\n            return await this.dashboardService.getBodyComposition(userId, period);\r\n        } catch (error) {\r\n            if (error instanceof HttpException) {\r\n                throw error;\r\n            }\r\n            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);\r\n        }\r\n    }\r\n}\r\n"], "names": ["DashboardController", "validateRequest", "userId", "period", "HttpException", "HttpStatus", "UNAUTHORIZED", "validPeriods", "includes", "BAD_REQUEST", "getStats", "totalUsers", "totalProfessionals", "activeSubscriptions", "monthlyRevenue", "getWorkoutData", "req", "user", "dashboardService", "getWorkoutDashboardData", "getProgressWeight", "query", "getWeightProgress", "error", "INTERNAL_SERVER_ERROR", "getProgressAnalytics", "getAnalyticsOverview", "getNutritionalSummary", "Error", "getAISuggestions", "suggestions", "status", "data", "slice", "getWorkoutAnalytics", "getNutritionAnalytics", "getCaloricBalance", "getStrengthProgress", "getBodyComposition", "constructor"], "mappings": ";;;;+BAKaA;;;eAAAA;;;wBALyE;8BACzD;kCACI;;;;;;;;;;;;;;;AAG1B,IAAA,AAAMA,sBAAN,MAAMA;IAGDC,gBAAgBC,MAAc,EAAEC,MAAc,EAAE;QACpD,IAAI,CAACD,QAAQ;YACT,MAAM,IAAIE,qBAAa,CAAC,qBAAqBC,kBAAU,CAACC,YAAY;QACxE;QAEA,MAAMC,eAAe;YAAC;YAAQ;YAAS;YAAY;YAAQ;SAAM;QACjE,IAAI,CAACA,aAAaC,QAAQ,CAACL,SAAS;YAChC,MAAM,IAAIC,qBAAa,CAAC,4BAA4BC,kBAAU,CAACI,WAAW;QAC9E;IACJ;IAEAC,WAAW;QACP,OAAO;YACHC,YAAY;YACZC,oBAAoB;YACpBC,qBAAqB;YACrBC,gBAAgB;QAClB;IACN;IAEA,MAEMC,eAAe,AAAWC,GAAQ,EAAE;QACtC,MAAMd,SAASc,IAAIC,IAAI,CAACf,MAAM;QAC9B,OAAO,MAAM,IAAI,CAACgB,gBAAgB,CAACC,uBAAuB,CAACjB;IAC/D;IAEA,MAEMkB,kBAAkB,AAAWJ,GAAQ,EAAE,AAASK,KAAU,EAAE;QAC9D,IAAI;YACA,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;YAC9B,MAAMC,SAASkB,MAAMlB,MAAM,IAAI;YAC/B,IAAI,CAACF,eAAe,CAACC,QAAQC;YAC7B,OAAO,MAAM,IAAI,CAACe,gBAAgB,CAACI,iBAAiB,CAACpB,QAAQC;QACjE,EAAE,OAAOoB,OAAO;YACZ,IAAIA,iBAAiBnB,qBAAa,EAAE;gBAChC,MAAMmB;YACV;YACA,MAAM,IAAInB,qBAAa,CAAC,yBAAyBC,kBAAU,CAACmB,qBAAqB;QACrF;IACJ;IAEA,MAEMC,qBAAqB,AAAWT,GAAQ,EAAE,AAASK,KAAU,EAAE;QACjE,IAAI;YACA,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;YAC9B,MAAMC,SAASkB,MAAMlB,MAAM,IAAI;YAC/B,IAAI,CAACF,eAAe,CAACC,QAAQC;YAC7B,OAAO,MAAM,IAAI,CAACe,gBAAgB,CAACQ,oBAAoB,CAACxB,QAAQC;QACpE,EAAE,OAAOoB,OAAO;YACZ,IAAIA,iBAAiBnB,qBAAa,EAAE;gBAChC,MAAMmB;YACV;YACA,MAAM,IAAInB,qBAAa,CAAC,yBAAyBC,kBAAU,CAACmB,qBAAqB;QACrF;IACJ;IAEA,MAEMG,sBAAsB,AAAWX,GAAQ,EAAE,AAASK,KAAU,EAAE;QAClE,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACT,MAAM,IAAI0B,MAAM;QACpB;QAEA,OAAO,IAAI,CAACV,gBAAgB,CAACS,qBAAqB,CAACzB,QAAQmB;IAC/D;IAIAQ,iBAAiB,AAAWb,GAAQ,EAAE,AAASK,KAAU,EAAE;QACvD,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;QAE9B,sBAAsB;QACtB,MAAM4B,cAAc;YAChB;YACA;YACA;YACA;YACA;SACH;QAED,OAAO;YACHC,QAAQ;YACRC,MAAMF,YAAYG,KAAK,CAAC,GAAG,GAAG,8BAA8B;QAChE;IACJ;IAGA,MAEMC,oBAAoB,AAAWlB,GAAQ,EAAE,AAASK,KAAU,EAAE;QAChE,IAAI;YACA,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;YAC9B,MAAMC,SAASkB,MAAMlB,MAAM,IAAI;YAC/B,IAAI,CAACF,eAAe,CAACC,QAAQC;YAC7B,OAAO,MAAM,IAAI,CAACe,gBAAgB,CAACgB,mBAAmB,CAAChC,QAAQC;QACnE,EAAE,OAAOoB,OAAO;YACZ,IAAIA,iBAAiBnB,qBAAa,EAAE;gBAChC,MAAMmB;YACV;YACA,MAAM,IAAInB,qBAAa,CAAC,yBAAyBC,kBAAU,CAACmB,qBAAqB;QACrF;IACJ;IAEA,MAEMW,sBAAsB,AAAWnB,GAAQ,EAAE,AAASK,KAAU,EAAE;QAClE,IAAI;YACA,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;YAC9B,MAAMC,SAASkB,MAAMlB,MAAM,IAAI;YAC/B,IAAI,CAACF,eAAe,CAACC,QAAQC;YAC7B,OAAO,MAAM,IAAI,CAACe,gBAAgB,CAACiB,qBAAqB,CAACjC,QAAQC;QACrE,EAAE,OAAOoB,OAAO;YACZ,IAAIA,iBAAiBnB,qBAAa,EAAE;gBAChC,MAAMmB;YACV;YACA,MAAM,IAAInB,qBAAa,CAAC,yBAAyBC,kBAAU,CAACmB,qBAAqB;QACrF;IACJ;IAEA,MAEMY,kBAAkB,AAAWpB,GAAQ,EAAE,AAASK,KAAU,EAAE;QAC9D,IAAI;YACA,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;YAC9B,MAAMC,SAASkB,MAAMlB,MAAM,IAAI;YAC/B,IAAI,CAACF,eAAe,CAACC,QAAQC;YAC7B,OAAO,MAAM,IAAI,CAACe,gBAAgB,CAACkB,iBAAiB,CAAClC,QAAQC;QACjE,EAAE,OAAOoB,OAAO;YACZ,IAAIA,iBAAiBnB,qBAAa,EAAE;gBAChC,MAAMmB;YACV;YACA,MAAM,IAAInB,qBAAa,CAAC,yBAAyBC,kBAAU,CAACmB,qBAAqB;QACrF;IACJ;IAEA,MAEMa,oBAAoB,AAAWrB,GAAQ,EAAE,AAASK,KAAU,EAAE;QAChE,IAAI;YACA,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;YAC9B,MAAMC,SAASkB,MAAMlB,MAAM,IAAI;YAC/B,IAAI,CAACF,eAAe,CAACC,QAAQC;YAC7B,OAAO,MAAM,IAAI,CAACe,gBAAgB,CAACmB,mBAAmB,CAACnC,QAAQC;QACnE,EAAE,OAAOoB,OAAO;YACZ,IAAIA,iBAAiBnB,qBAAa,EAAE;gBAChC,MAAMmB;YACV;YACA,MAAM,IAAInB,qBAAa,CAAC,yBAAyBC,kBAAU,CAACmB,qBAAqB;QACrF;IACJ;IAEA,MAEMc,mBAAmB,AAAWtB,GAAQ,EAAE,AAASK,KAAU,EAAE;QAC/D,IAAI;YACA,MAAMnB,SAASc,IAAIC,IAAI,CAACf,MAAM;YAC9B,MAAMC,SAASkB,MAAMlB,MAAM,IAAI;YAC/B,IAAI,CAACF,eAAe,CAACC,QAAQC;YAC7B,OAAO,MAAM,IAAI,CAACe,gBAAgB,CAACoB,kBAAkB,CAACpC,QAAQC;QAClE,EAAE,OAAOoB,OAAO;YACZ,IAAIA,iBAAiBnB,qBAAa,EAAE;gBAChC,MAAMmB;YACV;YACA,MAAM,IAAInB,qBAAa,CAAC,yBAAyBC,kBAAU,CAACmB,qBAAqB;QACrF;IACJ;IA3KAe,YAAY,AAAiBrB,gBAAkC,CAAE;aAApCA,mBAAAA;IAAqC;AA4KtE"}