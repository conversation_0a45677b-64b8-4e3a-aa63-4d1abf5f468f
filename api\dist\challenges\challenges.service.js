"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ChallengesService", {
    enumerable: true,
    get: function() {
        return ChallengesService;
    }
});
const _common = require("@nestjs/common");
const _database = require("../database");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let ChallengesService = class ChallengesService {
    async getChallenges(userId, query) {
        try {
            const { status = 'active', category, page = 1, limit = 20 } = query;
            const offset = (page - 1) * limit;
            let sql = `
        SELECT 
          c.id,
          c.title,
          c.description,
          c.category,
          c.type,
          c.target_value as targetValue,
          c.target_unit as targetUnit,
          c.duration_days as durationDays,
          c.start_date as startDate,
          c.end_date as endDate,
          c.status,
          c.reward_points as rewardPoints,
          c.reward_description as rewardDescription,
          c.image_url as imageUrl,
          c.created_at as createdAt,
          COUNT(cp.user_id) as participantCount,
          CASE WHEN cp_user.user_id IS NOT NULL THEN true ELSE false END as isParticipating
        FROM challenges c
        LEFT JOIN challenge_participants cp ON c.id = cp.challenge_id
        LEFT JOIN challenge_participants cp_user ON c.id = cp_user.challenge_id AND cp_user.user_id = ?
        WHERE c.status = ?
      `;
            const params = [
                userId,
                status
            ];
            if (category) {
                sql += ` AND c.category = ?`;
                params.push(category);
            }
            sql += ` GROUP BY c.id ORDER BY c.created_at DESC LIMIT ? OFFSET ?`;
            params.push(limit, offset);
            const challenges = await (0, _database.executeRawSql)(sql, params);
            // Get total count
            let countSql = `SELECT COUNT(*) as total FROM challenges WHERE status = ?`;
            const countParams = [
                status
            ];
            if (category) {
                countSql += ` AND category = ?`;
                countParams.push(category);
            }
            const countResult = await (0, _database.executeRawSql)(countSql, countParams);
            const total = countResult[0]?.total || 0;
            return {
                status: 'success',
                data: {
                    challenges,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total,
                        totalPages: Math.ceil(total / limit)
                    }
                }
            };
        } catch (error) {
            console.error('Error getting challenges:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get challenges'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getMyChallenges(userId) {
        try {
            const sql = `
        SELECT 
          c.id,
          c.title,
          c.description,
          c.category,
          c.type,
          c.target_value as targetValue,
          c.target_unit as targetUnit,
          c.duration_days as durationDays,
          c.start_date as startDate,
          c.end_date as endDate,
          c.status,
          c.reward_points as rewardPoints,
          c.reward_description as rewardDescription,
          c.image_url as imageUrl,
          cp.joined_at as joinedAt,
          cp.current_progress as currentProgress,
          cp.completed_at as completedAt,
          cp.status as participationStatus
        FROM challenges c
        INNER JOIN challenge_participants cp ON c.id = cp.challenge_id
        WHERE cp.user_id = ?
        ORDER BY cp.joined_at DESC
      `;
            const challenges = await (0, _database.executeRawSql)(sql, [
                userId
            ]);
            return {
                status: 'success',
                data: challenges
            };
        } catch (error) {
            console.error('Error getting my challenges:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get my challenges'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getChallenge(challengeId, userId) {
        try {
            const sql = `
        SELECT 
          c.id,
          c.title,
          c.description,
          c.category,
          c.type,
          c.target_value as targetValue,
          c.target_unit as targetUnit,
          c.duration_days as durationDays,
          c.start_date as startDate,
          c.end_date as endDate,
          c.status,
          c.reward_points as rewardPoints,
          c.reward_description as rewardDescription,
          c.image_url as imageUrl,
          c.rules,
          c.created_at as createdAt,
          COUNT(cp.user_id) as participantCount,
          CASE WHEN cp_user.user_id IS NOT NULL THEN true ELSE false END as isParticipating,
          cp_user.current_progress as myProgress,
          cp_user.joined_at as myJoinedAt,
          cp_user.status as myStatus
        FROM challenges c
        LEFT JOIN challenge_participants cp ON c.id = cp.challenge_id
        LEFT JOIN challenge_participants cp_user ON c.id = cp_user.challenge_id AND cp_user.user_id = ?
        WHERE c.id = ?
        GROUP BY c.id
      `;
            const result = await (0, _database.executeRawSql)(sql, [
                userId,
                challengeId
            ]);
            if (!result[0]) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Challenge not found'
                }, _common.HttpStatus.NOT_FOUND);
            }
            return {
                status: 'success',
                data: result[0]
            };
        } catch (error) {
            console.error('Error getting challenge:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get challenge'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createChallenge(challengeData, userId) {
        try {
            const { title, description, category, type, targetValue, targetUnit, durationDays, rewardPoints, rewardDescription, imageUrl, rules } = challengeData;
            const startDate = new Date();
            const endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000);
            const [result] = await _database.db.execute(`INSERT INTO challenges (
          title, description, category, type, target_value, target_unit,
          duration_days, start_date, end_date, status, reward_points,
          reward_description, image_url, rules, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, ?, ?, NOW(), NOW())`, [
                title,
                description,
                category,
                type,
                targetValue,
                targetUnit,
                durationDays,
                startDate.toISOString(),
                endDate.toISOString(),
                rewardPoints,
                rewardDescription,
                imageUrl,
                rules,
                userId
            ]);
            // Auto-join the creator
            await _database.db.execute(`INSERT INTO challenge_participants (challenge_id, user_id, joined_at, status)
         VALUES (?, ?, NOW(), 'active')`, [
                result.insertId,
                userId
            ]);
            return {
                status: 'success',
                message: 'Challenge created successfully',
                data: {
                    id: result.insertId,
                    ...challengeData
                }
            };
        } catch (error) {
            console.error('Error creating challenge:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to create challenge'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async joinChallenge(challengeId, userId) {
        try {
            // Check if challenge exists and is active
            const [challenge] = await _database.db.execute('SELECT id, status, end_date FROM challenges WHERE id = ?', [
                challengeId
            ]);
            if (!challenge[0]) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Challenge not found'
                }, _common.HttpStatus.NOT_FOUND);
            }
            if (challenge[0].status !== 'active') {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Challenge is not active'
                }, _common.HttpStatus.BAD_REQUEST);
            }
            if (new Date(challenge[0].end_date) < new Date()) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Challenge has ended'
                }, _common.HttpStatus.BAD_REQUEST);
            }
            // Check if already participating
            const [existing] = await _database.db.execute('SELECT id FROM challenge_participants WHERE challenge_id = ? AND user_id = ?', [
                challengeId,
                userId
            ]);
            if (existing[0]) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Already participating in this challenge'
                }, _common.HttpStatus.CONFLICT);
            }
            // Join challenge
            await _database.db.execute(`INSERT INTO challenge_participants (challenge_id, user_id, joined_at, status, current_progress)
         VALUES (?, ?, NOW(), 'active', 0)`, [
                challengeId,
                userId
            ]);
            return {
                status: 'success',
                message: 'Successfully joined challenge'
            };
        } catch (error) {
            console.error('Error joining challenge:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to join challenge'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async leaveChallenge(challengeId, userId) {
        try {
            const result = await _database.db.execute('DELETE FROM challenge_participants WHERE challenge_id = ? AND user_id = ?', [
                challengeId,
                userId
            ]);
            if (result.affectedRows === 0) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Not participating in this challenge'
                }, _common.HttpStatus.NOT_FOUND);
            }
            return {
                status: 'success',
                message: 'Successfully left challenge'
            };
        } catch (error) {
            console.error('Error leaving challenge:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to leave challenge'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getChallengeLeaderboard(challengeId, userId) {
        try {
            const sql = `
        SELECT 
          u.id,
          u.name,
          u.username,
          u.photo,
          cp.current_progress as progress,
          cp.completed_at as completedAt,
          cp.joined_at as joinedAt,
          CASE WHEN u.id = ? THEN true ELSE false END as isCurrentUser
        FROM challenge_participants cp
        INNER JOIN users u ON cp.user_id = u.id
        WHERE cp.challenge_id = ?
        ORDER BY cp.current_progress DESC, cp.completed_at ASC, cp.joined_at ASC
      `;
            const [results] = await _database.db.execute(sql, [
                userId,
                challengeId
            ]);
            // Add ranking
            const leaderboard = results.map((participant, index)=>({
                    ...participant,
                    rank: index + 1
                }));
            return {
                status: 'success',
                data: leaderboard
            };
        } catch (error) {
            console.error('Error getting challenge leaderboard:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get challenge leaderboard'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateChallengeProgress(challengeId, userId, progressData) {
        try {
            const { progress, completed = false } = progressData;
            // Check if participating
            const [participation] = await _database.db.execute('SELECT id, current_progress FROM challenge_participants WHERE challenge_id = ? AND user_id = ?', [
                challengeId,
                userId
            ]);
            if (!participation[0]) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Not participating in this challenge'
                }, _common.HttpStatus.NOT_FOUND);
            }
            // Update progress
            const updateData = {
                current_progress: progress,
                updated_at: new Date().toISOString()
            };
            if (completed) {
                updateData.completed_at = new Date().toISOString();
                updateData.status = 'completed';
            }
            await _database.db.execute(`UPDATE challenge_participants 
         SET current_progress = ?, completed_at = ?, status = ?, updated_at = NOW()
         WHERE challenge_id = ? AND user_id = ?`, [
                progress,
                completed ? new Date().toISOString() : null,
                completed ? 'completed' : 'active',
                challengeId,
                userId
            ]);
            // Award points if completed
            if (completed) {
                const [challenge] = await _database.db.execute('SELECT reward_points FROM challenges WHERE id = ?', [
                    challengeId
                ]);
                if (challenge[0]?.reward_points > 0) {
                    await this.awardPoints(userId, challenge[0].reward_points, 'challenge_completion');
                }
            }
            return {
                status: 'success',
                message: completed ? 'Challenge completed!' : 'Progress updated successfully'
            };
        } catch (error) {
            console.error('Error updating challenge progress:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to update challenge progress'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async awardPoints(userId, points, reason) {
        try {
            // Update user points
            await _database.db.execute(`INSERT INTO user_points (user_id, total_points, monthly_points, yearly_points, updated_at)
         VALUES (?, ?, ?, ?, NOW())
         ON DUPLICATE KEY UPDATE
         total_points = total_points + ?,
         monthly_points = monthly_points + ?,
         yearly_points = yearly_points + ?,
         updated_at = NOW()`, [
                userId,
                points,
                points,
                points,
                points,
                points,
                points
            ]);
            // Log points transaction
            await _database.db.execute(`INSERT INTO points_transactions (user_id, points, type, reason, created_at)
         VALUES (?, ?, 'earned', ?, NOW())`, [
                userId,
                points,
                reason
            ]);
            return true;
        } catch (error) {
            console.error('Error awarding points:', error);
            return false;
        }
    }
};
ChallengesService = _ts_decorate([
    (0, _common.Injectable)()
], ChallengesService);

//# sourceMappingURL=challenges.service.js.map