@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Shimmer animation for skeleton loading */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@supports (font-variation-settings: normal) {
  :root {
    font-family: 'Poppins', sans-serif;
    --snapfit-green: #B9FF43;
    --snapfit-dark-green: #1A3201;
    --snapfit-black: #000000;
    --snapfit-gray: #1E1E1E;
    --snapfit-light-gray: #F5F5F5;

    /* Mobile-first approach */
    font-size: 14px;
  }
}

@layer base {
  html {
    -webkit-tap-highlight-color: transparent;
    background-color: #000000 !important;
  }

  #root {
    background-color: #000000 !important;
    min-height: 100vh;
  }

  body {
    @apply bg-black min-h-screen text-white transition-colors duration-300;
    background:
      radial-gradient(circle at 10% 20%, rgba(185, 255, 67, 0.03) 0%, transparent 20%),
      radial-gradient(circle at 90% 80%, rgba(185, 255, 67, 0.03) 0%, transparent 20%),
      linear-gradient(to bottom, #000000, #121212) !important;
    background-attachment: fixed;
    overscroll-behavior-y: none;
    position: relative;
  }

  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23B9FF43' fill-opacity='0.03'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    pointer-events: none;
    z-index: -1;
  }

  /* Modern Typography - Mobile-first approach */
  h1 {
    @apply text-2xl font-bold font-display tracking-tight text-white;
    text-shadow: 0 0 10px rgba(185, 255, 67, 0.3);
  }

  h2 {
    @apply text-xl font-bold font-display tracking-tight text-white;
  }

  h3 {
    @apply text-lg font-semibold tracking-tight text-white;
  }

  p {
    @apply text-gray-400 leading-relaxed;
  }

  .text-gradient {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-snapfit-green to-snapfit-green-light;
  }

  .text-glow {
    text-shadow: 0 0 8px rgba(185, 255, 67, 0.5);
  }

  /* Tablet and above */
  @media (min-width: 640px) {
    h1 {
      @apply text-3xl;
    }

    h2 {
      @apply text-2xl;
    }

    h3 {
      @apply text-xl;
    }
  }

  /* Safe area for iOS devices */
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

  .safe-top {
    padding-top: env(safe-area-inset-top, 0);
  }

  /* Scrollbar hiding */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Custom scrollbar for modals */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(185, 255, 67, 0.5) rgba(30, 30, 30, 0.5);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgba(30, 30, 30, 0.5);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgba(185, 255, 67, 0.5);
    border-radius: 3px;
    transition: background 0.3s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgba(185, 255, 67, 0.8);
  }

  .scrollbar-track-snapfit-dark-gray::-webkit-scrollbar-track {
    background: rgba(30, 30, 30, 0.8);
  }

  .scrollbar-thumb-snapfit-green\/50::-webkit-scrollbar-thumb {
    background: rgba(185, 255, 67, 0.5);
  }

  .hover\:scrollbar-thumb-snapfit-green:hover::-webkit-scrollbar-thumb {
    background: rgba(185, 255, 67, 0.8);
  }
}

@layer components {
  /* Modern Card Styles - Based on Behance References */
  .card {
    @apply bg-snapfit-gray rounded-2xl shadow-lg p-4 sm:p-6
           hover:shadow-xl transition-all duration-300
           hover:translate-y-[-5px] relative overflow-hidden
           w-full mx-auto border-0;
  }

  .card-highlight {
    @apply bg-snapfit-gray rounded-2xl shadow-lg p-4 sm:p-6
           hover:shadow-xl transition-all duration-300
           hover:translate-y-[-5px] relative overflow-hidden
           w-full mx-auto border-0
           ring-2 ring-snapfit-green;
  }

  .card-progress {
    @apply bg-snapfit-gray rounded-2xl shadow-lg p-4 sm:p-6
           hover:shadow-xl transition-all duration-300
           hover:translate-y-[-5px] relative overflow-hidden
           w-full mx-auto border-0;
  }

  .card-progress-tag {
    @apply inline-block px-3 py-1 bg-snapfit-green text-black rounded-full
           text-xs font-bold mb-2;
  }

  .card-gradient {
    @apply bg-gradient-to-br from-snapfit-gray to-black
           rounded-2xl shadow-lg p-4 sm:p-6
           hover:shadow-xl transition-all duration-300
           hover:translate-y-[-5px] relative overflow-hidden
           w-full mx-auto border-0;
  }

  .card-glass {
    @apply bg-snapfit-gray/90 backdrop-blur-md
           rounded-2xl shadow-lg p-4 sm:p-6
           hover:shadow-xl transition-all duration-300
           hover:translate-y-[-5px] relative overflow-hidden
           w-full mx-auto border-0;
  }

  /* Card variations */
  .card-stat {
    @apply flex flex-col items-center justify-center text-center p-4 sm:p-5
           bg-snapfit-gray rounded-2xl shadow-lg
           border-t-4 border-snapfit-green
           hover:shadow-xl transition-all duration-300
           hover:translate-y-[-5px];
  }

  .card-stat-value {
    @apply text-2xl sm:text-3xl font-bold text-snapfit-green;
  }

  .card-stat-label {
    @apply text-sm text-gray-300 mt-1;
  }

  /* Button Styles - Based on Behance References */
  .btn-primary {
    @apply bg-snapfit-green text-black rounded-full px-6 py-3
           font-bold hover:bg-snapfit-green/90 transition-all duration-300
           shadow-lg hover:shadow-snapfit-green/50 active:scale-95
           relative overflow-hidden flex items-center justify-center gap-2;
    position: relative;
    z-index: 1;
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255,255,255,0.8) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: -1;
  }

  .btn-primary:hover::before {
    opacity: 0.4;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { transform: scale(0.8); opacity: 0.4; }
    50% { transform: scale(1.2); opacity: 0.2; }
    100% { transform: scale(0.8); opacity: 0.4; }
  }

  .btn-primary::after {
    @apply content-[''] absolute top-0 left-0 w-full h-full bg-white/20
           transform -translate-x-full skew-x-12 transition-transform duration-700;
    z-index: -2;
  }

  .btn-primary:hover::after {
    @apply transform translate-x-full;
  }

  .btn-primary-arrow {
    @apply bg-snapfit-green text-black rounded-full px-6 py-3
           font-bold hover:bg-snapfit-green/90 transition-all duration-300
           shadow-lg hover:shadow-snapfit-green/50 active:scale-95
           relative overflow-hidden flex items-center justify-between;
  }

  .btn-primary-arrow svg {
    @apply ml-2 transition-transform duration-300;
  }

  .btn-primary-arrow:hover svg {
    @apply transform translate-x-1;
  }

  .btn-secondary {
    @apply bg-snapfit-gray text-white rounded-full px-6 py-3
           font-bold hover:bg-snapfit-gray/80 transition-all duration-300
           shadow-lg hover:shadow-snapfit-green/25 active:scale-95
           border border-snapfit-green/30;
  }

  .btn-glass {
    @apply bg-snapfit-gray/50 backdrop-blur-md
           text-white rounded-full px-6 py-3
           font-bold hover:bg-snapfit-gray/70 transition-all duration-300
           shadow-lg hover:shadow-xl active:scale-95
           border border-snapfit-green/30;
  }

  .btn-icon {
    @apply p-3 rounded-full bg-snapfit-gray hover:bg-snapfit-green/20
           text-white hover:text-snapfit-green
           transition-all duration-300 active:scale-95
           border border-snapfit-green/30;
    position: relative;
    overflow: hidden;
  }

  .btn-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(185, 255, 67, 0.4) 0%, transparent 70%);
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    opacity: 0;
  }

  .btn-icon:hover::before {
    transform: translate(-50%, -50%) scale(2);
    opacity: 1;
  }

  .btn-icon-small {
    @apply p-2 rounded-full bg-snapfit-gray hover:bg-snapfit-green/20
           text-white hover:text-snapfit-green
           transition-all duration-300 active:scale-95
           border border-snapfit-green/30;
  }

  .btn-outline {
    @apply bg-transparent text-snapfit-green rounded-full px-6 py-3
           font-bold hover:bg-snapfit-green/10 transition-all duration-300
           shadow-none hover:shadow-snapfit-green/25 active:scale-95
           border border-snapfit-green;
  }

  .btn-tab {
    @apply px-4 py-2 rounded-full text-white hover:text-snapfit-green
           font-medium transition-all duration-300
           hover:bg-snapfit-gray;
  }

  .btn-tab-active {
    @apply px-4 py-2 rounded-full text-black bg-snapfit-green
           font-medium transition-all duration-300;
  }

  /* Input Styles - Based on Behance References */
  .input {
    @apply w-full p-4 border border-snapfit-gray/50 rounded-xl
           bg-snapfit-gray text-white
           focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green
           transition-all duration-300 shadow-sm hover:shadow-md;
  }

  .input-search {
    @apply w-full p-3 pl-10 border border-snapfit-gray/50 rounded-full
           bg-snapfit-gray text-white
           focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green
           transition-all duration-300 shadow-sm hover:shadow-md;
  }

  .input-group {
    @apply relative flex items-center;
  }

  .input-icon {
    @apply absolute left-3 text-snapfit-green;
  }

  /* Navigation Styles - Based on Behance References */
  .nav-item {
    @apply flex flex-col items-center py-2 px-2 text-xs sm:text-sm
           font-medium transition-all duration-300;
  }

  .nav-item-active {
    @apply text-snapfit-green relative;
  }

  .nav-item-icon {
    @apply p-2 rounded-full bg-snapfit-green text-black mb-1;
  }

  .nav-item-inactive-icon {
    @apply p-2 rounded-full bg-transparent text-gray-400 mb-1;
  }

  .nav-item-inactive {
    @apply text-gray-400 hover:text-snapfit-green;
  }

  .nav-container {
    @apply fixed bottom-0 left-0 right-0 bg-snapfit-gray/95 backdrop-blur-lg
           border-t border-snapfit-green/20 px-2 py-1 z-10
           flex justify-around items-center safe-bottom;
  }

  /* Z-Index Hierarchy for SnapFit */
  /*
    z-0 to z-10: Base layout (header, navigation, content)
    z-20 to z-50: Dropdowns, tooltips, popovers
    z-100 to z-1000: RSuite components (modals, drawers, notifications)
    z-9999: Critical overlays (account deletion, system modals)
  */

  /* Modal overlay styles to ensure visibility */
  .modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 9999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
    overflow-y: auto !important;
  }

  .modal-content {
    position: relative !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    margin: 2rem 0 !important;
  }

  /* Ensure custom modals are above RSuite components */
  .snapfit-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 999999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
    overflow-y: auto !important;
    pointer-events: auto !important;
  }

  /* Portal container for modals */
  #modal-root {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    z-index: 999999 !important;
  }

  /* Critical modal styles that override everything */
  .critical-modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 999999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 1rem !important;
    overflow-y: auto !important;
    pointer-events: auto !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(4px) !important;
  }

  .critical-modal-content {
    position: relative !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    margin: 2rem 0 !important;
    pointer-events: auto !important;
    z-index: 999999 !important;
  }

  /* Force all other elements to be below critical modals */
  body > *:not(#modal-root) {
    position: relative;
    z-index: 1;
  }

  /* Ensure no element can override critical modal z-index */
  .critical-modal,
  .critical-modal *,
  #modal-root,
  #modal-root * {
    z-index: 999999 !important;
  }

  /* Disable backdrop-blur on elements when modal is open */
  body.modal-open header,
  body.modal-open nav,
  body.modal-open .backdrop-blur-lg,
  body.modal-open .backdrop-blur-md,
  body.modal-open .backdrop-blur-sm {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  /* Toggle Switch - Based on Behance References */
  .toggle-switch {
    @apply relative inline-block w-14 h-7;
  }

  .toggle-switch input {
    @apply opacity-0 w-0 h-0;
  }

  .toggle-slider {
    @apply absolute cursor-pointer top-0 left-0 right-0 bottom-0
           bg-snapfit-gray transition-all duration-300 rounded-full
           shadow-inner border border-snapfit-green/30;
  }

  .toggle-slider:before {
    @apply absolute content-[''] h-5 w-5 left-1 bottom-1
           bg-white transition-all duration-300 rounded-full
           shadow-md;
  }

  input:checked + .toggle-slider {
    @apply bg-snapfit-green;
  }

  input:checked + .toggle-slider:before {
    @apply transform translate-x-7 bg-black scale-110;
  }

  /* Animation Classes */
  .animate-fade-in {
    animation: fade-in 0.5s ease-out forwards;
  }

  .animate-slide-up {
    animation: slide-up 0.5s ease-out forwards;
  }

  .animate-slide-down {
    animation: slide-down 0.5s ease-out forwards;
  }

  .animate-scale-in {
    animation: scale-in 0.3s ease-out forwards;
  }

  .animate-bounce-in {
    animation: bounce-in 0.5s ease-out forwards;
  }

  /* Custom Animations */
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slide-up {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes slide-down {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes scale-in {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }

  @keyframes bounce-in {
    0% { transform: scale(0.8); opacity: 0; }
    70% { transform: scale(1.05); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
  }

  @keyframes sound-wave {
    0% { transform: scaleY(0.3); }
    50% { transform: scaleY(1); }
    100% { transform: scaleY(0.3); }
  }

  @keyframes pulse-slow {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }

  @keyframes bubble {
    0% { transform: translateY(100%); opacity: 0.5; }
    50% { transform: translateY(50%) translateX(10px); opacity: 0.7; }
    100% { transform: translateY(0%); opacity: 0; }
  }

  @keyframes slide-in-right {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  @keyframes slide-in-left {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  .animate-sound-wave {
    animation: sound-wave 1s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-bubble {
    animation: bubble 3s ease-in-out infinite;
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.5s ease-out;
  }

  .animate-slide-in-left {
    animation: slide-in-left 0.5s ease-out;
  }

  .animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
  }

  /* Staggered animations for lists */
  .stagger-item:nth-child(1) { animation-delay: 0.1s; }
  .stagger-item:nth-child(2) { animation-delay: 0.2s; }
  .stagger-item:nth-child(3) { animation-delay: 0.3s; }
  .stagger-item:nth-child(4) { animation-delay: 0.4s; }
  .stagger-item:nth-child(5) { animation-delay: 0.5s; }
  .stagger-item:nth-child(6) { animation-delay: 0.6s; }
  .stagger-item:nth-child(7) { animation-delay: 0.7s; }
  .stagger-item:nth-child(8) { animation-delay: 0.8s; }

  /* Easing functions */
  .ease-out-expo {
    transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  }

  /* Modern Chart Styles */
  .chart-container {
    @apply w-full h-64 sm:h-80 p-4 bg-snapfit-gray rounded-2xl shadow-lg
           border-l-4 border-snapfit-green
           relative overflow-hidden;
  }

  .chart-container::before {
    @apply content-[''] absolute top-0 left-0 w-1 h-full bg-snapfit-green
           opacity-50;
  }

  .chart-title {
    @apply text-lg font-bold text-white mb-4;
  }

  .chart-legend {
    @apply flex flex-wrap gap-4 mt-4;
  }

  .chart-legend-item {
    @apply flex items-center gap-2;
  }

  .chart-legend-color {
    @apply w-3 h-3 rounded-full;
  }

  .chart-legend-label {
    @apply text-sm text-gray-300;
  }

  /* Progress Bar */
  .progress-bar {
    @apply w-full h-2 bg-gray-800 rounded-full overflow-hidden;
  }

  .progress-bar-fill {
    @apply h-full bg-snapfit-green rounded-full transition-all duration-500 ease-out;
  }

  /* Circular Progress */
  .circular-progress {
    @apply relative w-24 h-24 rounded-full;
  }

  .circular-progress-bg {
    @apply absolute inset-0 border-4 border-gray-800 rounded-full;
  }

  .circular-progress-fill {
    @apply absolute inset-0 border-4 border-snapfit-green rounded-full;
    clip: rect(0px, 24px, 48px, 0px);
    transform: rotate(0deg);
  }

  .circular-progress-value {
    @apply absolute inset-0 flex items-center justify-center text-lg font-bold text-snapfit-green;
  }

  /* Data Grid */
  .data-grid {
    @apply w-full overflow-x-auto;
  }

  .data-grid-table {
    @apply w-full border-collapse;
  }

  .data-grid-header {
    @apply bg-snapfit-gray/20 text-left;
  }

  .data-grid-header th {
    @apply p-3 font-semibold text-gray-300 text-sm;
  }

  .data-grid-body tr {
    @apply border-b border-gray-800;
  }

  .data-grid-body tr:last-child {
    @apply border-b-0;
  }

  /* Scrollbar hide utility */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .data-grid-body td {
    @apply p-3 text-white;
  }

  /* Mobile-specific styles */
  @media (max-width: 768px) {
    .mobile-cards-wrapper {
      @apply relative;
    }

    .mobile-scroll-container {
      @apply overflow-x-auto pb-4 -mx-4 px-4;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
    }

    .mobile-scroll-container::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .mobile-scroll-content {
      @apply flex gap-4 pb-2;
      /* Remove grid on mobile to enable horizontal scroll */
      display: flex !important;
    }

    .mobile-card {
      @apply flex-shrink-0;
      width: calc(82vw - 2rem); /* Show more of the next card to indicate scrollability */
      max-width: 280px;
      min-width: 240px;
    }

    /* Hide scroll indicator - clean minimal approach */
    .mobile-scroll-indicator {
      display: none;
    }
  }

  /* Desktop styles - ensure grid works properly */
  @media (min-width: 769px) {
    .mobile-cards-wrapper {
      @apply relative;
    }

    .mobile-scroll-container {
      @apply overflow-visible;
    }

    .mobile-scroll-content {
      @apply gap-4;
      display: grid !important;
      grid-template-columns: repeat(2, 1fr);
    }

    .mobile-card {
      @apply w-full;
    }

    /* Hide scroll indicator on desktop */
    .mobile-scroll-indicator {
      display: none;
    }
  }

  /* Large desktop styles - show 3 items per row on very large screens */
  @media (min-width: 1280px) {
    .mobile-scroll-content {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  /* Responsive padding adjustments */
  @media (max-width: 640px) {
    .card, .card-gradient, .card-glass {
      @apply p-4;
    }

    h1 {
      @apply text-xl;
    }

    h2 {
      @apply text-lg;
    }

    h3 {
      @apply text-base;
    }

    .btn-primary, .btn-secondary, .btn-glass {
      @apply px-4 py-2 text-sm;
    }
  }

  /* Scrollbar personalizada para containers de exercícios */
  .exercise-scroll-container::-webkit-scrollbar {
    width: 6px;
  }

  .exercise-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  .exercise-scroll-container::-webkit-scrollbar-thumb {
    background: #00ff88;
    border-radius: 3px;
  }

  .exercise-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #00cc6a;
  }

  /* Esconder scrollbar nos filtros horizontais */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}