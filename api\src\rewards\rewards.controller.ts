import { 
  Controller, 
  Get, 
  Post, 
  Param, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RewardsService } from './rewards.service';

@Controller('rewards')
@UseGuards(JwtAuthGuard)
export class RewardsController {
  constructor(private readonly rewardsService: RewardsService) {}

  @Get('available')
  async getAvailableRewards(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.rewardsService.getAvailableRewards(userId, query);
  }

  @Get('my')
  async getMyRewards(@Request() req: any) {
    const userId = req.user.userId;
    return this.rewardsService.getMyRewards(userId);
  }

  @Post(':id/redeem')
  async redeemReward(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.rewardsService.redeemReward(Number(id), userId);
  }

  @Get('categories')
  async getRewardCategories() {
    return this.rewardsService.getRewardCategories();
  }
}
