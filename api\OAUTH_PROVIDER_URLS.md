# OAuth Provider URLs Configuration

## ✅ Configuração Corrigida

Agora temos URLs específicas para cada provider OAuth, conforme solicitado.

### Variáveis de Ambiente (.env)

```env
# OAuth Configuration
GOOGLE_CLIENT_ID=556809602963-2bc3l6ndjl7ti86f5ik0u5usl51h7d59.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
APPLE_CLIENT_ID=br.com.mysnapfit.auth
APPLE_TEAM_ID=BH5AGNXCJC
APPLE_KEY_ID=8JA3C8P9XW
APPLE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----

# OAuth Redirect URLs - Frontend callback URLs
GOOGLE_REDIRECT_URL=https://mysnapfit.com.br/auth/callback
APPLE_REDIRECT_URL=https://mysnapfit.com.br/auth/callback
```

### Fluxo OAuth Completo

#### Google OAuth
1. **Iniciação**: `https://mysnapfit.com.br/auth/oauth/google`
2. **API Redirect**: `https://api.mysnapfit.com.br/auth/google`
3. **Google OAuth**: Usuário autoriza no Google
4. **API Callback**: `https://api.mysnapfit.com.br/auth/google/callback`
5. **Frontend Redirect**: `https://mysnapfit.com.br/auth/callback?access_token=...`

#### Apple OAuth
1. **Iniciação**: `https://mysnapfit.com.br/auth/oauth/apple`
2. **API Redirect**: `https://api.mysnapfit.com.br/auth/apple`
3. **Apple OAuth**: Usuário autoriza na Apple
4. **API Callback**: `https://api.mysnapfit.com.br/auth/apple/callback`
5. **Frontend Redirect**: `https://mysnapfit.com.br/auth/callback?access_token=...`

### Configurações nos Consoles OAuth

#### Google Cloud Console
- **Authorized JavaScript origins**: `https://mysnapfit.com.br`
- **Authorized redirect URIs**: `https://api.mysnapfit.com.br/auth/google/callback`

#### Apple Developer Console
- **Domains and Subdomains**: `mysnapfit.com.br`
- **Return URLs**: `https://api.mysnapfit.com.br/auth/apple/callback`

### Código Atualizado

#### AuthController
```typescript
// Google callback
@Get('google/callback')
async googleAuthRedirect(@Request() req: any, @Res() res: any) {
  const redirectUrl = `${process.env.GOOGLE_REDIRECT_URL}?access_token=...`;
  return res.redirect(redirectUrl);
}

// Apple callback
@Post('apple/callback')
async appleAuthRedirect(@Request() req: any, @Res() res: any) {
  const redirectUrl = `${process.env.APPLE_REDIRECT_URL}?access_token=...`;
  return res.redirect(redirectUrl);
}
```

### Vantagens da Nova Configuração

1. **URLs Específicas**: Cada provider tem sua própria URL de redirect
2. **Flexibilidade**: Pode redirecionar para diferentes páginas se necessário
3. **Debugging**: Mais fácil debuggar problemas específicos de cada provider
4. **Configuração Limpa**: Separação clara entre Google e Apple
5. **Escalabilidade**: Fácil adicionar novos providers no futuro

### Teste da Configuração

Execute o script de teste:
```bash
node test-oauth-config.js
```

Resultado esperado:
```
✅ Nenhum problema encontrado!
```

### Próximos Passos

1. **Reiniciar o servidor** para aplicar as mudanças
2. **Testar o fluxo OAuth** completo
3. **Verificar logs** para confirmar que as URLs estão corretas
4. **Validar** que ambos os providers redirecionam corretamente

A configuração agora está otimizada com URLs específicas para cada provider OAuth! 🎉
