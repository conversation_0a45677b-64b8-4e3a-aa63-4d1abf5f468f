import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsController } from './clients.controller';
import { ClientsService } from './clients.service';
import { User } from '../users/entities/user.entity';
import { Protocol } from '../protocols/entities/protocol.entity';
import { Assessment } from '../progress/entities/assessment.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Protocol, Assessment])
  ],
  controllers: [ClientsController],
  providers: [ClientsService],
  exports: [ClientsService]
})
export class ClientsModule {}