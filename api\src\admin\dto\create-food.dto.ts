// src/admin/dto/create-food.dto.ts
import { IsString, IsNotEmpty, IsInt, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateFoodDto {
  @IsString()
  @IsNotEmpty()
  name: string; // Nome do alimento

  @IsInt()
  @Type(() => Number)
  category_id: number; // ID da categoria

  @IsNumber()
  @Min(0)
  quantity: number; // Quantidade

  @IsString()
  unit: string; // Unidade

  @IsNumber()
  @Min(0)
  calories: number; // Calorias

  @IsNumber()
  @Min(0)
  protein: number; // Proteína

  @IsNumber()
  @Min(0)
  carbs: number; // Carboidratos

  @IsNumber()
  @Min(0)
  fat: number; // Gorduras

  @IsNumber()
  @Min(0)
  fiber: number; // Fibras
}