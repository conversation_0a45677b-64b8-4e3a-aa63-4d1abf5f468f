"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AdminController", {
    enumerable: true,
    get: function() {
        return AdminController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _adminservice = require("./admin.service");
const _getusersquerydto = require("./get-users-query.dto");
const _createuserdto = require("./dto/create-user.dto");
const _updateuserdto = require("./dto/update-user.dto");
const _getallfoodsquerydto = require("./dto/get-all-foods-query.dto");
const _createfooddto = require("./dto/create-food.dto");
const _updatefooddto = require("./dto/update-food.dto");
const _createexercisedto = require("./dto/create-exercise.dto");
const _getallexercisesquerydto = require("./dto/get-all-exercises-query.dto");
const _createplandto = require("./dto/plans/create-plan.dto");
const _updateplandto = require("./dto/plans/update-plan.dto");
const _configplandto = require("./dto/plans/config-plan.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let AdminController = class AdminController {
    // Stats
    // @UseGuards(JwtAuthGuard)
    getStats() {
        return this.adminService.getStats();
    }
    // Users
    // @UseGuards(JwtAuthGuard)
    getAllUsers(query) {
        return this.adminService.getAllUsers(query);
    }
    createUser(createUserDto) {
        return this.adminService.createUser(createUserDto);
    }
    async updateUser(id, updateUserDto) {
        return this.adminService.updateUser(Number(id), updateUserDto);
    }
    async deleteUser(id) {
        return this.adminService.deleteUser(Number(id));
    }
    // Foods
    async getSelectOptions(type) {
        /*
      const types_allowed = ['foods_categories'];

      if (!types_allowed.includes(type)) {
        throw new NotFoundException('Type not found');
      }
        */ return this.adminService.getSelectOptions(type);
    }
    async getAllFoods(query) {
        return this.adminService.getAllFoods(query);
    }
    async searchFoods(query) {
        return this.adminService.searchFoods(query);
    }
    async createFood(createFoodDto) {
        return this.adminService.createFood(createFoodDto);
    }
    async updateFood(id, updateFoodDto) {
        return this.adminService.updateFood(Number(id), updateFoodDto);
    }
    async deleteFood(id) {
        return this.adminService.deleteFood(Number(id));
    }
    // Exercícios
    async getAllExercises(query) {
        return this.adminService.getAllExercises(query);
    }
    async createExercise(createExercise) {
        return this.adminService.createExercise(createExercise);
    }
    async deleteExercise(id) {
        return this.adminService.deleteExercise(Number(id));
    }
    // Plans management
    async getAllPlans() {
        return this.adminService.getAllPlans();
    }
    async createPlan(req, createPlanDto) {
        const userId = req.user.userId;
        return this.adminService.createPlan(createPlanDto, userId);
    }
    async updatePlan(id, updatePlanDto) {
        return this.adminService.updatePlan(Number(id), updatePlanDto);
    }
    async configPlan(id, configPlanDto) {
        return this.adminService.configPlan(Number(id), configPlanDto);
    }
    async deletePlan(id) {
        return this.adminService.deletePlan(Number(id));
    }
    async deletePlanConfig(id) {
        return this.adminService.deletePlanConfig(Number(id));
    }
    // Subscriptions with pagination
    async getAllSubscriptions(query) {
        return this.adminService.getAllSubscriptions(query);
    }
    // Transactions
    async getAllTransactions(query) {
        return this.adminService.getAllTransactions(query);
    }
    // Affiliates
    async getAffiliates(query) {
        return this.adminService.getAffiliates(query);
    }
    async updateAffiliate(id, updateAffiliateDto) {
        return this.adminService.updateAffiliate(Number(id), updateAffiliateDto);
    }
    // Subscriptions
    async getUserSubscriptions(id) {
        return this.adminService.getUserSubscriptions(Number(id));
    }
    async getUserTransactions(id) {
        return this.adminService.getUserTransactions(Number(id));
    }
    async getTransactionDetails(userId, id) {
        return this.adminService.getTransactionDetails(Number(id), Number(userId));
    }
    // Webhook Stripe
    async webhook(body, req) {
        // Em produção, você deve verificar a assinatura do webhook
        // const signature = req.headers['stripe-signature'];
        return this.adminService.webhook(body);
    }
    // Registrar comissões manualmente
    async registerCommissionsForSubscription(id) {
        return this.adminService.registerCommissionsForSubscription(Number(id));
    }
    constructor(adminService){
        this.adminService = adminService;
    }
};
_ts_decorate([
    (0, _common.Get)('stats'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], AdminController.prototype, "getStats", null);
_ts_decorate([
    (0, _common.Get)('users'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _getusersquerydto.GetAllUsersQueryDto === "undefined" ? Object : _getusersquerydto.GetAllUsersQueryDto
    ]),
    _ts_metadata("design:returntype", void 0)
], AdminController.prototype, "getAllUsers", null);
_ts_decorate([
    (0, _common.Post)('users'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createuserdto.CreateUserDto === "undefined" ? Object : _createuserdto.CreateUserDto
    ]),
    _ts_metadata("design:returntype", void 0)
], AdminController.prototype, "createUser", null);
_ts_decorate([
    (0, _common.Put)('users/:id'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _updateuserdto.UpdateUserDto === "undefined" ? Object : _updateuserdto.UpdateUserDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "updateUser", null);
_ts_decorate([
    (0, _common.Delete)('users/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "deleteUser", null);
_ts_decorate([
    (0, _common.Get)('/select_options/:type'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Param)('type')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getSelectOptions", null);
_ts_decorate([
    (0, _common.Get)('foods'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _getallfoodsquerydto.GetAllFoodsQueryDto === "undefined" ? Object : _getallfoodsquerydto.GetAllFoodsQueryDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getAllFoods", null);
_ts_decorate([
    (0, _common.Get)('foods/search'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "searchFoods", null);
_ts_decorate([
    (0, _common.Post)('foods'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createfooddto.CreateFoodDto === "undefined" ? Object : _createfooddto.CreateFoodDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "createFood", null);
_ts_decorate([
    (0, _common.Put)('foods/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _updatefooddto.UpdateFoodDto === "undefined" ? Object : _updatefooddto.UpdateFoodDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "updateFood", null);
_ts_decorate([
    (0, _common.Delete)('foods/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "deleteFood", null);
_ts_decorate([
    (0, _common.Get)('exercises'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _getallexercisesquerydto.GetAllExercisesQueryDto === "undefined" ? Object : _getallexercisesquerydto.GetAllExercisesQueryDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getAllExercises", null);
_ts_decorate([
    (0, _common.Post)('exercises'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createexercisedto.CreateExerciseDto === "undefined" ? Object : _createexercisedto.CreateExerciseDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "createExercise", null);
_ts_decorate([
    (0, _common.Delete)('exercises/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "deleteExercise", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('plans'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getAllPlans", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('plans'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        typeof _createplandto.CreatePlanDto === "undefined" ? Object : _createplandto.CreatePlanDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "createPlan", null);
_ts_decorate([
    (0, _common.Put)('plans/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _updateplandto.UpdatePlanDto === "undefined" ? Object : _updateplandto.UpdatePlanDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "updatePlan", null);
_ts_decorate([
    (0, _common.Post)('plans/:id/config'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _configplandto.ConfigPlanDto === "undefined" ? Object : _configplandto.ConfigPlanDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "configPlan", null);
_ts_decorate([
    (0, _common.Delete)('plans/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "deletePlan", null);
_ts_decorate([
    (0, _common.Delete)('plans/config/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "deletePlanConfig", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('subscriptions'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getAllSubscriptions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('transactions'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getAllTransactions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('affiliates'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getAffiliates", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Put)('affiliates/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "updateAffiliate", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('users/:id/subscriptions'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getUserSubscriptions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('users/:id/transactions'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getUserTransactions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('users/:userId/transactions/:id'),
    _ts_param(0, (0, _common.Param)('userId')),
    _ts_param(1, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "getTransactionDetails", null);
_ts_decorate([
    (0, _common.Post)('webhook'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "webhook", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('subscriptions/:id/register-commissions'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], AdminController.prototype, "registerCommissionsForSubscription", null);
AdminController = _ts_decorate([
    (0, _common.Controller)('admin'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _adminservice.AdminService === "undefined" ? Object : _adminservice.AdminService
    ])
], AdminController);

//# sourceMappingURL=admin.controller.js.map