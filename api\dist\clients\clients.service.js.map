{"version": 3, "sources": ["../../src/clients/clients.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { CreateClientDto } from './dto/create-client.dto';\nimport { UpdateClientDto } from './dto/update-client.dto';\nimport { db } from '../database';\n\n@Injectable()\nexport class ClientsService {\n  \n  async getClients(professionalId: number, query: any) {\n    try {\n      const { search, page = 1, limit = 10, status } = query;\n      const offset = (page - 1) * limit;\n\n      let sql = `\n        SELECT \n          u.id,\n          u.name,\n          u.email,\n          u.phone,\n          u.photo,\n          u.created_at as createdAt,\n          u.updated_at as updatedAt,\n          up.height,\n          up.weight,\n          up.activity_level as activityLevel,\n          up.goals,\n          up.medical_conditions as medicalConditions,\n          up.allergies,\n          pc.created_at as relationshipCreatedAt,\n          pc.status as relationshipStatus,\n          pc.notes\n        FROM users u\n        INNER JOIN professional_clients pc ON u.id = pc.client_id\n        LEFT JOIN user_profiles up ON u.id = up.user_id\n        WHERE pc.professional_id = ?\n      `;\n\n      const params: any[] = [professionalId];\n\n      if (search) {\n        sql += ` AND (u.name LIKE ? OR u.email LIKE ?)`;\n        params.push(`%${search}%`, `%${search}%`);\n      }\n\n      if (status) {\n        sql += ` AND pc.status = ?`;\n        params.push(status);\n      }\n\n      sql += ` ORDER BY u.name ASC LIMIT ? OFFSET ?`;\n      params.push(limit, offset);\n\n      const clients = await db.execute(sql, params);\n\n      // Get total count for pagination\n      let countSql = `\n        SELECT COUNT(*) as total\n        FROM users u\n        INNER JOIN professional_clients pc ON u.id = pc.client_id\n        WHERE pc.professional_id = ?\n      `;\n      const countParams: any[] = [professionalId];\n\n      if (search) {\n        countSql += ` AND (u.name LIKE ? OR u.email LIKE ?)`;\n        countParams.push(`%${search}%`, `%${search}%`);\n      }\n\n      if (status) {\n        countSql += ` AND pc.status = ?`;\n        countParams.push(status);\n      }\n\n      const [countResult] = await db.execute(countSql, countParams);\n      const total = countResult[0]?.total || 0;\n\n      return {\n        status: 'success',\n        data: {\n          clients: clients[0],\n          pagination: {\n            page: Number(page),\n            limit: Number(limit),\n            total,\n            totalPages: Math.ceil(total / limit)\n          }\n        }\n      };\n    } catch (error) {\n      console.error('Error getting clients:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get clients'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getClient(clientId: number, professionalId: number) {\n    try {\n      const sql = `\n        SELECT \n          u.id,\n          u.name,\n          u.email,\n          u.phone,\n          u.photo,\n          u.created_at as createdAt,\n          u.updated_at as updatedAt,\n          up.height,\n          up.weight,\n          up.activity_level as activityLevel,\n          up.goals,\n          up.medical_conditions as medicalConditions,\n          up.allergies,\n          pc.created_at as relationshipCreatedAt,\n          pc.status as relationshipStatus,\n          pc.notes\n        FROM users u\n        INNER JOIN professional_clients pc ON u.id = pc.client_id\n        LEFT JOIN user_profiles up ON u.id = up.user_id\n        WHERE u.id = ? AND pc.professional_id = ?\n      `;\n\n      const [result] = await db.execute(sql, [clientId, professionalId]);\n      \n      if (!result[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      return {\n        status: 'success',\n        data: result[0]\n      };\n    } catch (error) {\n      console.error('Error getting client:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get client'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async createClient(createClientDto: CreateClientDto, professionalId: number) {\n    try {\n      // Check if email already exists\n      const [existingUser] = await db.execute(\n        'SELECT id FROM users WHERE email = ?',\n        [createClientDto.email]\n      );\n\n      if (existingUser[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Email already exists'\n        }, HttpStatus.CONFLICT);\n      }\n\n      // Create user\n      const [userResult] = await db.execute(\n        `INSERT INTO users (name, email, password, phone, photo, role, created_at, updated_at) \n         VALUES (?, ?, ?, ?, ?, 'client', NOW(), NOW())`,\n        [\n          createClientDto.name,\n          createClientDto.email,\n          createClientDto.password, // Should be hashed in production\n          createClientDto.phone || null,\n          createClientDto.photo || null\n        ]\n      );\n\n      const userId = userResult.insertId;\n\n      // Create user profile\n      if (createClientDto.height || createClientDto.weight || createClientDto.activityLevel) {\n        await db.execute(\n          `INSERT INTO user_profiles (user_id, height, weight, activity_level, goals, medical_conditions, allergies, created_at, updated_at)\n           VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,\n          [\n            userId,\n            createClientDto.height || null,\n            createClientDto.weight || null,\n            createClientDto.activityLevel || null,\n            createClientDto.goals || null,\n            createClientDto.medicalConditions || null,\n            createClientDto.allergies || null\n          ]\n        );\n      }\n\n      // Create professional-client relationship\n      await db.execute(\n        `INSERT INTO professional_clients (professional_id, client_id, status, notes, created_at, updated_at)\n         VALUES (?, ?, 'active', ?, NOW(), NOW())`,\n        [professionalId, userId, createClientDto.notes || null]\n      );\n\n      return {\n        status: 'success',\n        message: 'Client created successfully',\n        data: {\n          id: userId,\n          ...createClientDto\n        }\n      };\n    } catch (error) {\n      console.error('Error creating client:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to create client'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async updateClient(clientId: number, updateClientDto: UpdateClientDto, professionalId: number) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // Update user basic info\n      const userFields = ['name', 'email', 'phone', 'photo'];\n      const userUpdates: string[] = [];\n      const userParams: any[] = [];\n\n      userFields.forEach(field => {\n        if (updateClientDto[field] !== undefined) {\n          userUpdates.push(`${field} = ?`);\n          userParams.push(updateClientDto[field]);\n        }\n      });\n\n      if (userUpdates.length > 0) {\n        userParams.push(clientId);\n        await db.execute(\n          `UPDATE users SET ${userUpdates.join(', ')}, updated_at = NOW() WHERE id = ?`,\n          userParams\n        );\n      }\n\n      // Update user profile\n      const profileFields = ['height', 'weight', 'activityLevel', 'goals', 'medicalConditions', 'allergies'];\n      const profileUpdates: string[] = [];\n      const profileParams: any[] = [];\n\n      profileFields.forEach(field => {\n        if (updateClientDto[field] !== undefined) {\n          const dbField = field === 'activityLevel' ? 'activity_level' : \n                         field === 'medicalConditions' ? 'medical_conditions' : field;\n          profileUpdates.push(`${dbField} = ?`);\n          profileParams.push(updateClientDto[field]);\n        }\n      });\n\n      if (profileUpdates.length > 0) {\n        profileParams.push(clientId);\n        \n        // Check if profile exists\n        const [profileExists] = await db.execute(\n          'SELECT id FROM user_profiles WHERE user_id = ?',\n          [clientId]\n        );\n\n        if (profileExists[0]) {\n          await db.execute(\n            `UPDATE user_profiles SET ${profileUpdates.join(', ')}, updated_at = NOW() WHERE user_id = ?`,\n            profileParams\n          );\n        } else {\n          // Create profile if it doesn't exist\n          const profileColumns = profileFields\n            .filter(field => updateClientDto[field] !== undefined)\n            .map(field => field === 'activityLevel' ? 'activity_level' : \n                         field === 'medicalConditions' ? 'medical_conditions' : field);\n          \n          const profileValues = profileFields\n            .filter(field => updateClientDto[field] !== undefined)\n            .map(field => updateClientDto[field]);\n\n          await db.execute(\n            `INSERT INTO user_profiles (user_id, ${profileColumns.join(', ')}, created_at, updated_at)\n             VALUES (?, ${profileColumns.map(() => '?').join(', ')}, NOW(), NOW())`,\n            [clientId, ...profileValues]\n          );\n        }\n      }\n\n      // Update notes in professional_clients relationship\n      if (updateClientDto.notes !== undefined) {\n        await db.execute(\n          'UPDATE professional_clients SET notes = ?, updated_at = NOW() WHERE professional_id = ? AND client_id = ?',\n          [updateClientDto.notes, professionalId, clientId]\n        );\n      }\n\n      return {\n        status: 'success',\n        message: 'Client updated successfully'\n      };\n    } catch (error) {\n      console.error('Error updating client:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to update client'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async deleteClient(clientId: number, professionalId: number) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // Instead of deleting, we'll mark the relationship as inactive\n      await db.execute(\n        'UPDATE professional_clients SET status = \"inactive\", updated_at = NOW() WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Client relationship deactivated successfully'\n      };\n    } catch (error) {\n      console.error('Error deleting client:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to delete client'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getClientProtocols(clientId: number, professionalId: number) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // Get workout protocols\n      const [workoutProtocols] = await db.execute(\n        `SELECT id, name, description, status, created_at as createdAt, updated_at as updatedAt\n         FROM user_protocols_workout \n         WHERE user_id = ? AND created_by = ?\n         ORDER BY created_at DESC`,\n        [clientId, professionalId]\n      );\n\n      // Get diet protocols\n      const [dietProtocols] = await db.execute(\n        `SELECT id, name, description, status, created_at as createdAt, updated_at as updatedAt\n         FROM user_protocols_diet \n         WHERE user_id = ? AND created_by = ?\n         ORDER BY created_at DESC`,\n        [clientId, professionalId]\n      );\n\n      return {\n        status: 'success',\n        data: {\n          workoutProtocols: workoutProtocols || [],\n          dietProtocols: dietProtocols || []\n        }\n      };\n    } catch (error) {\n      console.error('Error getting client protocols:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get client protocols'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getClientAssessments(clientId: number, professionalId: number) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      const [assessments] = await db.execute(\n        `SELECT id, weight, bf as bodyFat, front_image as frontImage, back_image as backImage, \n                side_image as sideImage, created_at as createdAt\n         FROM user_progress_evaluations \n         WHERE user_id = ?\n         ORDER BY created_at DESC`,\n        [clientId]\n      );\n\n      return {\n        status: 'success',\n        data: assessments || []\n      };\n    } catch (error) {\n      console.error('Error getting client assessments:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get client assessments'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async createClientProtocol(clientId: number, protocolData: any, professionalId: number) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // This would delegate to the appropriate protocol creation service\n      // For now, return a success response\n      return {\n        status: 'success',\n        message: 'Protocol created successfully',\n        data: {\n          id: Date.now(),\n          ...protocolData,\n          clientId,\n          createdBy: professionalId\n        }\n      };\n    } catch (error) {\n      console.error('Error creating client protocol:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to create client protocol'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getClientProgress(clientId: number, professionalId: number, query: any) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // This would return comprehensive progress data\n      // For now, return mock structure\n      return {\n        status: 'success',\n        data: {\n          weight: [],\n          bodyFat: [],\n          measurements: [],\n          workouts: [],\n          nutrition: []\n        }\n      };\n    } catch (error) {\n      console.error('Error getting client progress:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get client progress'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getClientNutritionData(clientId: number, professionalId: number, query: any) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // This would return nutrition tracking data\n      return {\n        status: 'success',\n        data: {\n          dailyIntake: [],\n          macros: [],\n          adherence: []\n        }\n      };\n    } catch (error) {\n      console.error('Error getting client nutrition data:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get client nutrition data'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getClientWorkoutData(clientId: number, professionalId: number, query: any) {\n    try {\n      // Verify access to client\n      const [accessCheck] = await db.execute(\n        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',\n        [professionalId, clientId]\n      );\n\n      if (!accessCheck[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Client not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // This would return workout tracking data\n      return {\n        status: 'success',\n        data: {\n          workouts: [],\n          volume: [],\n          strength: []\n        }\n      };\n    } catch (error) {\n      console.error('Error getting client workout data:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get client workout data'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n}\n"], "names": ["ClientsService", "getClients", "professionalId", "query", "search", "page", "limit", "status", "offset", "sql", "params", "push", "clients", "db", "execute", "countSql", "countParams", "count<PERSON><PERSON><PERSON>", "total", "data", "pagination", "Number", "totalPages", "Math", "ceil", "error", "console", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "getClient", "clientId", "result", "NOT_FOUND", "createClient", "createClientDto", "existingUser", "email", "CONFLICT", "userResult", "name", "password", "phone", "photo", "userId", "insertId", "height", "weight", "activityLevel", "goals", "medicalConditions", "allergies", "notes", "id", "updateClient", "updateClientDto", "accessCheck", "userFields", "userUpdates", "userParams", "for<PERSON>ach", "field", "undefined", "length", "join", "profileFields", "profileUpdates", "profileParams", "db<PERSON><PERSON>", "profileExists", "profileColumns", "filter", "map", "profileValues", "deleteClient", "getClientProtocols", "workoutProtocols", "dietProtocols", "getClientAssessments", "assessments", "createClientProtocol", "protocolData", "Date", "now", "created<PERSON>y", "getClientProgress", "bodyFat", "measurements", "workouts", "nutrition", "getClientNutritionData", "dailyIntake", "macros", "adherence", "getClientWorkoutData", "volume", "strength"], "mappings": ";;;;+BAMaA;;;eAAAA;;;wBANyC;0BAGnC;;;;;;;AAGZ,IAAA,AAAMA,iBAAN,MAAMA;IAEX,MAAMC,WAAWC,cAAsB,EAAEC,KAAU,EAAE;QACnD,IAAI;YACF,MAAM,EAAEC,MAAM,EAAEC,OAAO,CAAC,EAAEC,QAAQ,EAAE,EAAEC,MAAM,EAAE,GAAGJ;YACjD,MAAMK,SAAS,AAACH,CAAAA,OAAO,CAAA,IAAKC;YAE5B,IAAIG,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBX,CAAC;YAED,MAAMC,SAAgB;gBAACR;aAAe;YAEtC,IAAIE,QAAQ;gBACVK,OAAO,CAAC,sCAAsC,CAAC;gBAC/CC,OAAOC,IAAI,CAAC,CAAC,CAAC,EAAEP,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC;YAC1C;YAEA,IAAIG,QAAQ;gBACVE,OAAO,CAAC,kBAAkB,CAAC;gBAC3BC,OAAOC,IAAI,CAACJ;YACd;YAEAE,OAAO,CAAC,qCAAqC,CAAC;YAC9CC,OAAOC,IAAI,CAACL,OAAOE;YAEnB,MAAMI,UAAU,MAAMC,YAAE,CAACC,OAAO,CAACL,KAAKC;YAEtC,iCAAiC;YACjC,IAAIK,WAAW,CAAC;;;;;MAKhB,CAAC;YACD,MAAMC,cAAqB;gBAACd;aAAe;YAE3C,IAAIE,QAAQ;gBACVW,YAAY,CAAC,sCAAsC,CAAC;gBACpDC,YAAYL,IAAI,CAAC,CAAC,CAAC,EAAEP,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC;YAC/C;YAEA,IAAIG,QAAQ;gBACVQ,YAAY,CAAC,kBAAkB,CAAC;gBAChCC,YAAYL,IAAI,CAACJ;YACnB;YAEA,MAAM,CAACU,YAAY,GAAG,MAAMJ,YAAE,CAACC,OAAO,CAACC,UAAUC;YACjD,MAAME,QAAQD,WAAW,CAAC,EAAE,EAAEC,SAAS;YAEvC,OAAO;gBACLX,QAAQ;gBACRY,MAAM;oBACJP,SAASA,OAAO,CAAC,EAAE;oBACnBQ,YAAY;wBACVf,MAAMgB,OAAOhB;wBACbC,OAAOe,OAAOf;wBACdY;wBACAI,YAAYC,KAAKC,IAAI,CAACN,QAAQZ;oBAChC;gBACF;YACF;QACF,EAAE,OAAOmB,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMC,UAAUC,QAAgB,EAAE9B,cAAsB,EAAE;QACxD,IAAI;YACF,MAAMO,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBb,CAAC;YAED,MAAM,CAACwB,OAAO,GAAG,MAAMpB,YAAE,CAACC,OAAO,CAACL,KAAK;gBAACuB;gBAAU9B;aAAe;YAEjE,IAAI,CAAC+B,MAAM,CAAC,EAAE,EAAE;gBACd,MAAM,IAAIN,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,OAAO;gBACL3B,QAAQ;gBACRY,MAAMc,MAAM,CAAC,EAAE;YACjB;QACF,EAAE,OAAOR,OAAO;YACdC,QAAQD,KAAK,CAAC,yBAAyBA;YACvC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMK,aAAaC,eAAgC,EAAElC,cAAsB,EAAE;QAC3E,IAAI;YACF,gCAAgC;YAChC,MAAM,CAACmC,aAAa,GAAG,MAAMxB,YAAE,CAACC,OAAO,CACrC,wCACA;gBAACsB,gBAAgBE,KAAK;aAAC;YAGzB,IAAID,YAAY,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAIV,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACU,QAAQ;YACxB;YAEA,cAAc;YACd,MAAM,CAACC,WAAW,GAAG,MAAM3B,YAAE,CAACC,OAAO,CACnC,CAAC;uDAC8C,CAAC,EAChD;gBACEsB,gBAAgBK,IAAI;gBACpBL,gBAAgBE,KAAK;gBACrBF,gBAAgBM,QAAQ;gBACxBN,gBAAgBO,KAAK,IAAI;gBACzBP,gBAAgBQ,KAAK,IAAI;aAC1B;YAGH,MAAMC,SAASL,WAAWM,QAAQ;YAElC,sBAAsB;YACtB,IAAIV,gBAAgBW,MAAM,IAAIX,gBAAgBY,MAAM,IAAIZ,gBAAgBa,aAAa,EAAE;gBACrF,MAAMpC,YAAE,CAACC,OAAO,CACd,CAAC;qDAC0C,CAAC,EAC5C;oBACE+B;oBACAT,gBAAgBW,MAAM,IAAI;oBAC1BX,gBAAgBY,MAAM,IAAI;oBAC1BZ,gBAAgBa,aAAa,IAAI;oBACjCb,gBAAgBc,KAAK,IAAI;oBACzBd,gBAAgBe,iBAAiB,IAAI;oBACrCf,gBAAgBgB,SAAS,IAAI;iBAC9B;YAEL;YAEA,0CAA0C;YAC1C,MAAMvC,YAAE,CAACC,OAAO,CACd,CAAC;iDACwC,CAAC,EAC1C;gBAACZ;gBAAgB2C;gBAAQT,gBAAgBiB,KAAK,IAAI;aAAK;YAGzD,OAAO;gBACL9C,QAAQ;gBACRqB,SAAS;gBACTT,MAAM;oBACJmC,IAAIT;oBACJ,GAAGT,eAAe;gBACpB;YACF;QACF,EAAE,OAAOX,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMyB,aAAavB,QAAgB,EAAEwB,eAAgC,EAAEtD,cAAsB,EAAE;QAC7F,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACuD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,yBAAyB;YACzB,MAAMwB,aAAa;gBAAC;gBAAQ;gBAAS;gBAAS;aAAQ;YACtD,MAAMC,cAAwB,EAAE;YAChC,MAAMC,aAAoB,EAAE;YAE5BF,WAAWG,OAAO,CAACC,CAAAA;gBACjB,IAAIN,eAAe,CAACM,MAAM,KAAKC,WAAW;oBACxCJ,YAAYhD,IAAI,CAAC,GAAGmD,MAAM,IAAI,CAAC;oBAC/BF,WAAWjD,IAAI,CAAC6C,eAAe,CAACM,MAAM;gBACxC;YACF;YAEA,IAAIH,YAAYK,MAAM,GAAG,GAAG;gBAC1BJ,WAAWjD,IAAI,CAACqB;gBAChB,MAAMnB,YAAE,CAACC,OAAO,CACd,CAAC,iBAAiB,EAAE6C,YAAYM,IAAI,CAAC,MAAM,iCAAiC,CAAC,EAC7EL;YAEJ;YAEA,sBAAsB;YACtB,MAAMM,gBAAgB;gBAAC;gBAAU;gBAAU;gBAAiB;gBAAS;gBAAqB;aAAY;YACtG,MAAMC,iBAA2B,EAAE;YACnC,MAAMC,gBAAuB,EAAE;YAE/BF,cAAcL,OAAO,CAACC,CAAAA;gBACpB,IAAIN,eAAe,CAACM,MAAM,KAAKC,WAAW;oBACxC,MAAMM,UAAUP,UAAU,kBAAkB,mBAC7BA,UAAU,sBAAsB,uBAAuBA;oBACtEK,eAAexD,IAAI,CAAC,GAAG0D,QAAQ,IAAI,CAAC;oBACpCD,cAAczD,IAAI,CAAC6C,eAAe,CAACM,MAAM;gBAC3C;YACF;YAEA,IAAIK,eAAeH,MAAM,GAAG,GAAG;gBAC7BI,cAAczD,IAAI,CAACqB;gBAEnB,0BAA0B;gBAC1B,MAAM,CAACsC,cAAc,GAAG,MAAMzD,YAAE,CAACC,OAAO,CACtC,kDACA;oBAACkB;iBAAS;gBAGZ,IAAIsC,aAAa,CAAC,EAAE,EAAE;oBACpB,MAAMzD,YAAE,CAACC,OAAO,CACd,CAAC,yBAAyB,EAAEqD,eAAeF,IAAI,CAAC,MAAM,sCAAsC,CAAC,EAC7FG;gBAEJ,OAAO;oBACL,qCAAqC;oBACrC,MAAMG,iBAAiBL,cACpBM,MAAM,CAACV,CAAAA,QAASN,eAAe,CAACM,MAAM,KAAKC,WAC3CU,GAAG,CAACX,CAAAA,QAASA,UAAU,kBAAkB,mBAC7BA,UAAU,sBAAsB,uBAAuBA;oBAEtE,MAAMY,gBAAgBR,cACnBM,MAAM,CAACV,CAAAA,QAASN,eAAe,CAACM,MAAM,KAAKC,WAC3CU,GAAG,CAACX,CAAAA,QAASN,eAAe,CAACM,MAAM;oBAEtC,MAAMjD,YAAE,CAACC,OAAO,CACd,CAAC,oCAAoC,EAAEyD,eAAeN,IAAI,CAAC,MAAM;wBACrD,EAAEM,eAAeE,GAAG,CAAC,IAAM,KAAKR,IAAI,CAAC,MAAM,eAAe,CAAC,EACvE;wBAACjC;2BAAa0C;qBAAc;gBAEhC;YACF;YAEA,oDAAoD;YACpD,IAAIlB,gBAAgBH,KAAK,KAAKU,WAAW;gBACvC,MAAMlD,YAAE,CAACC,OAAO,CACd,6GACA;oBAAC0C,gBAAgBH,KAAK;oBAAEnD;oBAAgB8B;iBAAS;YAErD;YAEA,OAAO;gBACLzB,QAAQ;gBACRqB,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM6C,aAAa3C,QAAgB,EAAE9B,cAAsB,EAAE;QAC3D,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACuD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,+DAA+D;YAC/D,MAAMrB,YAAE,CAACC,OAAO,CACd,uHACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,OAAO;gBACLzB,QAAQ;gBACRqB,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM8C,mBAAmB5C,QAAgB,EAAE9B,cAAsB,EAAE;QACjE,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACuD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,wBAAwB;YACxB,MAAM,CAAC2C,iBAAiB,GAAG,MAAMhE,YAAE,CAACC,OAAO,CACzC,CAAC;;;iCAGwB,CAAC,EAC1B;gBAACkB;gBAAU9B;aAAe;YAG5B,qBAAqB;YACrB,MAAM,CAAC4E,cAAc,GAAG,MAAMjE,YAAE,CAACC,OAAO,CACtC,CAAC;;;iCAGwB,CAAC,EAC1B;gBAACkB;gBAAU9B;aAAe;YAG5B,OAAO;gBACLK,QAAQ;gBACRY,MAAM;oBACJ0D,kBAAkBA,oBAAoB,EAAE;oBACxCC,eAAeA,iBAAiB,EAAE;gBACpC;YACF;QACF,EAAE,OAAOrD,OAAO;YACdC,QAAQD,KAAK,CAAC,mCAAmCA;YACjD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMiD,qBAAqB/C,QAAgB,EAAE9B,cAAsB,EAAE;QACnE,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACuD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,MAAM,CAAC8C,YAAY,GAAG,MAAMnE,YAAE,CAACC,OAAO,CACpC,CAAC;;;;iCAIwB,CAAC,EAC1B;gBAACkB;aAAS;YAGZ,OAAO;gBACLzB,QAAQ;gBACRY,MAAM6D,eAAe,EAAE;YACzB;QACF,EAAE,OAAOvD,OAAO;YACdC,QAAQD,KAAK,CAAC,qCAAqCA;YACnD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMmD,qBAAqBjD,QAAgB,EAAEkD,YAAiB,EAAEhF,cAAsB,EAAE;QACtF,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACuD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,mEAAmE;YACnE,qCAAqC;YACrC,OAAO;gBACL3B,QAAQ;gBACRqB,SAAS;gBACTT,MAAM;oBACJmC,IAAI6B,KAAKC,GAAG;oBACZ,GAAGF,YAAY;oBACflD;oBACAqD,WAAWnF;gBACb;YACF;QACF,EAAE,OAAOuB,OAAO;YACdC,QAAQD,KAAK,CAAC,mCAAmCA;YACjD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMwD,kBAAkBtD,QAAgB,EAAE9B,cAAsB,EAAEC,KAAU,EAAE;QAC5E,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACsD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,gDAAgD;YAChD,iCAAiC;YACjC,OAAO;gBACL3B,QAAQ;gBACRY,MAAM;oBACJ6B,QAAQ,EAAE;oBACVuC,SAAS,EAAE;oBACXC,cAAc,EAAE;oBAChBC,UAAU,EAAE;oBACZC,WAAW,EAAE;gBACf;YACF;QACF,EAAE,OAAOjE,OAAO;YACdC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM6D,uBAAuB3D,QAAgB,EAAE9B,cAAsB,EAAEC,KAAU,EAAE;QACjF,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACsD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,4CAA4C;YAC5C,OAAO;gBACL3B,QAAQ;gBACRY,MAAM;oBACJyE,aAAa,EAAE;oBACfC,QAAQ,EAAE;oBACVC,WAAW,EAAE;gBACf;YACF;QACF,EAAE,OAAOrE,OAAO;YACdC,QAAQD,KAAK,CAAC,wCAAwCA;YACtD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMiE,qBAAqB/D,QAAgB,EAAE9B,cAAsB,EAAEC,KAAU,EAAE;QAC/E,IAAI;YACF,0BAA0B;YAC1B,MAAM,CAACsD,YAAY,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACpC,mFACA;gBAACZ;gBAAgB8B;aAAS;YAG5B,IAAI,CAACyB,WAAW,CAAC,EAAE,EAAE;gBACnB,MAAM,IAAI9B,qBAAa,CAAC;oBACtBpB,QAAQ;oBACRqB,SAAS;gBACX,GAAGC,kBAAU,CAACK,SAAS;YACzB;YAEA,0CAA0C;YAC1C,OAAO;gBACL3B,QAAQ;gBACRY,MAAM;oBACJsE,UAAU,EAAE;oBACZO,QAAQ,EAAE;oBACVC,UAAU,EAAE;gBACd;YACF;QACF,EAAE,OAAOxE,OAAO;YACdC,QAAQD,KAAK,CAAC,sCAAsCA;YACpD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBpB,QAAQ;gBACRqB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;AACF"}