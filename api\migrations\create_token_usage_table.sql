-- <PERSON><PERSON><PERSON> tabela para rastreamento de uso de tokens
CREATE TABLE IF NOT EXISTS token_usage (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  tokens_used INT NOT NULL DEFAULT 1,
  feature VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_user_month (user_id, created_at),
  INDEX idx_feature (feature),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Adicionar coluna plan_type na tabela users se não existir
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS plan_type VARCHAR(20) DEFAULT 'snapbasic';

-- Adicionar coluna category na tabela points_transactions se não existir
ALTER TABLE points_transactions 
ADD COLUMN IF NOT EXISTS category VARCHAR(50) DEFAULT 'general';
