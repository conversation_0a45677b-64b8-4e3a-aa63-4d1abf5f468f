"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SettingsController", {
    enumerable: true,
    get: function() {
        return SettingsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _settingsservice = require("./settings.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let SettingsController = class SettingsController {
    async getAllSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getAllSettings(userId);
    }
    async getProfileSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getProfileSettings(userId);
    }
    async updateProfileSettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updateProfileSettings(userId, settings);
    }
    async getPrivacySettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getPrivacySettings(userId);
    }
    async updatePrivacySettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updatePrivacySettings(userId, settings);
    }
    async getGoalSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getGoalSettings(userId);
    }
    async updateGoalSettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updateGoalSettings(userId, settings);
    }
    async getUnitSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getUnitSettings(userId);
    }
    async updateUnitSettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updateUnitSettings(userId, settings);
    }
    async getIntegrationSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getIntegrationSettings(userId);
    }
    async updateIntegrationSettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updateIntegrationSettings(userId, settings);
    }
    async getReminderSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getReminderSettings(userId);
    }
    async updateReminderSettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updateReminderSettings(userId, settings);
    }
    async testReminder(req, reminderData) {
        const userId = req.user.userId;
        return this.settingsService.testReminder(userId, reminderData);
    }
    async getThemeSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getThemeSettings(userId);
    }
    async updateThemeSettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updateThemeSettings(userId, settings);
    }
    async getDataRetentionSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.getDataRetentionSettings(userId);
    }
    async updateDataRetentionSettings(req, settings) {
        const userId = req.user.userId;
        return this.settingsService.updateDataRetentionSettings(userId, settings);
    }
    async resetSettings(req, resetOptions) {
        const userId = req.user.userId;
        return this.settingsService.resetSettings(userId, resetOptions);
    }
    async exportSettings(req) {
        const userId = req.user.userId;
        return this.settingsService.exportSettings(userId);
    }
    async importSettings(req, settingsData) {
        const userId = req.user.userId;
        return this.settingsService.importSettings(userId, settingsData);
    }
    constructor(settingsService){
        this.settingsService = settingsService;
    }
};
_ts_decorate([
    (0, _common.Get)(),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getAllSettings", null);
_ts_decorate([
    (0, _common.Get)('profile'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getProfileSettings", null);
_ts_decorate([
    (0, _common.Put)('profile'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updateProfileSettings", null);
_ts_decorate([
    (0, _common.Get)('privacy'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getPrivacySettings", null);
_ts_decorate([
    (0, _common.Put)('privacy'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updatePrivacySettings", null);
_ts_decorate([
    (0, _common.Get)('goals'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getGoalSettings", null);
_ts_decorate([
    (0, _common.Put)('goals'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updateGoalSettings", null);
_ts_decorate([
    (0, _common.Get)('units'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getUnitSettings", null);
_ts_decorate([
    (0, _common.Put)('units'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updateUnitSettings", null);
_ts_decorate([
    (0, _common.Get)('integrations'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getIntegrationSettings", null);
_ts_decorate([
    (0, _common.Put)('integrations'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updateIntegrationSettings", null);
_ts_decorate([
    (0, _common.Get)('reminders'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getReminderSettings", null);
_ts_decorate([
    (0, _common.Put)('reminders'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updateReminderSettings", null);
_ts_decorate([
    (0, _common.Post)('reminders/test'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "testReminder", null);
_ts_decorate([
    (0, _common.Get)('themes'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getThemeSettings", null);
_ts_decorate([
    (0, _common.Put)('themes'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updateThemeSettings", null);
_ts_decorate([
    (0, _common.Get)('data-retention'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "getDataRetentionSettings", null);
_ts_decorate([
    (0, _common.Put)('data-retention'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "updateDataRetentionSettings", null);
_ts_decorate([
    (0, _common.Post)('reset'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "resetSettings", null);
_ts_decorate([
    (0, _common.Get)('export'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "exportSettings", null);
_ts_decorate([
    (0, _common.Post)('import'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SettingsController.prototype, "importSettings", null);
SettingsController = _ts_decorate([
    (0, _common.Controller)('settings'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _settingsservice.SettingsService === "undefined" ? Object : _settingsservice.SettingsService
    ])
], SettingsController);

//# sourceMappingURL=settings.controller.js.map