"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AnalyticsService", {
    enumerable: true,
    get: function() {
        return AnalyticsService;
    }
});
const _common = require("@nestjs/common");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let AnalyticsService = class AnalyticsService {
    async getDashboardAnalytics(userId, query) {
        try {
            // Calculate period dates
            const period = query?.period || '7d';
            const endDate = new Date();
            const startDate = new Date();
            switch(period){
                case '7d':
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case '30d':
                    startDate.setDate(endDate.getDate() - 30);
                    break;
                case '90d':
                    startDate.setDate(endDate.getDate() - 90);
                    break;
                default:
                    startDate.setDate(endDate.getDate() - 7);
            }
            // For now, return enhanced mock data with realistic values
            // TODO: Replace with actual database queries
            const mockData = {
                totalWorkouts: this.generateRealisticWorkoutCount(period),
                totalCalories: this.generateRealisticCalorieCount(period),
                averageWeight: this.generateRealisticWeight(),
                progressPercentage: this.generateRealisticProgress(),
                weightTrend: this.generateWeightTrend(),
                calorieAdherence: this.generateCalorieAdherence(),
                workoutConsistency: this.generateWorkoutConsistency(period),
                strengthGains: this.generateStrengthGains(),
                bodyFatPercentage: this.generateBodyFatPercentage(),
                recommendations: this.generateRecommendations()
            };
            return {
                status: 'success',
                data: mockData
            };
        } catch (error) {
            console.error('Error in getDashboardAnalytics:', error);
            return {
                status: 'error',
                message: 'Failed to fetch dashboard analytics',
                data: null
            };
        }
    }
    async getProgressSummary(userId, query) {
        try {
            const period = query?.period || '30d';
            // Generate realistic progress data
            const weightProgress = this.generateWeightProgressData(period);
            const bodyFatProgress = this.generateBodyFatProgressData(period);
            const strengthProgress = this.generateStrengthProgressData(period);
            return {
                status: 'success',
                data: {
                    weightProgress,
                    bodyFatProgress,
                    strengthProgress
                }
            };
        } catch (error) {
            console.error('Error in getProgressSummary:', error);
            return {
                status: 'error',
                message: 'Failed to fetch progress summary',
                data: {
                    weightProgress: [],
                    bodyFatProgress: [],
                    strengthProgress: []
                }
            };
        }
    }
    async getNutritionTrends(userId, query) {
        try {
            const period = query?.period || '7d';
            // Generate realistic nutrition trends
            const caloriesTrend = this.generateCaloriesTrend(period);
            const macrosTrend = this.generateMacrosTrend(period);
            const mealFrequency = this.generateMealFrequency(period);
            return {
                status: 'success',
                data: {
                    caloriesTrend,
                    macrosTrend,
                    mealFrequency
                }
            };
        } catch (error) {
            console.error('Error in getNutritionTrends:', error);
            return {
                status: 'error',
                message: 'Failed to fetch nutrition trends',
                data: {
                    caloriesTrend: [],
                    macrosTrend: [],
                    mealFrequency: []
                }
            };
        }
    }
    async getWorkoutTrends(userId, query) {
        try {
            const period = query?.period || '7d';
            // Generate realistic workout trends
            const workoutFrequency = this.generateWorkoutFrequency(period);
            const exerciseTypes = this.generateExerciseTypes();
            const intensityTrends = this.generateIntensityTrends(period);
            return {
                status: 'success',
                data: {
                    workoutFrequency,
                    exerciseTypes,
                    intensityTrends
                }
            };
        } catch (error) {
            console.error('Error in getWorkoutTrends:', error);
            return {
                status: 'error',
                message: 'Failed to fetch workout trends',
                data: {
                    workoutFrequency: [],
                    exerciseTypes: [],
                    intensityTrends: []
                }
            };
        }
    }
    async getBodyCompositionAnalysis(userId, query) {
        return {
            status: 'success',
            data: {
                bodyFatHistory: [],
                muscleGainHistory: [],
                weightHistory: []
            }
        };
    }
    async getHabitsAnalysis(userId, query) {
        return {
            status: 'success',
            data: {
                sleepPatterns: [],
                hydrationLevels: [],
                consistencyScore: 0
            }
        };
    }
    async getGoalsTracking(userId, query) {
        return {
            status: 'success',
            data: {
                activeGoals: [],
                completedGoals: [],
                progressTowardsGoals: []
            }
        };
    }
    async getSocialStats(userId) {
        return {
            status: 'success',
            data: {
                friendsCount: 0,
                challengesCompleted: 0,
                socialInteractions: 0
            }
        };
    }
    async getGamificationStats(userId) {
        return {
            status: 'success',
            data: {
                totalPoints: 0,
                level: 1,
                badges: [],
                achievements: []
            }
        };
    }
    async getWeeklyReport(userId, query) {
        return {
            status: 'success',
            data: {
                weekSummary: {},
                workoutsSummary: {},
                nutritionSummary: {}
            }
        };
    }
    async getMonthlyReport(userId, query) {
        return {
            status: 'success',
            data: {
                monthSummary: {},
                progressSummary: {},
                goalsSummary: {}
            }
        };
    }
    async getAIInsights(userId, query) {
        try {
            const { category, period = 'week' } = query;
            // Enhanced AI insights with category-specific analysis
            if (category === 'recovery') {
                return this.getRecoveryAIInsights(userId, period);
            }
            // General AI insights
            return {
                status: 'success',
                data: {
                    insights: [
                        {
                            type: 'nutrition',
                            title: 'Padrão Alimentar Detectado',
                            description: 'Você tem consumido mais carboidratos nos fins de semana',
                            confidence: 0.85,
                            recommendation: 'Considere manter a consistência durante toda a semana'
                        },
                        {
                            type: 'workout',
                            title: 'Progresso de Força',
                            description: 'Aumento de 12% na força dos membros superiores no último mês',
                            confidence: 0.92,
                            recommendation: 'Continue com o protocolo atual, está funcionando bem'
                        }
                    ],
                    recommendations: [
                        'Mantenha a consistência alimentar durante toda a semana',
                        'Continue com o protocolo de treino atual'
                    ],
                    predictions: []
                }
            };
        } catch (error) {
            console.error('Error getting AI insights:', error);
            return {
                status: 'error',
                data: {
                    insights: [],
                    recommendations: [],
                    predictions: []
                }
            };
        }
    }
    async getRecoveryAIInsights(userId, period) {
        try {
            console.log('🔄 Getting recovery AI insights for user:', userId, 'period:', period);
            // Calculate date range
            const endDate = new Date();
            const startDate = new Date();
            switch(period){
                case 'week':
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case 'month':
                    startDate.setMonth(endDate.getMonth() - 1);
                    break;
                case 'semester':
                    startDate.setMonth(endDate.getMonth() - 6);
                    break;
                case 'year':
                    startDate.setFullYear(endDate.getFullYear() - 1);
                    break;
                default:
                    startDate.setDate(endDate.getDate() - 7);
            }
            // Get wearables data for recovery analysis
            // Note: This would integrate with actual wearables service
            const mockWearablesData = {
                sleep: {
                    average_duration: 7.2,
                    quality_score: 78,
                    deep_sleep_percentage: 22,
                    rem_sleep_percentage: 18,
                    efficiency: 85
                },
                hrv: {
                    average: 42,
                    trend: 'improving',
                    recovery_score: 75
                },
                stress: {
                    average_level: 35,
                    trend: 'stable'
                },
                energy: {
                    average_level: 72,
                    trend: 'improving'
                },
                heart_rate: {
                    resting_average: 62,
                    max_recorded: 185,
                    zones: {
                        zone1: 25,
                        zone2: 35,
                        zone3: 25,
                        zone4: 10,
                        zone5: 5
                    }
                }
            };
            // AI analysis based on the data
            const recoveryStatus = this.calculateRecoveryStatus(mockWearablesData);
            const fatigueLevel = this.calculateFatigueLevel(mockWearablesData);
            const recommendations = this.generateRecoveryRecommendations(mockWearablesData, recoveryStatus);
            return {
                status: 'success',
                data: {
                    recovery_status: recoveryStatus,
                    fatigue_level: fatigueLevel,
                    overall_recovery_score: this.calculateOverallRecoveryScore(mockWearablesData),
                    recommendations,
                    insights: [
                        {
                            type: 'sleep',
                            title: 'Qualidade do Sono',
                            description: `Sua qualidade de sono está em ${mockWearablesData.sleep.quality_score}%`,
                            confidence: 0.88
                        },
                        {
                            type: 'hrv',
                            title: 'Variabilidade Cardíaca',
                            description: `HRV mostra tendência ${mockWearablesData.hrv.trend === 'improving' ? 'de melhoria' : 'estável'}`,
                            confidence: 0.82
                        }
                    ],
                    confidence: 0.85,
                    period,
                    analysis_date: new Date().toISOString()
                }
            };
        } catch (error) {
            console.error('Error getting recovery AI insights:', error);
            return {
                status: 'error',
                data: {
                    recovery_status: 'unknown',
                    fatigue_level: 0,
                    overall_recovery_score: 0,
                    recommendations: [],
                    insights: [],
                    confidence: 0
                }
            };
        }
    }
    async getWeightPredictions(userId, query) {
        return {
            status: 'success',
            data: {
                predictions: [],
                confidence: 0,
                factors: []
            }
        };
    }
    async getBenchmarkComparison(userId, query) {
        return {
            status: 'success',
            data: {
                userStats: {},
                benchmarks: {},
                comparison: {}
            }
        };
    }
    // Helper methods for recovery analysis
    calculateRecoveryStatus(data) {
        const sleepScore = data.sleep.quality_score;
        const hrvScore = data.hrv.recovery_score;
        const stressLevel = data.stress.average_level;
        const overallScore = (sleepScore + hrvScore + (100 - stressLevel)) / 3;
        if (overallScore >= 80) return 'recovered';
        if (overallScore >= 60) return 'recovering';
        if (overallScore >= 40) return 'fatigued';
        return 'overworked';
    }
    calculateFatigueLevel(data) {
        const sleepScore = data.sleep.quality_score;
        const stressLevel = data.stress.average_level;
        const energyLevel = data.energy.average_level;
        // Higher fatigue = lower sleep quality + higher stress + lower energy
        const fatigueScore = (100 - sleepScore + stressLevel + (100 - energyLevel)) / 3;
        return Math.round(fatigueScore);
    }
    calculateOverallRecoveryScore(data) {
        const sleepScore = data.sleep.quality_score;
        const hrvScore = data.hrv.recovery_score;
        const energyScore = data.energy.average_level;
        const stressScore = 100 - data.stress.average_level;
        return Math.round((sleepScore + hrvScore + energyScore + stressScore) / 4);
    }
    generateRecoveryRecommendations(data, status) {
        const recommendations = [];
        if (data.sleep.quality_score < 70) {
            recommendations.push('Melhore a higiene do sono: evite telas 1h antes de dormir');
        }
        if (data.stress.average_level > 60) {
            recommendations.push('Pratique técnicas de relaxamento como meditação ou respiração profunda');
        }
        if (data.energy.average_level < 60) {
            recommendations.push('Considere ajustar a intensidade dos treinos por alguns dias');
        }
        if (status === 'overworked') {
            recommendations.push('Tome um dia de descanso completo para recuperação');
        }
        if (recommendations.length === 0) {
            recommendations.push('Continue mantendo seus hábitos atuais de recuperação');
        }
        return recommendations;
    }
    // Helper methods for generating realistic mock data
    generateRealisticWorkoutCount(period) {
        const baseWorkouts = {
            '7d': 3,
            '30d': 12,
            '90d': 36
        };
        return baseWorkouts[period] || 3;
    }
    generateRealisticCalorieCount(period) {
        const baseCalories = {
            '7d': 14000,
            '30d': 60000,
            '90d': 180000
        };
        return baseCalories[period] || 14000;
    }
    generateRealisticWeight() {
        return Math.round((70 + Math.random() * 20) * 10) / 10; // 70-90kg
    }
    generateRealisticProgress() {
        return Math.round((60 + Math.random() * 30) * 10) / 10; // 60-90%
    }
    generateWeightTrend() {
        const trends = [
            'decreasing',
            'stable',
            'increasing'
        ];
        return trends[Math.floor(Math.random() * trends.length)];
    }
    generateCalorieAdherence() {
        return Math.round((1800 + Math.random() * 400) * 10) / 10; // 1800-2200 kcal
    }
    generateWorkoutConsistency(period) {
        return Math.round((70 + Math.random() * 25) * 10) / 10; // 70-95%
    }
    generateStrengthGains() {
        return Math.round((5 + Math.random() * 15) * 10) / 10; // 5-20%
    }
    generateBodyFatPercentage() {
        return Math.round((15 + Math.random() * 10) * 10) / 10; // 15-25%
    }
    generateRecommendations() {
        const recommendations = [
            'Manter consistência nos treinos',
            'Aumentar ingestão de proteínas',
            'Focar em exercícios compostos',
            'Melhorar qualidade do sono',
            'Aumentar hidratação diária',
            'Incluir mais vegetais na dieta'
        ];
        // Return 2-4 random recommendations
        const count = 2 + Math.floor(Math.random() * 3);
        return recommendations.sort(()=>0.5 - Math.random()).slice(0, count);
    }
    generateWeightProgressData(period) {
        const days = this.getPeriodDays(period);
        const data = [];
        const startWeight = 75 + Math.random() * 10; // 75-85kg starting weight
        for(let i = 0; i < days; i++){
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            // Simulate gradual weight loss/gain
            const progress = i / days;
            const weightChange = (Math.random() - 0.5) * 2; // -1 to +1 kg variation
            const weight = startWeight - progress * 2 + weightChange; // Gradual 2kg loss over period
            data.push({
                date: date.toISOString().split('T')[0],
                weight: Math.round(weight * 10) / 10,
                bodyFat: Math.round((18 + Math.random() * 4) * 10) / 10 // 18-22%
            });
        }
        return data;
    }
    generateBodyFatProgressData(period) {
        const days = this.getPeriodDays(period);
        const data = [];
        const startBodyFat = 20 + Math.random() * 5; // 20-25% starting
        for(let i = 0; i < days; i++){
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            const progress = i / days;
            const bodyFatChange = (Math.random() - 0.5) * 0.5; // -0.25 to +0.25% variation
            const bodyFat = startBodyFat - progress * 1.5 + bodyFatChange; // Gradual 1.5% loss
            data.push({
                date: date.toISOString().split('T')[0],
                bodyFat: Math.round(bodyFat * 10) / 10
            });
        }
        return data;
    }
    generateStrengthProgressData(period) {
        const exercises = [
            'Supino',
            'Agachamento',
            'Levantamento Terra',
            'Desenvolvimento'
        ];
        const data = [];
        exercises.forEach((exercise)=>{
            const baseWeight = 60 + Math.random() * 40; // 60-100kg base
            const history = [];
            const days = this.getPeriodDays(period);
            for(let i = 0; i < Math.min(days / 7, 10); i++){
                const date = new Date();
                date.setDate(date.getDate() - (days - i * 7));
                const progress = i / 10;
                const weight = baseWeight + progress * 10 + (Math.random() - 0.5) * 2;
                history.push({
                    date: date.toISOString().split('T')[0],
                    weight: Math.round(weight * 2) / 2 // Round to nearest 0.5kg
                });
            }
            data.push({
                exercise,
                current: history[history.length - 1]?.weight || baseWeight,
                previous: history[0]?.weight || baseWeight,
                change: history.length > 1 ? Math.round((history[history.length - 1].weight - history[0].weight) * 10) / 10 : 0,
                history
            });
        });
        return data;
    }
    getPeriodDays(period) {
        switch(period){
            case '7d':
                return 7;
            case '30d':
                return 30;
            case '90d':
                return 90;
            default:
                return 30;
        }
    }
    generateCaloriesTrend(period) {
        const days = this.getPeriodDays(period);
        const data = [];
        const targetCalories = 2000;
        for(let i = 0; i < days; i++){
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            const variation = (Math.random() - 0.5) * 400; // ±200 calories variation
            const calories = targetCalories + variation;
            data.push({
                date: date.toISOString().split('T')[0],
                calories: Math.round(calories),
                target: targetCalories,
                adherence: Math.round(calories / targetCalories * 100)
            });
        }
        return data;
    }
    generateMacrosTrend(period) {
        const days = this.getPeriodDays(period);
        const data = [];
        for(let i = 0; i < days; i++){
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            data.push({
                date: date.toISOString().split('T')[0],
                protein: Math.round(120 + (Math.random() - 0.5) * 40),
                carbs: Math.round(200 + (Math.random() - 0.5) * 60),
                fat: Math.round(70 + (Math.random() - 0.5) * 20),
                fiber: Math.round(25 + (Math.random() - 0.5) * 10) // 20-30g
            });
        }
        return data;
    }
    generateMealFrequency(period) {
        const mealTypes = [
            'Café da Manhã',
            'Almoço',
            'Lanche',
            'Jantar'
        ];
        const days = this.getPeriodDays(period);
        return mealTypes.map((mealType)=>({
                mealType,
                frequency: Math.round((0.7 + Math.random() * 0.3) * days),
                averageCalories: Math.round(300 + Math.random() * 400) // 300-700 calories
            }));
    }
    generateWorkoutFrequency(period) {
        const days = this.getPeriodDays(period);
        const data = [];
        for(let i = 0; i < days; i++){
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            // Simulate workout frequency (higher on weekdays)
            const dayOfWeek = date.getDay();
            const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
            const workoutProbability = isWeekend ? 0.3 : 0.7;
            const hasWorkout = Math.random() < workoutProbability;
            data.push({
                date: date.toISOString().split('T')[0],
                workouts: hasWorkout ? 1 : 0,
                duration: hasWorkout ? Math.round(45 + Math.random() * 30) : 0,
                calories: hasWorkout ? Math.round(300 + Math.random() * 200) : 0 // 300-500 calories
            });
        }
        return data;
    }
    generateExerciseTypes() {
        const exercises = [
            {
                type: 'Cardio',
                percentage: 30,
                color: '#FF6B6B'
            },
            {
                type: 'Força',
                percentage: 40,
                color: '#4ECDC4'
            },
            {
                type: 'Flexibilidade',
                percentage: 15,
                color: '#45B7D1'
            },
            {
                type: 'Funcional',
                percentage: 15,
                color: '#96CEB4'
            }
        ];
        return exercises;
    }
    generateIntensityTrends(period) {
        const days = this.getPeriodDays(period);
        const data = [];
        for(let i = 0; i < days; i++){
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            data.push({
                date: date.toISOString().split('T')[0],
                lowIntensity: Math.round(10 + Math.random() * 20),
                moderateIntensity: Math.round(20 + Math.random() * 25),
                highIntensity: Math.round(5 + Math.random() * 15) // 5-20 minutes
            });
        }
        return data;
    }
};
AnalyticsService = _ts_decorate([
    (0, _common.Injectable)()
], AnalyticsService);

//# sourceMappingURL=analytics.service.js.map