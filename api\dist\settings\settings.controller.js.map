{"version": 3, "sources": ["../../src/settings/settings.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Put, \n  Post, \n  Delete, \n  Body, \n  Param, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { SettingsService } from './settings.service';\n\n@Controller('settings')\n@UseGuards(JwtAuthGuard)\nexport class SettingsController {\n  constructor(private readonly settingsService: SettingsService) {}\n\n  @Get()\n  async getAllSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getAllSettings(userId);\n  }\n\n  @Get('profile')\n  async getProfileSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getProfileSettings(userId);\n  }\n\n  @Put('profile')\n  async updateProfileSettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updateProfileSettings(userId, settings);\n  }\n\n  @Get('privacy')\n  async getPrivacySettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getPrivacySettings(userId);\n  }\n\n  @Put('privacy')\n  async updatePrivacySettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updatePrivacySettings(userId, settings);\n  }\n\n  @Get('goals')\n  async getGoalSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getGoalSettings(userId);\n  }\n\n  @Put('goals')\n  async updateGoalSettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updateGoalSettings(userId, settings);\n  }\n\n  @Get('units')\n  async getUnitSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getUnitSettings(userId);\n  }\n\n  @Put('units')\n  async updateUnitSettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updateUnitSettings(userId, settings);\n  }\n\n  @Get('integrations')\n  async getIntegrationSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getIntegrationSettings(userId);\n  }\n\n  @Put('integrations')\n  async updateIntegrationSettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updateIntegrationSettings(userId, settings);\n  }\n\n  @Get('reminders')\n  async getReminderSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getReminderSettings(userId);\n  }\n\n  @Put('reminders')\n  async updateReminderSettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updateReminderSettings(userId, settings);\n  }\n\n  @Post('reminders/test')\n  async testReminder(@Request() req: any, @Body() reminderData: any) {\n    const userId = req.user.userId;\n    return this.settingsService.testReminder(userId, reminderData);\n  }\n\n  @Get('themes')\n  async getThemeSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getThemeSettings(userId);\n  }\n\n  @Put('themes')\n  async updateThemeSettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updateThemeSettings(userId, settings);\n  }\n\n  @Get('data-retention')\n  async getDataRetentionSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.getDataRetentionSettings(userId);\n  }\n\n  @Put('data-retention')\n  async updateDataRetentionSettings(@Request() req: any, @Body() settings: any) {\n    const userId = req.user.userId;\n    return this.settingsService.updateDataRetentionSettings(userId, settings);\n  }\n\n  @Post('reset')\n  async resetSettings(@Request() req: any, @Body() resetOptions: any) {\n    const userId = req.user.userId;\n    return this.settingsService.resetSettings(userId, resetOptions);\n  }\n\n  @Get('export')\n  async exportSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.settingsService.exportSettings(userId);\n  }\n\n  @Post('import')\n  async importSettings(@Request() req: any, @Body() settingsData: any) {\n    const userId = req.user.userId;\n    return this.settingsService.importSettings(userId, settingsData);\n  }\n}\n"], "names": ["SettingsController", "getAllSettings", "req", "userId", "user", "settingsService", "getProfileSettings", "updateProfileSettings", "settings", "getPrivacySettings", "updatePrivacySettings", "getGoalSettings", "updateGoalSettings", "getUnitSettings", "updateUnitSettings", "getIntegrationSettings", "updateIntegrationSettings", "getReminderSettings", "updateReminderSettings", "testReminder", "reminderData", "getThemeSettings", "updateThemeSettings", "getDataRetentionSettings", "updateDataRetentionSettings", "resetSettings", "resetOptions", "exportSettings", "importSettings", "settingsData", "constructor"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;wBANN;8BACsB;iCACG;;;;;;;;;;;;;;;AAIzB,IAAA,AAAMA,qBAAN,MAAMA;IAGX,MACMC,eAAe,AAAWC,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACJ,cAAc,CAACE;IAC7C;IAEA,MACMG,mBAAmB,AAAWJ,GAAQ,EAAE;QAC5C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACC,kBAAkB,CAACH;IACjD;IAEA,MACMI,sBAAsB,AAAWL,GAAQ,EAAE,AAAQM,QAAa,EAAE;QACtE,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACE,qBAAqB,CAACJ,QAAQK;IAC5D;IAEA,MACMC,mBAAmB,AAAWP,GAAQ,EAAE;QAC5C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACI,kBAAkB,CAACN;IACjD;IAEA,MACMO,sBAAsB,AAAWR,GAAQ,EAAE,AAAQM,QAAa,EAAE;QACtE,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACK,qBAAqB,CAACP,QAAQK;IAC5D;IAEA,MACMG,gBAAgB,AAAWT,GAAQ,EAAE;QACzC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACM,eAAe,CAACR;IAC9C;IAEA,MACMS,mBAAmB,AAAWV,GAAQ,EAAE,AAAQM,QAAa,EAAE;QACnE,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACO,kBAAkB,CAACT,QAAQK;IACzD;IAEA,MACMK,gBAAgB,AAAWX,GAAQ,EAAE;QACzC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACQ,eAAe,CAACV;IAC9C;IAEA,MACMW,mBAAmB,AAAWZ,GAAQ,EAAE,AAAQM,QAAa,EAAE;QACnE,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACS,kBAAkB,CAACX,QAAQK;IACzD;IAEA,MACMO,uBAAuB,AAAWb,GAAQ,EAAE;QAChD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACU,sBAAsB,CAACZ;IACrD;IAEA,MACMa,0BAA0B,AAAWd,GAAQ,EAAE,AAAQM,QAAa,EAAE;QAC1E,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACW,yBAAyB,CAACb,QAAQK;IAChE;IAEA,MACMS,oBAAoB,AAAWf,GAAQ,EAAE;QAC7C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACY,mBAAmB,CAACd;IAClD;IAEA,MACMe,uBAAuB,AAAWhB,GAAQ,EAAE,AAAQM,QAAa,EAAE;QACvE,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACa,sBAAsB,CAACf,QAAQK;IAC7D;IAEA,MACMW,aAAa,AAAWjB,GAAQ,EAAE,AAAQkB,YAAiB,EAAE;QACjE,MAAMjB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACc,YAAY,CAAChB,QAAQiB;IACnD;IAEA,MACMC,iBAAiB,AAAWnB,GAAQ,EAAE;QAC1C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACgB,gBAAgB,CAAClB;IAC/C;IAEA,MACMmB,oBAAoB,AAAWpB,GAAQ,EAAE,AAAQM,QAAa,EAAE;QACpE,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACiB,mBAAmB,CAACnB,QAAQK;IAC1D;IAEA,MACMe,yBAAyB,AAAWrB,GAAQ,EAAE;QAClD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACkB,wBAAwB,CAACpB;IACvD;IAEA,MACMqB,4BAA4B,AAAWtB,GAAQ,EAAE,AAAQM,QAAa,EAAE;QAC5E,MAAML,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACmB,2BAA2B,CAACrB,QAAQK;IAClE;IAEA,MACMiB,cAAc,AAAWvB,GAAQ,EAAE,AAAQwB,YAAiB,EAAE;QAClE,MAAMvB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACoB,aAAa,CAACtB,QAAQuB;IACpD;IAEA,MACMC,eAAe,AAAWzB,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACsB,cAAc,CAACxB;IAC7C;IAEA,MACMyB,eAAe,AAAW1B,GAAQ,EAAE,AAAQ2B,YAAiB,EAAE;QACnE,MAAM1B,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,eAAe,CAACuB,cAAc,CAACzB,QAAQ0B;IACrD;IA9HAC,YAAY,AAAiBzB,eAAgC,CAAE;aAAlCA,kBAAAA;IAAmC;AA+HlE"}