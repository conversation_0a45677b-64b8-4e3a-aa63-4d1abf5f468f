import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { ClientsService } from './clients.service';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { UserRole } from '../users/enums/user-role.enum';

@ApiTags('clients')
@Controller('clients')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ClientsController {
  constructor(private readonly clientsService: ClientsService) {}

  @Get()
  @Roles(UserRole.NUTRITIONIST)
  @ApiOperation({ summary: 'Get all clients' })
  async findAll(@Request() req: any, @Query('search') search?: string) {
    return this.clientsService.findAll(req.user.id, search);
  }

  @Get(':id')
  @Roles(UserRole.NUTRITIONIST)
  @ApiOperation({ summary: 'Get client by id' })
  async findOne(@Request() req: any, @Param('id') id: string) {
    return this.clientsService.findOne(req.user.id, id);
  }

  @Post()
  @Roles(UserRole.NUTRITIONIST)
  @ApiOperation({ summary: 'Create client' })
  async create(@Request() req: any, @Body() createClientDto: CreateClientDto) {
    return this.clientsService.create(req.user.id, createClientDto);
  }

  @Put(':id')
  @Roles(UserRole.NUTRITIONIST)
  @ApiOperation({ summary: 'Update client' })
  async update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateClientDto: UpdateClientDto
  ) {
    return this.clientsService.update(req.user.id, id, updateClientDto);
  }

  @Delete(':id')
  @Roles(UserRole.NUTRITIONIST)
  @ApiOperation({ summary: 'Delete client' })
  async remove(@Request() req: any, @Param('id') id: string) {
    return this.clientsService.remove(req.user.id, id);
  }

  @Get(':id/protocols')
  @Roles(UserRole.NUTRITIONIST)
  @ApiOperation({ summary: 'Get client protocols' })
  async getProtocols(@Request() req: any, @Param('id') id: string) {
    return this.clientsService.getProtocols(req.user.id, id);
  }

  @Get(':id/assessments')
  @Roles(UserRole.NUTRITIONIST)
  @ApiOperation({ summary: 'Get client assessments' })
  async getAssessments(@Request() req: any, @Param('id') id: string) {
    return this.clientsService.getAssessments(req.user.id, id);
  }
}