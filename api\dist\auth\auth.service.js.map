{"version": 3, "sources": ["../../src/auth/auth.service.ts"], "sourcesContent": ["import { HttpException, HttpStatus, Injectable, Res } from '@nestjs/common';\r\nimport * as bcrypt from 'bcryptjs';\r\nimport { CreateUserDto } from './create-user.dto';\r\nimport { JwtService } from '@nestjs/jwt';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { db } from '../database';\r\nimport * as dotenv from 'dotenv';\r\nimport dayjs from 'dayjs';\r\n\r\ndotenv.config();\r\n\r\n@Injectable()\r\nexport class AuthService {\r\n  constructor(\r\n    private readonly jwtService: JwtService,\r\n  ) {}  \r\n\r\n  formatDatetime(datetime: string): string {\r\n    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');\r\n  }\r\n\r\n  async login(user: {email: string, password: string}): Promise<any> {\r\n    if (!user?.email || !user?.password) {\r\n      throw new HttpException({message: ['Email e senha são obrigatórios'], status: HttpStatus.BAD_REQUEST},\r\n        HttpStatus.BAD_REQUEST);\r\n    }\r\n\r\n    const checkUser: any = await db.selectFrom('users')\r\n    .selectAll()\r\n    .where('email', '=', user.email)\r\n    .executeTakeFirst();\r\n\r\n    if (!checkUser) {\r\n      throw new HttpException({message: ['Usuário não encontrado'], status: HttpStatus.NOT_FOUND},\r\n        HttpStatus.NOT_FOUND);\r\n    }\r\n\r\n    // check password\r\n    const isPasswordValid = await bcrypt.compare(user.password, checkUser.password);\r\n    if (!isPasswordValid) {\r\n      throw new HttpException({message: ['Senha inválida'], status: HttpStatus.UNAUTHORIZED},\r\n        HttpStatus.UNAUTHORIZED);\r\n    }\r\n\r\n    // Check if account is soft deleted\r\n    if (checkUser.deleted_at) {\r\n      const deletedDate = new Date(checkUser.deleted_at);\r\n      const now = new Date();\r\n      const daysDifference = Math.floor((now.getTime() - deletedDate.getTime()) / (1000 * 60 * 60 * 24));\r\n\r\n      // If deleted more than 30 days ago, treat as permanently deleted\r\n      if (daysDifference > 30) {\r\n        throw new HttpException({\r\n          message: ['Esta conta foi permanentemente excluída.'],\r\n          status: HttpStatus.FORBIDDEN\r\n        }, HttpStatus.FORBIDDEN);\r\n      }\r\n\r\n      // Account is within recovery period - return special response\r\n      return {\r\n        status: \"account_recovery_required\",\r\n        data: {\r\n          message: `Sua conta foi marcada para exclusão há ${daysDifference} dias. Você tem ${30 - daysDifference} dias restantes para recuperá-la.`,\r\n          userId: checkUser.id,\r\n          daysRemaining: 30 - daysDifference,\r\n          deletedAt: checkUser.deleted_at\r\n        }\r\n      };\r\n    }\r\n\r\n    const payload = { userId: checkUser.id };\r\n    const access_token = this.jwtService.sign(payload, {\r\n      expiresIn: process.env.JWT_EXPIRATION_TIME,\r\n    });\r\n    const refresh_token = uuidv4();\r\n    const provider_id = 1;\r\n    const device_uid = uuidv4();\r\n\r\n    // save refresh token in database\r\n    const userAuthData: any = {\r\n      user_id: checkUser.id,\r\n      provider_uid: user.email,\r\n      provider_id: provider_id,\r\n      device_uid,\r\n      refresh_token,\r\n      expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora\r\n    }\r\n\r\n    await db.insertInto('user_auths')\r\n    .values(userAuthData)\r\n    .executeTakeFirstOrThrow();\r\n\r\n    return {\r\n      status: \"success\",\r\n      data: {\r\n        access_token,\r\n        refresh_token,\r\n        device_uid\r\n      }\r\n    };\r\n  }\r\n\r\n  async recoverAccount(userId: number): Promise<any> {\r\n    // Check if user exists and is deleted\r\n    const checkUser: any = await db.selectFrom('users')\r\n    .select(['id', 'deleted_at', 'email'])\r\n    .where('id', '=', userId)\r\n    .executeTakeFirst();\r\n\r\n    if (!checkUser) {\r\n      throw new HttpException({message: ['Usuário não encontrado'], status: HttpStatus.NOT_FOUND},\r\n        HttpStatus.NOT_FOUND);\r\n    }\r\n\r\n    if (!checkUser.deleted_at) {\r\n      throw new HttpException({message: ['Esta conta não está marcada para exclusão'], status: HttpStatus.BAD_REQUEST},\r\n        HttpStatus.BAD_REQUEST);\r\n    }\r\n\r\n    // Check if still within recovery period\r\n    const deletedDate = new Date(checkUser.deleted_at);\r\n    const now = new Date();\r\n    const daysDifference = Math.floor((now.getTime() - deletedDate.getTime()) / (1000 * 60 * 60 * 24));\r\n\r\n    if (daysDifference > 30) {\r\n      throw new HttpException({\r\n        message: ['O período de recuperação de 30 dias expirou. Esta conta foi permanentemente excluída.'],\r\n        status: HttpStatus.FORBIDDEN\r\n      }, HttpStatus.FORBIDDEN);\r\n    }\r\n\r\n    // Clear deleted_at to recover the account\r\n    await db\r\n      .updateTable('users')\r\n      .set({\r\n        deleted_at: null,\r\n        updated_at: new Date(),\r\n      })\r\n      .where('id', '=', userId)\r\n      .execute();\r\n\r\n    // Generate new tokens for the recovered account\r\n    const payload = { userId: checkUser.id };\r\n    const access_token = this.jwtService.sign(payload, {\r\n      expiresIn: process.env.JWT_EXPIRATION_TIME,\r\n    });\r\n    const refresh_token = uuidv4();\r\n    const provider_id = 1;\r\n    const device_uid = uuidv4();\r\n\r\n    // save refresh token in database\r\n    const userAuthData: any = {\r\n      user_id: checkUser.id,\r\n      provider_uid: checkUser.email,\r\n      provider_id: provider_id,\r\n      device_uid,\r\n      refresh_token,\r\n      expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora\r\n    }\r\n\r\n    await db.insertInto('user_auths')\r\n    .values(userAuthData)\r\n    .executeTakeFirstOrThrow();\r\n\r\n    return {\r\n      status: \"success\",\r\n      data: {\r\n        access_token,\r\n        refresh_token,\r\n        device_uid,\r\n        message: 'Conta recuperada com sucesso!'\r\n      }\r\n    };\r\n  }\r\n\r\n  // Método para registrar o usuário\r\n  async register(createUserDto: CreateUserDto): Promise<any> {\r\n    const { name, email, password, invite } = createUserDto;\r\n    let aff_id = null;\r\n    \r\n    const existingUser: any = await db.selectFrom('users')\r\n    .where('email', '=', email)\r\n    .selectAll()\r\n    .executeTakeFirst();\r\n\r\n    if (existingUser) {\r\n      throw new HttpException({message: ['O email já está cadastrado'], status: HttpStatus.CONFLICT},\r\n        HttpStatus.CONFLICT);\r\n    }\r\n\r\n    const hashedPassword = await bcrypt.hash(password, 10);\r\n\r\n    if(invite) {\r\n      // get invite and user_id\r\n      const inviteData: any = await db.selectFrom('affiliate_links')\r\n      .where('invite', '=', invite.toLowerCase().trim())\r\n      .select(['user_id'])\r\n      .executeTakeFirst();\r\n\r\n      if (inviteData) {\r\n        aff_id = inviteData.user_id;\r\n      }\r\n\r\n      if (!inviteData) {\r\n        throw new HttpException({message: ['Código promocional inválido'], status: HttpStatus.BAD_REQUEST},\r\n          HttpStatus.BAD_REQUEST);\r\n      }\r\n    }\r\n\r\n    const userData: any = {\r\n      name: name.trim(),\r\n      email: email.trim(),\r\n      password: hashedPassword,\r\n      invite: invite ? invite.toLowerCase().trim() : null,\r\n      aff_id\r\n    }\r\n\r\n    const user: any = await db.insertInto('users')\r\n    .values(userData)\r\n    .executeTakeFirstOrThrow();\r\n\r\n    const userId = parseInt(user.insertId) || user.insertId;\r\n\r\n    // Salvar o usuário no banco\r\n    // const savedUser = await this.userRepository.save(user);\r\n\r\n    const payload = { userId: userId };\r\n\r\n    const access_token = this.jwtService.sign(payload, { expiresIn: process.env.JWT_EXPIRATION_TIME });\r\n\r\n    const deviceUid = uuidv4();\r\n\r\n    const refresh_token = uuidv4();\r\n\r\n    const provider_id = 1; // Local\r\n\r\n\r\n    // insert on user_auths\r\n    /*\r\n    const userAuth = this.userAuthRepository.create({\r\n      user: savedUser,\r\n      provider: { id: provider_id },\r\n      providerUid: email,\r\n      deviceUid,\r\n      refreshToken: refresh_token,\r\n      expireDate: new Date(Date.now() + 60 * 60 * 1000), // 1 hora\r\n    });\r\n    */\r\n   const userAuthData: any = {\r\n    user_id: userId,\r\n    provider_id,\r\n    provider_uid: email,\r\n    device_uid: deviceUid,\r\n    refresh_token,\r\n    expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora\r\n   }\r\n\r\n   const userAuth = await db.insertInto('user_auths')\r\n   .values(userAuthData)\r\n   .executeTakeFirstOrThrow();\r\n\r\n\r\n    // const savedUserAuth = await this.userAuthRepository.save(userAuth);\r\n\r\n    return {\r\n        \"status\": \"success\",\r\n        \"data\": {\r\n            \"access_token\": access_token,\r\n            \"refresh_token\": refresh_token,\r\n            \"device_uid\": deviceUid\r\n        }\r\n    };\r\n  }\r\n\r\n  // Refresh token\r\n  async refreshToken(refresh_token: string, device_uid: string) {\r\n    try {\r\n     const userAuth = await db.selectFrom('user_auths')\r\n     .select(['user_id', 'provider_id', 'provider_uid', 'device_uid'])\r\n     .where('refresh_token', '=', refresh_token)\r\n     .where('device_uid', '=', device_uid)\r\n     .executeTakeFirst();\r\n\r\n      if (!userAuth) {\r\n        throw new HttpException({message: ['Refresh token inválido'], status: HttpStatus.UNAUTHORIZED},\r\n          HttpStatus.UNAUTHORIZED);\r\n      }\r\n\r\n      const userId = userAuth.user_id;\r\n      \r\n      await db.deleteFrom('user_auths')\r\n      .where('user_id', '=', userId)\r\n      .where('device_uid', '=', device_uid)\r\n      .executeTakeFirstOrThrow();\r\n      \r\n      // generate new refresh token\r\n      const new_refresh_token = uuidv4();\r\n      \r\n      // add new refresh token to db\r\n      const userAuthData: any = {\r\n        user_id: userId,\r\n        provider_id: userAuth.provider_id,\r\n        provider_uid: userAuth.provider_uid,\r\n        device_uid,\r\n        refresh_token: new_refresh_token,\r\n        expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora\r\n      }\r\n      \r\n      await db.insertInto('user_auths')\r\n      .values(userAuthData)\r\n      .executeTakeFirstOrThrow();\r\n\r\n      // generate new access token\r\n      const payload = { userId: userId };\r\n      const new_access_token = this.jwtService.sign(payload, {\r\n        expiresIn: process.env.JWT_EXPIRATION_TIME,\r\n      });\r\n      \r\n      return {\r\n        \"status\": \"success\",\r\n        \"data\": {\r\n            \"access_token\": new_access_token,\r\n            \"refresh_token\": new_refresh_token,\r\n            \"device_uid\": device_uid\r\n        }\r\n      };\r\n      \r\n    } catch (error) {\r\n      throw new HttpException({message: ['Refresh token inválido'], status: HttpStatus.UNAUTHORIZED},\r\n        HttpStatus.UNAUTHORIZED);\r\n    }\r\n    \r\n  }\r\n\r\n  async getRole(userId: number, role: string) {\r\n    const rolesIds = {\r\n      'admin': 1,\r\n      'coach': 2,\r\n      'nutritionist': 3,\r\n      'user': 4\r\n    };\r\n\r\n    const userRole = await db\r\n    .selectFrom('users_roles')\r\n    .select(['role_id'])\r\n    .where('user_id', '=', userId)\r\n    .where('role_id', '=', rolesIds[role])\r\n    .executeTakeFirst();\r\n\r\n    if (!userRole) {\r\n      if (role === 'admin') {\r\n        return {\r\n          status: 'error',\r\n          role: null,\r\n        };\r\n      }\r\n\r\n      // add role\r\n      const newRole = await db\r\n        .insertInto('users_roles')\r\n        .values({\r\n          user_id: userId,\r\n          role_id: rolesIds[role]\r\n        })\r\n        .execute();\r\n    }\r\n\r\n    return {\r\n      status: 'success',\r\n      role: role\r\n    };\r\n  }\r\n\r\n  async requestPasswordReset(email: string): Promise<any> {\r\n    try {\r\n      const user = await db.selectFrom('users')\r\n        .selectAll()\r\n        .where('email', '=', email)\r\n        .executeTakeFirst();\r\n\r\n      if (!user) {\r\n        // Don't reveal if email exists or not for security\r\n        return {\r\n          status: 'success',\r\n          message: 'Se o email existir em nossa base, você receberá instruções para redefinir sua senha.'\r\n        };\r\n      }\r\n\r\n      // Generate reset token\r\n      const resetToken = uuidv4();\r\n      const expiresAt = new Date(Date.now() + 3600000); // 1 hour\r\n\r\n      // Store reset token\r\n      await db.insertInto('password_reset_tokens')\r\n        .values({\r\n          user_id: user.id,\r\n          token: resetToken,\r\n          expires_at: expiresAt,\r\n          created_at: new Date()\r\n        })\r\n        .execute();\r\n\r\n      // Here you would send email with reset link\r\n      // await this.emailService.sendPasswordResetEmail(email, resetToken);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Se o email existir em nossa base, você receberá instruções para redefinir sua senha.',\r\n        // For development only - remove in production\r\n        resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined\r\n      };\r\n    } catch (error) {\r\n      console.error('Error requesting password reset:', error);\r\n      throw new HttpException({\r\n        message: ['Erro interno do servidor'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async resetPassword(token: string, newPassword: string): Promise<any> {\r\n    try {\r\n      // Find valid reset token\r\n      const resetToken = await db.selectFrom('password_reset_tokens')\r\n        .selectAll()\r\n        .where('token', '=', token)\r\n        .where('expires_at', '>', new Date())\r\n        .where('used_at', 'is', null)\r\n        .executeTakeFirst();\r\n\r\n      if (!resetToken) {\r\n        throw new HttpException({\r\n          message: ['Token inválido ou expirado'],\r\n          status: HttpStatus.BAD_REQUEST\r\n        }, HttpStatus.BAD_REQUEST);\r\n      }\r\n\r\n      // Hash new password\r\n      const hashedPassword = await bcrypt.hash(newPassword, 10);\r\n\r\n      // Update user password\r\n      await db.updateTable('users')\r\n        .set({\r\n          password: hashedPassword,\r\n          updated_at: new Date()\r\n        })\r\n        .where('id', '=', resetToken.user_id)\r\n        .execute();\r\n\r\n      // Mark token as used\r\n      await db.updateTable('password_reset_tokens')\r\n        .set({ used_at: new Date() })\r\n        .where('id', '=', resetToken.id)\r\n        .execute();\r\n\r\n      // Invalidate all refresh tokens for security\r\n      await db.deleteFrom('refresh_tokens')\r\n        .where('user_id', '=', resetToken.user_id)\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Senha redefinida com sucesso'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error resetting password:', error);\r\n      if (error instanceof HttpException) throw error;\r\n\r\n      throw new HttpException({\r\n        message: ['Erro interno do servidor'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async verifyEmail(token: string): Promise<any> {\r\n    try {\r\n      // Find user by verification token\r\n      const user = await db.selectFrom('users')\r\n        .selectAll()\r\n        .where('email_verification_token', '=', token)\r\n        .where('email_verified_at', 'is', null)\r\n        .executeTakeFirst();\r\n\r\n      if (!user) {\r\n        throw new HttpException({\r\n          message: ['Token de verificação inválido'],\r\n          status: HttpStatus.BAD_REQUEST\r\n        }, HttpStatus.BAD_REQUEST);\r\n      }\r\n\r\n      // Mark email as verified\r\n      await db.updateTable('users')\r\n        .set({\r\n          email_verified_at: new Date(),\r\n          email_verification_token: null,\r\n          updated_at: new Date()\r\n        })\r\n        .where('id', '=', user.id)\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Email verificado com sucesso'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error verifying email:', error);\r\n      if (error instanceof HttpException) throw error;\r\n\r\n      throw new HttpException({\r\n        message: ['Erro interno do servidor'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async resendVerification(email: string): Promise<any> {\r\n    try {\r\n      const user = await db.selectFrom('users')\r\n        .selectAll()\r\n        .where('email', '=', email)\r\n        .where('email_verified_at', 'is', null)\r\n        .executeTakeFirst();\r\n\r\n      if (!user) {\r\n        return {\r\n          status: 'success',\r\n          message: 'Se o email existir e não estiver verificado, um novo link será enviado.'\r\n        };\r\n      }\r\n\r\n      // Generate new verification token\r\n      const verificationToken = uuidv4();\r\n\r\n      await db.updateTable('users')\r\n        .set({\r\n          email_verification_token: verificationToken,\r\n          updated_at: new Date()\r\n        })\r\n        .where('id', '=', user.id)\r\n        .execute();\r\n\r\n      // Here you would send verification email\r\n      // await this.emailService.sendVerificationEmail(email, verificationToken);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Se o email existir e não estiver verificado, um novo link será enviado.',\r\n        // For development only - remove in production\r\n        verificationToken: process.env.NODE_ENV === 'development' ? verificationToken : undefined\r\n      };\r\n    } catch (error) {\r\n      console.error('Error resending verification:', error);\r\n      throw new HttpException({\r\n        message: ['Erro interno do servidor'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async getUserSessions(userId: number): Promise<any> {\r\n    try {\r\n      const sessions = await db.selectFrom('refresh_tokens')\r\n        .select(['id', 'device_uid', 'created_at', 'last_used_at', 'expires_at'])\r\n        .where('user_id', '=', userId)\r\n        .where('expires_at', '>', new Date())\r\n        .orderBy('last_used_at', 'desc')\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        data: sessions.map(session => ({\r\n          id: session.id,\r\n          deviceId: session.device_uid,\r\n          createdAt: session.created_at,\r\n          lastUsedAt: session.last_used_at,\r\n          expiresAt: session.expires_at\r\n        }))\r\n      };\r\n    } catch (error) {\r\n      console.error('Error getting user sessions:', error);\r\n      throw new HttpException({\r\n        message: ['Erro interno do servidor'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async terminateSession(sessionId: string, userId: number): Promise<any> {\r\n    try {\r\n      const result = await db.deleteFrom('refresh_tokens')\r\n        .where('id', '=', Number(sessionId))\r\n        .where('user_id', '=', userId)\r\n        .execute();\r\n\r\n      if (result.length === 0) {\r\n        throw new HttpException({\r\n          message: ['Sessão não encontrada'],\r\n          status: HttpStatus.NOT_FOUND\r\n        }, HttpStatus.NOT_FOUND);\r\n      }\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Sessão encerrada com sucesso'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error terminating session:', error);\r\n      if (error instanceof HttpException) throw error;\r\n\r\n      throw new HttpException({\r\n        message: ['Erro interno do servidor'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async logoutAllSessions(userId: number): Promise<any> {\r\n    try {\r\n      await db.deleteFrom('refresh_tokens')\r\n        .where('user_id', '=', userId)\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Todas as sessões foram encerradas'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error logging out all sessions:', error);\r\n      throw new HttpException({\r\n        message: ['Erro interno do servidor'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  // OAuth Methods\r\n  async validateOAuthUser(oauthUser: any, provider: 'google' | 'apple'): Promise<any> {\r\n    try {\r\n      const { email, name, googleId, appleId } = oauthUser;\r\n      const providerId = provider === 'google' ? 2 : 3;\r\n      const providerUid = provider === 'google' ? googleId : appleId;\r\n\r\n      if (!email) {\r\n        throw new HttpException({\r\n          message: ['Email não fornecido pelo provedor OAuth'],\r\n          status: HttpStatus.BAD_REQUEST\r\n        }, HttpStatus.BAD_REQUEST);\r\n      }\r\n\r\n      // Check if user exists by email\r\n      let user: any = await db.selectFrom('users')\r\n        .selectAll()\r\n        .where('email', '=', email)\r\n        .executeTakeFirst();\r\n\r\n      if (!user) {\r\n        // Create new user for OAuth\r\n        const userData: any = {\r\n          name: name || email.split('@')[0],\r\n          email: email,\r\n          password: null, // OAuth users don't have passwords\r\n          photo: oauthUser.picture || null, // Store profile picture if available\r\n        };\r\n\r\n        const newUser: any = await db.insertInto('users')\r\n          .values(userData)\r\n          .executeTakeFirstOrThrow();\r\n\r\n        const userId = parseInt(newUser.insertId) || newUser.insertId;\r\n        user = { id: userId, ...userData };\r\n      }\r\n\r\n      // Check if OAuth provider is already linked\r\n      const existingAuth = await db.selectFrom('user_auths')\r\n        .selectAll()\r\n        .where('user_id', '=', user.id)\r\n        .where('provider_id', '=', providerId)\r\n        .executeTakeFirst();\r\n\r\n      if (!existingAuth) {\r\n        // Link OAuth provider to user\r\n        const deviceUid = uuidv4();\r\n        const refreshToken = uuidv4();\r\n\r\n        const userAuthData: any = {\r\n          user_id: user.id,\r\n          provider_id: providerId,\r\n          provider_uid: providerUid,\r\n          device_uid: deviceUid,\r\n          refresh_token: refreshToken,\r\n          expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hour\r\n        };\r\n\r\n        await db.insertInto('user_auths')\r\n          .values(userAuthData)\r\n          .executeTakeFirstOrThrow();\r\n      }\r\n\r\n      return user;\r\n    } catch (error) {\r\n      console.error('Error validating OAuth user:', error);\r\n      throw new HttpException({\r\n        message: ['Erro ao validar usuário OAuth'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async loginWithOAuth(user: any, provider: 'google' | 'apple'): Promise<any> {\r\n    try {\r\n      const validatedUser = await this.validateOAuthUser(user, provider);\r\n      const providerId = provider === 'google' ? 2 : 3;\r\n\r\n      // Generate JWT token\r\n      const payload = { userId: validatedUser.id };\r\n      const access_token = this.jwtService.sign(payload, {\r\n        expiresIn: process.env.JWT_EXPIRATION_TIME,\r\n      });\r\n\r\n      const refresh_token = uuidv4();\r\n      const device_uid = uuidv4();\r\n\r\n      // Update or create user_auths entry\r\n      await db.deleteFrom('user_auths')\r\n        .where('user_id', '=', validatedUser.id)\r\n        .where('provider_id', '=', providerId)\r\n        .execute();\r\n\r\n      const userAuthData: any = {\r\n        user_id: validatedUser.id,\r\n        provider_id: providerId,\r\n        provider_uid: provider === 'google' ? user.googleId : user.appleId,\r\n        device_uid,\r\n        refresh_token,\r\n        expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hour\r\n      };\r\n\r\n      await db.insertInto('user_auths')\r\n        .values(userAuthData)\r\n        .executeTakeFirstOrThrow();\r\n\r\n      return {\r\n        status: \"success\",\r\n        data: {\r\n          access_token,\r\n          refresh_token,\r\n          device_uid\r\n        }\r\n      };\r\n    } catch (error) {\r\n      console.error('Error in OAuth login:', error);\r\n      throw new HttpException({\r\n        message: ['Erro no login OAuth'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  // Frontend-initiated OAuth token validation methods\r\n  async validateGoogleToken(token: string, userInfo: any): Promise<any> {\r\n    try {\r\n      // In a real implementation, you would validate the token with Google's API\r\n      // For now, we'll trust the frontend-provided user info\r\n      // TODO: Add actual Google token validation\r\n\r\n      if (!userInfo.email) {\r\n        throw new HttpException({\r\n          message: ['Email não fornecido pelo Google'],\r\n          status: HttpStatus.BAD_REQUEST\r\n        }, HttpStatus.BAD_REQUEST);\r\n      }\r\n\r\n      const oauthUser = {\r\n        googleId: userInfo.sub || userInfo.id,\r\n        email: userInfo.email,\r\n        name: userInfo.name,\r\n        picture: userInfo.picture,\r\n        firstName: userInfo.given_name,\r\n        lastName: userInfo.family_name,\r\n      };\r\n\r\n      return await this.loginWithOAuth(oauthUser, 'google');\r\n    } catch (error) {\r\n      console.error('Error validating Google token:', error);\r\n      throw new HttpException({\r\n        message: ['Erro ao validar token do Google'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n  async validateAppleToken(token: string, userInfo: any): Promise<any> {\r\n    try {\r\n      // In a real implementation, you would validate the token with Apple's API\r\n      // For now, we'll trust the frontend-provided user info\r\n      // TODO: Add actual Apple token validation\r\n\r\n      if (!userInfo.email) {\r\n        throw new HttpException({\r\n          message: ['Email não fornecido pela Apple'],\r\n          status: HttpStatus.BAD_REQUEST\r\n        }, HttpStatus.BAD_REQUEST);\r\n      }\r\n\r\n      const oauthUser = {\r\n        appleId: userInfo.sub || userInfo.id,\r\n        email: userInfo.email,\r\n        name: userInfo.name || `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim(),\r\n        firstName: userInfo.firstName,\r\n        lastName: userInfo.lastName,\r\n      };\r\n\r\n      return await this.loginWithOAuth(oauthUser, 'apple');\r\n    } catch (error) {\r\n      console.error('Error validating Apple token:', error);\r\n      throw new HttpException({\r\n        message: ['Erro ao validar token da Apple'],\r\n        status: HttpStatus.INTERNAL_SERVER_ERROR\r\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\r\n    }\r\n  }\r\n\r\n}"], "names": ["AuthService", "dotenv", "config", "formatDatetime", "datetime", "dayjs", "format", "login", "user", "email", "password", "HttpException", "message", "status", "HttpStatus", "BAD_REQUEST", "checkUser", "db", "selectFrom", "selectAll", "where", "executeTakeFirst", "NOT_FOUND", "isPasswordValid", "bcrypt", "compare", "UNAUTHORIZED", "deleted_at", "deletedDate", "Date", "now", "daysDifference", "Math", "floor", "getTime", "FORBIDDEN", "data", "userId", "id", "daysRemaining", "deletedAt", "payload", "access_token", "jwtService", "sign", "expiresIn", "process", "env", "JWT_EXPIRATION_TIME", "refresh_token", "uuidv4", "provider_id", "device_uid", "userAuthData", "user_id", "provider_uid", "expire_date", "insertInto", "values", "executeTakeFirstOrThrow", "recoverAccount", "select", "updateTable", "set", "updated_at", "execute", "register", "createUserDto", "name", "invite", "aff_id", "existingUser", "CONFLICT", "hashedPassword", "hash", "inviteData", "toLowerCase", "trim", "userData", "parseInt", "insertId", "deviceUid", "userAuth", "refreshToken", "deleteFrom", "new_refresh_token", "new_access_token", "error", "getRole", "role", "rolesIds", "userRole", "newRole", "role_id", "requestPasswordReset", "resetToken", "expiresAt", "token", "expires_at", "created_at", "NODE_ENV", "undefined", "console", "INTERNAL_SERVER_ERROR", "resetPassword", "newPassword", "used_at", "verifyEmail", "email_verified_at", "email_verification_token", "resendVerification", "verificationToken", "getUserSessions", "sessions", "orderBy", "map", "session", "deviceId", "createdAt", "lastUsedAt", "last_used_at", "terminateSession", "sessionId", "result", "Number", "length", "logoutAllSessions", "validateOAuthUser", "<PERSON><PERSON><PERSON><PERSON>ser", "provider", "googleId", "appleId", "providerId", "providerUid", "split", "photo", "picture", "newUser", "existingAuth", "loginWithOAuth", "validatedUser", "validateGoogleToken", "userInfo", "sub", "firstName", "given_name", "lastName", "family_name", "validateAppleToken", "constructor"], "mappings": ";;;;+BAYaA;;;eAAAA;;;wBAZ8C;kEACnC;qBAEG;sBACE;0BACV;gEACK;8DACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElBC,QAAOC,MAAM;AAGN,IAAA,AAAMF,cAAN,MAAMA;IAKXG,eAAeC,QAAgB,EAAU;QACvC,OAAOC,IAAAA,cAAK,EAACD,UAAUE,MAAM,CAAC;IAChC;IAEA,MAAMC,MAAMC,IAAuC,EAAgB;QACjE,IAAI,CAACA,MAAMC,SAAS,CAACD,MAAME,UAAU;YACnC,MAAM,IAAIC,qBAAa,CAAC;gBAACC,SAAS;oBAAC;iBAAiC;gBAAEC,QAAQC,kBAAU,CAACC,WAAW;YAAA,GAClGD,kBAAU,CAACC,WAAW;QAC1B;QAEA,MAAMC,YAAiB,MAAMC,YAAE,CAACC,UAAU,CAAC,SAC1CC,SAAS,GACTC,KAAK,CAAC,SAAS,KAAKZ,KAAKC,KAAK,EAC9BY,gBAAgB;QAEjB,IAAI,CAACL,WAAW;YACd,MAAM,IAAIL,qBAAa,CAAC;gBAACC,SAAS;oBAAC;iBAAyB;gBAAEC,QAAQC,kBAAU,CAACQ,SAAS;YAAA,GACxFR,kBAAU,CAACQ,SAAS;QACxB;QAEA,iBAAiB;QACjB,MAAMC,kBAAkB,MAAMC,UAAOC,OAAO,CAACjB,KAAKE,QAAQ,EAAEM,UAAUN,QAAQ;QAC9E,IAAI,CAACa,iBAAiB;YACpB,MAAM,IAAIZ,qBAAa,CAAC;gBAACC,SAAS;oBAAC;iBAAiB;gBAAEC,QAAQC,kBAAU,CAACY,YAAY;YAAA,GACnFZ,kBAAU,CAACY,YAAY;QAC3B;QAEA,mCAAmC;QACnC,IAAIV,UAAUW,UAAU,EAAE;YACxB,MAAMC,cAAc,IAAIC,KAAKb,UAAUW,UAAU;YACjD,MAAMG,MAAM,IAAID;YAChB,MAAME,iBAAiBC,KAAKC,KAAK,CAAC,AAACH,CAAAA,IAAII,OAAO,KAAKN,YAAYM,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;YAE/F,iEAAiE;YACjE,IAAIH,iBAAiB,IAAI;gBACvB,MAAM,IAAIpB,qBAAa,CAAC;oBACtBC,SAAS;wBAAC;qBAA2C;oBACrDC,QAAQC,kBAAU,CAACqB,SAAS;gBAC9B,GAAGrB,kBAAU,CAACqB,SAAS;YACzB;YAEA,8DAA8D;YAC9D,OAAO;gBACLtB,QAAQ;gBACRuB,MAAM;oBACJxB,SAAS,CAAC,uCAAuC,EAAEmB,eAAe,gBAAgB,EAAE,KAAKA,eAAe,iCAAiC,CAAC;oBAC1IM,QAAQrB,UAAUsB,EAAE;oBACpBC,eAAe,KAAKR;oBACpBS,WAAWxB,UAAUW,UAAU;gBACjC;YACF;QACF;QAEA,MAAMc,UAAU;YAAEJ,QAAQrB,UAAUsB,EAAE;QAAC;QACvC,MAAMI,eAAe,IAAI,CAACC,UAAU,CAACC,IAAI,CAACH,SAAS;YACjDI,WAAWC,QAAQC,GAAG,CAACC,mBAAmB;QAC5C;QACA,MAAMC,gBAAgBC,IAAAA,QAAM;QAC5B,MAAMC,cAAc;QACpB,MAAMC,aAAaF,IAAAA,QAAM;QAEzB,iCAAiC;QACjC,MAAMG,eAAoB;YACxBC,SAAStC,UAAUsB,EAAE;YACrBiB,cAAc/C,KAAKC,KAAK;YACxB0C,aAAaA;YACbC;YACAH;YACAO,aAAa,IAAI3B,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAM,SAAS;QAC9D;QAEA,MAAMb,YAAE,CAACwC,UAAU,CAAC,cACnBC,MAAM,CAACL,cACPM,uBAAuB;QAExB,OAAO;YACL9C,QAAQ;YACRuB,MAAM;gBACJM;gBACAO;gBACAG;YACF;QACF;IACF;IAEA,MAAMQ,eAAevB,MAAc,EAAgB;QACjD,sCAAsC;QACtC,MAAMrB,YAAiB,MAAMC,YAAE,CAACC,UAAU,CAAC,SAC1C2C,MAAM,CAAC;YAAC;YAAM;YAAc;SAAQ,EACpCzC,KAAK,CAAC,MAAM,KAAKiB,QACjBhB,gBAAgB;QAEjB,IAAI,CAACL,WAAW;YACd,MAAM,IAAIL,qBAAa,CAAC;gBAACC,SAAS;oBAAC;iBAAyB;gBAAEC,QAAQC,kBAAU,CAACQ,SAAS;YAAA,GACxFR,kBAAU,CAACQ,SAAS;QACxB;QAEA,IAAI,CAACN,UAAUW,UAAU,EAAE;YACzB,MAAM,IAAIhB,qBAAa,CAAC;gBAACC,SAAS;oBAAC;iBAA4C;gBAAEC,QAAQC,kBAAU,CAACC,WAAW;YAAA,GAC7GD,kBAAU,CAACC,WAAW;QAC1B;QAEA,wCAAwC;QACxC,MAAMa,cAAc,IAAIC,KAAKb,UAAUW,UAAU;QACjD,MAAMG,MAAM,IAAID;QAChB,MAAME,iBAAiBC,KAAKC,KAAK,CAAC,AAACH,CAAAA,IAAII,OAAO,KAAKN,YAAYM,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;QAE/F,IAAIH,iBAAiB,IAAI;YACvB,MAAM,IAAIpB,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAAwF;gBAClGC,QAAQC,kBAAU,CAACqB,SAAS;YAC9B,GAAGrB,kBAAU,CAACqB,SAAS;QACzB;QAEA,0CAA0C;QAC1C,MAAMlB,YAAE,CACL6C,WAAW,CAAC,SACZC,GAAG,CAAC;YACHpC,YAAY;YACZqC,YAAY,IAAInC;QAClB,GACCT,KAAK,CAAC,MAAM,KAAKiB,QACjB4B,OAAO;QAEV,gDAAgD;QAChD,MAAMxB,UAAU;YAAEJ,QAAQrB,UAAUsB,EAAE;QAAC;QACvC,MAAMI,eAAe,IAAI,CAACC,UAAU,CAACC,IAAI,CAACH,SAAS;YACjDI,WAAWC,QAAQC,GAAG,CAACC,mBAAmB;QAC5C;QACA,MAAMC,gBAAgBC,IAAAA,QAAM;QAC5B,MAAMC,cAAc;QACpB,MAAMC,aAAaF,IAAAA,QAAM;QAEzB,iCAAiC;QACjC,MAAMG,eAAoB;YACxBC,SAAStC,UAAUsB,EAAE;YACrBiB,cAAcvC,UAAUP,KAAK;YAC7B0C,aAAaA;YACbC;YACAH;YACAO,aAAa,IAAI3B,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAM,SAAS;QAC9D;QAEA,MAAMb,YAAE,CAACwC,UAAU,CAAC,cACnBC,MAAM,CAACL,cACPM,uBAAuB;QAExB,OAAO;YACL9C,QAAQ;YACRuB,MAAM;gBACJM;gBACAO;gBACAG;gBACAxC,SAAS;YACX;QACF;IACF;IAEA,kCAAkC;IAClC,MAAMsD,SAASC,aAA4B,EAAgB;QACzD,MAAM,EAAEC,IAAI,EAAE3D,KAAK,EAAEC,QAAQ,EAAE2D,MAAM,EAAE,GAAGF;QAC1C,IAAIG,SAAS;QAEb,MAAMC,eAAoB,MAAMtD,YAAE,CAACC,UAAU,CAAC,SAC7CE,KAAK,CAAC,SAAS,KAAKX,OACpBU,SAAS,GACTE,gBAAgB;QAEjB,IAAIkD,cAAc;YAChB,MAAM,IAAI5D,qBAAa,CAAC;gBAACC,SAAS;oBAAC;iBAA6B;gBAAEC,QAAQC,kBAAU,CAAC0D,QAAQ;YAAA,GAC3F1D,kBAAU,CAAC0D,QAAQ;QACvB;QAEA,MAAMC,iBAAiB,MAAMjD,UAAOkD,IAAI,CAAChE,UAAU;QAEnD,IAAG2D,QAAQ;YACT,yBAAyB;YACzB,MAAMM,aAAkB,MAAM1D,YAAE,CAACC,UAAU,CAAC,mBAC3CE,KAAK,CAAC,UAAU,KAAKiD,OAAOO,WAAW,GAAGC,IAAI,IAC9ChB,MAAM,CAAC;gBAAC;aAAU,EAClBxC,gBAAgB;YAEjB,IAAIsD,YAAY;gBACdL,SAASK,WAAWrB,OAAO;YAC7B;YAEA,IAAI,CAACqB,YAAY;gBACf,MAAM,IAAIhE,qBAAa,CAAC;oBAACC,SAAS;wBAAC;qBAA8B;oBAAEC,QAAQC,kBAAU,CAACC,WAAW;gBAAA,GAC/FD,kBAAU,CAACC,WAAW;YAC1B;QACF;QAEA,MAAM+D,WAAgB;YACpBV,MAAMA,KAAKS,IAAI;YACfpE,OAAOA,MAAMoE,IAAI;YACjBnE,UAAU+D;YACVJ,QAAQA,SAASA,OAAOO,WAAW,GAAGC,IAAI,KAAK;YAC/CP;QACF;QAEA,MAAM9D,OAAY,MAAMS,YAAE,CAACwC,UAAU,CAAC,SACrCC,MAAM,CAACoB,UACPnB,uBAAuB;QAExB,MAAMtB,SAAS0C,SAASvE,KAAKwE,QAAQ,KAAKxE,KAAKwE,QAAQ;QAEvD,4BAA4B;QAC5B,0DAA0D;QAE1D,MAAMvC,UAAU;YAAEJ,QAAQA;QAAO;QAEjC,MAAMK,eAAe,IAAI,CAACC,UAAU,CAACC,IAAI,CAACH,SAAS;YAAEI,WAAWC,QAAQC,GAAG,CAACC,mBAAmB;QAAC;QAEhG,MAAMiC,YAAY/B,IAAAA,QAAM;QAExB,MAAMD,gBAAgBC,IAAAA,QAAM;QAE5B,MAAMC,cAAc,GAAG,QAAQ;QAG/B,uBAAuB;QACvB;;;;;;;;;IASA,GACD,MAAME,eAAoB;YACzBC,SAASjB;YACTc;YACAI,cAAc9C;YACd2C,YAAY6B;YACZhC;YACAO,aAAa,IAAI3B,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAM,SAAS;QAC7D;QAEA,MAAMoD,WAAW,MAAMjE,YAAE,CAACwC,UAAU,CAAC,cACpCC,MAAM,CAACL,cACPM,uBAAuB;QAGvB,sEAAsE;QAEtE,OAAO;YACH,UAAU;YACV,QAAQ;gBACJ,gBAAgBjB;gBAChB,iBAAiBO;gBACjB,cAAcgC;YAClB;QACJ;IACF;IAEA,gBAAgB;IAChB,MAAME,aAAalC,aAAqB,EAAEG,UAAkB,EAAE;QAC5D,IAAI;YACH,MAAM8B,WAAW,MAAMjE,YAAE,CAACC,UAAU,CAAC,cACpC2C,MAAM,CAAC;gBAAC;gBAAW;gBAAe;gBAAgB;aAAa,EAC/DzC,KAAK,CAAC,iBAAiB,KAAK6B,eAC5B7B,KAAK,CAAC,cAAc,KAAKgC,YACzB/B,gBAAgB;YAEhB,IAAI,CAAC6D,UAAU;gBACb,MAAM,IAAIvE,qBAAa,CAAC;oBAACC,SAAS;wBAAC;qBAAyB;oBAAEC,QAAQC,kBAAU,CAACY,YAAY;gBAAA,GAC3FZ,kBAAU,CAACY,YAAY;YAC3B;YAEA,MAAMW,SAAS6C,SAAS5B,OAAO;YAE/B,MAAMrC,YAAE,CAACmE,UAAU,CAAC,cACnBhE,KAAK,CAAC,WAAW,KAAKiB,QACtBjB,KAAK,CAAC,cAAc,KAAKgC,YACzBO,uBAAuB;YAExB,6BAA6B;YAC7B,MAAM0B,oBAAoBnC,IAAAA,QAAM;YAEhC,8BAA8B;YAC9B,MAAMG,eAAoB;gBACxBC,SAASjB;gBACTc,aAAa+B,SAAS/B,WAAW;gBACjCI,cAAc2B,SAAS3B,YAAY;gBACnCH;gBACAH,eAAeoC;gBACf7B,aAAa,IAAI3B,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAM,SAAS;YAC9D;YAEA,MAAMb,YAAE,CAACwC,UAAU,CAAC,cACnBC,MAAM,CAACL,cACPM,uBAAuB;YAExB,4BAA4B;YAC5B,MAAMlB,UAAU;gBAAEJ,QAAQA;YAAO;YACjC,MAAMiD,mBAAmB,IAAI,CAAC3C,UAAU,CAACC,IAAI,CAACH,SAAS;gBACrDI,WAAWC,QAAQC,GAAG,CAACC,mBAAmB;YAC5C;YAEA,OAAO;gBACL,UAAU;gBACV,QAAQ;oBACJ,gBAAgBsC;oBAChB,iBAAiBD;oBACjB,cAAcjC;gBAClB;YACF;QAEF,EAAE,OAAOmC,OAAO;YACd,MAAM,IAAI5E,qBAAa,CAAC;gBAACC,SAAS;oBAAC;iBAAyB;gBAAEC,QAAQC,kBAAU,CAACY,YAAY;YAAA,GAC3FZ,kBAAU,CAACY,YAAY;QAC3B;IAEF;IAEA,MAAM8D,QAAQnD,MAAc,EAAEoD,IAAY,EAAE;QAC1C,MAAMC,WAAW;YACf,SAAS;YACT,SAAS;YACT,gBAAgB;YAChB,QAAQ;QACV;QAEA,MAAMC,WAAW,MAAM1E,YAAE,CACxBC,UAAU,CAAC,eACX2C,MAAM,CAAC;YAAC;SAAU,EAClBzC,KAAK,CAAC,WAAW,KAAKiB,QACtBjB,KAAK,CAAC,WAAW,KAAKsE,QAAQ,CAACD,KAAK,EACpCpE,gBAAgB;QAEjB,IAAI,CAACsE,UAAU;YACb,IAAIF,SAAS,SAAS;gBACpB,OAAO;oBACL5E,QAAQ;oBACR4E,MAAM;gBACR;YACF;YAEA,WAAW;YACX,MAAMG,UAAU,MAAM3E,YAAE,CACrBwC,UAAU,CAAC,eACXC,MAAM,CAAC;gBACNJ,SAASjB;gBACTwD,SAASH,QAAQ,CAACD,KAAK;YACzB,GACCxB,OAAO;QACZ;QAEA,OAAO;YACLpD,QAAQ;YACR4E,MAAMA;QACR;IACF;IAEA,MAAMK,qBAAqBrF,KAAa,EAAgB;QACtD,IAAI;YACF,MAAMD,OAAO,MAAMS,YAAE,CAACC,UAAU,CAAC,SAC9BC,SAAS,GACTC,KAAK,CAAC,SAAS,KAAKX,OACpBY,gBAAgB;YAEnB,IAAI,CAACb,MAAM;gBACT,mDAAmD;gBACnD,OAAO;oBACLK,QAAQ;oBACRD,SAAS;gBACX;YACF;YAEA,uBAAuB;YACvB,MAAMmF,aAAa7C,IAAAA,QAAM;YACzB,MAAM8C,YAAY,IAAInE,KAAKA,KAAKC,GAAG,KAAK,UAAU,SAAS;YAE3D,oBAAoB;YACpB,MAAMb,YAAE,CAACwC,UAAU,CAAC,yBACjBC,MAAM,CAAC;gBACNJ,SAAS9C,KAAK8B,EAAE;gBAChB2D,OAAOF;gBACPG,YAAYF;gBACZG,YAAY,IAAItE;YAClB,GACCoC,OAAO;YAEV,4CAA4C;YAC5C,qEAAqE;YAErE,OAAO;gBACLpD,QAAQ;gBACRD,SAAS;gBACT,8CAA8C;gBAC9CmF,YAAYjD,QAAQC,GAAG,CAACqD,QAAQ,KAAK,gBAAgBL,aAAaM;YACpE;QACF,EAAE,OAAOd,OAAO;YACde,QAAQf,KAAK,CAAC,oCAAoCA;YAClD,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAA2B;gBACrCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAMC,cAAcP,KAAa,EAAEQ,WAAmB,EAAgB;QACpE,IAAI;YACF,yBAAyB;YACzB,MAAMV,aAAa,MAAM9E,YAAE,CAACC,UAAU,CAAC,yBACpCC,SAAS,GACTC,KAAK,CAAC,SAAS,KAAK6E,OACpB7E,KAAK,CAAC,cAAc,KAAK,IAAIS,QAC7BT,KAAK,CAAC,WAAW,MAAM,MACvBC,gBAAgB;YAEnB,IAAI,CAAC0E,YAAY;gBACf,MAAM,IAAIpF,qBAAa,CAAC;oBACtBC,SAAS;wBAAC;qBAA6B;oBACvCC,QAAQC,kBAAU,CAACC,WAAW;gBAChC,GAAGD,kBAAU,CAACC,WAAW;YAC3B;YAEA,oBAAoB;YACpB,MAAM0D,iBAAiB,MAAMjD,UAAOkD,IAAI,CAAC+B,aAAa;YAEtD,uBAAuB;YACvB,MAAMxF,YAAE,CAAC6C,WAAW,CAAC,SAClBC,GAAG,CAAC;gBACHrD,UAAU+D;gBACVT,YAAY,IAAInC;YAClB,GACCT,KAAK,CAAC,MAAM,KAAK2E,WAAWzC,OAAO,EACnCW,OAAO;YAEV,qBAAqB;YACrB,MAAMhD,YAAE,CAAC6C,WAAW,CAAC,yBAClBC,GAAG,CAAC;gBAAE2C,SAAS,IAAI7E;YAAO,GAC1BT,KAAK,CAAC,MAAM,KAAK2E,WAAWzD,EAAE,EAC9B2B,OAAO;YAEV,6CAA6C;YAC7C,MAAMhD,YAAE,CAACmE,UAAU,CAAC,kBACjBhE,KAAK,CAAC,WAAW,KAAK2E,WAAWzC,OAAO,EACxCW,OAAO;YAEV,OAAO;gBACLpD,QAAQ;gBACRD,SAAS;YACX;QACF,EAAE,OAAO2E,OAAO;YACde,QAAQf,KAAK,CAAC,6BAA6BA;YAC3C,IAAIA,iBAAiB5E,qBAAa,EAAE,MAAM4E;YAE1C,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAA2B;gBACrCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAMI,YAAYV,KAAa,EAAgB;QAC7C,IAAI;YACF,kCAAkC;YAClC,MAAMzF,OAAO,MAAMS,YAAE,CAACC,UAAU,CAAC,SAC9BC,SAAS,GACTC,KAAK,CAAC,4BAA4B,KAAK6E,OACvC7E,KAAK,CAAC,qBAAqB,MAAM,MACjCC,gBAAgB;YAEnB,IAAI,CAACb,MAAM;gBACT,MAAM,IAAIG,qBAAa,CAAC;oBACtBC,SAAS;wBAAC;qBAAgC;oBAC1CC,QAAQC,kBAAU,CAACC,WAAW;gBAChC,GAAGD,kBAAU,CAACC,WAAW;YAC3B;YAEA,yBAAyB;YACzB,MAAME,YAAE,CAAC6C,WAAW,CAAC,SAClBC,GAAG,CAAC;gBACH6C,mBAAmB,IAAI/E;gBACvBgF,0BAA0B;gBAC1B7C,YAAY,IAAInC;YAClB,GACCT,KAAK,CAAC,MAAM,KAAKZ,KAAK8B,EAAE,EACxB2B,OAAO;YAEV,OAAO;gBACLpD,QAAQ;gBACRD,SAAS;YACX;QACF,EAAE,OAAO2E,OAAO;YACde,QAAQf,KAAK,CAAC,0BAA0BA;YACxC,IAAIA,iBAAiB5E,qBAAa,EAAE,MAAM4E;YAE1C,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAA2B;gBACrCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAMO,mBAAmBrG,KAAa,EAAgB;QACpD,IAAI;YACF,MAAMD,OAAO,MAAMS,YAAE,CAACC,UAAU,CAAC,SAC9BC,SAAS,GACTC,KAAK,CAAC,SAAS,KAAKX,OACpBW,KAAK,CAAC,qBAAqB,MAAM,MACjCC,gBAAgB;YAEnB,IAAI,CAACb,MAAM;gBACT,OAAO;oBACLK,QAAQ;oBACRD,SAAS;gBACX;YACF;YAEA,kCAAkC;YAClC,MAAMmG,oBAAoB7D,IAAAA,QAAM;YAEhC,MAAMjC,YAAE,CAAC6C,WAAW,CAAC,SAClBC,GAAG,CAAC;gBACH8C,0BAA0BE;gBAC1B/C,YAAY,IAAInC;YAClB,GACCT,KAAK,CAAC,MAAM,KAAKZ,KAAK8B,EAAE,EACxB2B,OAAO;YAEV,yCAAyC;YACzC,2EAA2E;YAE3E,OAAO;gBACLpD,QAAQ;gBACRD,SAAS;gBACT,8CAA8C;gBAC9CmG,mBAAmBjE,QAAQC,GAAG,CAACqD,QAAQ,KAAK,gBAAgBW,oBAAoBV;YAClF;QACF,EAAE,OAAOd,OAAO;YACde,QAAQf,KAAK,CAAC,iCAAiCA;YAC/C,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAA2B;gBACrCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAMS,gBAAgB3E,MAAc,EAAgB;QAClD,IAAI;YACF,MAAM4E,WAAW,MAAMhG,YAAE,CAACC,UAAU,CAAC,kBAClC2C,MAAM,CAAC;gBAAC;gBAAM;gBAAc;gBAAc;gBAAgB;aAAa,EACvEzC,KAAK,CAAC,WAAW,KAAKiB,QACtBjB,KAAK,CAAC,cAAc,KAAK,IAAIS,QAC7BqF,OAAO,CAAC,gBAAgB,QACxBjD,OAAO;YAEV,OAAO;gBACLpD,QAAQ;gBACRuB,MAAM6E,SAASE,GAAG,CAACC,CAAAA,UAAY,CAAA;wBAC7B9E,IAAI8E,QAAQ9E,EAAE;wBACd+E,UAAUD,QAAQhE,UAAU;wBAC5BkE,WAAWF,QAAQjB,UAAU;wBAC7BoB,YAAYH,QAAQI,YAAY;wBAChCxB,WAAWoB,QAAQlB,UAAU;oBAC/B,CAAA;YACF;QACF,EAAE,OAAOX,OAAO;YACde,QAAQf,KAAK,CAAC,gCAAgCA;YAC9C,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAA2B;gBACrCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAMkB,iBAAiBC,SAAiB,EAAErF,MAAc,EAAgB;QACtE,IAAI;YACF,MAAMsF,SAAS,MAAM1G,YAAE,CAACmE,UAAU,CAAC,kBAChChE,KAAK,CAAC,MAAM,KAAKwG,OAAOF,YACxBtG,KAAK,CAAC,WAAW,KAAKiB,QACtB4B,OAAO;YAEV,IAAI0D,OAAOE,MAAM,KAAK,GAAG;gBACvB,MAAM,IAAIlH,qBAAa,CAAC;oBACtBC,SAAS;wBAAC;qBAAwB;oBAClCC,QAAQC,kBAAU,CAACQ,SAAS;gBAC9B,GAAGR,kBAAU,CAACQ,SAAS;YACzB;YAEA,OAAO;gBACLT,QAAQ;gBACRD,SAAS;YACX;QACF,EAAE,OAAO2E,OAAO;YACde,QAAQf,KAAK,CAAC,8BAA8BA;YAC5C,IAAIA,iBAAiB5E,qBAAa,EAAE,MAAM4E;YAE1C,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAA2B;gBACrCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAMuB,kBAAkBzF,MAAc,EAAgB;QACpD,IAAI;YACF,MAAMpB,YAAE,CAACmE,UAAU,CAAC,kBACjBhE,KAAK,CAAC,WAAW,KAAKiB,QACtB4B,OAAO;YAEV,OAAO;gBACLpD,QAAQ;gBACRD,SAAS;YACX;QACF,EAAE,OAAO2E,OAAO;YACde,QAAQf,KAAK,CAAC,mCAAmCA;YACjD,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAA2B;gBACrCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,gBAAgB;IAChB,MAAMwB,kBAAkBC,SAAc,EAAEC,QAA4B,EAAgB;QAClF,IAAI;YACF,MAAM,EAAExH,KAAK,EAAE2D,IAAI,EAAE8D,QAAQ,EAAEC,OAAO,EAAE,GAAGH;YAC3C,MAAMI,aAAaH,aAAa,WAAW,IAAI;YAC/C,MAAMI,cAAcJ,aAAa,WAAWC,WAAWC;YAEvD,IAAI,CAAC1H,OAAO;gBACV,MAAM,IAAIE,qBAAa,CAAC;oBACtBC,SAAS;wBAAC;qBAA0C;oBACpDC,QAAQC,kBAAU,CAACC,WAAW;gBAChC,GAAGD,kBAAU,CAACC,WAAW;YAC3B;YAEA,gCAAgC;YAChC,IAAIP,OAAY,MAAMS,YAAE,CAACC,UAAU,CAAC,SACjCC,SAAS,GACTC,KAAK,CAAC,SAAS,KAAKX,OACpBY,gBAAgB;YAEnB,IAAI,CAACb,MAAM;gBACT,4BAA4B;gBAC5B,MAAMsE,WAAgB;oBACpBV,MAAMA,QAAQ3D,MAAM6H,KAAK,CAAC,IAAI,CAAC,EAAE;oBACjC7H,OAAOA;oBACPC,UAAU;oBACV6H,OAAOP,UAAUQ,OAAO,IAAI;gBAC9B;gBAEA,MAAMC,UAAe,MAAMxH,YAAE,CAACwC,UAAU,CAAC,SACtCC,MAAM,CAACoB,UACPnB,uBAAuB;gBAE1B,MAAMtB,SAAS0C,SAAS0D,QAAQzD,QAAQ,KAAKyD,QAAQzD,QAAQ;gBAC7DxE,OAAO;oBAAE8B,IAAID;oBAAQ,GAAGyC,QAAQ;gBAAC;YACnC;YAEA,4CAA4C;YAC5C,MAAM4D,eAAe,MAAMzH,YAAE,CAACC,UAAU,CAAC,cACtCC,SAAS,GACTC,KAAK,CAAC,WAAW,KAAKZ,KAAK8B,EAAE,EAC7BlB,KAAK,CAAC,eAAe,KAAKgH,YAC1B/G,gBAAgB;YAEnB,IAAI,CAACqH,cAAc;gBACjB,8BAA8B;gBAC9B,MAAMzD,YAAY/B,IAAAA,QAAM;gBACxB,MAAMiC,eAAejC,IAAAA,QAAM;gBAE3B,MAAMG,eAAoB;oBACxBC,SAAS9C,KAAK8B,EAAE;oBAChBa,aAAaiF;oBACb7E,cAAc8E;oBACdjF,YAAY6B;oBACZhC,eAAekC;oBACf3B,aAAa,IAAI3B,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAM,SAAS;gBAC9D;gBAEA,MAAMb,YAAE,CAACwC,UAAU,CAAC,cACjBC,MAAM,CAACL,cACPM,uBAAuB;YAC5B;YAEA,OAAOnD;QACT,EAAE,OAAO+E,OAAO;YACde,QAAQf,KAAK,CAAC,gCAAgCA;YAC9C,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAAgC;gBAC1CC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAMoC,eAAenI,IAAS,EAAEyH,QAA4B,EAAgB;QAC1E,IAAI;YACF,MAAMW,gBAAgB,MAAM,IAAI,CAACb,iBAAiB,CAACvH,MAAMyH;YACzD,MAAMG,aAAaH,aAAa,WAAW,IAAI;YAE/C,qBAAqB;YACrB,MAAMxF,UAAU;gBAAEJ,QAAQuG,cAActG,EAAE;YAAC;YAC3C,MAAMI,eAAe,IAAI,CAACC,UAAU,CAACC,IAAI,CAACH,SAAS;gBACjDI,WAAWC,QAAQC,GAAG,CAACC,mBAAmB;YAC5C;YAEA,MAAMC,gBAAgBC,IAAAA,QAAM;YAC5B,MAAME,aAAaF,IAAAA,QAAM;YAEzB,oCAAoC;YACpC,MAAMjC,YAAE,CAACmE,UAAU,CAAC,cACjBhE,KAAK,CAAC,WAAW,KAAKwH,cAActG,EAAE,EACtClB,KAAK,CAAC,eAAe,KAAKgH,YAC1BnE,OAAO;YAEV,MAAMZ,eAAoB;gBACxBC,SAASsF,cAActG,EAAE;gBACzBa,aAAaiF;gBACb7E,cAAc0E,aAAa,WAAWzH,KAAK0H,QAAQ,GAAG1H,KAAK2H,OAAO;gBAClE/E;gBACAH;gBACAO,aAAa,IAAI3B,KAAKA,KAAKC,GAAG,KAAK,KAAK,KAAK,MAAM,SAAS;YAC9D;YAEA,MAAMb,YAAE,CAACwC,UAAU,CAAC,cACjBC,MAAM,CAACL,cACPM,uBAAuB;YAE1B,OAAO;gBACL9C,QAAQ;gBACRuB,MAAM;oBACJM;oBACAO;oBACAG;gBACF;YACF;QACF,EAAE,OAAOmC,OAAO;YACde,QAAQf,KAAK,CAAC,yBAAyBA;YACvC,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAAsB;gBAChCC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,oDAAoD;IACpD,MAAMsC,oBAAoB5C,KAAa,EAAE6C,QAAa,EAAgB;QACpE,IAAI;YACF,2EAA2E;YAC3E,uDAAuD;YACvD,2CAA2C;YAE3C,IAAI,CAACA,SAASrI,KAAK,EAAE;gBACnB,MAAM,IAAIE,qBAAa,CAAC;oBACtBC,SAAS;wBAAC;qBAAkC;oBAC5CC,QAAQC,kBAAU,CAACC,WAAW;gBAChC,GAAGD,kBAAU,CAACC,WAAW;YAC3B;YAEA,MAAMiH,YAAY;gBAChBE,UAAUY,SAASC,GAAG,IAAID,SAASxG,EAAE;gBACrC7B,OAAOqI,SAASrI,KAAK;gBACrB2D,MAAM0E,SAAS1E,IAAI;gBACnBoE,SAASM,SAASN,OAAO;gBACzBQ,WAAWF,SAASG,UAAU;gBAC9BC,UAAUJ,SAASK,WAAW;YAChC;YAEA,OAAO,MAAM,IAAI,CAACR,cAAc,CAACX,WAAW;QAC9C,EAAE,OAAOzC,OAAO;YACde,QAAQf,KAAK,CAAC,kCAAkCA;YAChD,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAAkC;gBAC5CC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAEA,MAAM6C,mBAAmBnD,KAAa,EAAE6C,QAAa,EAAgB;QACnE,IAAI;YACF,0EAA0E;YAC1E,uDAAuD;YACvD,0CAA0C;YAE1C,IAAI,CAACA,SAASrI,KAAK,EAAE;gBACnB,MAAM,IAAIE,qBAAa,CAAC;oBACtBC,SAAS;wBAAC;qBAAiC;oBAC3CC,QAAQC,kBAAU,CAACC,WAAW;gBAChC,GAAGD,kBAAU,CAACC,WAAW;YAC3B;YAEA,MAAMiH,YAAY;gBAChBG,SAASW,SAASC,GAAG,IAAID,SAASxG,EAAE;gBACpC7B,OAAOqI,SAASrI,KAAK;gBACrB2D,MAAM0E,SAAS1E,IAAI,IAAI,GAAG0E,SAASE,SAAS,IAAI,GAAG,CAAC,EAAEF,SAASI,QAAQ,IAAI,IAAI,CAACrE,IAAI;gBACpFmE,WAAWF,SAASE,SAAS;gBAC7BE,UAAUJ,SAASI,QAAQ;YAC7B;YAEA,OAAO,MAAM,IAAI,CAACP,cAAc,CAACX,WAAW;QAC9C,EAAE,OAAOzC,OAAO;YACde,QAAQf,KAAK,CAAC,iCAAiCA;YAC/C,MAAM,IAAI5E,qBAAa,CAAC;gBACtBC,SAAS;oBAAC;iBAAiC;gBAC3CC,QAAQC,kBAAU,CAACyF,qBAAqB;YAC1C,GAAGzF,kBAAU,CAACyF,qBAAqB;QACrC;IACF;IAxyBA8C,YACE,AAAiB1G,UAAsB,CACvC;aADiBA,aAAAA;IAChB;AAwyBL"}