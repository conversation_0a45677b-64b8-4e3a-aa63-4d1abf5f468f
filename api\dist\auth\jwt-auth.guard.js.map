{"version": 3, "sources": ["../../src/auth/jwt-auth.guard.ts"], "sourcesContent": ["import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';\r\nimport { AuthGuard } from '@nestjs/passport';\r\n\r\n@Injectable()\r\nexport class JwtAuthGuard extends AuthGuard('jwt') {\r\n  canActivate(context: ExecutionContext) {\r\n    return super.canActivate(context);\r\n  }\r\n}"], "names": ["JwtAuthGuard", "<PERSON><PERSON><PERSON><PERSON>", "canActivate", "context"], "mappings": ";;;;+BAIaA;;;eAAAA;;;wBAJ6C;0BAChC;;;;;;;AAGnB,IAAA,AAAMA,eAAN,MAAMA,qBAAqBC,IAAAA,mBAAS,EAAC;IAC1CC,YAAYC,OAAyB,EAAE;QACrC,OAAO,KAAK,CAACD,YAAYC;IAC3B;AACF"}