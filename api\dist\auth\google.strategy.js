"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "GoogleStrategy", {
    enumerable: true,
    get: function() {
        return GoogleStrategy;
    }
});
const _common = require("@nestjs/common");
const _passport = require("@nestjs/passport");
const _passportgoogleoauth20 = require("passport-google-oauth20");
const _dotenv = require("dotenv");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
(0, _dotenv.config)();
let GoogleStrategy = class GoogleStrategy extends (0, _passport.PassportStrategy)(_passportgoogleoauth20.Strategy, 'google') {
    async validate(accessToken, refreshToken, profile) {
        try {
            const { id, name, emails, photos } = profile;
            // Extract user data from Google profile
            const user = {
                googleId: id,
                email: emails[0]?.value,
                firstName: name?.givenName,
                lastName: name?.familyName,
                name: name?.givenName + ' ' + name?.familyName,
                picture: photos[0]?.value,
                accessToken,
                refreshToken
            };
            // Validate that we have required data
            if (!user.email) {
                throw new Error('Email not provided by Google');
            }
            return user;
        } catch (error) {
            throw error;
        }
    }
    constructor(){
        // Check if Google OAuth is properly configured
        if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
            console.warn('Google OAuth not properly configured. Some environment variables are missing.');
            // Use dummy values to prevent strategy initialization errors
            super({
                clientID: 'dummy',
                clientSecret: 'dummy',
                callbackURL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback',
                scope: [
                    'email',
                    'profile'
                ]
            });
            return;
        }
        super({
            clientID: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            callbackURL: process.env.GOOGLE_CALLBACK_URL,
            scope: [
                'email',
                'profile'
            ]
        });
    }
};
GoogleStrategy = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [])
], GoogleStrategy);

//# sourceMappingURL=google.strategy.js.map