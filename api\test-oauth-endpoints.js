const axios = require('axios');
require('dotenv').config();

const API_URL = process.env.API_URL || 'http://localhost:3000';

async function testOAuthEndpoints() {
  console.log('🔍 Testing OAuth endpoints...\n');

  try {
    // Test Google OAuth endpoint
    console.log('1. Testing Google OAuth endpoint...');
    try {
      const googleResponse = await axios.get(`${API_URL}/auth/google`, {
        maxRedirects: 0,
        validateStatus: (status) => status === 302 || status === 200
      });
      
      if (googleResponse.status === 302) {
        console.log('✅ Google OAuth endpoint working - redirects to Google');
        console.log(`   Redirect URL: ${googleResponse.headers.location}`);
      } else {
        console.log('✅ Google OAuth endpoint accessible');
      }
    } catch (error) {
      if (error.response && error.response.status === 302) {
        console.log('✅ Google OAuth endpoint working - redirects to Google');
      } else {
        console.log('❌ Google OAuth endpoint error:', error.message);
      }
    }

    // Test Apple OAuth endpoint
    console.log('\n2. Testing Apple OAuth endpoint...');
    try {
      const appleResponse = await axios.get(`${API_URL}/auth/apple`, {
        maxRedirects: 0,
        validateStatus: (status) => status === 302 || status === 200
      });
      
      if (appleResponse.status === 302) {
        console.log('✅ Apple OAuth endpoint working - redirects to Apple');
        console.log(`   Redirect URL: ${appleResponse.headers.location}`);
      } else {
        console.log('✅ Apple OAuth endpoint accessible');
      }
    } catch (error) {
      if (error.response && error.response.status === 302) {
        console.log('✅ Apple OAuth endpoint working - redirects to Apple');
      } else {
        console.log('❌ Apple OAuth endpoint error:', error.message);
      }
    }

    // Test regular login endpoint (should still work)
    console.log('\n3. Testing regular login endpoint...');
    try {
      const loginResponse = await axios.post(`${API_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }, {
        validateStatus: (status) => status >= 200 && status < 500
      });
      
      if (loginResponse.status === 404 || loginResponse.status === 401) {
        console.log('✅ Regular login endpoint working (expected auth failure)');
      } else {
        console.log('✅ Regular login endpoint accessible');
      }
    } catch (error) {
      console.log('❌ Regular login endpoint error:', error.message);
    }

    console.log('\n🎉 OAuth endpoint testing completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Configure OAuth credentials in .env file');
    console.log('2. Test with real Google/Apple OAuth apps');
    console.log('3. Verify frontend OAuth button functionality');

  } catch (error) {
    console.error('❌ Error testing OAuth endpoints:', error.message);
  }
}

testOAuthEndpoints();
