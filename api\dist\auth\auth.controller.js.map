{"version": 3, "sources": ["../../src/auth/auth.controller.ts"], "sourcesContent": ["import { <PERSON>, <PERSON>, Body, HttpStatus, Res, Bind, UseGuards, Get, Param, Request, HttpException } from '@nestjs/common';\r\nimport { AuthService } from './auth.service';\r\nimport { CreateUserDto } from './create-user.dto';\r\nimport { JwtAuthGuard } from './jwt-auth.guard';\r\nimport { AuthGuard } from '@nestjs/passport';\r\n\r\n@Controller('auth')\r\nexport class AuthController {\r\n  constructor(private readonly authService: AuthService) {}\r\n\r\n  // login\r\n  @Post('login')\r\n  async login(@Body() user: any): Promise<any> {\r\n      return this.authService.login(user);\r\n  }\r\n\r\n  // Rota POST /auth/register\r\n  @Post('register')\r\n  async register(@Body() createUserDto: CreateUserDto): Promise<any> {\r\n      return this.authService.register(createUserDto);\r\n  }\r\n\r\n  // Refresh token\r\n  @Post('refresh')\r\n  async refreshToken(@Body() { refresh_token, device_uid }: { refresh_token: string, device_uid: string }): Promise<any> {\r\n      if (!refresh_token) {\r\n          throw new Error('Refresh token is required');\r\n      }\r\n\r\n      if (!device_uid) {\r\n          throw new Error('Device UID is required');\r\n      }\r\n\r\n      return this.authService.refreshToken(refresh_token, device_uid);\r\n  }\r\n\r\n  // Account recovery\r\n  @Post('recover-account')\r\n  async recoverAccount(@Body() { userId }: { userId: number }): Promise<any> {\r\n      if (!userId) {\r\n          throw new Error('User ID is required');\r\n      }\r\n\r\n      return this.authService.recoverAccount(userId);\r\n  }\r\n\r\n  @UseGuards(JwtAuthGuard)\r\n  @Get('role/:role')\r\n  getRole(@Param('role') role: string, @Request() req: any) {\r\n    const userId: number = req.user.userId;\r\n      return this.authService.getRole(userId, role);\r\n  }\r\n\r\n  @Post('request-password-reset')\r\n  async requestPasswordReset(@Body() body: { email: string }) {\r\n    return this.authService.requestPasswordReset(body.email);\r\n  }\r\n\r\n  @Post('reset-password')\r\n  async resetPassword(@Body() body: { token: string; password: string }) {\r\n    return this.authService.resetPassword(body.token, body.password);\r\n  }\r\n\r\n  @Post('verify-email')\r\n  async verifyEmail(@Body() body: { token: string }) {\r\n    return this.authService.verifyEmail(body.token);\r\n  }\r\n\r\n  @Post('resend-verification')\r\n  async resendVerification(@Body() body: { email: string }) {\r\n    return this.authService.resendVerification(body.email);\r\n  }\r\n\r\n  @Get('sessions')\r\n  @UseGuards(JwtAuthGuard)\r\n  async getSessions(@Request() req: any) {\r\n    const userId = req.user.userId;\r\n    return this.authService.getUserSessions(userId);\r\n  }\r\n\r\n  @Post('sessions/:id/terminate')\r\n  @UseGuards(JwtAuthGuard)\r\n  async terminateSession(@Param('id') sessionId: string, @Request() req: any) {\r\n    const userId = req.user.userId;\r\n    return this.authService.terminateSession(sessionId, userId);\r\n  }\r\n\r\n  @Post('logout-all')\r\n  @UseGuards(JwtAuthGuard)\r\n  async logoutAllSessions(@Request() req: any) {\r\n    const userId = req.user.userId;\r\n    return this.authService.logoutAllSessions(userId);\r\n  }\r\n\r\n  // Google OAuth Routes\r\n  @Get('google')\r\n  @UseGuards(AuthGuard('google'))\r\n  async googleAuth(@Request() req: any) {\r\n    // This route initiates the Google OAuth flow\r\n  }\r\n\r\n  @Get('google/callback')\r\n  @UseGuards(AuthGuard('google'))\r\n  async googleAuthRedirect(@Request() req: any, @Res() res: any) {\r\n    try {\r\n      const result = await this.authService.loginWithOAuth(req.user, 'google');\r\n\r\n      // Redirect to frontend with tokens\r\n      const redirectUrl = `${process.env.GOOGLE_REDIRECT_URL}?access_token=${result.data.access_token}&refresh_token=${result.data.refresh_token}&device_uid=${result.data.device_uid}`;\r\n      return res.redirect(redirectUrl);\r\n    } catch (error) {\r\n      console.error('Google OAuth error:', error);\r\n      return res.redirect(`${process.env.GOOGLE_REDIRECT_URL}?error=oauth_failed`);\r\n    }\r\n  }\r\n\r\n  // Apple OAuth Routes\r\n  @Get('apple')\r\n  @UseGuards(AuthGuard('apple'))\r\n  async appleAuth(@Request() req: any) {\r\n    // This route initiates the Apple OAuth flow\r\n  }\r\n\r\n  @Post('apple/callback')\r\n  @UseGuards(AuthGuard('apple'))\r\n  async appleAuthRedirect(@Request() req: any, @Res() res: any) {\r\n    try {\r\n      const result = await this.authService.loginWithOAuth(req.user, 'apple');\r\n\r\n      // Redirect to frontend with tokens\r\n      const redirectUrl = `${process.env.APPLE_REDIRECT_URL}?access_token=${result.data.access_token}&refresh_token=${result.data.refresh_token}&device_uid=${result.data.device_uid}`;\r\n      return res.redirect(redirectUrl);\r\n    } catch (error) {\r\n      console.error('Apple OAuth error:', error);\r\n      return res.redirect(`${process.env.APPLE_REDIRECT_URL}?error=oauth_failed`);\r\n    }\r\n  }\r\n\r\n  // Frontend-initiated OAuth endpoints\r\n  @Post('oauth/google')\r\n  async googleOAuthToken(@Body() body: { token: string, userInfo: any }) {\r\n    try {\r\n      // Validate Google token and extract user info\r\n      const result = await this.authService.validateGoogleToken(body.token, body.userInfo);\r\n      return result;\r\n    } catch (error) {\r\n      console.error('Google OAuth token validation error:', error);\r\n      throw new HttpException('Invalid Google token', HttpStatus.UNAUTHORIZED);\r\n    }\r\n  }\r\n\r\n  @Post('oauth/apple')\r\n  async appleOAuthToken(@Body() body: { token: string, userInfo: any }) {\r\n    try {\r\n      // Validate Apple token and extract user info\r\n      const result = await this.authService.validateAppleToken(body.token, body.userInfo);\r\n      return result;\r\n    } catch (error) {\r\n      console.error('Apple OAuth token validation error:', error);\r\n      throw new HttpException('Invalid Apple token', HttpStatus.UNAUTHORIZED);\r\n    }\r\n  }\r\n}"], "names": ["AuthController", "login", "user", "authService", "register", "createUserDto", "refreshToken", "refresh_token", "device_uid", "Error", "recoverAccount", "userId", "getRole", "role", "req", "requestPasswordReset", "body", "email", "resetPassword", "token", "password", "verifyEmail", "resendVerification", "getSessions", "getUserSessions", "terminateSession", "sessionId", "logoutAllSessions", "googleAuth", "googleAuthRedirect", "res", "result", "loginWithOAuth", "redirectUrl", "process", "env", "GOOGLE_REDIRECT_URL", "data", "access_token", "redirect", "error", "console", "appleAuth", "appleAuthRedirect", "APPLE_REDIRECT_URL", "googleOAuthToken", "validateGoogleToken", "userInfo", "HttpException", "HttpStatus", "UNAUTHORIZED", "appleOAuthToken", "validateAppleToken", "constructor"], "mappings": ";;;;+BAOaA;;;eAAAA;;;wBAPgG;6BACjF;+BACE;8BACD;0BACH;;;;;;;;;;;;;;;AAGnB,IAAA,AAAMA,iBAAN,MAAMA;IAGX,QAAQ;IACR,MACMC,MAAM,AAAQC,IAAS,EAAgB;QACzC,OAAO,IAAI,CAACC,WAAW,CAACF,KAAK,CAACC;IAClC;IAEA,2BAA2B;IAC3B,MACME,SAAS,AAAQC,aAA4B,EAAgB;QAC/D,OAAO,IAAI,CAACF,WAAW,CAACC,QAAQ,CAACC;IACrC;IAEA,gBAAgB;IAChB,MACMC,aAAa,AAAQ,EAAEC,aAAa,EAAEC,UAAU,EAAiD,EAAgB;QACnH,IAAI,CAACD,eAAe;YAChB,MAAM,IAAIE,MAAM;QACpB;QAEA,IAAI,CAACD,YAAY;YACb,MAAM,IAAIC,MAAM;QACpB;QAEA,OAAO,IAAI,CAACN,WAAW,CAACG,YAAY,CAACC,eAAeC;IACxD;IAEA,mBAAmB;IACnB,MACME,eAAe,AAAQ,EAAEC,MAAM,EAAsB,EAAgB;QACvE,IAAI,CAACA,QAAQ;YACT,MAAM,IAAIF,MAAM;QACpB;QAEA,OAAO,IAAI,CAACN,WAAW,CAACO,cAAc,CAACC;IAC3C;IAIAC,QAAQ,AAAeC,IAAY,EAAE,AAAWC,GAAQ,EAAE;QACxD,MAAMH,SAAiBG,IAAIZ,IAAI,CAACS,MAAM;QACpC,OAAO,IAAI,CAACR,WAAW,CAACS,OAAO,CAACD,QAAQE;IAC5C;IAEA,MACME,qBAAqB,AAAQC,IAAuB,EAAE;QAC1D,OAAO,IAAI,CAACb,WAAW,CAACY,oBAAoB,CAACC,KAAKC,KAAK;IACzD;IAEA,MACMC,cAAc,AAAQF,IAAyC,EAAE;QACrE,OAAO,IAAI,CAACb,WAAW,CAACe,aAAa,CAACF,KAAKG,KAAK,EAAEH,KAAKI,QAAQ;IACjE;IAEA,MACMC,YAAY,AAAQL,IAAuB,EAAE;QACjD,OAAO,IAAI,CAACb,WAAW,CAACkB,WAAW,CAACL,KAAKG,KAAK;IAChD;IAEA,MACMG,mBAAmB,AAAQN,IAAuB,EAAE;QACxD,OAAO,IAAI,CAACb,WAAW,CAACmB,kBAAkB,CAACN,KAAKC,KAAK;IACvD;IAEA,MAEMM,YAAY,AAAWT,GAAQ,EAAE;QACrC,MAAMH,SAASG,IAAIZ,IAAI,CAACS,MAAM;QAC9B,OAAO,IAAI,CAACR,WAAW,CAACqB,eAAe,CAACb;IAC1C;IAEA,MAEMc,iBAAiB,AAAaC,SAAiB,EAAE,AAAWZ,GAAQ,EAAE;QAC1E,MAAMH,SAASG,IAAIZ,IAAI,CAACS,MAAM;QAC9B,OAAO,IAAI,CAACR,WAAW,CAACsB,gBAAgB,CAACC,WAAWf;IACtD;IAEA,MAEMgB,kBAAkB,AAAWb,GAAQ,EAAE;QAC3C,MAAMH,SAASG,IAAIZ,IAAI,CAACS,MAAM;QAC9B,OAAO,IAAI,CAACR,WAAW,CAACwB,iBAAiB,CAAChB;IAC5C;IAEA,sBAAsB;IACtB,MAEMiB,WAAW,AAAWd,GAAQ,EAAE;IACpC,6CAA6C;IAC/C;IAEA,MAEMe,mBAAmB,AAAWf,GAAQ,EAAE,AAAOgB,GAAQ,EAAE;QAC7D,IAAI;YACF,MAAMC,SAAS,MAAM,IAAI,CAAC5B,WAAW,CAAC6B,cAAc,CAAClB,IAAIZ,IAAI,EAAE;YAE/D,mCAAmC;YACnC,MAAM+B,cAAc,GAAGC,QAAQC,GAAG,CAACC,mBAAmB,CAAC,cAAc,EAAEL,OAAOM,IAAI,CAACC,YAAY,CAAC,eAAe,EAAEP,OAAOM,IAAI,CAAC9B,aAAa,CAAC,YAAY,EAAEwB,OAAOM,IAAI,CAAC7B,UAAU,EAAE;YACjL,OAAOsB,IAAIS,QAAQ,CAACN;QACtB,EAAE,OAAOO,OAAO;YACdC,QAAQD,KAAK,CAAC,uBAAuBA;YACrC,OAAOV,IAAIS,QAAQ,CAAC,GAAGL,QAAQC,GAAG,CAACC,mBAAmB,CAAC,mBAAmB,CAAC;QAC7E;IACF;IAEA,qBAAqB;IACrB,MAEMM,UAAU,AAAW5B,GAAQ,EAAE;IACnC,4CAA4C;IAC9C;IAEA,MAEM6B,kBAAkB,AAAW7B,GAAQ,EAAE,AAAOgB,GAAQ,EAAE;QAC5D,IAAI;YACF,MAAMC,SAAS,MAAM,IAAI,CAAC5B,WAAW,CAAC6B,cAAc,CAAClB,IAAIZ,IAAI,EAAE;YAE/D,mCAAmC;YACnC,MAAM+B,cAAc,GAAGC,QAAQC,GAAG,CAACS,kBAAkB,CAAC,cAAc,EAAEb,OAAOM,IAAI,CAACC,YAAY,CAAC,eAAe,EAAEP,OAAOM,IAAI,CAAC9B,aAAa,CAAC,YAAY,EAAEwB,OAAOM,IAAI,CAAC7B,UAAU,EAAE;YAChL,OAAOsB,IAAIS,QAAQ,CAACN;QACtB,EAAE,OAAOO,OAAO;YACdC,QAAQD,KAAK,CAAC,sBAAsBA;YACpC,OAAOV,IAAIS,QAAQ,CAAC,GAAGL,QAAQC,GAAG,CAACS,kBAAkB,CAAC,mBAAmB,CAAC;QAC5E;IACF;IAEA,qCAAqC;IACrC,MACMC,iBAAiB,AAAQ7B,IAAsC,EAAE;QACrE,IAAI;YACF,8CAA8C;YAC9C,MAAMe,SAAS,MAAM,IAAI,CAAC5B,WAAW,CAAC2C,mBAAmB,CAAC9B,KAAKG,KAAK,EAAEH,KAAK+B,QAAQ;YACnF,OAAOhB;QACT,EAAE,OAAOS,OAAO;YACdC,QAAQD,KAAK,CAAC,wCAAwCA;YACtD,MAAM,IAAIQ,qBAAa,CAAC,wBAAwBC,kBAAU,CAACC,YAAY;QACzE;IACF;IAEA,MACMC,gBAAgB,AAAQnC,IAAsC,EAAE;QACpE,IAAI;YACF,6CAA6C;YAC7C,MAAMe,SAAS,MAAM,IAAI,CAAC5B,WAAW,CAACiD,kBAAkB,CAACpC,KAAKG,KAAK,EAAEH,KAAK+B,QAAQ;YAClF,OAAOhB;QACT,EAAE,OAAOS,OAAO;YACdC,QAAQD,KAAK,CAAC,uCAAuCA;YACrD,MAAM,IAAIQ,qBAAa,CAAC,uBAAuBC,kBAAU,CAACC,YAAY;QACxE;IACF;IAzJAG,YAAY,AAAiBlD,WAAwB,CAAE;aAA1BA,cAAAA;IAA2B;AA0J1D"}