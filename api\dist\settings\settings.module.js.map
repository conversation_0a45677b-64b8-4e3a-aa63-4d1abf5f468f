{"version": 3, "sources": ["../../src/settings/settings.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { SettingsController } from './settings.controller';\nimport { SettingsService } from './settings.service';\n\n@Module({\n  controllers: [SettingsController],\n  providers: [SettingsService],\n  exports: [SettingsService],\n})\nexport class SettingsModule {}\n"], "names": ["SettingsModule", "controllers", "SettingsController", "providers", "SettingsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;oCACY;iCACH;;;;;;;AAOzB,IAAA,AAAMA,iBAAN,MAAMA;AAAgB;;;QAJ3BC,aAAa;YAACC,sCAAkB;SAAC;QACjCC,WAAW;YAACC,gCAAe;SAAC;QAC5BC,SAAS;YAACD,gCAAe;SAAC"}