{"version": 3, "sources": ["../../src/auth/jwt.strategy.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { PassportStrategy } from '@nestjs/passport';\r\nimport { ExtractJwt, Strategy } from 'passport-jwt';\r\nimport { config } from 'dotenv';\r\n\r\nconfig();\r\n\r\n@Injectable()\r\nexport class JwtStrategy extends PassportStrategy(Strategy) {\r\n  constructor() {\r\n    super({\r\n      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),\r\n      ignoreExpiration: false,\r\n      secretOrKey: process.env.JWT_SECRET,\r\n    });\r\n  }\r\n\r\n  async validate(payload: any) {\r\n    return { userId: payload.userId };\r\n  }\r\n}"], "names": ["JwtStrategy", "config", "PassportStrategy", "Strategy", "validate", "payload", "userId", "constructor", "jwtFromRequest", "ExtractJwt", "fromAuthHeaderAsBearerToken", "ignoreExpiration", "secretOr<PERSON>ey", "process", "env", "JWT_SECRET"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARc;0BACM;6BACI;wBACd;;;;;;;;;;AAEvBC,IAAAA,cAAM;AAGC,IAAA,AAAMD,cAAN,MAAMA,oBAAoBE,IAAAA,0BAAgB,EAACC,qBAAQ;IASxD,MAAMC,SAASC,OAAY,EAAE;QAC3B,OAAO;YAAEC,QAAQD,QAAQC,MAAM;QAAC;IAClC;IAVAC,aAAc;QACZ,KAAK,CAAC;YACJC,gBAAgBC,uBAAU,CAACC,2BAA2B;YACtDC,kBAAkB;YAClBC,aAAaC,QAAQC,GAAG,CAACC,UAAU;QACrC;IACF;AAKF"}