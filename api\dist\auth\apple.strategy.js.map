{"version": 3, "sources": ["../../src/auth/apple.strategy.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { PassportStrategy } from '@nestjs/passport';\nimport { Strategy } from '@nicokaiser/passport-apple';\nimport { config } from 'dotenv';\n\nconfig();\n\n@Injectable()\nexport class AppleStrategy extends PassportStrategy(Strategy, 'apple') {\n  constructor() {\n    // Check if Apple OAuth is properly configured\n    if (!process.env.APPLE_CLIENT_ID || !process.env.APPLE_TEAM_ID || !process.env.APPLE_KEY_ID || !process.env.APPLE_PRIVATE_KEY) {\n      console.warn('Apple OAuth not properly configured. Some environment variables are missing.');\n      // Use dummy values to prevent strategy initialization errors\n      super({\n        clientID: 'dummy',\n        teamID: 'dummy',\n        keyID: 'dummy',\n        key: '-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\\n-----END PRIVATE KEY-----',\n        callbackURL: process.env.APPLE_CALLBACK_URL || 'http://localhost:3000/auth/apple/callback',\n        scope: ['email', 'name'],\n      });\n      return;\n    }\n\n    super({\n      clientID: process.env.APPLE_CLIENT_ID,\n      teamID: process.env.APPLE_TEAM_ID,\n      keyID: process.env.APPLE_KEY_ID,\n      key: process.env.APPLE_PRIVATE_KEY,\n      callbackURL: process.env.APPLE_CALLBACK_URL,\n      scope: ['email', 'name'],\n    });\n  }\n\n  async validate(\n    accessToken: string,\n    refreshToken: string,\n    idToken: any,\n    profile: any,\n  ): Promise<any> {\n    try {\n      // Apple provides user data differently than Google\n      const { sub, email, email_verified } = idToken;\n\n      // Extract user data from Apple profile\n      const user = {\n        appleId: sub,\n        email: email,\n        emailVerified: email_verified,\n        name: profile?.name ? `${profile.name.firstName || ''} ${profile.name.lastName || ''}`.trim() : null,\n        firstName: profile?.name?.firstName,\n        lastName: profile?.name?.lastName,\n        accessToken,\n        refreshToken,\n      };\n\n      // Validate that we have required data\n      if (!user.email) {\n        throw new Error('Email not provided by Apple');\n      }\n\n      return user;\n    } catch (error) {\n      throw error;\n    }\n  }\n}\n"], "names": ["AppleStrategy", "config", "PassportStrategy", "Strategy", "validate", "accessToken", "refreshToken", "idToken", "profile", "sub", "email", "email_verified", "user", "appleId", "emailVerified", "name", "firstName", "lastName", "trim", "Error", "error", "constructor", "process", "env", "APPLE_CLIENT_ID", "APPLE_TEAM_ID", "APPLE_KEY_ID", "APPLE_PRIVATE_KEY", "console", "warn", "clientID", "teamID", "keyID", "key", "callbackURL", "APPLE_CALLBACK_URL", "scope"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARc;0BACM;+BACR;wBACF;;;;;;;;;;AAEvBC,IAAAA,cAAM;AAGC,IAAA,AAAMD,gBAAN,MAAMA,sBAAsBE,IAAAA,0BAAgB,EAACC,uBAAQ,EAAE;IA2B5D,MAAMC,SACJC,WAAmB,EACnBC,YAAoB,EACpBC,OAAY,EACZC,OAAY,EACE;QACd,IAAI;YACF,mDAAmD;YACnD,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,cAAc,EAAE,GAAGJ;YAEvC,uCAAuC;YACvC,MAAMK,OAAO;gBACXC,SAASJ;gBACTC,OAAOA;gBACPI,eAAeH;gBACfI,MAAMP,SAASO,OAAO,GAAGP,QAAQO,IAAI,CAACC,SAAS,IAAI,GAAG,CAAC,EAAER,QAAQO,IAAI,CAACE,QAAQ,IAAI,IAAI,CAACC,IAAI,KAAK;gBAChGF,WAAWR,SAASO,MAAMC;gBAC1BC,UAAUT,SAASO,MAAME;gBACzBZ;gBACAC;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACM,KAAKF,KAAK,EAAE;gBACf,MAAM,IAAIS,MAAM;YAClB;YAEA,OAAOP;QACT,EAAE,OAAOQ,OAAO;YACd,MAAMA;QACR;IACF;IAzDAC,aAAc;QACZ,8CAA8C;QAC9C,IAAI,CAACC,QAAQC,GAAG,CAACC,eAAe,IAAI,CAACF,QAAQC,GAAG,CAACE,aAAa,IAAI,CAACH,QAAQC,GAAG,CAACG,YAAY,IAAI,CAACJ,QAAQC,GAAG,CAACI,iBAAiB,EAAE;YAC7HC,QAAQC,IAAI,CAAC;YACb,6DAA6D;YAC7D,KAAK,CAAC;gBACJC,UAAU;gBACVC,QAAQ;gBACRC,OAAO;gBACPC,KAAK;gBACLC,aAAaZ,QAAQC,GAAG,CAACY,kBAAkB,IAAI;gBAC/CC,OAAO;oBAAC;oBAAS;iBAAO;YAC1B;YACA;QACF;QAEA,KAAK,CAAC;YACJN,UAAUR,QAAQC,GAAG,CAACC,eAAe;YACrCO,QAAQT,QAAQC,GAAG,CAACE,aAAa;YACjCO,OAAOV,QAAQC,GAAG,CAACG,YAAY;YAC/BO,KAAKX,QAAQC,GAAG,CAACI,iBAAiB;YAClCO,aAAaZ,QAAQC,GAAG,CAACY,kBAAkB;YAC3CC,OAAO;gBAAC;gBAAS;aAAO;QAC1B;IACF;AAkCF"}