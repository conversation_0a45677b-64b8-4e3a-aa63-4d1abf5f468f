{"version": 3, "sources": ["../../src/dto/create-client.dto.ts"], "sourcesContent": ["import { IsString, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON>al, <PERSON>N<PERSON>ber, IsDate } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class CreateClientDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  email: string;\r\n\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  phone: string;\r\n\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  password: string;\r\n\r\n  @IsDate()\r\n  @Type(() => Date)\r\n  @IsOptional()\r\n  date_of_birth?: Date;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  photo?: string;\r\n\r\n  @IsString()\r\n  height: number;\r\n\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  weight: number;\r\n\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  goal_id: number;\r\n\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  activity_level_id: number;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  medical_conditions?: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  allergies?: string;\r\n}"], "names": ["CreateClientDto", "Date", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAHsD;kCAC9C;;;;;;;;;;AAEd,IAAA,AAAMA,kBAAN,MAAMA;AAgDb;;;;;;;;;;;;;;;;;;;;;;;oCA9BcC;;;;;;;;;;;;;;;oCAYAC;;;;;oCAIAA;;;;;oCAIAA"}