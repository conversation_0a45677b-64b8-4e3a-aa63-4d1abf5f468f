"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "DashboardController", {
    enumerable: true,
    get: function() {
        return DashboardController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _dashboardservice = require("./dashboard.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let DashboardController = class DashboardController {
    validateRequest(userId, period) {
        if (!userId) {
            throw new _common.HttpException('User ID not found', _common.HttpStatus.UNAUTHORIZED);
        }
        const validPeriods = [
            'week',
            'month',
            'semester',
            'year',
            'all'
        ];
        if (!validPeriods.includes(period)) {
            throw new _common.HttpException('Invalid period parameter', _common.HttpStatus.BAD_REQUEST);
        }
    }
    getStats() {
        return {
            totalUsers: 0,
            totalProfessionals: 0,
            activeSubscriptions: 0,
            monthlyRevenue: 0
        };
    }
    async getWorkoutData(req) {
        const userId = req.user.userId;
        return await this.dashboardService.getWorkoutDashboardData(userId);
    }
    async getProgressWeight(req, query) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getWeightProgress(userId, period);
        } catch (error) {
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Internal server error', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getProgressAnalytics(req, query) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getAnalyticsOverview(userId, period);
        } catch (error) {
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Internal server error', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getNutritionalSummary(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.dashboardService.getNutritionalSummary(userId, query);
    }
    getAISuggestions(req, query) {
        const userId = req.user.userId;
        // Mock AI suggestions
        const suggestions = [
            'Considere adicionar mais proteína na próxima refeição',
            'Você está próximo da sua meta de hidratação hoje!',
            'Que tal um treino de força hoje?',
            'Tente incluir mais vegetais na sua dieta',
            'Mantenha a consistência nos treinos'
        ];
        return {
            status: 'success',
            data: suggestions.slice(0, 3) // Return 3 random suggestions
        };
    }
    async getWorkoutAnalytics(req, query) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'week';
            this.validateRequest(userId, period);
            return await this.dashboardService.getWorkoutAnalytics(userId, period);
        } catch (error) {
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Internal server error', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getNutritionAnalytics(req, query) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'week';
            this.validateRequest(userId, period);
            return await this.dashboardService.getNutritionAnalytics(userId, period);
        } catch (error) {
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Internal server error', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getCaloricBalance(req, query) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'week';
            this.validateRequest(userId, period);
            return await this.dashboardService.getCaloricBalance(userId, period);
        } catch (error) {
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Internal server error', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getStrengthProgress(req, query) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getStrengthProgress(userId, period);
        } catch (error) {
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Internal server error', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getBodyComposition(req, query) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getBodyComposition(userId, period);
        } catch (error) {
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Internal server error', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    constructor(dashboardService){
        this.dashboardService = dashboardService;
    }
};
_ts_decorate([
    (0, _common.Get)('stats'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], DashboardController.prototype, "getStats", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('workout'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getWorkoutData", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/weight'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getProgressWeight", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/analytics'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getProgressAnalytics", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('nutritional-summary'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getNutritionalSummary", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('ai-suggestions'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], DashboardController.prototype, "getAISuggestions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('workout-analytics'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getWorkoutAnalytics", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('nutrition-analytics'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getNutritionAnalytics", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('caloric-balance'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getCaloricBalance", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('strength-progress'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getStrengthProgress", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('body-composition'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], DashboardController.prototype, "getBodyComposition", null);
DashboardController = _ts_decorate([
    (0, _common.Controller)('dashboard'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _dashboardservice.DashboardService === "undefined" ? Object : _dashboardservice.DashboardService
    ])
], DashboardController);

//# sourceMappingURL=dashboard.controller.js.map