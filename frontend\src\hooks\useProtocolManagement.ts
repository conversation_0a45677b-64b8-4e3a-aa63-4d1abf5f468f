import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { apiService } from '../services/api';
import { workoutQueryKeys } from './useWorkout';

// ============================================================================
// TYPES
// ============================================================================

interface Protocol {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'completed' | 'inactive';
  objective?: string;
  startDate?: string;
  endDate?: string;
  [key: string]: any;
}

interface FinalizeProtocolParams {
  protocolId: string;
  protocolType: 'workout' | 'diet';
}

interface ReuseProtocolParams {
  protocol: Protocol;
  protocolType: 'workout' | 'diet';
}

// ============================================================================
// FINALIZE PROTOCOL HOOK
// ============================================================================

export function useFinalizeProtocol() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ protocolId, protocolType }: FinalizeProtocolParams) => {
      console.log(`🏁 Finalizing ${protocolType} protocol:`, protocolId);
      
      const endpoint = protocolType === 'workout' 
        ? `users/protocols/workout/${protocolId}/finish`
        : `users/protocols/diet/${protocolId}/finish`;
      
      const response = await apiService.post(endpoint);
      return response.data;
    },
    onSuccess: (data, variables) => {
      const { protocolType } = variables;
      
      // Invalidate relevant queries
      if (protocolType === 'workout') {
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.protocols() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.activeProtocol() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.history() });
        queryClient.invalidateQueries({ queryKey: ['workout-stats'] });
        queryClient.invalidateQueries({ queryKey: ['workout-analytics'] });
      } else {
        queryClient.invalidateQueries({ queryKey: ['diet', 'protocols'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'active'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'history'] });
        queryClient.invalidateQueries({ queryKey: ['diet-stats'] });
        queryClient.invalidateQueries({ queryKey: ['diet-analytics'] });
      }

      // Invalidate unified protocol history and dashboard data
      queryClient.invalidateQueries({ queryKey: ['protocols', 'history', protocolType] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', protocolType] });
      
      toast.success('Protocolo finalizado com sucesso!', { position: 'bottom-right' });
    },
    onError: (error: any) => {
      console.error('Error finalizing protocol:', error);
      const message = error?.response?.data?.message || 'Erro ao finalizar protocolo';
      toast.error(message, { position: 'bottom-right' });
    },
  });
}

// ============================================================================
// REUSE PROTOCOL HOOK
// ============================================================================

export function useReuseProtocol() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ protocol, protocolType }: ReuseProtocolParams) => {
      console.log(`🔄 Reusing ${protocolType} protocol:`, protocol);

      // Use the duplication endpoint instead of creation endpoint
      // This handles active protocol conflicts automatically
      const endpoint = protocolType === 'workout'
        ? `users/protocols/workout/${protocol.id}/duplicate`
        : `users/protocols/diet/${protocol.id}/duplicate`;

      const payload = {
        startDate: new Date().toISOString().split('T')[0]
      };

      console.log(`🔄 Calling duplication endpoint: ${endpoint}`);
      const response = await apiService.post(endpoint, payload);
      return response.data;
    },
    onSuccess: (data, variables) => {
      const { protocolType } = variables;
      
      // Invalidate relevant queries
      if (protocolType === 'workout') {
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.protocols() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.activeProtocol() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.history() });
        queryClient.invalidateQueries({ queryKey: ['workout-stats'] });
        queryClient.invalidateQueries({ queryKey: ['workout-analytics'] });
      } else {
        queryClient.invalidateQueries({ queryKey: ['diet', 'protocols'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'active'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'history'] });
        queryClient.invalidateQueries({ queryKey: ['diet-stats'] });
        queryClient.invalidateQueries({ queryKey: ['diet-analytics'] });
      }

      // Invalidate unified protocol history and dashboard data
      queryClient.invalidateQueries({ queryKey: ['protocols', 'history', protocolType] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', protocolType] });
      
      toast.success('Protocolo reutilizado com sucesso!', { position: 'bottom-right' });
    },
    onError: (error: any) => {
      console.error('Error reusing protocol:', error);
      const message = error?.response?.data?.message || 'Erro ao reutilizar protocolo';
      toast.error(message, { position: 'bottom-right' });
    },
  });
}

// ============================================================================
// CHECK ACTIVE PROTOCOL HOOK
// ============================================================================

export function useCheckActiveProtocol() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (protocolType: 'workout' | 'diet') => {
      console.log(`🔍 Checking active ${protocolType} protocol`);

      const endpoint = protocolType === 'workout'
        ? 'users/protocols/workouts/active'
        : 'users/protocols/diets/active';

      try {
        const response = await apiService.get(endpoint);
        console.log(`🔍 Active protocol response:`, response);

        // Handle the API response structure
        if (response?.data?.status === 'success') {
          const protocolData = response.data.data;

          // If has_protocol is false, return null
          if (protocolData?.has_protocol === false) {
            console.log(`ℹ️ No active ${protocolType} protocol found`);
            return null;
          }

          // Return the actual protocol data
          return protocolData;
        }

        return response.data;
      } catch (error: any) {
        console.error(`❌ Error checking active ${protocolType} protocol:`, error);
        // If no active protocol found, return null
        if (error?.response?.status === 404) {
          return null;
        }
        throw error;
      }
    },
    onError: (error: any) => {
      console.error('Error checking active protocol:', error);
    },
  });
}

// ============================================================================
// CREATE PROTOCOL WITH ACTIVE CHECK HOOK
// ============================================================================

export function useCreateProtocolWithCheck() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      protocolData, 
      protocolType, 
      shouldFinalizeActive = false 
    }: { 
      protocolData: any; 
      protocolType: 'workout' | 'diet'; 
      shouldFinalizeActive?: boolean;
    }) => {
      console.log(`🆕 Creating ${protocolType} protocol with active check`);
      
      // If we should finalize the active protocol, do it first
      if (shouldFinalizeActive) {
        try {
          const activeEndpoint = protocolType === 'workout' 
            ? 'users/protocols/workouts/active'
            : 'users/protocols/diets/active';
          
          const activeResponse = await apiService.get(activeEndpoint);
          const activeProtocol = activeResponse.data;
          
          if (activeProtocol?.id) {
            const finalizeEndpoint = protocolType === 'workout' 
              ? `users/protocols/workout/${activeProtocol.id}/finish`
              : `users/protocols/diet/${activeProtocol.id}/finish`;
            
            await apiService.post(finalizeEndpoint);
            console.log(`✅ Active ${protocolType} protocol finalized`);
          }
        } catch (error) {
          console.log(`ℹ️ No active ${protocolType} protocol to finalize or error finalizing:`, error);
        }
      }
      
      // Create the new protocol
      const endpoint = protocolType === 'workout' 
        ? 'users/protocols/workout'
        : 'users/protocols/diet';
      
      const response = await apiService.post(endpoint, protocolData);
      return response.data;
    },
    onSuccess: (data, variables) => {
      const { protocolType } = variables;
      
      // Invalidate relevant queries
      if (protocolType === 'workout') {
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.protocols() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.activeProtocol() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.history() });
        queryClient.invalidateQueries({ queryKey: ['workout-stats'] });
        queryClient.invalidateQueries({ queryKey: ['workout-analytics'] });
      } else {
        queryClient.invalidateQueries({ queryKey: ['diet', 'protocols'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'active'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'history'] });
        queryClient.invalidateQueries({ queryKey: ['diet-stats'] });
        queryClient.invalidateQueries({ queryKey: ['diet-analytics'] });
      }

      // Invalidate unified protocol history and dashboard data
      queryClient.invalidateQueries({ queryKey: ['protocols', 'history', protocolType] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', protocolType] });
      
      toast.success('Protocolo criado com sucesso!', { position: 'bottom-right' });
    },
    onError: (error: any) => {
      console.error('Error creating protocol:', error);
      const message = error?.response?.data?.message || 'Erro ao criar protocolo';
      toast.error(message, { position: 'bottom-right' });
    },
  });
}

// ============================================================================
// REUSE PROTOCOL WITH ACTIVE CHECK HOOK
// ============================================================================

export function useReuseProtocolWithCheck() {
  const queryClient = useQueryClient();
  const checkActiveProtocol = useCheckActiveProtocol();
  const finalizeProtocol = useFinalizeProtocol();
  const reuseProtocol = useReuseProtocol();

  return useMutation({
    mutationFn: async ({ protocol, protocolType, shouldFinalizeActive = false }: {
      protocol: Protocol;
      protocolType: 'workout' | 'diet';
      shouldFinalizeActive?: boolean;
    }) => {
      console.log(`🔄 Reusing ${protocolType} protocol with active check:`, protocol);

      // If we should finalize the active protocol, do it first
      if (shouldFinalizeActive) {
        try {
          const activeProtocol = await checkActiveProtocol.mutateAsync(protocolType);

          if (activeProtocol?.id) {
            console.log(`🏁 Finalizing active ${protocolType} protocol before reuse`);
            await finalizeProtocol.mutateAsync({
              protocolId: activeProtocol.id.toString(),
              protocolType
            });
            console.log(`✅ Active ${protocolType} protocol finalized`);
          }
        } catch (error) {
          console.log(`ℹ️ No active ${protocolType} protocol to finalize or error finalizing:`, error);
        }
      }

      // Now reuse the protocol
      return await reuseProtocol.mutateAsync({ protocol, protocolType });
    },
    onSuccess: (data, variables) => {
      const { protocolType } = variables;

      // Invalidate relevant queries
      if (protocolType === 'workout') {
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.protocols() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.activeProtocol() });
        queryClient.invalidateQueries({ queryKey: workoutQueryKeys.history() });
        queryClient.invalidateQueries({ queryKey: ['workout-stats'] });
        queryClient.invalidateQueries({ queryKey: ['workout-analytics'] });
      } else {
        queryClient.invalidateQueries({ queryKey: ['diet', 'protocols'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'active'] });
        queryClient.invalidateQueries({ queryKey: ['diet', 'history'] });
        queryClient.invalidateQueries({ queryKey: ['diet-stats'] });
        queryClient.invalidateQueries({ queryKey: ['diet-analytics'] });
      }

      // Invalidate unified protocol history and dashboard data
      queryClient.invalidateQueries({ queryKey: ['protocols', 'history', protocolType] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', protocolType] });

      toast.success('Protocolo reutilizado com sucesso!', { position: 'bottom-right' });
    },
    onError: (error: any) => {
      console.error('Error reusing protocol with check:', error);
      const message = error?.response?.data?.message || 'Erro ao reutilizar protocolo';
      toast.error(message, { position: 'bottom-right' });
    },
  });
}

// ============================================================================
// EXPORT ALL HOOKS
// ============================================================================

export {
  type Protocol,
  type FinalizeProtocolParams,
  type ReuseProtocolParams,
};
