{"version": 3, "sources": ["../../src/admin/affiliates.service.ts"], "sourcesContent": ["import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';\r\nimport { db } from '../database';\r\n// import * as dayjs from 'dayjs';\r\nconst dayjs = require('dayjs');\r\nimport { sql } from 'kysely';\r\nimport { Stripe } from 'stripe';\r\n\r\n@Injectable()\r\nexport class AffiliatesService {\r\n    private stripe: Stripe;\r\n\r\n    constructor() {\r\n    // @ts-ignore\r\n    this.stripe = new Stripe(process.env.STRIPE_SK, {\r\n      // apiVersion: '2025-04-30',\r\n    });\r\n    }\r\n\r\n    async getAffiliateById(id: number) {\r\n        const affiliate = await db.selectFrom('affiliates')\r\n            .leftJoin('users', 'users.id', 'affiliates.user_id')\r\n            .select(['users.name', 'users.email', 'affiliates.is_master'])\r\n            .where('user_id', '=', id)\r\n            .where('affiliates.deleted_at', 'is', null)\r\n            .where('affiliates.status', '=', 'active')\r\n            .executeTakeFirst();\r\n\r\n        if (!affiliate) {\r\n            throw new NotFoundException('Affiliate not found');\r\n        }\r\n\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                name: affiliate.name,\r\n                email: affiliate.email,\r\n                is_master: affiliate.is_master,\r\n            },\r\n        };\r\n    }\r\n\r\n\r\n  async findAccountByAffiliateId(affiliateId: string): Promise<Stripe.Account | null> {\r\n    const affiliate = await db.selectFrom('affiliates')\r\n    .where('user_id', '=', Number(affiliateId))\r\n    .select(['stripeId'])\r\n    .executeTakeFirst();\r\n\r\n    if (!affiliate) {\r\n      throw new NotFoundException('Affiliate not found');\r\n    }\r\n\r\n    if (!affiliate.stripeId) {\r\n      return null;\r\n    }\r\n\r\n    const account = await this.stripe.accounts.retrieve(affiliate.stripeId);\r\n\r\n    return account;\r\n  }\r\n\r\n  async checkPaymentEligibilityByAffiliateId(affiliateId: string): Promise<any> {\r\n    const account = await this.findAccountByAffiliateId(affiliateId);\r\n    if (!account) {\r\n      return null;\r\n    }\r\n\r\n    const canReceivePayments =\r\n      account.capabilities?.card_payments === 'active' &&\r\n      account.capabilities?.transfers === 'active';\r\n    const detailsSubmitted = account.details_submitted;\r\n\r\n    return {\r\n      status: 'success',\r\n      data: {\r\n      onboarding: canReceivePayments\r\n        ? 'ready'\r\n        : detailsSubmitted\r\n          ? 'pending'\r\n          : 'onboarding'\r\n      },\r\n    };\r\n  }\r\n\r\n  async startOnboarding(affiliateId: string): Promise<any> {\r\n    const account = await this.findAccountByAffiliateId(affiliateId);\r\n    if (!account) {\r\n      return null;\r\n    }\r\n\r\n    const accountLink = await this.stripe.accountLinks.create({\r\n      account: account.id,\r\n      refresh_url: process.env.STRIPE_REFRESH_URL,\r\n      return_url: process.env.STRIPE_RETURN_URL,\r\n      type: 'account_onboarding',\r\n    });\r\n\r\n    return {\r\n      status: 'success',\r\n      data: {\r\n        accountLink: accountLink.url,\r\n      },\r\n    };\r\n  }\r\n\r\n  async getLinks(userId: number) {\r\n    const links = await db.selectFrom('affiliate_links')\r\n    .where('user_id', '=', userId)\r\n    .where('deleted_at', 'is', null)\r\n    .select(['id', 'name', 'invite', 'link_type', 'created_at', 'updated_at'])\r\n    .execute();\r\n\r\n    return {\r\n      status: 'success',\r\n      data: links,\r\n    };\r\n  }\r\n\r\n\r\n  async createLink(userId: number, body: any) {\r\n    const { name, invite, link_type } = body;\r\n\r\n    const existingLink = await db.selectFrom('affiliate_links')\r\n    .where('invite', '=', invite)\r\n    .select('id')\r\n    .executeTakeFirst();\r\n\r\n    if (existingLink) {\r\n      throw new ConflictException('Esse cupom já está em uso');\r\n    }\r\n\r\n    const newLink = await db.insertInto('affiliate_links')\r\n    .values({\r\n      user_id: userId,\r\n      name,\r\n      invite,\r\n      link_type,\r\n      created_at: new Date(),\r\n      updated_at: new Date(),\r\n    })\r\n    .executeTakeFirstOrThrow();\r\n\r\n    return {\r\n      status: 'success',\r\n      data: [],\r\n    };\r\n  }\r\n\r\n  async registerLinkVisit(id: string) {\r\n    const link = await db.selectFrom('affiliate_links')\r\n    .where('invite', '=', id)\r\n    .select(['id', 'user_id', 'link_type'])\r\n    .executeTakeFirst();\r\n\r\n    if (!link) {\r\n      throw new NotFoundException('Link not found');\r\n    }\r\n\r\n    const newVisit = await db.insertInto('affiliate_links_visits')\r\n    .values({\r\n      link_id: link.id,\r\n      created_at: new Date(),\r\n    })\r\n    .executeTakeFirstOrThrow();\r\n\r\n    return {\r\n      status: 'success',\r\n      data: [],\r\n    };\r\n  }\r\n\r\n  async getDashboardStats(userId: number) {\r\n    try {\r\n      console.log(`Buscando estatísticas do dashboard para o afiliado ${userId}`);\r\n\r\n      // Verificar se o usuário é um afiliado ativo\r\n      const affiliate = await db.selectFrom('affiliates')\r\n        .where('user_id', '=', userId)\r\n        .where('status', '=', 'active')\r\n        .where('deleted_at', 'is', null)\r\n        .select(['id', 'user_id', 'invite'])\r\n        .executeTakeFirst();\r\n\r\n      if (!affiliate) {\r\n        throw new NotFoundException('Afiliado não encontrado ou não está ativo');\r\n      }\r\n\r\n      console.log(`Afiliado encontrado: ${JSON.stringify(affiliate)}`);\r\n\r\n      // 1. Total de Cliques - buscar visitas aos links do afiliado\r\n      const totalClicks = await db\r\n        .selectFrom('affiliate_links_visits as alv')\r\n        .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')\r\n        .where('al.user_id', '=', userId)\r\n        .where('al.deleted_at', 'is', null)\r\n        .select(sql<number>`COUNT(*)`.as('total'))\r\n        .executeTakeFirst();\r\n\r\n      console.log(`Total de cliques: ${totalClicks?.total || 0}`);\r\n\r\n      // 2. Ganhos Totais - buscar comissões do afiliado\r\n      const totalEarnings = await db\r\n        .selectFrom('affiliate_commissions')\r\n        .where('aff_user_id', '=', userId)\r\n        .select([\r\n          sql<number>`SUM(commission_value)`.as('total_earnings'),\r\n          sql<number>`COUNT(*)`.as('total_commissions')\r\n        ])\r\n        .executeTakeFirst();\r\n\r\n      console.log(`Ganhos totais: ${totalEarnings?.total_earnings || 0}, Total de comissões: ${totalEarnings?.total_commissions || 0}`);\r\n\r\n      // 3. Conversões - total de usuários que têm este afiliado como referência\r\n      const conversions = await db\r\n        .selectFrom('users')\r\n        .where('aff_id', '=', userId)\r\n        .select(sql<number>`COUNT(*)`.as('total'))\r\n        .executeTakeFirst();\r\n\r\n      console.log(`Total de conversões: ${conversions?.total || 0}`);\r\n\r\n      // 4. Comissões por status\r\n      const commissionsByStatus = await db\r\n        .selectFrom('affiliate_commissions')\r\n        .where('aff_user_id', '=', userId)\r\n        .select([\r\n          'status',\r\n          sql<number>`COUNT(*)`.as('count'),\r\n          sql<number>`SUM(commission_value)`.as('total_value')\r\n        ])\r\n        .groupBy('status')\r\n        .execute();\r\n\r\n      console.log(`Comissões por status: ${JSON.stringify(commissionsByStatus)}`);\r\n\r\n      // 5. Estatísticas dos últimos 30 dias\r\n      const thirtyDaysAgo = dayjs().subtract(30, 'day').toDate();\r\n\r\n      const recentStats = await db\r\n        .selectFrom('affiliate_commissions')\r\n        .where('aff_user_id', '=', userId)\r\n        .where('created_at', '>=', thirtyDaysAgo)\r\n        .select([\r\n          sql<number>`COUNT(*)`.as('recent_commissions'),\r\n          sql<number>`SUM(commission_value)`.as('recent_earnings')\r\n        ])\r\n        .executeTakeFirst();\r\n\r\n      console.log(`Estatísticas dos últimos 30 dias: ${JSON.stringify(recentStats)}`);\r\n\r\n      // 6. Conversões dos últimos 30 dias\r\n      const recentConversions = await db\r\n        .selectFrom('users')\r\n        .where('aff_id', '=', userId)\r\n        .where('created_at', '>=', thirtyDaysAgo)\r\n        .select(sql<number>`COUNT(*)`.as('total'))\r\n        .executeTakeFirst();\r\n\r\n      console.log(`Conversões dos últimos 30 dias: ${recentConversions?.total || 0}`);\r\n\r\n      // 7. Cliques dos últimos 30 dias\r\n      const recentClicks = await db\r\n        .selectFrom('affiliate_links_visits as alv')\r\n        .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')\r\n        .where('al.user_id', '=', userId)\r\n        .where('al.deleted_at', 'is', null)\r\n        .where('alv.created_at', '>=', thirtyDaysAgo)\r\n        .select(sql<number>`COUNT(*)`.as('total'))\r\n        .executeTakeFirst();\r\n\r\n      console.log(`Cliques dos últimos 30 dias: ${recentClicks?.total || 0}`);\r\n\r\n      // 8. Último Pagamento - buscar a comissão mais recente com status 'paid'\r\n      const lastPayment = await db\r\n        .selectFrom('affiliate_commissions')\r\n        .where('aff_user_id', '=', userId)\r\n        .where('status', '=', 'paid')\r\n        .select([\r\n          'commission_value',\r\n          'created_at',\r\n          'commission_percent',\r\n          'metadata'\r\n        ])\r\n        .orderBy('created_at', 'desc')\r\n        .executeTakeFirst();\r\n\r\n      console.log(`Último pagamento: ${JSON.stringify(lastPayment)}`);\r\n\r\n      // Processar informações do último pagamento\r\n      let lastPaymentInfo: any = null;\r\n      if (lastPayment) {\r\n        let metadata: any = null;\r\n        try {\r\n          metadata = lastPayment.metadata ? JSON.parse(lastPayment.metadata as string) : null;\r\n        } catch (error) {\r\n          console.error('Erro ao processar metadata do último pagamento:', error);\r\n        }\r\n\r\n        lastPaymentInfo = {\r\n          value: Number(lastPayment.commission_value || 0),\r\n          date: lastPayment.created_at,\r\n          percent: Number(lastPayment.commission_percent || 0),\r\n          transaction_amount: metadata?.transaction_amount || null,\r\n          currency: metadata?.currency || 'BRL',\r\n          stripe_transfer_id: metadata?.stripe_transfer_id || null\r\n        };\r\n      }\r\n\r\n      const dashboardData = {\r\n        // Estatísticas principais\r\n        total_clicks: Number(totalClicks?.total || 0),\r\n        total_earnings: Number(totalEarnings?.total_earnings || 0),\r\n        total_conversions: Number(conversions?.total || 0),\r\n        total_commissions: Number(totalEarnings?.total_commissions || 0),\r\n\r\n        // Estatísticas dos últimos 30 dias\r\n        recent_stats: {\r\n          clicks: Number(recentClicks?.total || 0),\r\n          earnings: Number(recentStats?.recent_earnings || 0),\r\n          conversions: Number(recentConversions?.total || 0),\r\n          commissions: Number(recentStats?.recent_commissions || 0)\r\n        },\r\n\r\n        // Comissões por status\r\n        commissions_by_status: commissionsByStatus.map(item => ({\r\n          status: item.status,\r\n          count: Number(item.count),\r\n          total_value: Number(item.total_value || 0)\r\n        })),\r\n\r\n        // Taxa de conversão\r\n        conversion_rate: Number(totalClicks?.total || 0) > 0\r\n          ? ((Number(conversions?.total || 0) / Number(totalClicks?.total || 0)) * 100).toFixed(2)\r\n          : '0.00',\r\n\r\n        // Valor médio por comissão\r\n        average_commission: Number(totalEarnings?.total_commissions || 0) > 0\r\n          ? (Number(totalEarnings?.total_earnings || 0) / Number(totalEarnings?.total_commissions || 0)).toFixed(2)\r\n          : '0.00',\r\n\r\n        // Último pagamento\r\n        last_payment: lastPaymentInfo\r\n      };\r\n\r\n      console.log(`Dashboard data: ${JSON.stringify(dashboardData)}`);\r\n\r\n      return {\r\n        status: 'success',\r\n        data: dashboardData\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('Erro ao buscar estatísticas do dashboard:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getDetailedStats(userId: number, startDate?: string, endDate?: string) {\r\n    try {\r\n      console.log(`Buscando estatísticas detalhadas para o afiliado ${userId}, período: ${startDate} - ${endDate}`);\r\n\r\n      // Verificar se o usuário é um afiliado ativo\r\n      const affiliate = await db.selectFrom('affiliates')\r\n        .where('user_id', '=', userId)\r\n        .where('status', '=', 'active')\r\n        .where('deleted_at', 'is', null)\r\n        .select(['id', 'user_id', 'invite'])\r\n        .executeTakeFirst();\r\n\r\n      if (!affiliate) {\r\n        throw new NotFoundException('Afiliado não encontrado ou não está ativo');\r\n      }\r\n\r\n      // Definir período de consulta\r\n      let dateFilter: { start?: Date; end?: Date } = {};\r\n      if (startDate && endDate) {\r\n        const start = dayjs(startDate).startOf('day').toDate();\r\n        const end = dayjs(endDate).endOf('day').toDate();\r\n        dateFilter = {\r\n          start,\r\n          end\r\n        };\r\n      }\r\n\r\n      // Buscar comissões detalhadas\r\n      let commissionsQuery = db\r\n        .selectFrom('affiliate_commissions as ac')\r\n        .leftJoin('users as u', 'ac.user_id', 'u.id')\r\n        .leftJoin('plans as p', 'ac.plan_id', 'p.id')\r\n        .leftJoin('transactions as t', 'ac.transaction_id', 't.id')\r\n        .where('ac.aff_user_id', '=', userId)\r\n        .select([\r\n          'ac.id',\r\n          'ac.commission_value',\r\n          'ac.commission_percent',\r\n          'ac.status',\r\n          'ac.created_at',\r\n          'u.name as user_name',\r\n          'u.email as user_email',\r\n          'p.name as plan_name',\r\n          't.amount as transaction_amount',\r\n          't.currency as transaction_currency'\r\n        ])\r\n        .orderBy('ac.created_at', 'desc');\r\n\r\n      if (dateFilter.start && dateFilter.end) {\r\n        commissionsQuery = commissionsQuery\r\n          .where('ac.created_at', '>=', dateFilter.start)\r\n          .where('ac.created_at', '<=', dateFilter.end);\r\n      }\r\n\r\n      const commissions = await commissionsQuery.execute();\r\n\r\n      // Buscar conversões detalhadas\r\n      let conversionsQuery = db\r\n        .selectFrom('users as u')\r\n        .leftJoin('users_subscriptions as us', 'u.id', 'us.user_id')\r\n        .leftJoin('plans as p', 'us.plan_id', 'p.id')\r\n        .where('u.aff_id', '=', userId)\r\n        .select([\r\n          'u.id',\r\n          'u.name',\r\n          'u.email',\r\n          'u.created_at as conversion_date',\r\n          'us.status as subscription_status',\r\n          'p.name as plan_name',\r\n          'us.price as subscription_price'\r\n        ])\r\n        .orderBy('u.created_at', 'desc');\r\n\r\n      if (dateFilter.start && dateFilter.end) {\r\n        conversionsQuery = conversionsQuery\r\n          .where('u.created_at', '>=', dateFilter.start)\r\n          .where('u.created_at', '<=', dateFilter.end);\r\n      }\r\n\r\n      const conversions = await conversionsQuery.execute();\r\n\r\n      // Buscar cliques detalhados\r\n      let clicksQuery = db\r\n        .selectFrom('affiliate_links_visits as alv')\r\n        .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')\r\n        .where('al.user_id', '=', userId)\r\n        .where('al.deleted_at', 'is', null)\r\n        .select([\r\n          'alv.id',\r\n          'alv.created_at as click_date',\r\n          'al.name as link_name',\r\n          'al.invite as link_code',\r\n          'al.link_type'\r\n        ])\r\n        .orderBy('alv.created_at', 'desc');\r\n\r\n      if (dateFilter.start && dateFilter.end) {\r\n        clicksQuery = clicksQuery\r\n          .where('alv.created_at', '>=', dateFilter.start)\r\n          .where('alv.created_at', '<=', dateFilter.end);\r\n      }\r\n\r\n      const clicks = await clicksQuery.execute();\r\n\r\n      // Estatísticas por período (últimos 12 meses)\r\n      const monthlyStats: any[] = [];\r\n      for (let i = 11; i >= 0; i--) {\r\n        const monthStart = dayjs().subtract(i, 'month').startOf('month').toDate();\r\n        const monthEnd = dayjs().subtract(i, 'month').endOf('month').toDate();\r\n        const monthName = dayjs().subtract(i, 'month').format('YYYY-MM');\r\n\r\n        const monthCommissions = await db\r\n          .selectFrom('affiliate_commissions')\r\n          .where('aff_user_id', '=', userId)\r\n          .where('created_at', '>=', monthStart)\r\n          .where('created_at', '<=', monthEnd)\r\n          .select([\r\n            sql<number>`COUNT(*)`.as('count'),\r\n            sql<number>`SUM(commission_value)`.as('total_value')\r\n          ])\r\n          .executeTakeFirst();\r\n\r\n        const monthConversions = await db\r\n          .selectFrom('users')\r\n          .where('aff_id', '=', userId)\r\n          .where('created_at', '>=', monthStart)\r\n          .where('created_at', '<=', monthEnd)\r\n          .select(sql<number>`COUNT(*)`.as('count'))\r\n          .executeTakeFirst();\r\n\r\n        const monthClicks = await db\r\n          .selectFrom('affiliate_links_visits as alv')\r\n          .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')\r\n          .where('al.user_id', '=', userId)\r\n          .where('al.deleted_at', 'is', null)\r\n          .where('alv.created_at', '>=', monthStart)\r\n          .where('alv.created_at', '<=', monthEnd)\r\n          .select(sql<number>`COUNT(*)`.as('count'))\r\n          .executeTakeFirst();\r\n\r\n        monthlyStats.push({\r\n          month: monthName,\r\n          commissions: {\r\n            count: Number(monthCommissions?.count || 0),\r\n            total_value: Number(monthCommissions?.total_value || 0)\r\n          },\r\n          conversions: Number(monthConversions?.count || 0),\r\n          clicks: Number(monthClicks?.count || 0)\r\n        });\r\n      }\r\n\r\n      const detailedData = {\r\n        // Dados detalhados\r\n        commissions: commissions.map(commission => ({\r\n          id: commission.id,\r\n          value: Number(commission.commission_value || 0),\r\n          percent: Number(commission.commission_percent || 0),\r\n          status: commission.status,\r\n          date: commission.created_at,\r\n          user: {\r\n            name: commission.user_name,\r\n            email: commission.user_email\r\n          },\r\n          plan: commission.plan_name,\r\n          transaction: {\r\n            amount: Number(commission.transaction_amount || 0),\r\n            currency: commission.transaction_currency\r\n          }\r\n        })),\r\n\r\n        conversions: conversions.map(conversion => ({\r\n          id: conversion.id,\r\n          user: {\r\n            name: conversion.name,\r\n            email: conversion.email\r\n          },\r\n          conversion_date: conversion.conversion_date,\r\n          subscription: {\r\n            status: conversion.subscription_status,\r\n            plan: conversion.plan_name,\r\n            price: Number(conversion.subscription_price || 0)\r\n          }\r\n        })),\r\n\r\n        clicks: clicks.map(click => ({\r\n          id: click.id,\r\n          date: click.click_date,\r\n          link: {\r\n            name: click.link_name,\r\n            code: click.link_code,\r\n            type: click.link_type\r\n          }\r\n        })),\r\n\r\n        // Estatísticas mensais\r\n        monthly_stats: monthlyStats,\r\n\r\n        // Resumo do período\r\n        period_summary: {\r\n          total_commissions: commissions.length,\r\n          total_earnings: commissions.reduce((sum, c) => sum + Number(c.commission_value || 0), 0),\r\n          total_conversions: conversions.length,\r\n          total_clicks: clicks.length,\r\n          conversion_rate: clicks.length > 0\r\n            ? ((conversions.length / clicks.length) * 100).toFixed(2)\r\n            : '0.00'\r\n        }\r\n      };\r\n\r\n      return {\r\n        status: 'success',\r\n        data: detailedData\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('Erro ao buscar estatísticas detalhadas:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getPaymentHistory(userId: number, page: number = 1, limit: number = 10) {\r\n    try {\r\n      console.log(`Buscando histórico de pagamentos para o afiliado ${userId}, página ${page}, limite ${limit}`);\r\n\r\n      // Verificar se o usuário é um afiliado ativo\r\n      const affiliate = await db.selectFrom('affiliates')\r\n        .where('user_id', '=', userId)\r\n        .where('status', '=', 'active')\r\n        .where('deleted_at', 'is', null)\r\n        .select(['id', 'user_id'])\r\n        .executeTakeFirst();\r\n\r\n      if (!affiliate) {\r\n        throw new NotFoundException('Afiliado não encontrado ou não está ativo');\r\n      }\r\n\r\n      const offset = (page - 1) * limit;\r\n\r\n      // Buscar histórico de pagamentos (comissões pagas)\r\n      const payments = await db\r\n        .selectFrom('affiliate_commissions as ac')\r\n        .leftJoin('users as u', 'ac.user_id', 'u.id')\r\n        .leftJoin('plans as p', 'ac.plan_id', 'p.id')\r\n        .leftJoin('transactions as t', 'ac.transaction_id', 't.id')\r\n        .where('ac.aff_user_id', '=', userId)\r\n        .where('ac.status', '=', 'paid')\r\n        .select([\r\n          'ac.id',\r\n          'ac.commission_value',\r\n          'ac.commission_percent',\r\n          'ac.created_at as payment_date',\r\n          'ac.metadata',\r\n          'u.name as customer_name',\r\n          'u.email as customer_email',\r\n          'p.name as plan_name',\r\n          't.amount as transaction_amount',\r\n          't.currency as transaction_currency'\r\n        ])\r\n        .orderBy('ac.created_at', 'desc')\r\n        .limit(limit)\r\n        .offset(offset)\r\n        .execute();\r\n\r\n      // Contar total de pagamentos\r\n      const totalPayments = await db\r\n        .selectFrom('affiliate_commissions')\r\n        .where('aff_user_id', '=', userId)\r\n        .where('status', '=', 'paid')\r\n        .select(sql<number>`COUNT(*)`.as('total'))\r\n        .executeTakeFirst();\r\n\r\n      // Processar dados dos pagamentos\r\n      const processedPayments = payments.map(payment => {\r\n        let metadata: any = null;\r\n        try {\r\n          metadata = payment.metadata ? JSON.parse(payment.metadata as string) : null;\r\n        } catch (error) {\r\n          console.error('Erro ao processar metadata do pagamento:', error);\r\n        }\r\n\r\n        return {\r\n          id: payment.id,\r\n          value: Number(payment.commission_value || 0),\r\n          percent: Number(payment.commission_percent || 0),\r\n          date: payment.payment_date,\r\n          customer: {\r\n            name: payment.customer_name,\r\n            email: payment.customer_email\r\n          },\r\n          plan: payment.plan_name,\r\n          transaction: {\r\n            amount: Number(payment.transaction_amount || 0),\r\n            currency: payment.transaction_currency || 'BRL'\r\n          },\r\n          stripe_transfer_id: metadata?.stripe_transfer_id || null\r\n        };\r\n      });\r\n\r\n      const totalPages = Math.ceil(Number(totalPayments?.total || 0) / limit);\r\n\r\n      return {\r\n        status: 'success',\r\n        data: {\r\n          payments: processedPayments,\r\n          pagination: {\r\n            current_page: page,\r\n            total_pages: totalPages,\r\n            total_items: Number(totalPayments?.total || 0),\r\n            items_per_page: limit,\r\n            has_next: page < totalPages,\r\n            has_previous: page > 1\r\n          }\r\n        }\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('Erro ao buscar histórico de pagamentos:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getRecentTransactions(userId: number, page: number = 1, limit: number = 10) {\r\n  try {\r\n    console.log(`Buscando transações recentes para o afiliado ${userId}, página ${page}, limite ${limit}`);\r\n\r\n    // Verificar se o usuário é um afiliado ativo\r\n    const affiliate = await db.selectFrom('affiliates')\r\n      .where('user_id', '=', userId)\r\n      .where('status', '=', 'active')\r\n      .where('deleted_at', 'is', null)\r\n      .select(['id', 'user_id'])\r\n      .executeTakeFirst();\r\n\r\n    if (!affiliate) {\r\n      throw new NotFoundException('Afiliado não encontrado ou não está ativo');\r\n    }\r\n\r\n    const offset = (page - 1) * limit;\r\n\r\n    // Buscar transações recentes relacionadas às comissões do afiliado\r\n    // Removido o LEFT JOIN com users_subscriptions que estava causando duplicatas\r\n    const transactions = await db\r\n      .selectFrom('affiliate_commissions as ac')\r\n      .innerJoin('transactions as t', 'ac.transaction_id', 't.id')\r\n      .leftJoin('users as u', 'ac.user_id', 'u.id')\r\n      .leftJoin('plans as p', 'ac.plan_id', 'p.id')\r\n      .where('ac.aff_user_id', '=', userId)\r\n      .select([\r\n        'ac.id as commission_id',\r\n        'ac.commission_value',\r\n        'ac.commission_percent',\r\n        'ac.status as commission_status',\r\n        'ac.created_at as commission_date',\r\n        't.id as transaction_id',\r\n        't.amount as transaction_amount',\r\n        't.currency as transaction_currency',\r\n        't.status as transaction_status',\r\n        't.created_at as transaction_date',\r\n        't.provider_transaction_id',\r\n        't.source_id', // Adicionado para buscar a subscription depois\r\n        't.source_type', // Adicionado para confirmar se é subscription\r\n        'u.id as user_id',\r\n        'u.name as user_name',\r\n        'u.email as user_email',\r\n        'p.id as plan_id',\r\n        'p.name as plan_name',\r\n        'p.price as plan_price'\r\n      ])\r\n      .orderBy('t.created_at', 'desc')\r\n      .limit(limit)\r\n      .offset(offset)\r\n      .execute();\r\n\r\n    // Contar total de transações (sem o LEFT JOIN problemático)\r\n    const totalTransactions = await db\r\n      .selectFrom('affiliate_commissions as ac')\r\n      .innerJoin('transactions as t', 'ac.transaction_id', 't.id')\r\n      .where('ac.aff_user_id', '=', userId)\r\n      .select(sql<number>`COUNT(*)`.as('total'))\r\n      .executeTakeFirst();\r\n\r\n    // Buscar informações de subscription para cada transação (apenas se for source_type = 'subscription')\r\n    const subscriptionIds = transactions\r\n      .filter(t => t.source_type === 'subscription' && t.source_id)\r\n      .map(t => t.source_id);\r\n\r\n    let subscriptions: Record<number, string> = {};\r\n    \r\n    if (subscriptionIds.length > 0) {\r\n      const subscriptionData = await db\r\n        .selectFrom('users_subscriptions')\r\n        .where('id', 'in', subscriptionIds)\r\n        .select(['id', 'status'])\r\n        .execute();\r\n\r\n      subscriptions = subscriptionData.reduce((acc, sub) => {\r\n        if (sub.id && sub.status) {\r\n          acc[sub.id] = sub.status;\r\n        }\r\n        return acc;\r\n      }, {} as Record<number, string>);\r\n    }\r\n\r\n    // Processar dados das transações\r\n    const processedTransactions = transactions.map(transaction => {\r\n      // Buscar status da subscription se disponível\r\n      let subscriptionStatus: string | null = null;\r\n      if (transaction.source_type === 'subscription' && transaction.source_id) {\r\n        subscriptionStatus = subscriptions[transaction.source_id] || null;\r\n      }\r\n\r\n      return {\r\n        commission: {\r\n          id: transaction.commission_id,\r\n          value: Number(transaction.commission_value || 0),\r\n          percent: Number(transaction.commission_percent || 0),\r\n          status: transaction.commission_status,\r\n          date: transaction.commission_date\r\n        },\r\n        transaction: {\r\n          id: transaction.transaction_id,\r\n          amount: Number(transaction.transaction_amount || 0),\r\n          currency: transaction.transaction_currency || 'BRL',\r\n          status: transaction.transaction_status,\r\n          date: transaction.transaction_date,\r\n          provider_id: transaction.provider_transaction_id,\r\n          source_type: transaction.source_type,\r\n          source_id: transaction.source_id\r\n        },\r\n        customer: {\r\n          id: transaction.user_id,\r\n          name: transaction.user_name,\r\n          email: transaction.user_email\r\n        },\r\n        plan: {\r\n          id: transaction.plan_id,\r\n          name: transaction.plan_name,\r\n          price: Number(transaction.plan_price || 0)\r\n        },\r\n        subscription_status: subscriptionStatus\r\n      };\r\n    });\r\n\r\n    const totalPages = Math.ceil(Number(totalTransactions?.total || 0) / limit);\r\n\r\n    // Calcular estatísticas das transações da página atual\r\n    const pageStats = {\r\n      total_commission_value: processedTransactions.reduce((sum, t) => sum + t.commission.value, 0),\r\n      total_transaction_value: processedTransactions.reduce((sum, t) => sum + t.transaction.amount, 0),\r\n      paid_commissions: processedTransactions.filter(t => t.commission.status === 'paid').length,\r\n      pending_commissions: processedTransactions.filter(t => t.commission.status === 'pending').length,\r\n      successful_transactions: processedTransactions.filter(t => t.transaction.status === 'paid').length\r\n    };\r\n\r\n    console.log(`Encontradas ${processedTransactions.length} transações para a página ${page}`);\r\n    console.log(`Total de transações no banco: ${totalTransactions?.total}`);\r\n\r\n    return {\r\n      status: 'success',\r\n      data: {\r\n        transactions: processedTransactions,\r\n        page_stats: pageStats,\r\n        pagination: {\r\n          current_page: page,\r\n          total_pages: totalPages,\r\n          total_items: Number(totalTransactions?.total || 0),\r\n          items_per_page: limit,\r\n          has_next: page < totalPages,\r\n          has_previous: page > 1\r\n        }\r\n      }\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error('Erro ao buscar transações recentes:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n  async getRecentTransactionsOld(userId: number, page: number = 1, limit: number = 10) {\r\n    try {\r\n      console.log(`Buscando transações recentes para o afiliado ${userId}, página ${page}, limite ${limit}`);\r\n\r\n      // Verificar se o usuário é um afiliado ativo\r\n      const affiliate = await db.selectFrom('affiliates')\r\n        .where('user_id', '=', userId)\r\n        .where('status', '=', 'active')\r\n        .where('deleted_at', 'is', null)\r\n        .select(['id', 'user_id'])\r\n        .executeTakeFirst();\r\n\r\n      if (!affiliate) {\r\n        throw new NotFoundException('Afiliado não encontrado ou não está ativo');\r\n      }\r\n\r\n      const offset = (page - 1) * limit;\r\n\r\n      // Buscar transações recentes relacionadas às comissões do afiliado\r\n      const transactions = await db\r\n        .selectFrom('affiliate_commissions as ac')\r\n        .innerJoin('transactions as t', 'ac.transaction_id', 't.id')\r\n        .leftJoin('users as u', 'ac.user_id', 'u.id')\r\n        .leftJoin('plans as p', 'ac.plan_id', 'p.id')\r\n        .leftJoin('users_subscriptions as us', 'us.user_id', 'u.id')\r\n        .where('ac.aff_user_id', '=', userId)\r\n        .select([\r\n          'ac.id as commission_id',\r\n          'ac.commission_value',\r\n          'ac.commission_percent',\r\n          'ac.status as commission_status',\r\n          'ac.created_at as commission_date',\r\n          't.id as transaction_id',\r\n          't.amount as transaction_amount',\r\n          't.currency as transaction_currency',\r\n          't.status as transaction_status',\r\n          't.created_at as transaction_date',\r\n          't.provider_transaction_id',\r\n          'u.id as user_id',\r\n          'u.name as user_name',\r\n          'u.email as user_email',\r\n          'p.id as plan_id',\r\n          'p.name as plan_name',\r\n          'p.price as plan_price',\r\n          'us.status as subscription_status'\r\n        ])\r\n        .orderBy('t.created_at', 'desc')\r\n        .limit(limit)\r\n        .offset(offset)\r\n        .execute();\r\n\r\n      // Contar total de transações\r\n      const totalTransactions = await db\r\n        .selectFrom('affiliate_commissions as ac')\r\n        .innerJoin('transactions as t', 'ac.transaction_id', 't.id')\r\n        .where('ac.aff_user_id', '=', userId)\r\n        .select(sql<number>`COUNT(*)`.as('total'))\r\n        .executeTakeFirst();\r\n\r\n      // Processar dados das transações\r\n      const processedTransactions = transactions.map(transaction => {\r\n        return {\r\n          commission: {\r\n            id: transaction.commission_id,\r\n            value: Number(transaction.commission_value || 0),\r\n            percent: Number(transaction.commission_percent || 0),\r\n            status: transaction.commission_status,\r\n            date: transaction.commission_date\r\n          },\r\n          transaction: {\r\n            id: transaction.transaction_id,\r\n            amount: Number(transaction.transaction_amount || 0),\r\n            currency: transaction.transaction_currency || 'BRL',\r\n            status: transaction.transaction_status,\r\n            date: transaction.transaction_date,\r\n            provider_id: transaction.provider_transaction_id\r\n          },\r\n          customer: {\r\n            id: transaction.user_id,\r\n            name: transaction.user_name,\r\n            email: transaction.user_email\r\n          },\r\n          plan: {\r\n            id: transaction.plan_id,\r\n            name: transaction.plan_name,\r\n            price: Number(transaction.plan_price || 0)\r\n          },\r\n          subscription_status: transaction.subscription_status\r\n        };\r\n      });\r\n\r\n      const totalPages = Math.ceil(Number(totalTransactions?.total || 0) / limit);\r\n\r\n      // Calcular estatísticas das transações da página atual\r\n      const pageStats = {\r\n        total_commission_value: processedTransactions.reduce((sum, t) => sum + t.commission.value, 0),\r\n        total_transaction_value: processedTransactions.reduce((sum, t) => sum + t.transaction.amount, 0),\r\n        paid_commissions: processedTransactions.filter(t => t.commission.status === 'paid').length,\r\n        pending_commissions: processedTransactions.filter(t => t.commission.status === 'pending').length,\r\n        successful_transactions: processedTransactions.filter(t => t.transaction.status === 'paid').length\r\n      };\r\n\r\n      return {\r\n        status: 'success',\r\n        data: {\r\n          transactions: processedTransactions,\r\n          page_stats: pageStats,\r\n          pagination: {\r\n            current_page: page,\r\n            total_pages: totalPages,\r\n            total_items: Number(totalTransactions?.total || 0),\r\n            items_per_page: limit,\r\n            has_next: page < totalPages,\r\n            has_previous: page > 1\r\n          }\r\n        }\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('Erro ao buscar transações recentes:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n}"], "names": ["AffiliatesService", "dayjs", "require", "getAffiliateById", "id", "affiliate", "db", "selectFrom", "leftJoin", "select", "where", "executeTakeFirst", "NotFoundException", "status", "data", "name", "email", "is_master", "findAccountByAffiliateId", "affiliateId", "Number", "stripeId", "account", "stripe", "accounts", "retrieve", "checkPaymentEligibilityByAffiliateId", "canReceivePayments", "capabilities", "card_payments", "transfers", "detailsSubmitted", "details_submitted", "onboarding", "startOnboarding", "accountLink", "accountLinks", "create", "refresh_url", "process", "env", "STRIPE_REFRESH_URL", "return_url", "STRIPE_RETURN_URL", "type", "url", "getLinks", "userId", "links", "execute", "createLink", "body", "invite", "link_type", "existingLink", "ConflictException", "newLink", "insertInto", "values", "user_id", "created_at", "Date", "updated_at", "executeTakeFirstOrThrow", "registerLinkVisit", "link", "newVisit", "link_id", "getDashboardStats", "console", "log", "JSON", "stringify", "totalClicks", "innerJoin", "sql", "as", "total", "totalEarnings", "total_earnings", "total_commissions", "conversions", "commissionsByStatus", "groupBy", "thirtyDaysAgo", "subtract", "toDate", "recentStats", "recentConversions", "recentClicks", "lastPayment", "orderBy", "lastPaymentInfo", "metadata", "parse", "error", "value", "commission_value", "date", "percent", "commission_percent", "transaction_amount", "currency", "stripe_transfer_id", "dashboardData", "total_clicks", "total_conversions", "recent_stats", "clicks", "earnings", "recent_earnings", "commissions", "recent_commissions", "commissions_by_status", "map", "item", "count", "total_value", "conversion_rate", "toFixed", "average_commission", "last_payment", "getDetailedStats", "startDate", "endDate", "dateFilter", "start", "startOf", "end", "endOf", "commissions<PERSON><PERSON>y", "conversionsQuery", "clicksQuery", "monthlyStats", "i", "monthStart", "monthEnd", "monthName", "format", "monthCommissions", "monthConversions", "monthClicks", "push", "month", "detailedData", "commission", "user", "user_name", "user_email", "plan", "plan_name", "transaction", "amount", "transaction_currency", "conversion", "conversion_date", "subscription", "subscription_status", "price", "subscription_price", "click", "click_date", "link_name", "code", "link_code", "monthly_stats", "period_summary", "length", "reduce", "sum", "c", "getPaymentHistory", "page", "limit", "offset", "payments", "totalPayments", "processedPayments", "payment", "payment_date", "customer", "customer_name", "customer_email", "totalPages", "Math", "ceil", "pagination", "current_page", "total_pages", "total_items", "items_per_page", "has_next", "has_previous", "getRecentTransactions", "transactions", "totalTransactions", "subscriptionIds", "filter", "t", "source_type", "source_id", "subscriptions", "subscriptionData", "acc", "sub", "processedTransactions", "subscriptionStatus", "commission_id", "commission_status", "commission_date", "transaction_id", "transaction_status", "transaction_date", "provider_id", "provider_transaction_id", "plan_id", "plan_price", "pageStats", "total_commission_value", "total_transaction_value", "paid_commissions", "pending_commissions", "successful_transactions", "page_stats", "getRecentTransactionsOld", "constructor", "Stripe", "STRIPE_SK"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARoD;0BAC9C;wBAGC;wBACG;;;;;;;;;;AAHvB,kCAAkC;AAClC,MAAMC,QAAQC,QAAQ;AAKf,IAAA,AAAMF,oBAAN,MAAMA;IAUT,MAAMG,iBAAiBC,EAAU,EAAE;QAC/B,MAAMC,YAAY,MAAMC,YAAE,CAACC,UAAU,CAAC,cACjCC,QAAQ,CAAC,SAAS,YAAY,sBAC9BC,MAAM,CAAC;YAAC;YAAc;YAAe;SAAuB,EAC5DC,KAAK,CAAC,WAAW,KAAKN,IACtBM,KAAK,CAAC,yBAAyB,MAAM,MACrCA,KAAK,CAAC,qBAAqB,KAAK,UAChCC,gBAAgB;QAErB,IAAI,CAACN,WAAW;YACZ,MAAM,IAAIO,yBAAiB,CAAC;QAChC;QAEA,OAAO;YACHC,QAAQ;YACRC,MAAM;gBACFC,MAAMV,UAAUU,IAAI;gBACpBC,OAAOX,UAAUW,KAAK;gBACtBC,WAAWZ,UAAUY,SAAS;YAClC;QACJ;IACJ;IAGF,MAAMC,yBAAyBC,WAAmB,EAAkC;QAClF,MAAMd,YAAY,MAAMC,YAAE,CAACC,UAAU,CAAC,cACrCG,KAAK,CAAC,WAAW,KAAKU,OAAOD,cAC7BV,MAAM,CAAC;YAAC;SAAW,EACnBE,gBAAgB;QAEjB,IAAI,CAACN,WAAW;YACd,MAAM,IAAIO,yBAAiB,CAAC;QAC9B;QAEA,IAAI,CAACP,UAAUgB,QAAQ,EAAE;YACvB,OAAO;QACT;QAEA,MAAMC,UAAU,MAAM,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACpB,UAAUgB,QAAQ;QAEtE,OAAOC;IACT;IAEA,MAAMI,qCAAqCP,WAAmB,EAAgB;QAC5E,MAAMG,UAAU,MAAM,IAAI,CAACJ,wBAAwB,CAACC;QACpD,IAAI,CAACG,SAAS;YACZ,OAAO;QACT;QAEA,MAAMK,qBACJL,QAAQM,YAAY,EAAEC,kBAAkB,YACxCP,QAAQM,YAAY,EAAEE,cAAc;QACtC,MAAMC,mBAAmBT,QAAQU,iBAAiB;QAElD,OAAO;YACLnB,QAAQ;YACRC,MAAM;gBACNmB,YAAYN,qBACR,UACAI,mBACE,YACA;YACN;QACF;IACF;IAEA,MAAMG,gBAAgBf,WAAmB,EAAgB;QACvD,MAAMG,UAAU,MAAM,IAAI,CAACJ,wBAAwB,CAACC;QACpD,IAAI,CAACG,SAAS;YACZ,OAAO;QACT;QAEA,MAAMa,cAAc,MAAM,IAAI,CAACZ,MAAM,CAACa,YAAY,CAACC,MAAM,CAAC;YACxDf,SAASA,QAAQlB,EAAE;YACnBkC,aAAaC,QAAQC,GAAG,CAACC,kBAAkB;YAC3CC,YAAYH,QAAQC,GAAG,CAACG,iBAAiB;YACzCC,MAAM;QACR;QAEA,OAAO;YACL/B,QAAQ;YACRC,MAAM;gBACJqB,aAAaA,YAAYU,GAAG;YAC9B;QACF;IACF;IAEA,MAAMC,SAASC,MAAc,EAAE;QAC7B,MAAMC,QAAQ,MAAM1C,YAAE,CAACC,UAAU,CAAC,mBACjCG,KAAK,CAAC,WAAW,KAAKqC,QACtBrC,KAAK,CAAC,cAAc,MAAM,MAC1BD,MAAM,CAAC;YAAC;YAAM;YAAQ;YAAU;YAAa;YAAc;SAAa,EACxEwC,OAAO;QAER,OAAO;YACLpC,QAAQ;YACRC,MAAMkC;QACR;IACF;IAGA,MAAME,WAAWH,MAAc,EAAEI,IAAS,EAAE;QAC1C,MAAM,EAAEpC,IAAI,EAAEqC,MAAM,EAAEC,SAAS,EAAE,GAAGF;QAEpC,MAAMG,eAAe,MAAMhD,YAAE,CAACC,UAAU,CAAC,mBACxCG,KAAK,CAAC,UAAU,KAAK0C,QACrB3C,MAAM,CAAC,MACPE,gBAAgB;QAEjB,IAAI2C,cAAc;YAChB,MAAM,IAAIC,yBAAiB,CAAC;QAC9B;QAEA,MAAMC,UAAU,MAAMlD,YAAE,CAACmD,UAAU,CAAC,mBACnCC,MAAM,CAAC;YACNC,SAASZ;YACThC;YACAqC;YACAC;YACAO,YAAY,IAAIC;YAChBC,YAAY,IAAID;QAClB,GACCE,uBAAuB;QAExB,OAAO;YACLlD,QAAQ;YACRC,MAAM,EAAE;QACV;IACF;IAEA,MAAMkD,kBAAkB5D,EAAU,EAAE;QAClC,MAAM6D,OAAO,MAAM3D,YAAE,CAACC,UAAU,CAAC,mBAChCG,KAAK,CAAC,UAAU,KAAKN,IACrBK,MAAM,CAAC;YAAC;YAAM;YAAW;SAAY,EACrCE,gBAAgB;QAEjB,IAAI,CAACsD,MAAM;YACT,MAAM,IAAIrD,yBAAiB,CAAC;QAC9B;QAEA,MAAMsD,WAAW,MAAM5D,YAAE,CAACmD,UAAU,CAAC,0BACpCC,MAAM,CAAC;YACNS,SAASF,KAAK7D,EAAE;YAChBwD,YAAY,IAAIC;QAClB,GACCE,uBAAuB;QAExB,OAAO;YACLlD,QAAQ;YACRC,MAAM,EAAE;QACV;IACF;IAEA,MAAMsD,kBAAkBrB,MAAc,EAAE;QACtC,IAAI;YACFsB,QAAQC,GAAG,CAAC,CAAC,mDAAmD,EAAEvB,QAAQ;YAE1E,6CAA6C;YAC7C,MAAM1C,YAAY,MAAMC,YAAE,CAACC,UAAU,CAAC,cACnCG,KAAK,CAAC,WAAW,KAAKqC,QACtBrC,KAAK,CAAC,UAAU,KAAK,UACrBA,KAAK,CAAC,cAAc,MAAM,MAC1BD,MAAM,CAAC;gBAAC;gBAAM;gBAAW;aAAS,EAClCE,gBAAgB;YAEnB,IAAI,CAACN,WAAW;gBACd,MAAM,IAAIO,yBAAiB,CAAC;YAC9B;YAEAyD,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEC,KAAKC,SAAS,CAACnE,YAAY;YAE/D,6DAA6D;YAC7D,MAAMoE,cAAc,MAAMnE,YAAE,CACzBC,UAAU,CAAC,iCACXmE,SAAS,CAAC,yBAAyB,eAAe,SAClDhE,KAAK,CAAC,cAAc,KAAKqC,QACzBrC,KAAK,CAAC,iBAAiB,MAAM,MAC7BD,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;YAEnB0D,QAAQC,GAAG,CAAC,CAAC,kBAAkB,EAAEG,aAAaI,SAAS,GAAG;YAE1D,kDAAkD;YAClD,MAAMC,gBAAgB,MAAMxE,YAAE,CAC3BC,UAAU,CAAC,yBACXG,KAAK,CAAC,eAAe,KAAKqC,QAC1BtC,MAAM,CAAC;gBACNkE,IAAAA,WAAG,CAAQ,CAAC,qBAAqB,CAAC,CAACC,EAAE,CAAC;gBACtCD,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC;aAC1B,EACAjE,gBAAgB;YAEnB0D,QAAQC,GAAG,CAAC,CAAC,eAAe,EAAEQ,eAAeC,kBAAkB,EAAE,sBAAsB,EAAED,eAAeE,qBAAqB,GAAG;YAEhI,0EAA0E;YAC1E,MAAMC,cAAc,MAAM3E,YAAE,CACzBC,UAAU,CAAC,SACXG,KAAK,CAAC,UAAU,KAAKqC,QACrBtC,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;YAEnB0D,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEW,aAAaJ,SAAS,GAAG;YAE7D,0BAA0B;YAC1B,MAAMK,sBAAsB,MAAM5E,YAAE,CACjCC,UAAU,CAAC,yBACXG,KAAK,CAAC,eAAe,KAAKqC,QAC1BtC,MAAM,CAAC;gBACN;gBACAkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC;gBACzBD,IAAAA,WAAG,CAAQ,CAAC,qBAAqB,CAAC,CAACC,EAAE,CAAC;aACvC,EACAO,OAAO,CAAC,UACRlC,OAAO;YAEVoB,QAAQC,GAAG,CAAC,CAAC,sBAAsB,EAAEC,KAAKC,SAAS,CAACU,sBAAsB;YAE1E,sCAAsC;YACtC,MAAME,gBAAgBnF,QAAQoF,QAAQ,CAAC,IAAI,OAAOC,MAAM;YAExD,MAAMC,cAAc,MAAMjF,YAAE,CACzBC,UAAU,CAAC,yBACXG,KAAK,CAAC,eAAe,KAAKqC,QAC1BrC,KAAK,CAAC,cAAc,MAAM0E,eAC1B3E,MAAM,CAAC;gBACNkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC;gBACzBD,IAAAA,WAAG,CAAQ,CAAC,qBAAqB,CAAC,CAACC,EAAE,CAAC;aACvC,EACAjE,gBAAgB;YAEnB0D,QAAQC,GAAG,CAAC,CAAC,kCAAkC,EAAEC,KAAKC,SAAS,CAACe,cAAc;YAE9E,oCAAoC;YACpC,MAAMC,oBAAoB,MAAMlF,YAAE,CAC/BC,UAAU,CAAC,SACXG,KAAK,CAAC,UAAU,KAAKqC,QACrBrC,KAAK,CAAC,cAAc,MAAM0E,eAC1B3E,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;YAEnB0D,QAAQC,GAAG,CAAC,CAAC,gCAAgC,EAAEkB,mBAAmBX,SAAS,GAAG;YAE9E,iCAAiC;YACjC,MAAMY,eAAe,MAAMnF,YAAE,CAC1BC,UAAU,CAAC,iCACXmE,SAAS,CAAC,yBAAyB,eAAe,SAClDhE,KAAK,CAAC,cAAc,KAAKqC,QACzBrC,KAAK,CAAC,iBAAiB,MAAM,MAC7BA,KAAK,CAAC,kBAAkB,MAAM0E,eAC9B3E,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;YAEnB0D,QAAQC,GAAG,CAAC,CAAC,6BAA6B,EAAEmB,cAAcZ,SAAS,GAAG;YAEtE,yEAAyE;YACzE,MAAMa,cAAc,MAAMpF,YAAE,CACzBC,UAAU,CAAC,yBACXG,KAAK,CAAC,eAAe,KAAKqC,QAC1BrC,KAAK,CAAC,UAAU,KAAK,QACrBD,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;aACD,EACAkF,OAAO,CAAC,cAAc,QACtBhF,gBAAgB;YAEnB0D,QAAQC,GAAG,CAAC,CAAC,kBAAkB,EAAEC,KAAKC,SAAS,CAACkB,cAAc;YAE9D,4CAA4C;YAC5C,IAAIE,kBAAuB;YAC3B,IAAIF,aAAa;gBACf,IAAIG,WAAgB;gBACpB,IAAI;oBACFA,WAAWH,YAAYG,QAAQ,GAAGtB,KAAKuB,KAAK,CAACJ,YAAYG,QAAQ,IAAc;gBACjF,EAAE,OAAOE,OAAO;oBACd1B,QAAQ0B,KAAK,CAAC,mDAAmDA;gBACnE;gBAEAH,kBAAkB;oBAChBI,OAAO5E,OAAOsE,YAAYO,gBAAgB,IAAI;oBAC9CC,MAAMR,YAAY9B,UAAU;oBAC5BuC,SAAS/E,OAAOsE,YAAYU,kBAAkB,IAAI;oBAClDC,oBAAoBR,UAAUQ,sBAAsB;oBACpDC,UAAUT,UAAUS,YAAY;oBAChCC,oBAAoBV,UAAUU,sBAAsB;gBACtD;YACF;YAEA,MAAMC,gBAAgB;gBACpB,0BAA0B;gBAC1BC,cAAcrF,OAAOqD,aAAaI,SAAS;gBAC3CE,gBAAgB3D,OAAO0D,eAAeC,kBAAkB;gBACxD2B,mBAAmBtF,OAAO6D,aAAaJ,SAAS;gBAChDG,mBAAmB5D,OAAO0D,eAAeE,qBAAqB;gBAE9D,mCAAmC;gBACnC2B,cAAc;oBACZC,QAAQxF,OAAOqE,cAAcZ,SAAS;oBACtCgC,UAAUzF,OAAOmE,aAAauB,mBAAmB;oBACjD7B,aAAa7D,OAAOoE,mBAAmBX,SAAS;oBAChDkC,aAAa3F,OAAOmE,aAAayB,sBAAsB;gBACzD;gBAEA,uBAAuB;gBACvBC,uBAAuB/B,oBAAoBgC,GAAG,CAACC,CAAAA,OAAS,CAAA;wBACtDtG,QAAQsG,KAAKtG,MAAM;wBACnBuG,OAAOhG,OAAO+F,KAAKC,KAAK;wBACxBC,aAAajG,OAAO+F,KAAKE,WAAW,IAAI;oBAC1C,CAAA;gBAEA,oBAAoB;gBACpBC,iBAAiBlG,OAAOqD,aAAaI,SAAS,KAAK,IAC/C,AAAC,CAAA,AAACzD,OAAO6D,aAAaJ,SAAS,KAAKzD,OAAOqD,aAAaI,SAAS,KAAM,GAAE,EAAG0C,OAAO,CAAC,KACpF;gBAEJ,2BAA2B;gBAC3BC,oBAAoBpG,OAAO0D,eAAeE,qBAAqB,KAAK,IAChE,AAAC5D,CAAAA,OAAO0D,eAAeC,kBAAkB,KAAK3D,OAAO0D,eAAeE,qBAAqB,EAAC,EAAGuC,OAAO,CAAC,KACrG;gBAEJ,mBAAmB;gBACnBE,cAAc7B;YAChB;YAEAvB,QAAQC,GAAG,CAAC,CAAC,gBAAgB,EAAEC,KAAKC,SAAS,CAACgC,gBAAgB;YAE9D,OAAO;gBACL3F,QAAQ;gBACRC,MAAM0F;YACR;QAEF,EAAE,OAAOT,OAAO;YACd1B,QAAQ0B,KAAK,CAAC,6CAA6CA;YAC3D,MAAMA;QACR;IACF;IAEA,MAAM2B,iBAAiB3E,MAAc,EAAE4E,SAAkB,EAAEC,OAAgB,EAAE;QAC3E,IAAI;YACFvD,QAAQC,GAAG,CAAC,CAAC,iDAAiD,EAAEvB,OAAO,WAAW,EAAE4E,UAAU,GAAG,EAAEC,SAAS;YAE5G,6CAA6C;YAC7C,MAAMvH,YAAY,MAAMC,YAAE,CAACC,UAAU,CAAC,cACnCG,KAAK,CAAC,WAAW,KAAKqC,QACtBrC,KAAK,CAAC,UAAU,KAAK,UACrBA,KAAK,CAAC,cAAc,MAAM,MAC1BD,MAAM,CAAC;gBAAC;gBAAM;gBAAW;aAAS,EAClCE,gBAAgB;YAEnB,IAAI,CAACN,WAAW;gBACd,MAAM,IAAIO,yBAAiB,CAAC;YAC9B;YAEA,8BAA8B;YAC9B,IAAIiH,aAA2C,CAAC;YAChD,IAAIF,aAAaC,SAAS;gBACxB,MAAME,QAAQ7H,MAAM0H,WAAWI,OAAO,CAAC,OAAOzC,MAAM;gBACpD,MAAM0C,MAAM/H,MAAM2H,SAASK,KAAK,CAAC,OAAO3C,MAAM;gBAC9CuC,aAAa;oBACXC;oBACAE;gBACF;YACF;YAEA,8BAA8B;YAC9B,IAAIE,mBAAmB5H,YAAE,CACtBC,UAAU,CAAC,+BACXC,QAAQ,CAAC,cAAc,cAAc,QACrCA,QAAQ,CAAC,cAAc,cAAc,QACrCA,QAAQ,CAAC,qBAAqB,qBAAqB,QACnDE,KAAK,CAAC,kBAAkB,KAAKqC,QAC7BtC,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAkF,OAAO,CAAC,iBAAiB;YAE5B,IAAIkC,WAAWC,KAAK,IAAID,WAAWG,GAAG,EAAE;gBACtCE,mBAAmBA,iBAChBxH,KAAK,CAAC,iBAAiB,MAAMmH,WAAWC,KAAK,EAC7CpH,KAAK,CAAC,iBAAiB,MAAMmH,WAAWG,GAAG;YAChD;YAEA,MAAMjB,cAAc,MAAMmB,iBAAiBjF,OAAO;YAElD,+BAA+B;YAC/B,IAAIkF,mBAAmB7H,YAAE,CACtBC,UAAU,CAAC,cACXC,QAAQ,CAAC,6BAA6B,QAAQ,cAC9CA,QAAQ,CAAC,cAAc,cAAc,QACrCE,KAAK,CAAC,YAAY,KAAKqC,QACvBtC,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAkF,OAAO,CAAC,gBAAgB;YAE3B,IAAIkC,WAAWC,KAAK,IAAID,WAAWG,GAAG,EAAE;gBACtCG,mBAAmBA,iBAChBzH,KAAK,CAAC,gBAAgB,MAAMmH,WAAWC,KAAK,EAC5CpH,KAAK,CAAC,gBAAgB,MAAMmH,WAAWG,GAAG;YAC/C;YAEA,MAAM/C,cAAc,MAAMkD,iBAAiBlF,OAAO;YAElD,4BAA4B;YAC5B,IAAImF,cAAc9H,YAAE,CACjBC,UAAU,CAAC,iCACXmE,SAAS,CAAC,yBAAyB,eAAe,SAClDhE,KAAK,CAAC,cAAc,KAAKqC,QACzBrC,KAAK,CAAC,iBAAiB,MAAM,MAC7BD,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;aACD,EACAkF,OAAO,CAAC,kBAAkB;YAE7B,IAAIkC,WAAWC,KAAK,IAAID,WAAWG,GAAG,EAAE;gBACtCI,cAAcA,YACX1H,KAAK,CAAC,kBAAkB,MAAMmH,WAAWC,KAAK,EAC9CpH,KAAK,CAAC,kBAAkB,MAAMmH,WAAWG,GAAG;YACjD;YAEA,MAAMpB,SAAS,MAAMwB,YAAYnF,OAAO;YAExC,8CAA8C;YAC9C,MAAMoF,eAAsB,EAAE;YAC9B,IAAK,IAAIC,IAAI,IAAIA,KAAK,GAAGA,IAAK;gBAC5B,MAAMC,aAAatI,QAAQoF,QAAQ,CAACiD,GAAG,SAASP,OAAO,CAAC,SAASzC,MAAM;gBACvE,MAAMkD,WAAWvI,QAAQoF,QAAQ,CAACiD,GAAG,SAASL,KAAK,CAAC,SAAS3C,MAAM;gBACnE,MAAMmD,YAAYxI,QAAQoF,QAAQ,CAACiD,GAAG,SAASI,MAAM,CAAC;gBAEtD,MAAMC,mBAAmB,MAAMrI,YAAE,CAC9BC,UAAU,CAAC,yBACXG,KAAK,CAAC,eAAe,KAAKqC,QAC1BrC,KAAK,CAAC,cAAc,MAAM6H,YAC1B7H,KAAK,CAAC,cAAc,MAAM8H,UAC1B/H,MAAM,CAAC;oBACNkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC;oBACzBD,IAAAA,WAAG,CAAQ,CAAC,qBAAqB,CAAC,CAACC,EAAE,CAAC;iBACvC,EACAjE,gBAAgB;gBAEnB,MAAMiI,mBAAmB,MAAMtI,YAAE,CAC9BC,UAAU,CAAC,SACXG,KAAK,CAAC,UAAU,KAAKqC,QACrBrC,KAAK,CAAC,cAAc,MAAM6H,YAC1B7H,KAAK,CAAC,cAAc,MAAM8H,UAC1B/H,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;gBAEnB,MAAMkI,cAAc,MAAMvI,YAAE,CACzBC,UAAU,CAAC,iCACXmE,SAAS,CAAC,yBAAyB,eAAe,SAClDhE,KAAK,CAAC,cAAc,KAAKqC,QACzBrC,KAAK,CAAC,iBAAiB,MAAM,MAC7BA,KAAK,CAAC,kBAAkB,MAAM6H,YAC9B7H,KAAK,CAAC,kBAAkB,MAAM8H,UAC9B/H,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;gBAEnB0H,aAAaS,IAAI,CAAC;oBAChBC,OAAON;oBACP1B,aAAa;wBACXK,OAAOhG,OAAOuH,kBAAkBvB,SAAS;wBACzCC,aAAajG,OAAOuH,kBAAkBtB,eAAe;oBACvD;oBACApC,aAAa7D,OAAOwH,kBAAkBxB,SAAS;oBAC/CR,QAAQxF,OAAOyH,aAAazB,SAAS;gBACvC;YACF;YAEA,MAAM4B,eAAe;gBACnB,mBAAmB;gBACnBjC,aAAaA,YAAYG,GAAG,CAAC+B,CAAAA,aAAe,CAAA;wBAC1C7I,IAAI6I,WAAW7I,EAAE;wBACjB4F,OAAO5E,OAAO6H,WAAWhD,gBAAgB,IAAI;wBAC7CE,SAAS/E,OAAO6H,WAAW7C,kBAAkB,IAAI;wBACjDvF,QAAQoI,WAAWpI,MAAM;wBACzBqF,MAAM+C,WAAWrF,UAAU;wBAC3BsF,MAAM;4BACJnI,MAAMkI,WAAWE,SAAS;4BAC1BnI,OAAOiI,WAAWG,UAAU;wBAC9B;wBACAC,MAAMJ,WAAWK,SAAS;wBAC1BC,aAAa;4BACXC,QAAQpI,OAAO6H,WAAW5C,kBAAkB,IAAI;4BAChDC,UAAU2C,WAAWQ,oBAAoB;wBAC3C;oBACF,CAAA;gBAEAxE,aAAaA,YAAYiC,GAAG,CAACwC,CAAAA,aAAe,CAAA;wBAC1CtJ,IAAIsJ,WAAWtJ,EAAE;wBACjB8I,MAAM;4BACJnI,MAAM2I,WAAW3I,IAAI;4BACrBC,OAAO0I,WAAW1I,KAAK;wBACzB;wBACA2I,iBAAiBD,WAAWC,eAAe;wBAC3CC,cAAc;4BACZ/I,QAAQ6I,WAAWG,mBAAmB;4BACtCR,MAAMK,WAAWJ,SAAS;4BAC1BQ,OAAO1I,OAAOsI,WAAWK,kBAAkB,IAAI;wBACjD;oBACF,CAAA;gBAEAnD,QAAQA,OAAOM,GAAG,CAAC8C,CAAAA,QAAU,CAAA;wBAC3B5J,IAAI4J,MAAM5J,EAAE;wBACZ8F,MAAM8D,MAAMC,UAAU;wBACtBhG,MAAM;4BACJlD,MAAMiJ,MAAME,SAAS;4BACrBC,MAAMH,MAAMI,SAAS;4BACrBxH,MAAMoH,MAAM3G,SAAS;wBACvB;oBACF,CAAA;gBAEA,uBAAuB;gBACvBgH,eAAehC;gBAEf,oBAAoB;gBACpBiC,gBAAgB;oBACdtF,mBAAmB+B,YAAYwD,MAAM;oBACrCxF,gBAAgBgC,YAAYyD,MAAM,CAAC,CAACC,KAAKC,IAAMD,MAAMrJ,OAAOsJ,EAAEzE,gBAAgB,IAAI,IAAI;oBACtFS,mBAAmBzB,YAAYsF,MAAM;oBACrC9D,cAAcG,OAAO2D,MAAM;oBAC3BjD,iBAAiBV,OAAO2D,MAAM,GAAG,IAC7B,AAAC,CAAA,AAACtF,YAAYsF,MAAM,GAAG3D,OAAO2D,MAAM,GAAI,GAAE,EAAGhD,OAAO,CAAC,KACrD;gBACN;YACF;YAEA,OAAO;gBACL1G,QAAQ;gBACRC,MAAMkI;YACR;QAEF,EAAE,OAAOjD,OAAO;YACd1B,QAAQ0B,KAAK,CAAC,2CAA2CA;YACzD,MAAMA;QACR;IACF;IAEA,MAAM4E,kBAAkB5H,MAAc,EAAE6H,OAAe,CAAC,EAAEC,QAAgB,EAAE,EAAE;QAC5E,IAAI;YACFxG,QAAQC,GAAG,CAAC,CAAC,iDAAiD,EAAEvB,OAAO,SAAS,EAAE6H,KAAK,SAAS,EAAEC,OAAO;YAEzG,6CAA6C;YAC7C,MAAMxK,YAAY,MAAMC,YAAE,CAACC,UAAU,CAAC,cACnCG,KAAK,CAAC,WAAW,KAAKqC,QACtBrC,KAAK,CAAC,UAAU,KAAK,UACrBA,KAAK,CAAC,cAAc,MAAM,MAC1BD,MAAM,CAAC;gBAAC;gBAAM;aAAU,EACxBE,gBAAgB;YAEnB,IAAI,CAACN,WAAW;gBACd,MAAM,IAAIO,yBAAiB,CAAC;YAC9B;YAEA,MAAMkK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5B,mDAAmD;YACnD,MAAME,WAAW,MAAMzK,YAAE,CACtBC,UAAU,CAAC,+BACXC,QAAQ,CAAC,cAAc,cAAc,QACrCA,QAAQ,CAAC,cAAc,cAAc,QACrCA,QAAQ,CAAC,qBAAqB,qBAAqB,QACnDE,KAAK,CAAC,kBAAkB,KAAKqC,QAC7BrC,KAAK,CAAC,aAAa,KAAK,QACxBD,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAkF,OAAO,CAAC,iBAAiB,QACzBkF,KAAK,CAACA,OACNC,MAAM,CAACA,QACP7H,OAAO;YAEV,6BAA6B;YAC7B,MAAM+H,gBAAgB,MAAM1K,YAAE,CAC3BC,UAAU,CAAC,yBACXG,KAAK,CAAC,eAAe,KAAKqC,QAC1BrC,KAAK,CAAC,UAAU,KAAK,QACrBD,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;YAEnB,iCAAiC;YACjC,MAAMsK,oBAAoBF,SAAS7D,GAAG,CAACgE,CAAAA;gBACrC,IAAIrF,WAAgB;gBACpB,IAAI;oBACFA,WAAWqF,QAAQrF,QAAQ,GAAGtB,KAAKuB,KAAK,CAACoF,QAAQrF,QAAQ,IAAc;gBACzE,EAAE,OAAOE,OAAO;oBACd1B,QAAQ0B,KAAK,CAAC,4CAA4CA;gBAC5D;gBAEA,OAAO;oBACL3F,IAAI8K,QAAQ9K,EAAE;oBACd4F,OAAO5E,OAAO8J,QAAQjF,gBAAgB,IAAI;oBAC1CE,SAAS/E,OAAO8J,QAAQ9E,kBAAkB,IAAI;oBAC9CF,MAAMgF,QAAQC,YAAY;oBAC1BC,UAAU;wBACRrK,MAAMmK,QAAQG,aAAa;wBAC3BrK,OAAOkK,QAAQI,cAAc;oBAC/B;oBACAjC,MAAM6B,QAAQ5B,SAAS;oBACvBC,aAAa;wBACXC,QAAQpI,OAAO8J,QAAQ7E,kBAAkB,IAAI;wBAC7CC,UAAU4E,QAAQzB,oBAAoB,IAAI;oBAC5C;oBACAlD,oBAAoBV,UAAUU,sBAAsB;gBACtD;YACF;YAEA,MAAMgF,aAAaC,KAAKC,IAAI,CAACrK,OAAO4J,eAAenG,SAAS,KAAKgG;YAEjE,OAAO;gBACLhK,QAAQ;gBACRC,MAAM;oBACJiK,UAAUE;oBACVS,YAAY;wBACVC,cAAcf;wBACdgB,aAAaL;wBACbM,aAAazK,OAAO4J,eAAenG,SAAS;wBAC5CiH,gBAAgBjB;wBAChBkB,UAAUnB,OAAOW;wBACjBS,cAAcpB,OAAO;oBACvB;gBACF;YACF;QAEF,EAAE,OAAO7E,OAAO;YACd1B,QAAQ0B,KAAK,CAAC,2CAA2CA;YACzD,MAAMA;QACR;IACF;IAEA,MAAMkG,sBAAsBlJ,MAAc,EAAE6H,OAAe,CAAC,EAAEC,QAAgB,EAAE,EAAE;QAClF,IAAI;YACFxG,QAAQC,GAAG,CAAC,CAAC,6CAA6C,EAAEvB,OAAO,SAAS,EAAE6H,KAAK,SAAS,EAAEC,OAAO;YAErG,6CAA6C;YAC7C,MAAMxK,YAAY,MAAMC,YAAE,CAACC,UAAU,CAAC,cACnCG,KAAK,CAAC,WAAW,KAAKqC,QACtBrC,KAAK,CAAC,UAAU,KAAK,UACrBA,KAAK,CAAC,cAAc,MAAM,MAC1BD,MAAM,CAAC;gBAAC;gBAAM;aAAU,EACxBE,gBAAgB;YAEnB,IAAI,CAACN,WAAW;gBACd,MAAM,IAAIO,yBAAiB,CAAC;YAC9B;YAEA,MAAMkK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5B,mEAAmE;YACnE,8EAA8E;YAC9E,MAAMqB,eAAe,MAAM5L,YAAE,CAC1BC,UAAU,CAAC,+BACXmE,SAAS,CAAC,qBAAqB,qBAAqB,QACpDlE,QAAQ,CAAC,cAAc,cAAc,QACrCA,QAAQ,CAAC,cAAc,cAAc,QACrCE,KAAK,CAAC,kBAAkB,KAAKqC,QAC7BtC,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAkF,OAAO,CAAC,gBAAgB,QACxBkF,KAAK,CAACA,OACNC,MAAM,CAACA,QACP7H,OAAO;YAEV,4DAA4D;YAC5D,MAAMkJ,oBAAoB,MAAM7L,YAAE,CAC/BC,UAAU,CAAC,+BACXmE,SAAS,CAAC,qBAAqB,qBAAqB,QACpDhE,KAAK,CAAC,kBAAkB,KAAKqC,QAC7BtC,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;YAEnB,sGAAsG;YACtG,MAAMyL,kBAAkBF,aACrBG,MAAM,CAACC,CAAAA,IAAKA,EAAEC,WAAW,KAAK,kBAAkBD,EAAEE,SAAS,EAC3DtF,GAAG,CAACoF,CAAAA,IAAKA,EAAEE,SAAS;YAEvB,IAAIC,gBAAwC,CAAC;YAE7C,IAAIL,gBAAgB7B,MAAM,GAAG,GAAG;gBAC9B,MAAMmC,mBAAmB,MAAMpM,YAAE,CAC9BC,UAAU,CAAC,uBACXG,KAAK,CAAC,MAAM,MAAM0L,iBAClB3L,MAAM,CAAC;oBAAC;oBAAM;iBAAS,EACvBwC,OAAO;gBAEVwJ,gBAAgBC,iBAAiBlC,MAAM,CAAC,CAACmC,KAAKC;oBAC5C,IAAIA,IAAIxM,EAAE,IAAIwM,IAAI/L,MAAM,EAAE;wBACxB8L,GAAG,CAACC,IAAIxM,EAAE,CAAC,GAAGwM,IAAI/L,MAAM;oBAC1B;oBACA,OAAO8L;gBACT,GAAG,CAAC;YACN;YAEA,iCAAiC;YACjC,MAAME,wBAAwBX,aAAahF,GAAG,CAACqC,CAAAA;gBAC7C,8CAA8C;gBAC9C,IAAIuD,qBAAoC;gBACxC,IAAIvD,YAAYgD,WAAW,KAAK,kBAAkBhD,YAAYiD,SAAS,EAAE;oBACvEM,qBAAqBL,aAAa,CAAClD,YAAYiD,SAAS,CAAC,IAAI;gBAC/D;gBAEA,OAAO;oBACLvD,YAAY;wBACV7I,IAAImJ,YAAYwD,aAAa;wBAC7B/G,OAAO5E,OAAOmI,YAAYtD,gBAAgB,IAAI;wBAC9CE,SAAS/E,OAAOmI,YAAYnD,kBAAkB,IAAI;wBAClDvF,QAAQ0I,YAAYyD,iBAAiB;wBACrC9G,MAAMqD,YAAY0D,eAAe;oBACnC;oBACA1D,aAAa;wBACXnJ,IAAImJ,YAAY2D,cAAc;wBAC9B1D,QAAQpI,OAAOmI,YAAYlD,kBAAkB,IAAI;wBACjDC,UAAUiD,YAAYE,oBAAoB,IAAI;wBAC9C5I,QAAQ0I,YAAY4D,kBAAkB;wBACtCjH,MAAMqD,YAAY6D,gBAAgB;wBAClCC,aAAa9D,YAAY+D,uBAAuB;wBAChDf,aAAahD,YAAYgD,WAAW;wBACpCC,WAAWjD,YAAYiD,SAAS;oBAClC;oBACApB,UAAU;wBACRhL,IAAImJ,YAAY5F,OAAO;wBACvB5C,MAAMwI,YAAYJ,SAAS;wBAC3BnI,OAAOuI,YAAYH,UAAU;oBAC/B;oBACAC,MAAM;wBACJjJ,IAAImJ,YAAYgE,OAAO;wBACvBxM,MAAMwI,YAAYD,SAAS;wBAC3BQ,OAAO1I,OAAOmI,YAAYiE,UAAU,IAAI;oBAC1C;oBACA3D,qBAAqBiD;gBACvB;YACF;YAEA,MAAMvB,aAAaC,KAAKC,IAAI,CAACrK,OAAO+K,mBAAmBtH,SAAS,KAAKgG;YAErE,uDAAuD;YACvD,MAAM4C,YAAY;gBAChBC,wBAAwBb,sBAAsBrC,MAAM,CAAC,CAACC,KAAK6B,IAAM7B,MAAM6B,EAAErD,UAAU,CAACjD,KAAK,EAAE;gBAC3F2H,yBAAyBd,sBAAsBrC,MAAM,CAAC,CAACC,KAAK6B,IAAM7B,MAAM6B,EAAE/C,WAAW,CAACC,MAAM,EAAE;gBAC9FoE,kBAAkBf,sBAAsBR,MAAM,CAACC,CAAAA,IAAKA,EAAErD,UAAU,CAACpI,MAAM,KAAK,QAAQ0J,MAAM;gBAC1FsD,qBAAqBhB,sBAAsBR,MAAM,CAACC,CAAAA,IAAKA,EAAErD,UAAU,CAACpI,MAAM,KAAK,WAAW0J,MAAM;gBAChGuD,yBAAyBjB,sBAAsBR,MAAM,CAACC,CAAAA,IAAKA,EAAE/C,WAAW,CAAC1I,MAAM,KAAK,QAAQ0J,MAAM;YACpG;YAEAlG,QAAQC,GAAG,CAAC,CAAC,YAAY,EAAEuI,sBAAsBtC,MAAM,CAAC,0BAA0B,EAAEK,MAAM;YAC1FvG,QAAQC,GAAG,CAAC,CAAC,8BAA8B,EAAE6H,mBAAmBtH,OAAO;YAEvE,OAAO;gBACLhE,QAAQ;gBACRC,MAAM;oBACJoL,cAAcW;oBACdkB,YAAYN;oBACZ/B,YAAY;wBACVC,cAAcf;wBACdgB,aAAaL;wBACbM,aAAazK,OAAO+K,mBAAmBtH,SAAS;wBAChDiH,gBAAgBjB;wBAChBkB,UAAUnB,OAAOW;wBACjBS,cAAcpB,OAAO;oBACvB;gBACF;YACF;QAEF,EAAE,OAAO7E,OAAO;YACd1B,QAAQ0B,KAAK,CAAC,uCAAuCA;YACrD,MAAMA;QACR;IACF;IAEE,MAAMiI,yBAAyBjL,MAAc,EAAE6H,OAAe,CAAC,EAAEC,QAAgB,EAAE,EAAE;QACnF,IAAI;YACFxG,QAAQC,GAAG,CAAC,CAAC,6CAA6C,EAAEvB,OAAO,SAAS,EAAE6H,KAAK,SAAS,EAAEC,OAAO;YAErG,6CAA6C;YAC7C,MAAMxK,YAAY,MAAMC,YAAE,CAACC,UAAU,CAAC,cACnCG,KAAK,CAAC,WAAW,KAAKqC,QACtBrC,KAAK,CAAC,UAAU,KAAK,UACrBA,KAAK,CAAC,cAAc,MAAM,MAC1BD,MAAM,CAAC;gBAAC;gBAAM;aAAU,EACxBE,gBAAgB;YAEnB,IAAI,CAACN,WAAW;gBACd,MAAM,IAAIO,yBAAiB,CAAC;YAC9B;YAEA,MAAMkK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5B,mEAAmE;YACnE,MAAMqB,eAAe,MAAM5L,YAAE,CAC1BC,UAAU,CAAC,+BACXmE,SAAS,CAAC,qBAAqB,qBAAqB,QACpDlE,QAAQ,CAAC,cAAc,cAAc,QACrCA,QAAQ,CAAC,cAAc,cAAc,QACrCA,QAAQ,CAAC,6BAA6B,cAAc,QACpDE,KAAK,CAAC,kBAAkB,KAAKqC,QAC7BtC,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAkF,OAAO,CAAC,gBAAgB,QACxBkF,KAAK,CAACA,OACNC,MAAM,CAACA,QACP7H,OAAO;YAEV,6BAA6B;YAC7B,MAAMkJ,oBAAoB,MAAM7L,YAAE,CAC/BC,UAAU,CAAC,+BACXmE,SAAS,CAAC,qBAAqB,qBAAqB,QACpDhE,KAAK,CAAC,kBAAkB,KAAKqC,QAC7BtC,MAAM,CAACkE,IAAAA,WAAG,CAAQ,CAAC,QAAQ,CAAC,CAACC,EAAE,CAAC,UAChCjE,gBAAgB;YAEnB,iCAAiC;YACjC,MAAMkM,wBAAwBX,aAAahF,GAAG,CAACqC,CAAAA;gBAC7C,OAAO;oBACLN,YAAY;wBACV7I,IAAImJ,YAAYwD,aAAa;wBAC7B/G,OAAO5E,OAAOmI,YAAYtD,gBAAgB,IAAI;wBAC9CE,SAAS/E,OAAOmI,YAAYnD,kBAAkB,IAAI;wBAClDvF,QAAQ0I,YAAYyD,iBAAiB;wBACrC9G,MAAMqD,YAAY0D,eAAe;oBACnC;oBACA1D,aAAa;wBACXnJ,IAAImJ,YAAY2D,cAAc;wBAC9B1D,QAAQpI,OAAOmI,YAAYlD,kBAAkB,IAAI;wBACjDC,UAAUiD,YAAYE,oBAAoB,IAAI;wBAC9C5I,QAAQ0I,YAAY4D,kBAAkB;wBACtCjH,MAAMqD,YAAY6D,gBAAgB;wBAClCC,aAAa9D,YAAY+D,uBAAuB;oBAClD;oBACAlC,UAAU;wBACRhL,IAAImJ,YAAY5F,OAAO;wBACvB5C,MAAMwI,YAAYJ,SAAS;wBAC3BnI,OAAOuI,YAAYH,UAAU;oBAC/B;oBACAC,MAAM;wBACJjJ,IAAImJ,YAAYgE,OAAO;wBACvBxM,MAAMwI,YAAYD,SAAS;wBAC3BQ,OAAO1I,OAAOmI,YAAYiE,UAAU,IAAI;oBAC1C;oBACA3D,qBAAqBN,YAAYM,mBAAmB;gBACtD;YACF;YAEA,MAAM0B,aAAaC,KAAKC,IAAI,CAACrK,OAAO+K,mBAAmBtH,SAAS,KAAKgG;YAErE,uDAAuD;YACvD,MAAM4C,YAAY;gBAChBC,wBAAwBb,sBAAsBrC,MAAM,CAAC,CAACC,KAAK6B,IAAM7B,MAAM6B,EAAErD,UAAU,CAACjD,KAAK,EAAE;gBAC3F2H,yBAAyBd,sBAAsBrC,MAAM,CAAC,CAACC,KAAK6B,IAAM7B,MAAM6B,EAAE/C,WAAW,CAACC,MAAM,EAAE;gBAC9FoE,kBAAkBf,sBAAsBR,MAAM,CAACC,CAAAA,IAAKA,EAAErD,UAAU,CAACpI,MAAM,KAAK,QAAQ0J,MAAM;gBAC1FsD,qBAAqBhB,sBAAsBR,MAAM,CAACC,CAAAA,IAAKA,EAAErD,UAAU,CAACpI,MAAM,KAAK,WAAW0J,MAAM;gBAChGuD,yBAAyBjB,sBAAsBR,MAAM,CAACC,CAAAA,IAAKA,EAAE/C,WAAW,CAAC1I,MAAM,KAAK,QAAQ0J,MAAM;YACpG;YAEA,OAAO;gBACL1J,QAAQ;gBACRC,MAAM;oBACJoL,cAAcW;oBACdkB,YAAYN;oBACZ/B,YAAY;wBACVC,cAAcf;wBACdgB,aAAaL;wBACbM,aAAazK,OAAO+K,mBAAmBtH,SAAS;wBAChDiH,gBAAgBjB;wBAChBkB,UAAUnB,OAAOW;wBACjBS,cAAcpB,OAAO;oBACvB;gBACF;YACF;QAEF,EAAE,OAAO7E,OAAO;YACd1B,QAAQ0B,KAAK,CAAC,uCAAuCA;YACrD,MAAMA;QACR;IACF;IAn7BEkI,aAAc;QACd,aAAa;QACb,IAAI,CAAC1M,MAAM,GAAG,IAAI2M,cAAM,CAAC3L,QAAQC,GAAG,CAAC2L,SAAS,EAAE;QAEhD;IACA;AAg7BJ"}