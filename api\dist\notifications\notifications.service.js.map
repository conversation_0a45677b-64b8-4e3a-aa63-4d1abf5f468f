{"version": 3, "sources": ["../../src/notifications/notifications.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db } from '../database';\n\n@Injectable()\nexport class NotificationsService {\n\n  async getNotifications(userId: number, query: any) {\n    try {\n      const { page = 1, limit = 20, type, read } = query;\n      const limitNum = parseInt(limit, 10) || 20;\n      const pageNum = parseInt(page, 10) || 1;\n      const offset = (pageNum - 1) * limitNum;\n\n      let sql = `\n        SELECT \n          id,\n          type,\n          title,\n          message,\n          data,\n          read_at as readAt,\n          created_at as createdAt\n        FROM notifications \n        WHERE user_id = ?\n      `;\n\n      const params = [userId];\n\n      if (type) {\n        sql += ` AND type = ?`;\n        params.push(type);\n      }\n\n      if (read !== undefined) {\n        if (read === 'true') {\n          sql += ` AND read_at IS NOT NULL`;\n        } else {\n          sql += ` AND read_at IS NULL`;\n        }\n      }\n\n      sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;\n      params.push(limitNum, offset);\n\n      const [notifications] = await db.execute(sql, params);\n\n      // Check if notifications is valid\n      if (!notifications || !Array.isArray(notifications)) {\n        console.log('No notifications or invalid notifications format:', notifications);\n        return {\n          status: 'success',\n          data: {\n            notifications: [],\n            pagination: {\n              page: pageNum,\n              limit: limitNum,\n              total: 0,\n              totalPages: 0\n            }\n          }\n        };\n      }\n\n      // Get total count\n      let countSql = `SELECT COUNT(*) as total FROM notifications WHERE user_id = ?`;\n      const countParams = [userId];\n\n      if (type) {\n        countSql += ` AND type = ?`;\n        countParams.push(type);\n      }\n\n      if (read !== undefined) {\n        if (read === 'true') {\n          countSql += ` AND read_at IS NOT NULL`;\n        } else {\n          countSql += ` AND read_at IS NULL`;\n        }\n      }\n\n      const [countResult] = await db.execute(countSql, countParams);\n      const total = countResult[0]?.total || 0;\n\n      return {\n        status: 'success',\n        data: {\n          notifications: notifications.map(notification => ({\n            ...notification,\n            data: notification.data ? JSON.parse(notification.data) : null,\n            isRead: !!notification.readAt\n          })),\n          pagination: {\n            page: pageNum,\n            limit: limitNum,\n            total,\n            totalPages: Math.ceil(total / limitNum)\n          }\n        }\n      };\n    } catch (error) {\n      console.error('Error getting notifications:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get notifications'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getUnreadCount(userId: number) {\n    try {\n      const [result] = await db.execute(\n        'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND read_at IS NULL',\n        [userId]\n      );\n\n      return {\n        status: 'success',\n        data: {\n          unreadCount: result[0]?.count || 0\n        }\n      };\n    } catch (error) {\n      console.error('Error getting unread count:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get unread count'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async markAsRead(notificationIds: number[], userId: number) {\n    try {\n      if (!notificationIds || notificationIds.length === 0) {\n        throw new HttpException({\n          status: 'error',\n          message: 'No notification IDs provided'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      const placeholders = notificationIds.map(() => '?').join(',');\n      await db.execute(\n        `UPDATE notifications \n         SET read_at = NOW() \n         WHERE id IN (${placeholders}) AND user_id = ? AND read_at IS NULL`,\n        [...notificationIds, userId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Notifications marked as read'\n      };\n    } catch (error) {\n      console.error('Error marking notifications as read:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to mark notifications as read'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async markAllAsRead(userId: number) {\n    try {\n      await db.execute(\n        'UPDATE notifications SET read_at = NOW() WHERE user_id = ? AND read_at IS NULL',\n        [userId]\n      );\n\n      return {\n        status: 'success',\n        message: 'All notifications marked as read'\n      };\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to mark all notifications as read'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getNotificationSettings(userId: number) {\n    try {\n      const [settings] = await db.execute(\n        `SELECT \n          push_enabled,\n          email_enabled,\n          workout_reminders,\n          meal_reminders,\n          progress_updates,\n          social_notifications,\n          marketing_emails\n         FROM notification_settings \n         WHERE user_id = ?`,\n        [userId]\n      );\n\n      // Return default settings if none exist\n      const defaultSettings = {\n        pushEnabled: true,\n        emailEnabled: true,\n        workoutReminders: true,\n        mealReminders: true,\n        progressUpdates: true,\n        socialNotifications: true,\n        marketingEmails: false\n      };\n\n      const userSettings = settings[0] ? {\n        pushEnabled: !!settings[0].push_enabled,\n        emailEnabled: !!settings[0].email_enabled,\n        workoutReminders: !!settings[0].workout_reminders,\n        mealReminders: !!settings[0].meal_reminders,\n        progressUpdates: !!settings[0].progress_updates,\n        socialNotifications: !!settings[0].social_notifications,\n        marketingEmails: !!settings[0].marketing_emails\n      } : defaultSettings;\n\n      return {\n        status: 'success',\n        data: userSettings\n      };\n    } catch (error) {\n      console.error('Error getting notification settings:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get notification settings'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async updateNotificationSettings(userId: number, settings: any) {\n    try {\n      const {\n        pushEnabled,\n        emailEnabled,\n        workoutReminders,\n        mealReminders,\n        progressUpdates,\n        socialNotifications,\n        marketingEmails\n      } = settings;\n\n      // Check if settings exist\n      const [existing] = await db.execute(\n        'SELECT id FROM notification_settings WHERE user_id = ?',\n        [userId]\n      );\n\n      if (existing[0]) {\n        // Update existing settings\n        await db.execute(\n          `UPDATE notification_settings SET\n           push_enabled = ?,\n           email_enabled = ?,\n           workout_reminders = ?,\n           meal_reminders = ?,\n           progress_updates = ?,\n           social_notifications = ?,\n           marketing_emails = ?,\n           updated_at = NOW()\n           WHERE user_id = ?`,\n          [\n            pushEnabled, emailEnabled, workoutReminders, mealReminders,\n            progressUpdates, socialNotifications, marketingEmails, userId\n          ]\n        );\n      } else {\n        // Create new settings\n        await db.execute(\n          `INSERT INTO notification_settings (\n           user_id, push_enabled, email_enabled, workout_reminders,\n           meal_reminders, progress_updates, social_notifications,\n           marketing_emails, created_at, updated_at\n           ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,\n          [\n            userId, pushEnabled, emailEnabled, workoutReminders,\n            mealReminders, progressUpdates, socialNotifications, marketingEmails\n          ]\n        );\n      }\n\n      return {\n        status: 'success',\n        message: 'Notification settings updated successfully'\n      };\n    } catch (error) {\n      console.error('Error updating notification settings:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to update notification settings'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async subscribeToPushNotifications(userId: number, subscription: any) {\n    try {\n      const { endpoint, keys } = subscription;\n\n      // Check if subscription already exists\n      const [existing] = await db.execute(\n        'SELECT id FROM push_subscriptions WHERE user_id = ? AND endpoint = ?',\n        [userId, endpoint]\n      );\n\n      if (!existing[0]) {\n        await db.execute(\n          `INSERT INTO push_subscriptions (user_id, endpoint, p256dh_key, auth_key, created_at, updated_at)\n           VALUES (?, ?, ?, ?, NOW(), NOW())`,\n          [userId, endpoint, keys.p256dh, keys.auth]\n        );\n      }\n\n      return {\n        status: 'success',\n        message: 'Successfully subscribed to push notifications'\n      };\n    } catch (error) {\n      console.error('Error subscribing to push notifications:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to subscribe to push notifications'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async unsubscribeFromPushNotifications(userId: number, endpoint: string) {\n    try {\n      await db.execute(\n        'DELETE FROM push_subscriptions WHERE user_id = ? AND endpoint = ?',\n        [userId, endpoint]\n      );\n\n      return {\n        status: 'success',\n        message: 'Successfully unsubscribed from push notifications'\n      };\n    } catch (error) {\n      console.error('Error unsubscribing from push notifications:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to unsubscribe from push notifications'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async sendTestNotification(userId: number) {\n    try {\n      await this.createNotification(userId, {\n        type: 'test',\n        title: 'Notificação de Teste',\n        message: 'Esta é uma notificação de teste para verificar se o sistema está funcionando corretamente.',\n        data: {\n          testData: true,\n          timestamp: new Date().toISOString()\n        }\n      });\n\n      return {\n        status: 'success',\n        message: 'Test notification sent successfully'\n      };\n    } catch (error) {\n      console.error('Error sending test notification:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to send test notification'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  // Helper method to create notifications\n  async createNotification(userId: number, notification: {\n    type: string;\n    title: string;\n    message: string;\n    data?: any;\n  }) {\n    try {\n      await db.execute(\n        `INSERT INTO notifications (user_id, type, title, message, data, created_at)\n         VALUES (?, ?, ?, ?, ?, NOW())`,\n        [\n          userId,\n          notification.type,\n          notification.title,\n          notification.message,\n          notification.data ? JSON.stringify(notification.data) : null\n        ]\n      );\n\n      // Here you would also trigger push notifications if enabled\n      // await this.sendPushNotification(userId, notification);\n\n      return true;\n    } catch (error) {\n      console.error('Error creating notification:', error);\n      return false;\n    }\n  }\n\n  // Helper method for sending push notifications (to be implemented)\n  private async sendPushNotification(userId: number, notification: any) {\n    // Implementation for sending actual push notifications\n    // This would use web-push library or similar\n    console.log('Push notification would be sent:', { userId, notification });\n  }\n}\n"], "names": ["NotificationsService", "getNotifications", "userId", "query", "page", "limit", "type", "read", "limitNum", "parseInt", "pageNum", "offset", "sql", "params", "push", "undefined", "notifications", "db", "execute", "Array", "isArray", "console", "log", "status", "data", "pagination", "total", "totalPages", "countSql", "countParams", "count<PERSON><PERSON><PERSON>", "map", "notification", "JSON", "parse", "isRead", "readAt", "Math", "ceil", "error", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "getUnreadCount", "result", "unreadCount", "count", "mark<PERSON><PERSON><PERSON>", "notificationIds", "length", "BAD_REQUEST", "placeholders", "join", "markAllAsRead", "getNotificationSettings", "settings", "defaultSettings", "pushEnabled", "emailEnabled", "workoutReminders", "meal<PERSON><PERSON><PERSON>s", "progressUpdates", "socialNotifications", "marketingEmails", "userSettings", "push_enabled", "email_enabled", "workout_reminders", "meal_reminders", "progress_updates", "social_notifications", "marketing_emails", "updateNotificationSettings", "existing", "subscribeToPushNotifications", "subscription", "endpoint", "keys", "p256dh", "auth", "unsubscribeFromPushNotifications", "sendTestNotification", "createNotification", "title", "testData", "timestamp", "Date", "toISOString", "stringify", "sendPushNotification"], "mappings": ";;;;+BAIaA;;;eAAAA;;;wBAJyC;0BACnC;;;;;;;AAGZ,IAAA,AAAMA,uBAAN,MAAMA;IAEX,MAAMC,iBAAiBC,MAAc,EAAEC,KAAU,EAAE;QACjD,IAAI;YACF,MAAM,EAAEC,OAAO,CAAC,EAAEC,QAAQ,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE,GAAGJ;YAC7C,MAAMK,WAAWC,SAASJ,OAAO,OAAO;YACxC,MAAMK,UAAUD,SAASL,MAAM,OAAO;YACtC,MAAMO,SAAS,AAACD,CAAAA,UAAU,CAAA,IAAKF;YAE/B,IAAII,MAAM,CAAC;;;;;;;;;;;MAWX,CAAC;YAED,MAAMC,SAAS;gBAACX;aAAO;YAEvB,IAAII,MAAM;gBACRM,OAAO,CAAC,aAAa,CAAC;gBACtBC,OAAOC,IAAI,CAACR;YACd;YAEA,IAAIC,SAASQ,WAAW;gBACtB,IAAIR,SAAS,QAAQ;oBACnBK,OAAO,CAAC,wBAAwB,CAAC;gBACnC,OAAO;oBACLA,OAAO,CAAC,oBAAoB,CAAC;gBAC/B;YACF;YAEAA,OAAO,CAAC,0CAA0C,CAAC;YACnDC,OAAOC,IAAI,CAACN,UAAUG;YAEtB,MAAM,CAACK,cAAc,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACN,KAAKC;YAE9C,kCAAkC;YAClC,IAAI,CAACG,iBAAiB,CAACG,MAAMC,OAAO,CAACJ,gBAAgB;gBACnDK,QAAQC,GAAG,CAAC,qDAAqDN;gBACjE,OAAO;oBACLO,QAAQ;oBACRC,MAAM;wBACJR,eAAe,EAAE;wBACjBS,YAAY;4BACVrB,MAAMM;4BACNL,OAAOG;4BACPkB,OAAO;4BACPC,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,kBAAkB;YAClB,IAAIC,WAAW,CAAC,6DAA6D,CAAC;YAC9E,MAAMC,cAAc;gBAAC3B;aAAO;YAE5B,IAAII,MAAM;gBACRsB,YAAY,CAAC,aAAa,CAAC;gBAC3BC,YAAYf,IAAI,CAACR;YACnB;YAEA,IAAIC,SAASQ,WAAW;gBACtB,IAAIR,SAAS,QAAQ;oBACnBqB,YAAY,CAAC,wBAAwB,CAAC;gBACxC,OAAO;oBACLA,YAAY,CAAC,oBAAoB,CAAC;gBACpC;YACF;YAEA,MAAM,CAACE,YAAY,GAAG,MAAMb,YAAE,CAACC,OAAO,CAACU,UAAUC;YACjD,MAAMH,QAAQI,WAAW,CAAC,EAAE,EAAEJ,SAAS;YAEvC,OAAO;gBACLH,QAAQ;gBACRC,MAAM;oBACJR,eAAeA,cAAce,GAAG,CAACC,CAAAA,eAAiB,CAAA;4BAChD,GAAGA,YAAY;4BACfR,MAAMQ,aAAaR,IAAI,GAAGS,KAAKC,KAAK,CAACF,aAAaR,IAAI,IAAI;4BAC1DW,QAAQ,CAAC,CAACH,aAAaI,MAAM;wBAC/B,CAAA;oBACAX,YAAY;wBACVrB,MAAMM;wBACNL,OAAOG;wBACPkB;wBACAC,YAAYU,KAAKC,IAAI,CAACZ,QAAQlB;oBAChC;gBACF;YACF;QACF,EAAE,OAAO+B,OAAO;YACdlB,QAAQkB,KAAK,CAAC,gCAAgCA;YAC9C,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMC,eAAe1C,MAAc,EAAE;QACnC,IAAI;YACF,MAAM,CAAC2C,OAAO,GAAG,MAAM5B,YAAE,CAACC,OAAO,CAC/B,qFACA;gBAAChB;aAAO;YAGV,OAAO;gBACLqB,QAAQ;gBACRC,MAAM;oBACJsB,aAAaD,MAAM,CAAC,EAAE,EAAEE,SAAS;gBACnC;YACF;QACF,EAAE,OAAOR,OAAO;YACdlB,QAAQkB,KAAK,CAAC,+BAA+BA;YAC7C,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMK,WAAWC,eAAyB,EAAE/C,MAAc,EAAE;QAC1D,IAAI;YACF,IAAI,CAAC+C,mBAAmBA,gBAAgBC,MAAM,KAAK,GAAG;gBACpD,MAAM,IAAIV,qBAAa,CAAC;oBACtBjB,QAAQ;oBACRkB,SAAS;gBACX,GAAGC,kBAAU,CAACS,WAAW;YAC3B;YAEA,MAAMC,eAAeH,gBAAgBlB,GAAG,CAAC,IAAM,KAAKsB,IAAI,CAAC;YACzD,MAAMpC,YAAE,CAACC,OAAO,CACd,CAAC;;sBAEa,EAAEkC,aAAa,qCAAqC,CAAC,EACnE;mBAAIH;gBAAiB/C;aAAO;YAG9B,OAAO;gBACLqB,QAAQ;gBACRkB,SAAS;YACX;QACF,EAAE,OAAOF,OAAO;YACdlB,QAAQkB,KAAK,CAAC,wCAAwCA;YACtD,IAAIA,iBAAiBC,qBAAa,EAAE,MAAMD;YAE1C,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMW,cAAcpD,MAAc,EAAE;QAClC,IAAI;YACF,MAAMe,YAAE,CAACC,OAAO,CACd,kFACA;gBAAChB;aAAO;YAGV,OAAO;gBACLqB,QAAQ;gBACRkB,SAAS;YACX;QACF,EAAE,OAAOF,OAAO;YACdlB,QAAQkB,KAAK,CAAC,4CAA4CA;YAC1D,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMY,wBAAwBrD,MAAc,EAAE;QAC5C,IAAI;YACF,MAAM,CAACsD,SAAS,GAAG,MAAMvC,YAAE,CAACC,OAAO,CACjC,CAAC;;;;;;;;;0BASiB,CAAC,EACnB;gBAAChB;aAAO;YAGV,wCAAwC;YACxC,MAAMuD,kBAAkB;gBACtBC,aAAa;gBACbC,cAAc;gBACdC,kBAAkB;gBAClBC,eAAe;gBACfC,iBAAiB;gBACjBC,qBAAqB;gBACrBC,iBAAiB;YACnB;YAEA,MAAMC,eAAeT,QAAQ,CAAC,EAAE,GAAG;gBACjCE,aAAa,CAAC,CAACF,QAAQ,CAAC,EAAE,CAACU,YAAY;gBACvCP,cAAc,CAAC,CAACH,QAAQ,CAAC,EAAE,CAACW,aAAa;gBACzCP,kBAAkB,CAAC,CAACJ,QAAQ,CAAC,EAAE,CAACY,iBAAiB;gBACjDP,eAAe,CAAC,CAACL,QAAQ,CAAC,EAAE,CAACa,cAAc;gBAC3CP,iBAAiB,CAAC,CAACN,QAAQ,CAAC,EAAE,CAACc,gBAAgB;gBAC/CP,qBAAqB,CAAC,CAACP,QAAQ,CAAC,EAAE,CAACe,oBAAoB;gBACvDP,iBAAiB,CAAC,CAACR,QAAQ,CAAC,EAAE,CAACgB,gBAAgB;YACjD,IAAIf;YAEJ,OAAO;gBACLlC,QAAQ;gBACRC,MAAMyC;YACR;QACF,EAAE,OAAO1B,OAAO;YACdlB,QAAQkB,KAAK,CAAC,wCAAwCA;YACtD,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM8B,2BAA2BvE,MAAc,EAAEsD,QAAa,EAAE;QAC9D,IAAI;YACF,MAAM,EACJE,WAAW,EACXC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,mBAAmB,EACnBC,eAAe,EAChB,GAAGR;YAEJ,0BAA0B;YAC1B,MAAM,CAACkB,SAAS,GAAG,MAAMzD,YAAE,CAACC,OAAO,CACjC,0DACA;gBAAChB;aAAO;YAGV,IAAIwE,QAAQ,CAAC,EAAE,EAAE;gBACf,2BAA2B;gBAC3B,MAAMzD,YAAE,CAACC,OAAO,CACd,CAAC;;;;;;;;;4BASiB,CAAC,EACnB;oBACEwC;oBAAaC;oBAAcC;oBAAkBC;oBAC7CC;oBAAiBC;oBAAqBC;oBAAiB9D;iBACxD;YAEL,OAAO;gBACL,sBAAsB;gBACtB,MAAMe,YAAE,CAACC,OAAO,CACd,CAAC;;;;0DAI+C,CAAC,EACjD;oBACEhB;oBAAQwD;oBAAaC;oBAAcC;oBACnCC;oBAAeC;oBAAiBC;oBAAqBC;iBACtD;YAEL;YAEA,OAAO;gBACLzC,QAAQ;gBACRkB,SAAS;YACX;QACF,EAAE,OAAOF,OAAO;YACdlB,QAAQkB,KAAK,CAAC,yCAAyCA;YACvD,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMgC,6BAA6BzE,MAAc,EAAE0E,YAAiB,EAAE;QACpE,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGF;YAE3B,uCAAuC;YACvC,MAAM,CAACF,SAAS,GAAG,MAAMzD,YAAE,CAACC,OAAO,CACjC,wEACA;gBAAChB;gBAAQ2E;aAAS;YAGpB,IAAI,CAACH,QAAQ,CAAC,EAAE,EAAE;gBAChB,MAAMzD,YAAE,CAACC,OAAO,CACd,CAAC;4CACiC,CAAC,EACnC;oBAAChB;oBAAQ2E;oBAAUC,KAAKC,MAAM;oBAAED,KAAKE,IAAI;iBAAC;YAE9C;YAEA,OAAO;gBACLzD,QAAQ;gBACRkB,SAAS;YACX;QACF,EAAE,OAAOF,OAAO;YACdlB,QAAQkB,KAAK,CAAC,4CAA4CA;YAC1D,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMsC,iCAAiC/E,MAAc,EAAE2E,QAAgB,EAAE;QACvE,IAAI;YACF,MAAM5D,YAAE,CAACC,OAAO,CACd,qEACA;gBAAChB;gBAAQ2E;aAAS;YAGpB,OAAO;gBACLtD,QAAQ;gBACRkB,SAAS;YACX;QACF,EAAE,OAAOF,OAAO;YACdlB,QAAQkB,KAAK,CAAC,gDAAgDA;YAC9D,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMuC,qBAAqBhF,MAAc,EAAE;QACzC,IAAI;YACF,MAAM,IAAI,CAACiF,kBAAkB,CAACjF,QAAQ;gBACpCI,MAAM;gBACN8E,OAAO;gBACP3C,SAAS;gBACTjB,MAAM;oBACJ6D,UAAU;oBACVC,WAAW,IAAIC,OAAOC,WAAW;gBACnC;YACF;YAEA,OAAO;gBACLjE,QAAQ;gBACRkB,SAAS;YACX;QACF,EAAE,OAAOF,OAAO;YACdlB,QAAQkB,KAAK,CAAC,oCAAoCA;YAClD,MAAM,IAAIC,qBAAa,CAAC;gBACtBjB,QAAQ;gBACRkB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,wCAAwC;IACxC,MAAMwC,mBAAmBjF,MAAc,EAAE8B,YAKxC,EAAE;QACD,IAAI;YACF,MAAMf,YAAE,CAACC,OAAO,CACd,CAAC;sCAC6B,CAAC,EAC/B;gBACEhB;gBACA8B,aAAa1B,IAAI;gBACjB0B,aAAaoD,KAAK;gBAClBpD,aAAaS,OAAO;gBACpBT,aAAaR,IAAI,GAAGS,KAAKwD,SAAS,CAACzD,aAAaR,IAAI,IAAI;aACzD;YAGH,4DAA4D;YAC5D,yDAAyD;YAEzD,OAAO;QACT,EAAE,OAAOe,OAAO;YACdlB,QAAQkB,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAcmD,qBAAqBxF,MAAc,EAAE8B,YAAiB,EAAE;QACpE,uDAAuD;QACvD,6CAA6C;QAC7CX,QAAQC,GAAG,CAAC,oCAAoC;YAAEpB;YAAQ8B;QAAa;IACzE;AACF"}