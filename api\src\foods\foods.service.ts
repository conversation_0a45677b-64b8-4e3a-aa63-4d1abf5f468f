import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { db } from '../database';
import axios from 'axios';

@Injectable()
export class FoodsService {

  async searchFoods(query: { q: string; limit?: number; source?: string }) {
    try {
      const { q, limit = 20, source } = query;
      
      if (!q || q.length < 2) {
        return {
          status: 'success',
          data: []
        };
      }

      // Search in local database first
      const localResults = await this.searchLocalFoods(q, limit);
      
      // If source is specified, search only that source
      if (source) {
        switch (source) {
          case 'nutritionix':
            return this.searchNutritionix(q);
          case 'edamam':
            return this.searchEdamam(q);
          case 'openfoodfacts':
            return this.searchOpenFoodFacts(q);
          default:
            return localResults;
        }
      }

      // Return local results for now
      return localResults;
    } catch (error) {
      console.error('Error searching foods:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to search foods'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async searchLocalFoods(query: string, limit: number) {
    try {
      const sql = `
        SELECT 
          id,
          name,
          brand,
          category,
          calories_per_100g as caloriesPer100g,
          protein_per_100g as proteinPer100g,
          carbs_per_100g as carbsPer100g,
          fat_per_100g as fatPer100g,
          fiber_per_100g as fiberPer100g,
          sugar_per_100g as sugarPer100g,
          sodium_per_100g as sodiumPer100g,
          serving_size as servingSize,
          serving_unit as servingUnit,
          barcode,
          image_url as imageUrl,
          source
        FROM foods 
        WHERE name LIKE ? OR brand LIKE ?
        ORDER BY 
          CASE 
            WHEN name LIKE ? THEN 1
            WHEN name LIKE ? THEN 2
            ELSE 3
          END,
          name ASC
        LIMIT ?
      `;

      const searchPattern = `%${query}%`;
      const exactPattern = `${query}%`;
      
      const [results] = await db.execute(sql, [
        searchPattern, searchPattern, exactPattern, searchPattern, limit
      ]);

      return {
        status: 'success',
        data: results
      };
    } catch (error) {
      console.error('Error searching local foods:', error);
      return {
        status: 'success',
        data: []
      };
    }
  }

  async searchNutritionix(query: string) {
    try {
      // This would integrate with Nutritionix API
      // For now, return mock data
      return {
        status: 'success',
        data: [
          {
            id: `nutritionix_${Date.now()}`,
            name: `${query} (Nutritionix)`,
            brand: 'Nutritionix',
            caloriesPer100g: 200,
            proteinPer100g: 10,
            carbsPer100g: 30,
            fatPer100g: 5,
            source: 'nutritionix'
          }
        ]
      };
    } catch (error) {
      console.error('Error searching Nutritionix:', error);
      return {
        status: 'success',
        data: []
      };
    }
  }

  async searchEdamam(query: string) {
    try {
      // This would integrate with Edamam API
      // For now, return mock data
      return {
        status: 'success',
        data: [
          {
            id: `edamam_${Date.now()}`,
            name: `${query} (Edamam)`,
            brand: 'Edamam',
            caloriesPer100g: 180,
            proteinPer100g: 8,
            carbsPer100g: 25,
            fatPer100g: 7,
            source: 'edamam'
          }
        ]
      };
    } catch (error) {
      console.error('Error searching Edamam:', error);
      return {
        status: 'success',
        data: []
      };
    }
  }

  async searchOpenFoodFacts(query: string) {
    try {
      // This would integrate with Open Food Facts API
      // For now, return mock data
      return {
        status: 'success',
        data: [
          {
            id: `openfoodfacts_${Date.now()}`,
            name: `${query} (Open Food Facts)`,
            brand: 'Open Food Facts',
            caloriesPer100g: 220,
            proteinPer100g: 12,
            carbsPer100g: 35,
            fatPer100g: 6,
            source: 'openfoodfacts'
          }
        ]
      };
    } catch (error) {
      console.error('Error searching Open Food Facts:', error);
      return {
        status: 'success',
        data: []
      };
    }
  }

  async createCustomFood(foodData: any, userId: number) {
    try {
      const {
        name,
        brand,
        category,
        caloriesPer100g,
        proteinPer100g,
        carbsPer100g,
        fatPer100g,
        fiberPer100g,
        sugarPer100g,
        sodiumPer100g,
        servingSize,
        servingUnit,
        barcode,
        imageUrl
      } = foodData;

      const [result] = await db.execute(
        `INSERT INTO foods (
          name, brand, category, calories_per_100g, protein_per_100g,
          carbs_per_100g, fat_per_100g, fiber_per_100g, sugar_per_100g,
          sodium_per_100g, serving_size, serving_unit, barcode, image_url,
          source, created_by, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'custom', ?, NOW(), NOW())`,
        [
          name, brand, category, caloriesPer100g, proteinPer100g,
          carbsPer100g, fatPer100g, fiberPer100g, sugarPer100g,
          sodiumPer100g, servingSize, servingUnit, barcode, imageUrl, userId
        ]
      );

      return {
        status: 'success',
        message: 'Custom food created successfully',
        data: {
          id: result.insertId,
          ...foodData
        }
      };
    } catch (error) {
      console.error('Error creating custom food:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to create custom food'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getCustomFoods(userId: number) {
    try {
      const sql = `
        SELECT 
          id,
          name,
          brand,
          category,
          calories_per_100g as caloriesPer100g,
          protein_per_100g as proteinPer100g,
          carbs_per_100g as carbsPer100g,
          fat_per_100g as fatPer100g,
          fiber_per_100g as fiberPer100g,
          sugar_per_100g as sugarPer100g,
          sodium_per_100g as sodiumPer100g,
          serving_size as servingSize,
          serving_unit as servingUnit,
          barcode,
          image_url as imageUrl,
          created_at as createdAt
        FROM foods 
        WHERE created_by = ? AND source = 'custom'
        ORDER BY created_at DESC
      `;

      const [results] = await db.execute(sql, [userId]);

      return {
        status: 'success',
        data: results
      };
    } catch (error) {
      console.error('Error getting custom foods:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get custom foods'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async importFoodDatabase(importData: any) {
    try {
      // This would handle bulk import of food data
      // For now, return success
      return {
        status: 'success',
        message: 'Food database import initiated',
        data: {
          imported: 0,
          skipped: 0,
          errors: 0
        }
      };
    } catch (error) {
      console.error('Error importing food database:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to import food database'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getFoodCategories() {
    try {
      const sql = `
        SELECT DISTINCT category
        FROM foods 
        WHERE category IS NOT NULL AND category != ''
        ORDER BY category ASC
      `;

      const [results] = await db.execute(sql);

      return {
        status: 'success',
        data: results.map(row => row.category)
      };
    } catch (error) {
      console.error('Error getting food categories:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get food categories'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPopularFoods(query: { limit?: number }) {
    try {
      const { limit = 20 } = query;

      // This would be based on usage statistics
      // For now, return some common foods
      const sql = `
        SELECT 
          id,
          name,
          brand,
          category,
          calories_per_100g as caloriesPer100g,
          protein_per_100g as proteinPer100g,
          carbs_per_100g as carbsPer100g,
          fat_per_100g as fatPer100g,
          image_url as imageUrl,
          source
        FROM foods 
        WHERE source != 'custom'
        ORDER BY name ASC
        LIMIT ?
      `;

      const [results] = await db.execute(sql, [limit]);

      return {
        status: 'success',
        data: results
      };
    } catch (error) {
      console.error('Error getting popular foods:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get popular foods'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getRecentFoods(userId: number, query: { limit?: number }) {
    try {
      const { limit = 10 } = query;

      // This would get recently used foods by the user
      // For now, return recent custom foods
      const sql = `
        SELECT DISTINCT
          f.id,
          f.name,
          f.brand,
          f.category,
          f.calories_per_100g as caloriesPer100g,
          f.protein_per_100g as proteinPer100g,
          f.carbs_per_100g as carbsPer100g,
          f.fat_per_100g as fatPer100g,
          f.image_url as imageUrl,
          f.source,
          MAX(dl.created_at) as lastUsed
        FROM foods f
        LEFT JOIN diary_logs dl ON f.id = dl.food_id AND dl.user_id = ?
        WHERE dl.food_id IS NOT NULL
        ORDER BY lastUsed DESC
        LIMIT ?
      `;

      const [results] = await db.execute(sql, [userId, limit]);

      return {
        status: 'success',
        data: results
      };
    } catch (error) {
      console.error('Error getting recent foods:', error);
      // Return empty array instead of error for better UX
      return {
        status: 'success',
        data: []
      };
    }
  }
}
