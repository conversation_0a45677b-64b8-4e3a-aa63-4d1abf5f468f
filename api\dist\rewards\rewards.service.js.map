{"version": 3, "sources": ["../../src/rewards/rewards.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db } from '../database';\n\n@Injectable()\nexport class RewardsService {\n\n  async getAvailableRewards(userId: number, query: any) {\n    try {\n      const { category, minCost, maxCost, page = 1, limit = 20 } = query;\n      const offset = (page - 1) * limit;\n\n      // Get user's current points\n      const [userPoints] = await db.execute(\n        'SELECT total_points FROM user_points WHERE user_id = ?',\n        [userId]\n      );\n\n      const currentPoints = userPoints[0]?.total_points || 0;\n\n      let sql = `\n        SELECT \n          r.id,\n          r.title,\n          r.description,\n          r.category,\n          r.cost_points as costPoints,\n          r.image_url as imageUrl,\n          r.terms_conditions as termsConditions,\n          r.expiry_days as expiryDays,\n          r.max_redemptions as maxRedemptions,\n          r.status,\n          CASE WHEN r.cost_points <= ? THEN true ELSE false END as canAfford,\n          COUNT(ur.id) as timesRedeemed\n        FROM rewards r\n        LEFT JOIN user_rewards ur ON r.id = ur.reward_id AND ur.user_id = ?\n        WHERE r.status = 'active'\n      `;\n\n      const params = [currentPoints, userId];\n\n      if (category) {\n        sql += ` AND r.category = ?`;\n        params.push(category);\n      }\n\n      if (minCost) {\n        sql += ` AND r.cost_points >= ?`;\n        params.push(minCost);\n      }\n\n      if (maxCost) {\n        sql += ` AND r.cost_points <= ?`;\n        params.push(maxCost);\n      }\n\n      sql += ` GROUP BY r.id ORDER BY r.cost_points ASC LIMIT ? OFFSET ?`;\n      params.push(limit, offset);\n\n      const [rewards] = await db.execute(sql, params);\n\n      // Get total count\n      let countSql = `SELECT COUNT(*) as total FROM rewards WHERE status = 'active'`;\n      const countParams: any[] = [];\n\n      if (category) {\n        countSql += ` AND category = ?`;\n        countParams.push(category);\n      }\n\n      if (minCost) {\n        countSql += ` AND cost_points >= ?`;\n        countParams.push(minCost);\n      }\n\n      if (maxCost) {\n        countSql += ` AND cost_points <= ?`;\n        countParams.push(maxCost);\n      }\n\n      const [countResult] = await db.execute(countSql, countParams);\n      const total = countResult[0]?.total || 0;\n\n      return {\n        status: 'success',\n        data: {\n          rewards,\n          userPoints: currentPoints,\n          pagination: {\n            page: Number(page),\n            limit: Number(limit),\n            total,\n            totalPages: Math.ceil(total / limit)\n          }\n        }\n      };\n    } catch (error) {\n      console.error('Error getting available rewards:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get available rewards'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getMyRewards(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          ur.id,\n          ur.redeemed_at as redeemedAt,\n          ur.used_at as usedAt,\n          ur.expires_at as expiresAt,\n          ur.status,\n          ur.redemption_code as redemptionCode,\n          r.title,\n          r.description,\n          r.category,\n          r.cost_points as costPoints,\n          r.image_url as imageUrl,\n          r.terms_conditions as termsConditions,\n          CASE \n            WHEN ur.expires_at < NOW() THEN 'expired'\n            WHEN ur.used_at IS NOT NULL THEN 'used'\n            ELSE 'active'\n          END as currentStatus\n        FROM user_rewards ur\n        INNER JOIN rewards r ON ur.reward_id = r.id\n        WHERE ur.user_id = ?\n        ORDER BY ur.redeemed_at DESC\n      `;\n\n      const [rewards] = await db.execute(sql, [userId]);\n\n      // Group by status\n      const groupedRewards = {\n        active: rewards.filter(r => r.currentStatus === 'active'),\n        used: rewards.filter(r => r.currentStatus === 'used'),\n        expired: rewards.filter(r => r.currentStatus === 'expired')\n      };\n\n      return {\n        status: 'success',\n        data: {\n          rewards,\n          grouped: groupedRewards,\n          summary: {\n            total: rewards.length,\n            active: groupedRewards.active.length,\n            used: groupedRewards.used.length,\n            expired: groupedRewards.expired.length\n          }\n        }\n      };\n    } catch (error) {\n      console.error('Error getting my rewards:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get my rewards'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async redeemReward(rewardId: number, userId: number) {\n    try {\n      // Get reward details\n      const [reward] = await db.execute(\n        'SELECT id, title, cost_points, max_redemptions, expiry_days, status FROM rewards WHERE id = ?',\n        [rewardId]\n      );\n\n      if (!reward[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Reward not found'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      if (reward[0].status !== 'active') {\n        throw new HttpException({\n          status: 'error',\n          message: 'Reward is not available'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      // Check user's points\n      const [userPoints] = await db.execute(\n        'SELECT total_points FROM user_points WHERE user_id = ?',\n        [userId]\n      );\n\n      const currentPoints = userPoints[0]?.total_points || 0;\n\n      if (currentPoints < reward[0].cost_points) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Insufficient points'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      // Check redemption limit\n      if (reward[0].max_redemptions > 0) {\n        const [redemptionCount] = await db.execute(\n          'SELECT COUNT(*) as count FROM user_rewards WHERE user_id = ? AND reward_id = ?',\n          [userId, rewardId]\n        );\n\n        if (redemptionCount[0].count >= reward[0].max_redemptions) {\n          throw new HttpException({\n            status: 'error',\n            message: 'Redemption limit reached for this reward'\n          }, HttpStatus.BAD_REQUEST);\n        }\n      }\n\n      // Calculate expiry date\n      const expiresAt = reward[0].expiry_days > 0 ? \n        new Date(Date.now() + (reward[0].expiry_days * 24 * 60 * 60 * 1000)) : null;\n\n      // Generate redemption code\n      const redemptionCode = this.generateRedemptionCode();\n\n      // Start transaction\n      await db.execute('START TRANSACTION');\n\n      try {\n        // Deduct points\n        await db.execute(\n          'UPDATE user_points SET total_points = total_points - ?, updated_at = NOW() WHERE user_id = ?',\n          [reward[0].cost_points, userId]\n        );\n\n        // Log points transaction\n        await db.execute(\n          `INSERT INTO points_transactions (user_id, points, type, reason, created_at)\n           VALUES (?, ?, 'spent', ?, NOW())`,\n          [userId, reward[0].cost_points, `Redeemed: ${reward[0].title}`]\n        );\n\n        // Create user reward\n        const [userRewardResult] = await db.execute(\n          `INSERT INTO user_rewards (\n            user_id, reward_id, redeemed_at, expires_at, \n            status, redemption_code, created_at\n          ) VALUES (?, ?, NOW(), ?, 'redeemed', ?, NOW())`,\n          [userId, rewardId, expiresAt?.toISOString() || null, redemptionCode]\n        );\n\n        await db.execute('COMMIT');\n\n        // Create notification\n        await this.createRewardNotification(userId, reward[0].title, redemptionCode);\n\n        return {\n          status: 'success',\n          message: 'Reward redeemed successfully',\n          data: {\n            id: userRewardResult.insertId,\n            rewardTitle: reward[0].title,\n            costPoints: reward[0].cost_points,\n            redemptionCode,\n            expiresAt,\n            remainingPoints: currentPoints - reward[0].cost_points\n          }\n        };\n      } catch (error) {\n        await db.execute('ROLLBACK');\n        throw error;\n      }\n    } catch (error) {\n      console.error('Error redeeming reward:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to redeem reward'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getRewardCategories() {\n    try {\n      const sql = `\n        SELECT \n          category,\n          COUNT(*) as rewardCount,\n          MIN(cost_points) as minCost,\n          MAX(cost_points) as maxCost,\n          AVG(cost_points) as avgCost\n        FROM rewards \n        WHERE status = 'active'\n        GROUP BY category\n        ORDER BY category ASC\n      `;\n\n      const [categories] = await db.execute(sql);\n\n      return {\n        status: 'success',\n        data: categories\n      };\n    } catch (error) {\n      console.error('Error getting reward categories:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get reward categories'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  private generateRedemptionCode(): string {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    let result = '';\n    for (let i = 0; i < 8; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n  }\n\n  private async createRewardNotification(userId: number, rewardTitle: string, redemptionCode: string) {\n    try {\n      await db.execute(\n        `INSERT INTO notifications (user_id, type, title, message, data, created_at)\n         VALUES (?, 'reward_redeemed', 'Recompensa Resgatada!', ?, ?, NOW())`,\n        [\n          userId,\n          `Você resgatou: ${rewardTitle}. Código: ${redemptionCode}`,\n          JSON.stringify({ \n            rewardTitle, \n            redemptionCode, \n            type: 'reward_redeemed' \n          })\n        ]\n      );\n    } catch (error) {\n      console.error('Error creating reward notification:', error);\n    }\n  }\n\n  // Admin methods for managing rewards\n  async createReward(rewardData: any) {\n    try {\n      const {\n        title,\n        description,\n        category,\n        costPoints,\n        imageUrl,\n        termsConditions,\n        expiryDays,\n        maxRedemptions\n      } = rewardData;\n\n      const [result] = await db.execute(\n        `INSERT INTO rewards (\n          title, description, category, cost_points, image_url,\n          terms_conditions, expiry_days, max_redemptions, status,\n          created_at, updated_at\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW(), NOW())`,\n        [\n          title, description, category, costPoints, imageUrl,\n          termsConditions, expiryDays, maxRedemptions\n        ]\n      );\n\n      return {\n        status: 'success',\n        message: 'Reward created successfully',\n        data: {\n          id: result.insertId,\n          ...rewardData\n        }\n      };\n    } catch (error) {\n      console.error('Error creating reward:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to create reward'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async updateReward(rewardId: number, updateData: any) {\n    try {\n      const updates: string[] = [];\n      const params: any[] = [];\n\n      Object.keys(updateData).forEach(key => {\n        if (updateData[key] !== undefined) {\n          const dbKey = key === 'costPoints' ? 'cost_points' :\n                       key === 'imageUrl' ? 'image_url' :\n                       key === 'termsConditions' ? 'terms_conditions' :\n                       key === 'expiryDays' ? 'expiry_days' :\n                       key === 'maxRedemptions' ? 'max_redemptions' : key;\n          updates.push(`${dbKey} = ?`);\n          params.push(updateData[key]);\n        }\n      });\n\n      if (updates.length === 0) {\n        throw new HttpException({\n          status: 'error',\n          message: 'No fields to update'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      params.push(rewardId);\n\n      await db.execute(\n        `UPDATE rewards SET ${updates.join(', ')}, updated_at = NOW() WHERE id = ?`,\n        params\n      );\n\n      return {\n        status: 'success',\n        message: 'Reward updated successfully'\n      };\n    } catch (error) {\n      console.error('Error updating reward:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to update reward'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async deleteReward(rewardId: number) {\n    try {\n      await db.execute(\n        'UPDATE rewards SET status = \"inactive\", updated_at = NOW() WHERE id = ?',\n        [rewardId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Reward deactivated successfully'\n      };\n    } catch (error) {\n      console.error('Error deleting reward:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to delete reward'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  // Method to expire old rewards (to be called by cron job)\n  async expireOldRewards() {\n    try {\n      await db.execute(\n        `UPDATE user_rewards \n         SET status = 'expired' \n         WHERE expires_at < NOW() AND status = 'redeemed'`\n      );\n\n      console.log('Expired old rewards');\n      return true;\n    } catch (error) {\n      console.error('Error expiring old rewards:', error);\n      return false;\n    }\n  }\n}\n"], "names": ["RewardsService", "getAvailableRewards", "userId", "query", "category", "minCost", "maxCost", "page", "limit", "offset", "userPoints", "db", "execute", "currentPoints", "total_points", "sql", "params", "push", "rewards", "countSql", "countParams", "count<PERSON><PERSON><PERSON>", "total", "status", "data", "pagination", "Number", "totalPages", "Math", "ceil", "error", "console", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "getMyRewards", "groupedRewards", "active", "filter", "r", "currentStatus", "used", "expired", "grouped", "summary", "length", "redeemReward", "rewardId", "reward", "NOT_FOUND", "BAD_REQUEST", "cost_points", "max_redemptions", "redemptionCount", "count", "expiresAt", "expiry_days", "Date", "now", "redemptionCode", "generateRedemptionCode", "title", "userRewardResult", "toISOString", "createRewardNotification", "id", "insertId", "rewardTitle", "costPoints", "remainingPoints", "getRewardCategories", "categories", "chars", "result", "i", "char<PERSON>t", "floor", "random", "JSON", "stringify", "type", "createReward", "rewardData", "description", "imageUrl", "termsConditions", "expiryDays", "maxRedemptions", "updateReward", "updateData", "updates", "Object", "keys", "for<PERSON>ach", "key", "undefined", "db<PERSON><PERSON>", "join", "deleteReward", "expireOldRewards", "log"], "mappings": ";;;;+BAIaA;;;eAAAA;;;wBAJyC;0BACnC;;;;;;;AAGZ,IAAA,AAAMA,iBAAN,MAAMA;IAEX,MAAMC,oBAAoBC,MAAc,EAAEC,KAAU,EAAE;QACpD,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC,EAAEC,QAAQ,EAAE,EAAE,GAAGL;YAC7D,MAAMM,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5B,4BAA4B;YAC5B,MAAM,CAACE,WAAW,GAAG,MAAMC,YAAE,CAACC,OAAO,CACnC,0DACA;gBAACV;aAAO;YAGV,MAAMW,gBAAgBH,UAAU,CAAC,EAAE,EAAEI,gBAAgB;YAErD,IAAIC,MAAM,CAAC;;;;;;;;;;;;;;;;;MAiBX,CAAC;YAED,MAAMC,SAAS;gBAACH;gBAAeX;aAAO;YAEtC,IAAIE,UAAU;gBACZW,OAAO,CAAC,mBAAmB,CAAC;gBAC5BC,OAAOC,IAAI,CAACb;YACd;YAEA,IAAIC,SAAS;gBACXU,OAAO,CAAC,uBAAuB,CAAC;gBAChCC,OAAOC,IAAI,CAACZ;YACd;YAEA,IAAIC,SAAS;gBACXS,OAAO,CAAC,uBAAuB,CAAC;gBAChCC,OAAOC,IAAI,CAACX;YACd;YAEAS,OAAO,CAAC,0DAA0D,CAAC;YACnEC,OAAOC,IAAI,CAACT,OAAOC;YAEnB,MAAM,CAACS,QAAQ,GAAG,MAAMP,YAAE,CAACC,OAAO,CAACG,KAAKC;YAExC,kBAAkB;YAClB,IAAIG,WAAW,CAAC,6DAA6D,CAAC;YAC9E,MAAMC,cAAqB,EAAE;YAE7B,IAAIhB,UAAU;gBACZe,YAAY,CAAC,iBAAiB,CAAC;gBAC/BC,YAAYH,IAAI,CAACb;YACnB;YAEA,IAAIC,SAAS;gBACXc,YAAY,CAAC,qBAAqB,CAAC;gBACnCC,YAAYH,IAAI,CAACZ;YACnB;YAEA,IAAIC,SAAS;gBACXa,YAAY,CAAC,qBAAqB,CAAC;gBACnCC,YAAYH,IAAI,CAACX;YACnB;YAEA,MAAM,CAACe,YAAY,GAAG,MAAMV,YAAE,CAACC,OAAO,CAACO,UAAUC;YACjD,MAAME,QAAQD,WAAW,CAAC,EAAE,EAAEC,SAAS;YAEvC,OAAO;gBACLC,QAAQ;gBACRC,MAAM;oBACJN;oBACAR,YAAYG;oBACZY,YAAY;wBACVlB,MAAMmB,OAAOnB;wBACbC,OAAOkB,OAAOlB;wBACdc;wBACAK,YAAYC,KAAKC,IAAI,CAACP,QAAQd;oBAChC;gBACF;YACF;QACF,EAAE,OAAOsB,OAAO;YACdC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMC,aAAalC,MAAc,EAAE;QACjC,IAAI;YACF,MAAMa,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;MAuBb,CAAC;YAED,MAAM,CAACG,QAAQ,GAAG,MAAMP,YAAE,CAACC,OAAO,CAACG,KAAK;gBAACb;aAAO;YAEhD,kBAAkB;YAClB,MAAMmC,iBAAiB;gBACrBC,QAAQpB,QAAQqB,MAAM,CAACC,CAAAA,IAAKA,EAAEC,aAAa,KAAK;gBAChDC,MAAMxB,QAAQqB,MAAM,CAACC,CAAAA,IAAKA,EAAEC,aAAa,KAAK;gBAC9CE,SAASzB,QAAQqB,MAAM,CAACC,CAAAA,IAAKA,EAAEC,aAAa,KAAK;YACnD;YAEA,OAAO;gBACLlB,QAAQ;gBACRC,MAAM;oBACJN;oBACA0B,SAASP;oBACTQ,SAAS;wBACPvB,OAAOJ,QAAQ4B,MAAM;wBACrBR,QAAQD,eAAeC,MAAM,CAACQ,MAAM;wBACpCJ,MAAML,eAAeK,IAAI,CAACI,MAAM;wBAChCH,SAASN,eAAeM,OAAO,CAACG,MAAM;oBACxC;gBACF;YACF;QACF,EAAE,OAAOhB,OAAO;YACdC,QAAQD,KAAK,CAAC,6BAA6BA;YAC3C,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMY,aAAaC,QAAgB,EAAE9C,MAAc,EAAE;QACnD,IAAI;YACF,qBAAqB;YACrB,MAAM,CAAC+C,OAAO,GAAG,MAAMtC,YAAE,CAACC,OAAO,CAC/B,iGACA;gBAACoC;aAAS;YAGZ,IAAI,CAACC,MAAM,CAAC,EAAE,EAAE;gBACd,MAAM,IAAIjB,qBAAa,CAAC;oBACtBT,QAAQ;oBACRU,SAAS;gBACX,GAAGC,kBAAU,CAACgB,SAAS;YACzB;YAEA,IAAID,MAAM,CAAC,EAAE,CAAC1B,MAAM,KAAK,UAAU;gBACjC,MAAM,IAAIS,qBAAa,CAAC;oBACtBT,QAAQ;oBACRU,SAAS;gBACX,GAAGC,kBAAU,CAACiB,WAAW;YAC3B;YAEA,sBAAsB;YACtB,MAAM,CAACzC,WAAW,GAAG,MAAMC,YAAE,CAACC,OAAO,CACnC,0DACA;gBAACV;aAAO;YAGV,MAAMW,gBAAgBH,UAAU,CAAC,EAAE,EAAEI,gBAAgB;YAErD,IAAID,gBAAgBoC,MAAM,CAAC,EAAE,CAACG,WAAW,EAAE;gBACzC,MAAM,IAAIpB,qBAAa,CAAC;oBACtBT,QAAQ;oBACRU,SAAS;gBACX,GAAGC,kBAAU,CAACiB,WAAW;YAC3B;YAEA,yBAAyB;YACzB,IAAIF,MAAM,CAAC,EAAE,CAACI,eAAe,GAAG,GAAG;gBACjC,MAAM,CAACC,gBAAgB,GAAG,MAAM3C,YAAE,CAACC,OAAO,CACxC,kFACA;oBAACV;oBAAQ8C;iBAAS;gBAGpB,IAAIM,eAAe,CAAC,EAAE,CAACC,KAAK,IAAIN,MAAM,CAAC,EAAE,CAACI,eAAe,EAAE;oBACzD,MAAM,IAAIrB,qBAAa,CAAC;wBACtBT,QAAQ;wBACRU,SAAS;oBACX,GAAGC,kBAAU,CAACiB,WAAW;gBAC3B;YACF;YAEA,wBAAwB;YACxB,MAAMK,YAAYP,MAAM,CAAC,EAAE,CAACQ,WAAW,GAAG,IACxC,IAAIC,KAAKA,KAAKC,GAAG,KAAMV,MAAM,CAAC,EAAE,CAACQ,WAAW,GAAG,KAAK,KAAK,KAAK,QAAS;YAEzE,2BAA2B;YAC3B,MAAMG,iBAAiB,IAAI,CAACC,sBAAsB;YAElD,oBAAoB;YACpB,MAAMlD,YAAE,CAACC,OAAO,CAAC;YAEjB,IAAI;gBACF,gBAAgB;gBAChB,MAAMD,YAAE,CAACC,OAAO,CACd,gGACA;oBAACqC,MAAM,CAAC,EAAE,CAACG,WAAW;oBAAElD;iBAAO;gBAGjC,yBAAyB;gBACzB,MAAMS,YAAE,CAACC,OAAO,CACd,CAAC;2CACgC,CAAC,EAClC;oBAACV;oBAAQ+C,MAAM,CAAC,EAAE,CAACG,WAAW;oBAAE,CAAC,UAAU,EAAEH,MAAM,CAAC,EAAE,CAACa,KAAK,EAAE;iBAAC;gBAGjE,qBAAqB;gBACrB,MAAM,CAACC,iBAAiB,GAAG,MAAMpD,YAAE,CAACC,OAAO,CACzC,CAAC;;;yDAG8C,CAAC,EAChD;oBAACV;oBAAQ8C;oBAAUQ,WAAWQ,iBAAiB;oBAAMJ;iBAAe;gBAGtE,MAAMjD,YAAE,CAACC,OAAO,CAAC;gBAEjB,sBAAsB;gBACtB,MAAM,IAAI,CAACqD,wBAAwB,CAAC/D,QAAQ+C,MAAM,CAAC,EAAE,CAACa,KAAK,EAAEF;gBAE7D,OAAO;oBACLrC,QAAQ;oBACRU,SAAS;oBACTT,MAAM;wBACJ0C,IAAIH,iBAAiBI,QAAQ;wBAC7BC,aAAanB,MAAM,CAAC,EAAE,CAACa,KAAK;wBAC5BO,YAAYpB,MAAM,CAAC,EAAE,CAACG,WAAW;wBACjCQ;wBACAJ;wBACAc,iBAAiBzD,gBAAgBoC,MAAM,CAAC,EAAE,CAACG,WAAW;oBACxD;gBACF;YACF,EAAE,OAAOtB,OAAO;gBACd,MAAMnB,YAAE,CAACC,OAAO,CAAC;gBACjB,MAAMkB;YACR;QACF,EAAE,OAAOA,OAAO;YACdC,QAAQD,KAAK,CAAC,2BAA2BA;YACzC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMoC,sBAAsB;QAC1B,IAAI;YACF,MAAMxD,MAAM,CAAC;;;;;;;;;;;MAWb,CAAC;YAED,MAAM,CAACyD,WAAW,GAAG,MAAM7D,YAAE,CAACC,OAAO,CAACG;YAEtC,OAAO;gBACLQ,QAAQ;gBACRC,MAAMgD;YACR;QACF,EAAE,OAAO1C,OAAO;YACdC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEQ0B,yBAAiC;QACvC,MAAMY,QAAQ;QACd,IAAIC,SAAS;QACb,IAAK,IAAIC,IAAI,GAAGA,IAAI,GAAGA,IAAK;YAC1BD,UAAUD,MAAMG,MAAM,CAAChD,KAAKiD,KAAK,CAACjD,KAAKkD,MAAM,KAAKL,MAAM3B,MAAM;QAChE;QACA,OAAO4B;IACT;IAEA,MAAcT,yBAAyB/D,MAAc,EAAEkE,WAAmB,EAAER,cAAsB,EAAE;QAClG,IAAI;YACF,MAAMjD,YAAE,CAACC,OAAO,CACd,CAAC;4EACmE,CAAC,EACrE;gBACEV;gBACA,CAAC,eAAe,EAAEkE,YAAY,UAAU,EAAER,gBAAgB;gBAC1DmB,KAAKC,SAAS,CAAC;oBACbZ;oBACAR;oBACAqB,MAAM;gBACR;aACD;QAEL,EAAE,OAAOnD,OAAO;YACdC,QAAQD,KAAK,CAAC,uCAAuCA;QACvD;IACF;IAEA,qCAAqC;IACrC,MAAMoD,aAAaC,UAAe,EAAE;QAClC,IAAI;YACF,MAAM,EACJrB,KAAK,EACLsB,WAAW,EACXhF,QAAQ,EACRiE,UAAU,EACVgB,QAAQ,EACRC,eAAe,EACfC,UAAU,EACVC,cAAc,EACf,GAAGL;YAEJ,MAAM,CAACT,OAAO,GAAG,MAAM/D,YAAE,CAACC,OAAO,CAC/B,CAAC;;;;iEAIwD,CAAC,EAC1D;gBACEkD;gBAAOsB;gBAAahF;gBAAUiE;gBAAYgB;gBAC1CC;gBAAiBC;gBAAYC;aAC9B;YAGH,OAAO;gBACLjE,QAAQ;gBACRU,SAAS;gBACTT,MAAM;oBACJ0C,IAAIQ,OAAOP,QAAQ;oBACnB,GAAGgB,UAAU;gBACf;YACF;QACF,EAAE,OAAOrD,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMsD,aAAazC,QAAgB,EAAE0C,UAAe,EAAE;QACpD,IAAI;YACF,MAAMC,UAAoB,EAAE;YAC5B,MAAM3E,SAAgB,EAAE;YAExB4E,OAAOC,IAAI,CAACH,YAAYI,OAAO,CAACC,CAAAA;gBAC9B,IAAIL,UAAU,CAACK,IAAI,KAAKC,WAAW;oBACjC,MAAMC,QAAQF,QAAQ,eAAe,gBACxBA,QAAQ,aAAa,cACrBA,QAAQ,oBAAoB,qBAC5BA,QAAQ,eAAe,gBACvBA,QAAQ,mBAAmB,oBAAoBA;oBAC5DJ,QAAQ1E,IAAI,CAAC,GAAGgF,MAAM,IAAI,CAAC;oBAC3BjF,OAAOC,IAAI,CAACyE,UAAU,CAACK,IAAI;gBAC7B;YACF;YAEA,IAAIJ,QAAQ7C,MAAM,KAAK,GAAG;gBACxB,MAAM,IAAId,qBAAa,CAAC;oBACtBT,QAAQ;oBACRU,SAAS;gBACX,GAAGC,kBAAU,CAACiB,WAAW;YAC3B;YAEAnC,OAAOC,IAAI,CAAC+B;YAEZ,MAAMrC,YAAE,CAACC,OAAO,CACd,CAAC,mBAAmB,EAAE+E,QAAQO,IAAI,CAAC,MAAM,iCAAiC,CAAC,EAC3ElF;YAGF,OAAO;gBACLO,QAAQ;gBACRU,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMgE,aAAanD,QAAgB,EAAE;QACnC,IAAI;YACF,MAAMrC,YAAE,CAACC,OAAO,CACd,2EACA;gBAACoC;aAAS;YAGZ,OAAO;gBACLzB,QAAQ;gBACRU,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,0DAA0D;IAC1D,MAAMiE,mBAAmB;QACvB,IAAI;YACF,MAAMzF,YAAE,CAACC,OAAO,CACd,CAAC;;yDAEgD,CAAC;YAGpDmB,QAAQsE,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAOvE,OAAO;YACdC,QAAQD,KAAK,CAAC,+BAA+BA;YAC7C,OAAO;QACT;IACF;AACF"}