// src/admin/dto/get-all-foods-query.dto.ts
import { IsOptional, IsString, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class GetAllFoodsQueryDto {
  @IsOptional()
  @IsString()
  name?: string; // Filtro por nome do alimento

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  category_id?: number; // Filtro por categoria

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  page?: number; // Página atual

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  limit?: number; // Limite de itens por página
}