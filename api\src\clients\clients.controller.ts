import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ClientsService } from './clients.service';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';

@Controller('clients')
@UseGuards(JwtAuthGuard)
export class ClientsController {
  constructor(private readonly clientsService: ClientsService) {}

  @Get()
  async getClients(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.clientsService.getClients(userId, query);
  }

  @Get(':id')
  async getClient(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.clientsService.getClient(Number(id), userId);
  }

  @Post()
  async createClient(@Body() createClientDto: CreateClientDto, @Request() req: any) {
    const userId = req.user.userId;
    return this.clientsService.createClient(createClientDto, userId);
  }

  @Put(':id')
  async updateClient(
    @Param('id') id: string, 
    @Body() updateClientDto: UpdateClientDto, 
    @Request() req: any
  ) {
    const userId = req.user.userId;
    return this.clientsService.updateClient(Number(id), updateClientDto, userId);
  }

  @Delete(':id')
  async deleteClient(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.clientsService.deleteClient(Number(id), userId);
  }

  @Get(':id/protocols')
  async getClientProtocols(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.clientsService.getClientProtocols(Number(id), userId);
  }

  @Get(':id/assessments')
  async getClientAssessments(@Param('id') id: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.clientsService.getClientAssessments(Number(id), userId);
  }

  @Post(':id/protocols')
  async createClientProtocol(
    @Param('id') id: string, 
    @Body() protocolData: any, 
    @Request() req: any
  ) {
    const userId = req.user.userId;
    return this.clientsService.createClientProtocol(Number(id), protocolData, userId);
  }

  @Get(':id/progress')
  async getClientProgress(@Param('id') id: string, @Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.clientsService.getClientProgress(Number(id), userId, query);
  }

  @Get(':id/nutrition-data')
  async getClientNutritionData(@Param('id') id: string, @Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.clientsService.getClientNutritionData(Number(id), userId, query);
  }

  @Get(':id/workout-data')
  async getClientWorkoutData(@Param('id') id: string, @Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.clientsService.getClientWorkoutData(Number(id), userId, query);
  }
}
