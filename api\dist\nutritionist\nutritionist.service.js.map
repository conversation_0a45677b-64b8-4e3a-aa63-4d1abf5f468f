{"version": 3, "sources": ["../../src/nutritionist/nutritionist.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { GetNutritionistClientsQueryDto } from '../admin/dto/get-nutritionist-clients-query.dto';\r\nimport { db } from '../database';\r\nimport * as dayjs from 'dayjs';\r\nimport { CreateNutritionistProtocolDto } from 'src/dto/create-nutritionist-protocol.dto';\r\nimport { CreateNutritionistClientProtocolDto } from '../dto/create-nutritionist-client-protocol.dto';\r\nimport { ImportNutritionistClientProtocolDto } from '../dto/import-nutritionist-client-protocol.dto';\r\nimport { CreateProtocolDietDto } from 'src/users/dto/create-protocol-diet.dto';\r\n\r\n@Injectable()\r\nexport class NutritionistService {\r\n    formatDatetime(datetime: any): string {\r\n        return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');\r\n    }\r\n\r\n    async getClients(query: GetNutritionistClientsQueryDto, userId: number) {\r\n        const { q } = query;\r\n        let { page = 1, limit = 100 } = query;\r\n        const offset = (page - 1) * limit;\r\n            \r\n        let queryBuilder = db\r\n            .selectFrom('users')\r\n            .innerJoin('users_roles', 'users.id', 'users_roles.user_id')\r\n            .innerJoin('clients', 'users.id', 'clients.client_id')\r\n            .leftJoin('nutritionist_protocols', 'nutritionist_protocols.user_id', 'users.id')\r\n            .select([\r\n            'clients.client_id as id',\r\n            'users.name',\r\n            'users.email',\r\n            'users.photo',\r\n            'users.created_at',\r\n            'nutritionist_protocols.name as protocol'\r\n            // db.fn<string>('group_concat', ['roles.name']).as('roles'),\r\n            ])\r\n            .where('clients.role_id', '=', 3)\r\n            .where('clients.user_id', '=', userId)\r\n            .groupBy('users.id')\r\n            .orderBy('users.created_at', 'desc');\r\n    \r\n        if (q) {\r\n            queryBuilder = queryBuilder.where((eb) =>\r\n            eb.or([\r\n                eb('users.name', 'like', `%${q}%`),\r\n                eb('users.email', 'like', `%${q}%`),\r\n            ])\r\n            );\r\n        }\r\n    \r\n        const [data, total] = await Promise.all([\r\n            queryBuilder.limit(limit).offset(offset).execute(),\r\n            db.selectFrom('users')\r\n            .innerJoin('users_roles', 'users.id', 'users_roles.user_id')\r\n            .innerJoin('clients', 'users.id', 'clients.user_id')\r\n            .leftJoin('nutritionist_protocols', 'nutritionist_protocols.user_id', 'users.id')\r\n            .select(db.fn.countAll().as('total'))\r\n            .groupBy('users.id')\r\n            .where('clients.role_id', '=', 3)\r\n            .where('clients.user_id', '=', userId)\r\n            .executeTakeFirst(),\r\n        ]);\r\n    \r\n        return {\r\n            status: 'success',\r\n            data: data.map((row) => ({\r\n            id: row.id,\r\n            name: row.name,\r\n            email: row.email,\r\n            photo: row.photo,\r\n            protocol: row.protocol,\r\n            plan: '',\r\n            plan_status: '',\r\n            attendance: 99,\r\n            date: this.formatDatetime(row.created_at),            \r\n            })),\r\n            pagination: {\r\n            page,\r\n            limit,\r\n            total: Number(total?.total),\r\n            },\r\n        };\r\n    }\r\n\r\n    async getClient(id: number, userId: number) {\r\n        const client = await db\r\n            .selectFrom('users')\r\n            .innerJoin('clients as c', 'users.id', 'c.client_id')\r\n            .select([\r\n                'users.id',\r\n                'users.name',\r\n                'users.email',\r\n                'users.photo',\r\n                'users.height',\r\n                'users.weight',\r\n                'c.created_at as client_date',\r\n            ])\r\n            .where('users.id', '=', id)\r\n            .where('c.user_id', '=', userId)\r\n            .where('c.role_id', '=', 3)\r\n            .groupBy('users.id')\r\n            .executeTakeFirst();\r\n\r\n\r\n            if (!client) {\r\n                return {\r\n                    status: 'error',\r\n                    message: 'Client not found',\r\n                };\r\n            }\r\n\r\n            const attendance = {\r\n                week: 999,\r\n                month: 999,\r\n                sequence: 999,\r\n                record: 999\r\n            };\r\n\r\n            const lastProtocol = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .where('p.user_id', '=', userId)\r\n                .where('p.client_id', '=', id)\r\n                .where('p.ended_at', 'is', null)\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select([\r\n                    'p.id',\r\n                    'p.name',\r\n                    'p.goal_calories',\r\n                    'p.goal_protein',\r\n                    'p.goal_carbs',\r\n                    'p.goal_fat',\r\n                    'p.goal_water',\r\n                    'p.started_at',\r\n                    'p.objective'\r\n                ])\r\n                .orderBy('p.id', 'desc')\r\n                .limit(1)\r\n                .executeTakeFirst();\r\n\r\n                let protocol: any = null;\r\n\r\n                if (lastProtocol) {\r\n                    protocol = {\r\n                        id: lastProtocol.id,\r\n                        name: lastProtocol.name,\r\n                        nutritional_goals: {\r\n                            calories: lastProtocol.goal_calories,\r\n                            protein: lastProtocol.goal_protein,\r\n                            carbs: lastProtocol.goal_carbs,\r\n                            fat: lastProtocol.goal_fat,\r\n                            water: lastProtocol.goal_water,\r\n                        },\r\n                        started_at: this.formatDatetime(lastProtocol.started_at),\r\n                        adherence: 999,\r\n                        objective: lastProtocol.objective\r\n                    }\r\n                }\r\n\r\n                const clientData = {\r\n                    id,\r\n                    name: client.name,\r\n                    email: client.email,\r\n                    photo: client.photo,\r\n                    height: client.height,\r\n                    weight: client.weight,\r\n                    client_date: this.formatDatetime(client.client_date),\r\n                    // last_protocol_date: this.formatDatetime(client.last_protocol_date),\r\n                    attendance,\r\n                    protocol\r\n                };\r\n                \r\n            return {\r\n                status: 'success',\r\n                data: clientData,\r\n            }\r\n        }\r\n\r\n        async createProtocol(createNutritionistProtocolDto: CreateNutritionistProtocolDto, userId: number) {\r\n            const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createNutritionistProtocolDto;\r\n\r\n            const new_protocol = await db\r\n                .insertInto('nutritionist_protocols_templates')\r\n                .values({\r\n                    name: name,\r\n                    type_id: type_id,\r\n                    objective: objective,\r\n                    goal_calories: nutritional_goals.calories,\r\n                    goal_protein: nutritional_goals.protein,\r\n                    goal_carbs: nutritional_goals.carbs,\r\n                    goal_fat: nutritional_goals.fat,\r\n                    goal_water: nutritional_goals.water,\r\n                    general_notes: general_notes,\r\n                    user_id: userId\r\n                })\r\n                .executeTakeFirst();\r\n\r\n            const new_protocol_id = Number(new_protocol.insertId);\r\n\r\n            meals.forEach(async (meal: any) => {\r\n                const new_meal = await db.insertInto('nutritionist_protocols_templates_meals')\r\n                .values({\r\n                    protocol_id: new_protocol_id,\r\n                    name: meal.name,\r\n                    meal_time: meal.meal_time,\r\n                })\r\n                .executeTakeFirst();\r\n\r\n                const new_meal_id = Number(new_meal.insertId);\r\n\r\n                meal?.foods?.forEach((food: any) => {\r\n                    db.insertInto('nutritionist_protocols_templates_meals_foods')\r\n                    .values({\r\n                        meal_id: new_meal_id,\r\n                        food_id: food.food_id,\r\n                        name: food.name,\r\n                        quantity: food.quantity,\r\n                        unit: food.unit,\r\n                        calories: food.calories,\r\n                        protein: food.protein,\r\n                        carbs: food.carbs,\r\n                        fat: food.fat,\r\n                        fiber: food.fiber,\r\n                    })\r\n                    .execute();\r\n                });\r\n            });\r\n\r\n            if (supplements) {\r\n                await db\r\n                    .insertInto('nutritionist_protocols_templates_supplements')\r\n                    .values(supplements.map((supplement) => ({\r\n                        protocol_id: new_protocol_id,\r\n                        name: supplement.name,\r\n                        dosage: supplement.dosage,\r\n                        supplement_time: supplement.supplement_time,\r\n                        notes: supplement.notes,\r\n                    })))\r\n                    .execute();\r\n            }\r\n\r\n            return {\r\n                status: 'success',\r\n                data: [],\r\n            };\r\n        }\r\n\r\n        async getProtocols(userId: number) {\r\n            // Consulta principal para obter os protocolos com contagens de refeições e supplementos\r\n            const protocols = await db\r\n  .selectFrom('nutritionist_protocols_templates as npt')\r\n  .leftJoin('select_options as so', 'so.id', 'npt.type_id')\r\n  .leftJoin('nutritionist_protocols_templates_meals as nptm', 'nptm.protocol_id', 'npt.id')\r\n  .leftJoin('nutritionist_protocols_templates_supplements as npts', 'npts.protocol_id', 'npt.id')\r\n  .where('npt.user_id', '=', userId)\r\n  .select([\r\n    'npt.id',\r\n    'npt.name',\r\n    (eb) => eb.ref('so.value_option').as('type'),\r\n    (eb) =>\r\n      eb.fn\r\n        .count(eb.ref('nptm.id'))\r\n        .distinct()\r\n        .as('meals_qty'), // Contagem de refeições únicas\r\n    (eb) =>\r\n      eb.fn\r\n        .count(eb.ref('npts.id'))\r\n        .distinct()\r\n        .as('supplements_qty'), // Contagem de supplementos únicos\r\n    'npt.created_at',\r\n  ])\r\n  .groupBy(['npt.id', 'npt.name', 'so.value_option', 'npt.created_at']) // Corrigido aqui\r\n  .orderBy('npt.id', 'desc')\r\n  .execute();\r\n          \r\n            const formattedProtocols = protocols.map((protocol) => ({\r\n              id: protocol.id,\r\n              name: protocol.name,\r\n              type: protocol.type,\r\n              meals_qty: Number(protocol.meals_qty),\r\n              supplements_qty: Number(protocol.supplements_qty),\r\n              created_at: protocol.created_at,\r\n            }));\r\n\r\n\r\n            return {\r\n              status: 'success',\r\n              data: formattedProtocols,\r\n            };\r\n    }\r\n\r\n    async clientProtocols(id: number, userId: number) {\r\n        const protocols = await db\r\n        .selectFrom('nutritionist_protocols as np')\r\n        .leftJoin('users as u', 'u.id', 'np.client_id')\r\n        .where('u.id', '=', id)\r\n        .where('np.user_id', '=', userId)\r\n        .select([\r\n          'np.id',\r\n          'np.name',\r\n          'np.objective',\r\n          'np.goal_calories',\r\n          'np.goal_protein',\r\n          'np.goal_carbs',\r\n          'np.goal_fat',\r\n          'np.goal_water',\r\n          'np.started_at',\r\n          'np.ended_at',\r\n        ])\r\n        .orderBy('np.id', 'desc')\r\n        .execute();\r\n\r\n      // Mapeia os resultados para o formato desejado\r\n      const formattedProtocols = protocols.map((protocol: any) => ({\r\n        id: protocol.id,\r\n        name: protocol.name,\r\n        objective: protocol.objective,\r\n        nutritional_goals: {\r\n          calories: protocol.goal_calories,\r\n          protein: protocol.goal_protein,\r\n          carbs: protocol.goal_carbs,\r\n          fat: protocol.goal_fat,\r\n          water: protocol.goal_water,\r\n        },\r\n        started_at: protocol.started_at,\r\n        ended_at: protocol.ended_at,\r\n        adherence: 999\r\n      }));\r\n\r\n      return {\r\n        status: 'success',\r\n        data: formattedProtocols,\r\n      };\r\n\r\n      }\r\n\r\n      async createClientProtocol(id: number, createNutritionistClientProtocolDto: CreateNutritionistClientProtocolDto, userId: number) {\r\n        const client = await db\r\n          .selectFrom('users')\r\n          .select(['id', 'name', 'email', 'weight', 'height'])\r\n          .where('id', '=', id)\r\n          .executeTakeFirst();\r\n\r\n        if (!client) {\r\n          return {\r\n            status: 'error',\r\n            message: 'Client not found',\r\n          };\r\n        }\r\n\r\n        const initial_weight = client.weight;\r\n\r\n        if(!initial_weight) {\r\n          return {\r\n            status: 'error',\r\n            message: 'Client weight not found',\r\n          };\r\n        }\r\n\r\n\r\n        const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createNutritionistClientProtocolDto;\r\n\r\n        const new_protocol = await db\r\n          .insertInto('nutritionist_protocols')\r\n          .values({\r\n            client_id: id,\r\n            user_id: userId,\r\n            name: name,\r\n            type_id: type_id,\r\n            objective: objective,\r\n            goal_calories: nutritional_goals.calories,\r\n            goal_protein: nutritional_goals.protein,\r\n            goal_carbs: nutritional_goals.carbs,\r\n            goal_fat: nutritional_goals.fat,\r\n            goal_water: nutritional_goals.water,\r\n            general_notes,\r\n            started_at: new Date(),\r\n            initial_weight: initial_weight,\r\n          })\r\n          .executeTakeFirst();\r\n\r\n        const new_protocol_id = Number(new_protocol.insertId);\r\n\r\n        meals?.forEach(async (meal: any) => {\r\n          const new_meal = await db.insertInto('nutritionist_protocols_meals')\r\n            .values({\r\n              protocol_id: new_protocol_id,\r\n              name: meal.name,\r\n              day_of_week: meal.day_of_week,\r\n              meal_time: meal.meal_time,\r\n            })\r\n            .executeTakeFirst();\r\n\r\n            const new_meal_id = new_meal.insertId;\r\n\r\n            meal?.foods.forEach(async (food: any) => {\r\n              db.insertInto('nutritionist_protocols_meals_foods')\r\n              .values({\r\n                  meal_id: Number(new_meal_id),\r\n                  food_id: food.food_id,\r\n                  name: food.name,\r\n                  quantity: food.quantity,\r\n                  unit: food.unit,\r\n                  calories: food.calories,\r\n                  protein: food.protein,\r\n                  carbs: food.carbs,\r\n                  fat: food.fat,\r\n                  fiber: food.fiber\r\n                })\r\n              .execute();\r\n            })\r\n        });\r\n\r\n        if (supplements) {\r\n          await db\r\n            .insertInto('nutritionist_protocols_supplements')\r\n            .values(supplements.map((supplement) => ({\r\n              protocol_id: new_protocol_id,\r\n              name: supplement.name,\r\n              dosage: supplement.dosage,\r\n              supplement_time: supplement.supplement_time,\r\n              notes: supplement.notes,\r\n            })))\r\n            .execute();\r\n        }\r\n\r\n        return {\r\n          status: 'success',\r\n          data: [],\r\n        };\r\n      }\r\n\r\n      async importClientProtocol(id: number, importNutritionistClientProtocolDto: ImportNutritionistClientProtocolDto, userId: number) {\r\n        const { protocol_id } = importNutritionistClientProtocolDto;\r\n\r\n        const protocol_template = await db\r\n          .selectFrom('nutritionist_protocols_templates')\r\n          .where('id', '=', protocol_id)\r\n          .where('user_id', '=', userId)\r\n          .selectAll()\r\n          .executeTakeFirst();\r\n\r\n          if (!protocol_template) {\r\n            return {\r\n              status: 'error',\r\n              message: 'Protocol not found',\r\n            };\r\n          }\r\n\r\n          const client = await db\r\n            .selectFrom('users')\r\n            .select(['id', 'name', 'email', 'weight', 'height'])\r\n            .where('id', '=', id)\r\n            .executeTakeFirst();\r\n\r\n          if (!client) {\r\n            return {\r\n              status: 'error',\r\n              message: 'Client not found',\r\n            };\r\n          }\r\n\r\n          const initial_weight = client.weight;\r\n\r\n          if(!initial_weight) {\r\n            return {\r\n              status: 'error',\r\n              message: 'Client weight not found',\r\n            };\r\n          }\r\n\r\n          const new_protocol = await db\r\n            .insertInto('nutritionist_protocols')\r\n            .values({\r\n              client_id: id,\r\n              user_id: userId,\r\n              name: protocol_template.name,\r\n              type_id: protocol_template.type_id,\r\n              goal_calories: protocol_template.goal_calories,\r\n              goal_protein: protocol_template.goal_protein,\r\n              goal_carbs: protocol_template.goal_carbs,\r\n              goal_fat: protocol_template.goal_fat,\r\n              goal_water: protocol_template.goal_water,\r\n              initial_weight: initial_weight,\r\n              objective: protocol_template.objective,\r\n              ref_id: protocol_template.id ? Number(protocol_template.id) : null,\r\n              started_at: new Date(),\r\n            })\r\n            .executeTakeFirst();\r\n\r\n            const new_protocol_id = Number(new_protocol.insertId);\r\n\r\n            const protocol_template_meals = await db\r\n              .selectFrom('nutritionist_protocols_templates_meals')\r\n              .where('protocol_id', '=', protocol_id)\r\n              .select(['id', 'meal_time'])\r\n              .execute();\r\n\r\n              protocol_template_meals.forEach((meal: any) => {\r\n                db.insertInto('nutritionist_protocols_meals')\r\n                  .values({\r\n                    protocol_id: new_protocol_id,\r\n                    name: meal.name,\r\n                    day_of_week: 'monday',\r\n                    meal_time: meal.meal_time,\r\n                  })\r\n                  .execute();\r\n              });\r\n\r\n              const protocol_template_supplements = await db\r\n                .selectFrom('nutritionist_protocols_templates_supplements')\r\n                .where('protocol_id', '=', protocol_id)\r\n                .select(['id', 'name', 'dosage', 'supplement_time', 'notes'])\r\n                .execute();\r\n\r\n                protocol_template_supplements.forEach((supplement) => {\r\n                  db.insertInto('nutritionist_protocols_supplements')\r\n                    .values({\r\n                      protocol_id: new_protocol_id,\r\n                      name: supplement.name,\r\n                      dosage: supplement.dosage,\r\n                      supplement_time: supplement.supplement_time,\r\n                      notes: supplement.notes,\r\n                    })\r\n                    .execute();\r\n                });\r\n\r\n          return {\r\n            status: 'success',\r\n            data: [],\r\n          };\r\n      }\r\n\r\n      async postProtocolsDiet(userId: number, createProtocolDietDto: CreateProtocolDietDto) {\r\n        const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createProtocolDietDto;\r\n\r\n        const new_protocol = await db\r\n          .insertInto('nutritionist_protocols')\r\n          .values({\r\n            name: name,\r\n            type_id: type_id,\r\n            initial_weight: 100,\r\n            objective: objective,\r\n            goal_calories: nutritional_goals.calories,\r\n            goal_protein: nutritional_goals.protein,\r\n            goal_carbs: nutritional_goals.carbs,\r\n            goal_fat: nutritional_goals.fat,\r\n            goal_water: nutritional_goals.water,\r\n            general_notes: general_notes,\r\n            started_at: new Date(),\r\n            client_id: userId,\r\n            created_at: new Date(),\r\n            updated_at: new Date(),\r\n          })\r\n          .executeTakeFirst();\r\n\r\n        const new_protocol_id = Number(new_protocol.insertId);\r\n\r\n\r\n        meals.forEach(async (meal: any) => {\r\n          const new_meal = await db.insertInto('nutritionist_protocols_meals')\r\n          .values({\r\n            protocol_id: new_protocol_id,\r\n            name: meal.name,\r\n            day_of_week: meal.day_of_week,\r\n            meal_time: meal.meal_time,\r\n          })\r\n          .executeTakeFirst();\r\n\r\n          const new_meal_id = Number(new_meal.insertId);\r\n\r\n          meal?.foods?.forEach((food: any) => {\r\n            db.insertInto('nutritionist_protocols_templates_meals_foods')\r\n            .values({\r\n                meal_id: new_meal_id,\r\n                food_id: food.food_id,\r\n                name: food.name,\r\n                quantity: food.quantity,\r\n                unit: food.unit,\r\n                calories: food.calories,\r\n                protein: food.protein,\r\n                carbs: food.carbs,\r\n                fat: food.fat,\r\n                fiber: food.fiber\r\n              })\r\n              .execute();\r\n          });\r\n        });\r\n\r\n        if (supplements) {\r\n          await db\r\n            .insertInto('nutritionist_protocols_templates_supplements')\r\n            .values(supplements.map((supplement) => ({\r\n              protocol_id: new_protocol_id,\r\n              name: supplement.name,\r\n              dosage: supplement.dosage,\r\n              supplement_time: supplement.supplement_time,\r\n              notes: supplement.notes,\r\n            })))\r\n            .execute();\r\n        }\r\n\r\n        return {\r\n          status: 'success',\r\n          data: [],\r\n        };\r\n      }\r\n\r\n        /*\r\n        async getProtocols(userId: number) {\r\n            // Consulta principal para obter os protocolos\r\n            const protocols = await db\r\n              .selectFrom('nutritionist_protocols_templates as npt')\r\n              .leftJoin('select_options as so', 'so.id', 'npt.type_id')\r\n              .leftJoin('nutritionist_protocols_templates_meals as nptm', 'nptm.protocol_id', 'npt.id')\r\n              .leftJoin('nutritionist_protocols_templates_supplements as npts', 'npts.protocol_id', 'npt.id')\r\n              .where('npt.user_id', '=', userId)\r\n              .select([\r\n                'npt.id',\r\n                'npt.name',\r\n                (eb) => eb.ref('so.value_option').as('type'),\r\n                (eb) =>\r\n                  eb.fn\r\n                    .count(eb.ref('nptm.id'))\r\n                    .distinct()\r\n                    .as('meals_qty'), // Contagem de refeições únicas\r\n                (eb) =>\r\n                  eb.fn\r\n                    .count(eb.ref('npts.id'))\r\n                    .distinct()\r\n                    .as('supplements_qty'), // Contagem de supplementos únicos\r\n                'npt.created_at',\r\n              ])\r\n              .groupBy('npt.id')\r\n              .orderBy('npt.id', 'desc')\r\n              .execute();\r\n          \r\n            // Função para buscar refeições associadas a um protocolo\r\n            const fetchMeals = async (protocolId: number) => {\r\n              return await db\r\n                .selectFrom('nutritionist_protocols_templates_meals as nptm')\r\n                .where('nptm.protocol_id', '=', protocolId)\r\n                .select(['nptm.id', 'nptm.food_id', 'nptm.meal_weight', 'nptm.meal_time'])\r\n                .execute();\r\n            };\r\n          \r\n            // Função para buscar supplementos associados a um protocolo\r\n            const fetchSupplements = async (protocolId: number) => {\r\n              return await db\r\n                .selectFrom('nutritionist_protocols_templates_supplements as npts')\r\n                .where('npts.protocol_id', '=', protocolId)\r\n                .select(['npts.id', 'npts.name', 'npts.dosage', 'npts.supplement_time', 'npts.notes'])\r\n                .execute();\r\n            };\r\n          \r\n            // Processar os dados para incluir meals e supplements\r\n            const formattedProtocols = await Promise.all(\r\n              protocols.map(async (protocol) => {\r\n                const meals = await fetchMeals(protocol.id);\r\n                const supplements = await fetchSupplements(protocol.id);\r\n          \r\n                return {\r\n                  id: protocol.id,\r\n                  name: protocol.name,\r\n                  type: protocol.type,\r\n                  meals_qty: Number(protocol.meals_qty),\r\n                  supplements_qty: Number(protocol.supplements_qty),\r\n                  meals: meals || [],\r\n                  supplements: supplements || [],\r\n                  created_at: protocol.created_at,\r\n                };\r\n              })\r\n            );\r\n          \r\n            return {\r\n              status: 'success',\r\n              data: formattedProtocols,\r\n            };\r\n          }\r\n    */\r\n\r\n    \r\n\r\n}\r\n"], "names": ["NutritionistService", "formatDatetime", "datetime", "dayjs", "format", "getClients", "query", "userId", "q", "page", "limit", "offset", "queryBuilder", "db", "selectFrom", "innerJoin", "leftJoin", "select", "where", "groupBy", "orderBy", "eb", "or", "data", "total", "Promise", "all", "execute", "fn", "countAll", "as", "executeTakeFirst", "status", "map", "row", "id", "name", "email", "photo", "protocol", "plan", "plan_status", "attendance", "date", "created_at", "pagination", "Number", "getClient", "client", "message", "week", "month", "sequence", "record", "lastProtocol", "nutritional_goals", "calories", "goal_calories", "protein", "goal_protein", "carbs", "goal_carbs", "fat", "goal_fat", "water", "goal_water", "started_at", "adherence", "objective", "clientData", "height", "weight", "client_date", "createProtocol", "createNutritionistProtocolDto", "type_id", "meals", "supplements", "general_notes", "new_protocol", "insertInto", "values", "user_id", "new_protocol_id", "insertId", "for<PERSON>ach", "meal", "new_meal", "protocol_id", "meal_time", "new_meal_id", "foods", "food", "meal_id", "food_id", "quantity", "unit", "fiber", "supplement", "dosage", "supplement_time", "notes", "getProtocols", "protocols", "ref", "count", "distinct", "formattedProtocols", "type", "meals_qty", "supplements_qty", "clientProtocols", "ended_at", "createClientProtocol", "createNutritionistClientProtocolDto", "initial_weight", "client_id", "Date", "day_of_week", "importClientProtocol", "importNutritionistClientProtocolDto", "protocol_template", "selectAll", "ref_id", "protocol_template_meals", "protocol_template_supplements", "postProtocolsDiet", "createProtocolDietDto", "updated_at"], "mappings": ";;;;+BAUaA;;;eAAAA;;;wBAVc;0BAER;+DACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhB,IAAA,AAAMA,sBAAN,MAAMA;IACTC,eAAeC,QAAa,EAAU;QAClC,OAAOC,OAAMD,UAAUE,MAAM,CAAC;IAClC;IAEA,MAAMC,WAAWC,KAAqC,EAAEC,MAAc,EAAE;QACpE,MAAM,EAAEC,CAAC,EAAE,GAAGF;QACd,IAAI,EAAEG,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAChC,MAAMK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,IAAIE,eAAeC,YAAE,CAChBC,UAAU,CAAC,SACXC,SAAS,CAAC,eAAe,YAAY,uBACrCA,SAAS,CAAC,WAAW,YAAY,qBACjCC,QAAQ,CAAC,0BAA0B,kCAAkC,YACrEC,MAAM,CAAC;YACR;YACA;YACA;YACA;YACA;YACA;SAEC,EACAC,KAAK,CAAC,mBAAmB,KAAK,GAC9BA,KAAK,CAAC,mBAAmB,KAAKX,QAC9BY,OAAO,CAAC,YACRC,OAAO,CAAC,oBAAoB;QAEjC,IAAIZ,GAAG;YACHI,eAAeA,aAAaM,KAAK,CAAC,CAACG,KACnCA,GAAGC,EAAE,CAAC;oBACFD,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAEb,EAAE,CAAC,CAAC;oBACjCa,GAAG,eAAe,QAAQ,CAAC,CAAC,EAAEb,EAAE,CAAC,CAAC;iBACrC;QAEL;QAEA,MAAM,CAACe,MAAMC,MAAM,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACpCd,aAAaF,KAAK,CAACA,OAAOC,MAAM,CAACA,QAAQgB,OAAO;YAChDd,YAAE,CAACC,UAAU,CAAC,SACbC,SAAS,CAAC,eAAe,YAAY,uBACrCA,SAAS,CAAC,WAAW,YAAY,mBACjCC,QAAQ,CAAC,0BAA0B,kCAAkC,YACrEC,MAAM,CAACJ,YAAE,CAACe,EAAE,CAACC,QAAQ,GAAGC,EAAE,CAAC,UAC3BX,OAAO,CAAC,YACRD,KAAK,CAAC,mBAAmB,KAAK,GAC9BA,KAAK,CAAC,mBAAmB,KAAKX,QAC9BwB,gBAAgB;SACpB;QAED,OAAO;YACHC,QAAQ;YACRT,MAAMA,KAAKU,GAAG,CAAC,CAACC,MAAS,CAAA;oBACzBC,IAAID,IAAIC,EAAE;oBACVC,MAAMF,IAAIE,IAAI;oBACdC,OAAOH,IAAIG,KAAK;oBAChBC,OAAOJ,IAAII,KAAK;oBAChBC,UAAUL,IAAIK,QAAQ;oBACtBC,MAAM;oBACNC,aAAa;oBACbC,YAAY;oBACZC,MAAM,IAAI,CAAC1C,cAAc,CAACiC,IAAIU,UAAU;gBACxC,CAAA;YACAC,YAAY;gBACZpC;gBACAC;gBACAc,OAAOsB,OAAOtB,OAAOA;YACrB;QACJ;IACJ;IAEA,MAAMuB,UAAUZ,EAAU,EAAE5B,MAAc,EAAE;QACxC,MAAMyC,SAAS,MAAMnC,YAAE,CAClBC,UAAU,CAAC,SACXC,SAAS,CAAC,gBAAgB,YAAY,eACtCE,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAC,KAAK,CAAC,YAAY,KAAKiB,IACvBjB,KAAK,CAAC,aAAa,KAAKX,QACxBW,KAAK,CAAC,aAAa,KAAK,GACxBC,OAAO,CAAC,YACRY,gBAAgB;QAGjB,IAAI,CAACiB,QAAQ;YACT,OAAO;gBACHhB,QAAQ;gBACRiB,SAAS;YACb;QACJ;QAEA,MAAMP,aAAa;YACfQ,MAAM;YACNC,OAAO;YACPC,UAAU;YACVC,QAAQ;QACZ;QAEA,MAAMC,eAAe,MAAMzC,YAAE,CACxBC,UAAU,CAAC,+BACXI,KAAK,CAAC,aAAa,KAAKX,QACxBW,KAAK,CAAC,eAAe,KAAKiB,IAC1BjB,KAAK,CAAC,cAAc,MAAM,MAC1BF,QAAQ,CAAC,uBAAuB,QAAQ,aACxCC,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAG,OAAO,CAAC,QAAQ,QAChBV,KAAK,CAAC,GACNqB,gBAAgB;QAEjB,IAAIQ,WAAgB;QAEpB,IAAIe,cAAc;YACdf,WAAW;gBACPJ,IAAImB,aAAanB,EAAE;gBACnBC,MAAMkB,aAAalB,IAAI;gBACvBmB,mBAAmB;oBACfC,UAAUF,aAAaG,aAAa;oBACpCC,SAASJ,aAAaK,YAAY;oBAClCC,OAAON,aAAaO,UAAU;oBAC9BC,KAAKR,aAAaS,QAAQ;oBAC1BC,OAAOV,aAAaW,UAAU;gBAClC;gBACAC,YAAY,IAAI,CAACjE,cAAc,CAACqD,aAAaY,UAAU;gBACvDC,WAAW;gBACXC,WAAWd,aAAac,SAAS;YACrC;QACJ;QAEA,MAAMC,aAAa;YACflC;YACAC,MAAMY,OAAOZ,IAAI;YACjBC,OAAOW,OAAOX,KAAK;YACnBC,OAAOU,OAAOV,KAAK;YACnBgC,QAAQtB,OAAOsB,MAAM;YACrBC,QAAQvB,OAAOuB,MAAM;YACrBC,aAAa,IAAI,CAACvE,cAAc,CAAC+C,OAAOwB,WAAW;YACnD,sEAAsE;YACtE9B;YACAH;QACJ;QAEJ,OAAO;YACHP,QAAQ;YACRT,MAAM8C;QACV;IACJ;IAEA,MAAMI,eAAeC,6BAA4D,EAAEnE,MAAc,EAAE;QAC/F,MAAM,EAAE6B,IAAI,EAAEuC,OAAO,EAAEP,SAAS,EAAEb,iBAAiB,EAAEqB,KAAK,EAAEC,WAAW,EAAEC,aAAa,EAAE,GAAGJ;QAE3F,MAAMK,eAAe,MAAMlE,YAAE,CACxBmE,UAAU,CAAC,oCACXC,MAAM,CAAC;YACJ7C,MAAMA;YACNuC,SAASA;YACTP,WAAWA;YACXX,eAAeF,kBAAkBC,QAAQ;YACzCG,cAAcJ,kBAAkBG,OAAO;YACvCG,YAAYN,kBAAkBK,KAAK;YACnCG,UAAUR,kBAAkBO,GAAG;YAC/BG,YAAYV,kBAAkBS,KAAK;YACnCc,eAAeA;YACfI,SAAS3E;QACb,GACCwB,gBAAgB;QAErB,MAAMoD,kBAAkBrC,OAAOiC,aAAaK,QAAQ;QAEpDR,MAAMS,OAAO,CAAC,OAAOC;YACjB,MAAMC,WAAW,MAAM1E,YAAE,CAACmE,UAAU,CAAC,0CACpCC,MAAM,CAAC;gBACJO,aAAaL;gBACb/C,MAAMkD,KAAKlD,IAAI;gBACfqD,WAAWH,KAAKG,SAAS;YAC7B,GACC1D,gBAAgB;YAEjB,MAAM2D,cAAc5C,OAAOyC,SAASH,QAAQ;YAE5CE,MAAMK,OAAON,QAAQ,CAACO;gBAClB/E,YAAE,CAACmE,UAAU,CAAC,gDACbC,MAAM,CAAC;oBACJY,SAASH;oBACTI,SAASF,KAAKE,OAAO;oBACrB1D,MAAMwD,KAAKxD,IAAI;oBACf2D,UAAUH,KAAKG,QAAQ;oBACvBC,MAAMJ,KAAKI,IAAI;oBACfxC,UAAUoC,KAAKpC,QAAQ;oBACvBE,SAASkC,KAAKlC,OAAO;oBACrBE,OAAOgC,KAAKhC,KAAK;oBACjBE,KAAK8B,KAAK9B,GAAG;oBACbmC,OAAOL,KAAKK,KAAK;gBACrB,GACCtE,OAAO;YACZ;QACJ;QAEA,IAAIkD,aAAa;YACb,MAAMhE,YAAE,CACHmE,UAAU,CAAC,gDACXC,MAAM,CAACJ,YAAY5C,GAAG,CAAC,CAACiE,aAAgB,CAAA;oBACrCV,aAAaL;oBACb/C,MAAM8D,WAAW9D,IAAI;oBACrB+D,QAAQD,WAAWC,MAAM;oBACzBC,iBAAiBF,WAAWE,eAAe;oBAC3CC,OAAOH,WAAWG,KAAK;gBAC3B,CAAA,IACC1E,OAAO;QAChB;QAEA,OAAO;YACHK,QAAQ;YACRT,MAAM,EAAE;QACZ;IACJ;IAEA,MAAM+E,aAAa/F,MAAc,EAAE;QAC/B,wFAAwF;QACxF,MAAMgG,YAAY,MAAM1F,YAAE,CACnCC,UAAU,CAAC,2CACXE,QAAQ,CAAC,wBAAwB,SAAS,eAC1CA,QAAQ,CAAC,kDAAkD,oBAAoB,UAC/EA,QAAQ,CAAC,wDAAwD,oBAAoB,UACrFE,KAAK,CAAC,eAAe,KAAKX,QAC1BU,MAAM,CAAC;YACN;YACA;YACA,CAACI,KAAOA,GAAGmF,GAAG,CAAC,mBAAmB1E,EAAE,CAAC;YACrC,CAACT,KACCA,GAAGO,EAAE,CACF6E,KAAK,CAACpF,GAAGmF,GAAG,CAAC,YACbE,QAAQ,GACR5E,EAAE,CAAC;YACR,CAACT,KACCA,GAAGO,EAAE,CACF6E,KAAK,CAACpF,GAAGmF,GAAG,CAAC,YACbE,QAAQ,GACR5E,EAAE,CAAC;YACR;SACD,EACAX,OAAO,CAAC;YAAC;YAAU;YAAY;YAAmB;SAAiB,EAAE,iBAAiB;SACtFC,OAAO,CAAC,UAAU,QAClBO,OAAO;QAEE,MAAMgF,qBAAqBJ,UAAUtE,GAAG,CAAC,CAACM,WAAc,CAAA;gBACtDJ,IAAII,SAASJ,EAAE;gBACfC,MAAMG,SAASH,IAAI;gBACnBwE,MAAMrE,SAASqE,IAAI;gBACnBC,WAAW/D,OAAOP,SAASsE,SAAS;gBACpCC,iBAAiBhE,OAAOP,SAASuE,eAAe;gBAChDlE,YAAYL,SAASK,UAAU;YACjC,CAAA;QAGA,OAAO;YACLZ,QAAQ;YACRT,MAAMoF;QACR;IACR;IAEA,MAAMI,gBAAgB5E,EAAU,EAAE5B,MAAc,EAAE;QAC9C,MAAMgG,YAAY,MAAM1F,YAAE,CACzBC,UAAU,CAAC,gCACXE,QAAQ,CAAC,cAAc,QAAQ,gBAC/BE,KAAK,CAAC,QAAQ,KAAKiB,IACnBjB,KAAK,CAAC,cAAc,KAAKX,QACzBU,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAG,OAAO,CAAC,SAAS,QACjBO,OAAO;QAEV,+CAA+C;QAC/C,MAAMgF,qBAAqBJ,UAAUtE,GAAG,CAAC,CAACM,WAAmB,CAAA;gBAC3DJ,IAAII,SAASJ,EAAE;gBACfC,MAAMG,SAASH,IAAI;gBACnBgC,WAAW7B,SAAS6B,SAAS;gBAC7Bb,mBAAmB;oBACjBC,UAAUjB,SAASkB,aAAa;oBAChCC,SAASnB,SAASoB,YAAY;oBAC9BC,OAAOrB,SAASsB,UAAU;oBAC1BC,KAAKvB,SAASwB,QAAQ;oBACtBC,OAAOzB,SAAS0B,UAAU;gBAC5B;gBACAC,YAAY3B,SAAS2B,UAAU;gBAC/B8C,UAAUzE,SAASyE,QAAQ;gBAC3B7C,WAAW;YACb,CAAA;QAEA,OAAO;YACLnC,QAAQ;YACRT,MAAMoF;QACR;IAEA;IAEA,MAAMM,qBAAqB9E,EAAU,EAAE+E,mCAAwE,EAAE3G,MAAc,EAAE;QAC/H,MAAMyC,SAAS,MAAMnC,YAAE,CACpBC,UAAU,CAAC,SACXG,MAAM,CAAC;YAAC;YAAM;YAAQ;YAAS;YAAU;SAAS,EAClDC,KAAK,CAAC,MAAM,KAAKiB,IACjBJ,gBAAgB;QAEnB,IAAI,CAACiB,QAAQ;YACX,OAAO;gBACLhB,QAAQ;gBACRiB,SAAS;YACX;QACF;QAEA,MAAMkE,iBAAiBnE,OAAOuB,MAAM;QAEpC,IAAG,CAAC4C,gBAAgB;YAClB,OAAO;gBACLnF,QAAQ;gBACRiB,SAAS;YACX;QACF;QAGA,MAAM,EAAEb,IAAI,EAAEuC,OAAO,EAAEP,SAAS,EAAEb,iBAAiB,EAAEqB,KAAK,EAAEC,WAAW,EAAEC,aAAa,EAAE,GAAGoC;QAE3F,MAAMnC,eAAe,MAAMlE,YAAE,CAC1BmE,UAAU,CAAC,0BACXC,MAAM,CAAC;YACNmC,WAAWjF;YACX+C,SAAS3E;YACT6B,MAAMA;YACNuC,SAASA;YACTP,WAAWA;YACXX,eAAeF,kBAAkBC,QAAQ;YACzCG,cAAcJ,kBAAkBG,OAAO;YACvCG,YAAYN,kBAAkBK,KAAK;YACnCG,UAAUR,kBAAkBO,GAAG;YAC/BG,YAAYV,kBAAkBS,KAAK;YACnCc;YACAZ,YAAY,IAAImD;YAChBF,gBAAgBA;QAClB,GACCpF,gBAAgB;QAEnB,MAAMoD,kBAAkBrC,OAAOiC,aAAaK,QAAQ;QAEpDR,OAAOS,QAAQ,OAAOC;YACpB,MAAMC,WAAW,MAAM1E,YAAE,CAACmE,UAAU,CAAC,gCAClCC,MAAM,CAAC;gBACNO,aAAaL;gBACb/C,MAAMkD,KAAKlD,IAAI;gBACfkF,aAAahC,KAAKgC,WAAW;gBAC7B7B,WAAWH,KAAKG,SAAS;YAC3B,GACC1D,gBAAgB;YAEjB,MAAM2D,cAAcH,SAASH,QAAQ;YAErCE,MAAMK,MAAMN,QAAQ,OAAOO;gBACzB/E,YAAE,CAACmE,UAAU,CAAC,sCACbC,MAAM,CAAC;oBACJY,SAAS/C,OAAO4C;oBAChBI,SAASF,KAAKE,OAAO;oBACrB1D,MAAMwD,KAAKxD,IAAI;oBACf2D,UAAUH,KAAKG,QAAQ;oBACvBC,MAAMJ,KAAKI,IAAI;oBACfxC,UAAUoC,KAAKpC,QAAQ;oBACvBE,SAASkC,KAAKlC,OAAO;oBACrBE,OAAOgC,KAAKhC,KAAK;oBACjBE,KAAK8B,KAAK9B,GAAG;oBACbmC,OAAOL,KAAKK,KAAK;gBACnB,GACDtE,OAAO;YACV;QACJ;QAEA,IAAIkD,aAAa;YACf,MAAMhE,YAAE,CACLmE,UAAU,CAAC,sCACXC,MAAM,CAACJ,YAAY5C,GAAG,CAAC,CAACiE,aAAgB,CAAA;oBACvCV,aAAaL;oBACb/C,MAAM8D,WAAW9D,IAAI;oBACrB+D,QAAQD,WAAWC,MAAM;oBACzBC,iBAAiBF,WAAWE,eAAe;oBAC3CC,OAAOH,WAAWG,KAAK;gBACzB,CAAA,IACC1E,OAAO;QACZ;QAEA,OAAO;YACLK,QAAQ;YACRT,MAAM,EAAE;QACV;IACF;IAEA,MAAMgG,qBAAqBpF,EAAU,EAAEqF,mCAAwE,EAAEjH,MAAc,EAAE;QAC/H,MAAM,EAAEiF,WAAW,EAAE,GAAGgC;QAExB,MAAMC,oBAAoB,MAAM5G,YAAE,CAC/BC,UAAU,CAAC,oCACXI,KAAK,CAAC,MAAM,KAAKsE,aACjBtE,KAAK,CAAC,WAAW,KAAKX,QACtBmH,SAAS,GACT3F,gBAAgB;QAEjB,IAAI,CAAC0F,mBAAmB;YACtB,OAAO;gBACLzF,QAAQ;gBACRiB,SAAS;YACX;QACF;QAEA,MAAMD,SAAS,MAAMnC,YAAE,CACpBC,UAAU,CAAC,SACXG,MAAM,CAAC;YAAC;YAAM;YAAQ;YAAS;YAAU;SAAS,EAClDC,KAAK,CAAC,MAAM,KAAKiB,IACjBJ,gBAAgB;QAEnB,IAAI,CAACiB,QAAQ;YACX,OAAO;gBACLhB,QAAQ;gBACRiB,SAAS;YACX;QACF;QAEA,MAAMkE,iBAAiBnE,OAAOuB,MAAM;QAEpC,IAAG,CAAC4C,gBAAgB;YAClB,OAAO;gBACLnF,QAAQ;gBACRiB,SAAS;YACX;QACF;QAEA,MAAM8B,eAAe,MAAMlE,YAAE,CAC1BmE,UAAU,CAAC,0BACXC,MAAM,CAAC;YACNmC,WAAWjF;YACX+C,SAAS3E;YACT6B,MAAMqF,kBAAkBrF,IAAI;YAC5BuC,SAAS8C,kBAAkB9C,OAAO;YAClClB,eAAegE,kBAAkBhE,aAAa;YAC9CE,cAAc8D,kBAAkB9D,YAAY;YAC5CE,YAAY4D,kBAAkB5D,UAAU;YACxCE,UAAU0D,kBAAkB1D,QAAQ;YACpCE,YAAYwD,kBAAkBxD,UAAU;YACxCkD,gBAAgBA;YAChB/C,WAAWqD,kBAAkBrD,SAAS;YACtCuD,QAAQF,kBAAkBtF,EAAE,GAAGW,OAAO2E,kBAAkBtF,EAAE,IAAI;YAC9D+B,YAAY,IAAImD;QAClB,GACCtF,gBAAgB;QAEjB,MAAMoD,kBAAkBrC,OAAOiC,aAAaK,QAAQ;QAEpD,MAAMwC,0BAA0B,MAAM/G,YAAE,CACrCC,UAAU,CAAC,0CACXI,KAAK,CAAC,eAAe,KAAKsE,aAC1BvE,MAAM,CAAC;YAAC;YAAM;SAAY,EAC1BU,OAAO;QAERiG,wBAAwBvC,OAAO,CAAC,CAACC;YAC/BzE,YAAE,CAACmE,UAAU,CAAC,gCACXC,MAAM,CAAC;gBACNO,aAAaL;gBACb/C,MAAMkD,KAAKlD,IAAI;gBACfkF,aAAa;gBACb7B,WAAWH,KAAKG,SAAS;YAC3B,GACC9D,OAAO;QACZ;QAEA,MAAMkG,gCAAgC,MAAMhH,YAAE,CAC3CC,UAAU,CAAC,gDACXI,KAAK,CAAC,eAAe,KAAKsE,aAC1BvE,MAAM,CAAC;YAAC;YAAM;YAAQ;YAAU;YAAmB;SAAQ,EAC3DU,OAAO;QAERkG,8BAA8BxC,OAAO,CAAC,CAACa;YACrCrF,YAAE,CAACmE,UAAU,CAAC,sCACXC,MAAM,CAAC;gBACNO,aAAaL;gBACb/C,MAAM8D,WAAW9D,IAAI;gBACrB+D,QAAQD,WAAWC,MAAM;gBACzBC,iBAAiBF,WAAWE,eAAe;gBAC3CC,OAAOH,WAAWG,KAAK;YACzB,GACC1E,OAAO;QACZ;QAEN,OAAO;YACLK,QAAQ;YACRT,MAAM,EAAE;QACV;IACJ;IAEA,MAAMuG,kBAAkBvH,MAAc,EAAEwH,qBAA4C,EAAE;QACpF,MAAM,EAAE3F,IAAI,EAAEuC,OAAO,EAAEP,SAAS,EAAEb,iBAAiB,EAAEqB,KAAK,EAAEC,WAAW,EAAEC,aAAa,EAAE,GAAGiD;QAE3F,MAAMhD,eAAe,MAAMlE,YAAE,CAC1BmE,UAAU,CAAC,0BACXC,MAAM,CAAC;YACN7C,MAAMA;YACNuC,SAASA;YACTwC,gBAAgB;YAChB/C,WAAWA;YACXX,eAAeF,kBAAkBC,QAAQ;YACzCG,cAAcJ,kBAAkBG,OAAO;YACvCG,YAAYN,kBAAkBK,KAAK;YACnCG,UAAUR,kBAAkBO,GAAG;YAC/BG,YAAYV,kBAAkBS,KAAK;YACnCc,eAAeA;YACfZ,YAAY,IAAImD;YAChBD,WAAW7G;YACXqC,YAAY,IAAIyE;YAChBW,YAAY,IAAIX;QAClB,GACCtF,gBAAgB;QAEnB,MAAMoD,kBAAkBrC,OAAOiC,aAAaK,QAAQ;QAGpDR,MAAMS,OAAO,CAAC,OAAOC;YACnB,MAAMC,WAAW,MAAM1E,YAAE,CAACmE,UAAU,CAAC,gCACpCC,MAAM,CAAC;gBACNO,aAAaL;gBACb/C,MAAMkD,KAAKlD,IAAI;gBACfkF,aAAahC,KAAKgC,WAAW;gBAC7B7B,WAAWH,KAAKG,SAAS;YAC3B,GACC1D,gBAAgB;YAEjB,MAAM2D,cAAc5C,OAAOyC,SAASH,QAAQ;YAE5CE,MAAMK,OAAON,QAAQ,CAACO;gBACpB/E,YAAE,CAACmE,UAAU,CAAC,gDACbC,MAAM,CAAC;oBACJY,SAASH;oBACTI,SAASF,KAAKE,OAAO;oBACrB1D,MAAMwD,KAAKxD,IAAI;oBACf2D,UAAUH,KAAKG,QAAQ;oBACvBC,MAAMJ,KAAKI,IAAI;oBACfxC,UAAUoC,KAAKpC,QAAQ;oBACvBE,SAASkC,KAAKlC,OAAO;oBACrBE,OAAOgC,KAAKhC,KAAK;oBACjBE,KAAK8B,KAAK9B,GAAG;oBACbmC,OAAOL,KAAKK,KAAK;gBACnB,GACCtE,OAAO;YACZ;QACF;QAEA,IAAIkD,aAAa;YACf,MAAMhE,YAAE,CACLmE,UAAU,CAAC,gDACXC,MAAM,CAACJ,YAAY5C,GAAG,CAAC,CAACiE,aAAgB,CAAA;oBACvCV,aAAaL;oBACb/C,MAAM8D,WAAW9D,IAAI;oBACrB+D,QAAQD,WAAWC,MAAM;oBACzBC,iBAAiBF,WAAWE,eAAe;oBAC3CC,OAAOH,WAAWG,KAAK;gBACzB,CAAA,IACC1E,OAAO;QACZ;QAEA,OAAO;YACLK,QAAQ;YACRT,MAAM,EAAE;QACV;IACF;AA6EN"}