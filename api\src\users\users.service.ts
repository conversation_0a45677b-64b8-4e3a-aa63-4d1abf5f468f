import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { db } from '../database';
import { CreateClientDto } from '../dto/create-client.dto';
import * as bcrypt from 'bcryptjs';
import { DailyWaterDto } from './dto/daily-water.dto';
import { sql } from 'kysely';
// dayjs
// import { dayjs } from '../dayjs';
// import dayjs from 'dayjs';
// import * as dayjs from 'dayjs';
// import { default as dayjs } from 'dayjs';
const dayjs = require('dayjs');
const timezone = require('dayjs/plugin/timezone');
const utc = require('dayjs/plugin/utc');
const isoWeek = require('dayjs/plugin/isoWeek');
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isoWeek);

// Interfaces para tipagem
export interface SleepData {
  day: string;
  hours: number;
  quality: number;
}

export interface WeeklyConsolidatedData {
  day: string;
  date: string;
  nutrition: {
    completed: number;
    total: number;
    percentage: number;
  };
  workout: {
    completed: number;
    calories_burned: number;
    target: number;
    percentage: number;
  };
  water: {
    consumed: number;
    target: number;
    percentage: number;
  };
  sleep: {
    hours: number;
    quality: number;
    target: number;
    percentage: number;
  };
}
import { DailyWorkoutsActivitiesDto } from './dto/daily-workouts-activities.dto';
import { CreateProtocolWorkoutDto } from './dto/create-protocol-workout.dto';
import { CreateProtocolDietDto } from './dto/create-protocol-diet.dto';
import * as fs from 'fs';
import * as path from 'path';
import sharp from 'sharp';
import { randomUUID } from 'crypto';
import { convertToUTC } from 'src/common/utils/date.util';

import OpenAI from 'openai';

import { v4 as uuidv4 } from 'uuid';

import * as os from 'os';

import * as fsPromises from 'fs/promises';
import * as fsExtra from 'fs-extra';
import axios from 'axios';
import { jsonrepair } from 'jsonrepair';
import { Stripe } from 'stripe';

@Injectable()
export class UsersService {
    private stripe: Stripe;
    private openai: OpenAI;

    constructor() {
      // @ts-ignore
      this.stripe = new Stripe(process.env.STRIPE_SK, {
        // apiVersion: '2025-04-30',
      });

      this.openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    }


    async create(createClientDto: CreateClientDto, userId: number, role: string) {
        const checkUser = await db
            .selectFrom('users')
            .where('email', '=', createClientDto.email)
            .select('id')
            .executeTakeFirst();

            if (checkUser) {
                throw new HttpException({
                    status: 400,
                    message: ['O e-mail já está cadastrado.'],
                }, 400);
            }


        const roles = {
            'admin': 1,
            'coach': 2,
            'nutritionist': 3,
            'user': 4
        };

        const role_id: number = roles[role];

        const hashedPassword = await bcrypt.hash(createClientDto.password, 10);


        const new_client = await db
        .insertInto('users')
        .values({...createClientDto, password: hashedPassword})
        .executeTakeFirst();

        // add role id
        await db
        .insertInto('users_roles')
        .values({
            user_id: Number(new_client.insertId),
            role_id: Number(role_id),
        })
        .execute();

        // client
        await db
        .insertInto('clients')
        .values({
            client_id: Number(new_client.insertId),
            user_id: Number(userId),
            role_id: Number(role_id),
        })
        .execute();

        return {
            status: 'success',
            data: {
                id: Number(new_client.insertId),
            },
        };
    }

    async getMe(userId: number) {
        const user: any = await db
            .selectFrom('users as u')
            .where('u.id', '=', userId)
            .leftJoin('select_options as goal', 'goal.id', 'u.goal_id')
            .select([
                'u.id',
                'u.name',
                'u.email',
                'u.username',
                'u.phone',
                'u.photo',
                // 'u.created_at',
                // 'u.updated_at',
                'u.height',
                'u.weight',
                'u.bodyfat',
                'goal.value_option as goal',
                'u.date_of_birth',
            ])
            .groupBy('u.id')
            .executeTakeFirst();

            const dataFormat = {
                id: user.id,
                name: user.name,
                email: user.email,
                username: user.username,
                phone: user.phone,
                photo: user.photo,
                height: parseFloat(Number(user.height).toFixed(2)) || 0,
                weight: parseFloat(Number(user.weight).toFixed(2)) || 0,
                bodyfat: parseFloat(Number(user.bodyfat).toFixed(2)) || 0,
                goal: user.goal,
                date_of_birth: user.date_of_birth,
            };

        return {
            status: 'success',
            data: dataFormat,
        };
    }

    async getGoals() {
      const goals = await db
        .selectFrom('select_options')
        .select(['id', 'value_option as name'])
        .where('area_key', '=', 'goals')
        .orderBy('sort_order')
        .execute();

      return {
        status: 'success',
        data: goals,
      };
    }

    async updateMe(userId: number, body: any) {
      const {
        name,
        username,
        phone,
        height,
        weight,
        bodyfat,
        goal_id,
        date_of_birth,
        photo
      } = body;

      // Validar apenas campos obrigatórios se fornecidos
      if (height !== undefined && (height < 100 || height > 250)) {
        throw new HttpException({
          status: 400,
          message: ['Altura deve estar entre 100 e 250 cm.'],
        }, 400);
      }

      if (weight !== undefined && (weight < 30 || weight > 300)) {
        throw new HttpException({
          status: 400,
          message: ['Peso deve estar entre 30 e 300 kg.'],
        }, 400);
      }

      // Validar username se fornecido
      if (username !== undefined) {
        if (username.length < 3 || username.length > 30) {
          throw new HttpException({
            status: 400,
            message: ['Username deve ter entre 3 e 30 caracteres.'],
          }, 400);
        }

        // Verificar se username já existe
        const existingUser = await db
          .selectFrom('users')
          .where('username', '=', username)
          .where('id', '!=', userId)
          .select('id')
          .executeTakeFirst();

        if (existingUser) {
          throw new HttpException({
            status: 400,
            message: ['Este username já está em uso.'],
          }, 400);
        }
      }

      // Preparar dados para atualização (apenas campos fornecidos)
      const updateData: any = {
        updated_at: new Date(),
      };

      if (name !== undefined) updateData.name = name;
      if (username !== undefined) updateData.username = username;
      if (phone !== undefined) updateData.phone = phone;
      if (height !== undefined) updateData.height = height;
      if (weight !== undefined) updateData.weight = weight;
      if (bodyfat !== undefined) updateData.bodyfat = bodyfat;
      if (goal_id !== undefined) updateData.goal_id = goal_id;
      if (date_of_birth !== undefined) updateData.date_of_birth = new Date(date_of_birth);
      if (photo !== undefined) updateData.photo = photo;

      await db
        .updateTable('users')
        .set(updateData)
        .where('id', '=', userId)
        .execute();

      return {
        status: 'success',
        data: {
          message: 'Perfil atualizado com sucesso'
        },
      };
    }

    async softDeleteAccount(userId: number) {
      // Check if user exists and is not already deleted
      const user = await db
        .selectFrom('users')
        .where('id', '=', userId)
        .select(['id', 'deleted_at'])
        .executeTakeFirst();

      if (!user) {
        throw new HttpException({
          status: 404,
          message: ['Usuário não encontrado.'],
        }, 404);
      }

      if (user.deleted_at) {
        throw new HttpException({
          status: 400,
          message: ['Conta já está marcada para exclusão.'],
        }, 400);
      }

      // Set deleted_at timestamp for soft delete
      await db
        .updateTable('users')
        .set({
          deleted_at: new Date(),
          updated_at: new Date(),
        })
        .where('id', '=', userId)
        .execute();

      // Remove all active user sessions
      await db
        .deleteFrom('user_auths')
        .where('user_id', '=', userId)
        .execute();

      return {
        status: 'success',
        data: {
          message: 'Conta marcada para exclusão. Você tem 30 dias para recuperá-la fazendo login novamente.',
        },
      };
    }

    async updateMePhoto(userId: number, file: Express.Multer.File) {
      // Validar arquivo
      const maxFileSize = 30 * 1024 * 1024; // 30 MB
      if (file.size > maxFileSize) {
        throw new HttpException({
          status: 400,
          message: ['O arquivo deve ter no máximo 30MB.'],
        }, 400);
      }

      // Validar tipo de arquivo
      const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedMimeTypes.includes(file.mimetype)) {
        throw new HttpException({
          status: 400,
          message: ['Apenas arquivos de imagem são permitidos (JPEG, PNG, WebP).'],
        }, 400);
      }
    
      // Criar o diretório se não existir
      const uploadDir = path.join('media', 'users', userId.toString(), 'profile');
      await fs.promises.mkdir(uploadDir, { recursive: true });

      // Gerar nome aleatório para o arquivo
      const fileExtension = file.mimetype === 'image/png' ? 'png' : 'jpg';
      const randomName = `profile-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`;
      const filePath = path.join(uploadDir, randomName);
    
      // Verificar dimensões da imagem usando sharp
      const image: any = sharp(file.buffer);
      const metadata: any = await image.metadata();
    
      // Verificar se precisa de resize
      const MAX_DIMENSION = 1080;
      if (metadata.width > MAX_DIMENSION || metadata.height > MAX_DIMENSION) {
        // Calcular proporção para manter aspect ratio
        const resizeOptions: sharp.ResizeOptions = {
          width: metadata.width > metadata.height ? MAX_DIMENSION : undefined,
          height: metadata.height > metadata.width ? MAX_DIMENSION : undefined,
          fit: 'inside', // Mantém proporção sem cortar
          withoutEnlargement: true // Não aumenta se for menor que 1080
        };
    
        // Aplicar resize e salvar
        await image
          .resize(resizeOptions)
          .jpeg({ quality: 80 }) // Ajuste qualidade se necessário
          .toFile(filePath);
      } else {
        // Se não precisa de resize, só salvar
        await image
          .jpeg({ quality: 80 })
          .toFile(filePath);
      }
    
      // Gerar URL da foto para salvar no banco
      const photoUrl = `/media/users/${userId}/profile/${randomName}`;

      // Atualizar o banco de dados com a nova URL da foto
      await db
        .updateTable('users')
        .set({
          photo: photoUrl,
          updated_at: new Date(),
        })
        .where('id', '=', userId)
        .execute();

      return {
        status: 'success',
        message: 'Foto atualizada com sucesso',
        data: {
          photo: photoUrl,
          filename: randomName,
        }
      };
    }


    async getDailyNutritionalSummary(query: any, userId: number) {
      const { date_start, date_end } = query;

      const tz = 'America/Sao_Paulo';
      
      const localStart = dayjs.tz(date_start || undefined, tz).startOf('day');
      const localEnd = dayjs.tz(date_end || undefined, tz).endOf('day');
      
      const startOfDay = localStart.utc().toDate();
      const endOfDay = localEnd.utc().toDate();

        /*
        Get goals from protocols active
        - calories
        - protein
        - fat
        - carbs
        - fiber
        - water        
        */

        const goals: any = await db
        .selectFrom('nutritionist_protocols')
        .select([
            'goal_calories',
            'goal_protein',
            'goal_carbs',
            'goal_fat',
            'goal_water'
        ])
        .where('client_id', '=', userId)
        .where((eb) => eb.or([
            eb('started_at', '>=', startOfDay),
            eb('started_at', '<=', endOfDay),
        ]))
        .where('ended_at', 'is', null)
        .orderBy('started_at', 'desc')
        .executeTakeFirst();

        // water consumed
        const water: any = await db
        .selectFrom('daily_water')
        .where('user_id', '=', userId)
        .where('daily_at', '>=', startOfDay)
        .where('daily_at', '<=', endOfDay)
        // sum consumed
        .select([
            db.fn.sum('consumed').as('consumed')
        ])
        .groupBy('user_id')
        .executeTakeFirst();

        // daily_nutritionist_protocol
        const daily_nutritionist_protocol = await db
        .selectFrom('daily_meals as d')
        .where('d.user_id', '=', userId)
        .where('d.daily_at', '>=', startOfDay)
        .where('d.daily_at', '<=', endOfDay)
        .select([
            'd.calories',
            'd.protein',
            'd.carbs',
            'd.fat',
        ])
        .execute();

        /*
        // daily_meals daily_meals_foods
        const daily_meals = await db
        .selectFrom('daily_meals as d')
        .leftJoin('daily_meals_foods as f', 'f.meal_id', 'd.id')
        .leftJoin('foods as food', 'food.id', 'f.food_id')
        .where('d.user_id', '=', userId)
        .where('d.created_at', '>=', startOfDay)
        .where('d.created_at', '<=', endOfDay)
        .select([
            'f.calories',
            'f.protein',
            'f.carbs',
            'f.fat',
        ])
        .execute();
        */

        // daily_coach_protocol
        const dailyCoachProtocol: any = await db
        .selectFrom('daily_coach_protocol')
        .select([
            db.fn.sum('total_calories').as('total_calories'),
        ])
        .where('user_id', '=', userId)
        .where('daily_at', '>=', startOfDay)
        .where('daily_at', '<=', endOfDay)
        .executeTakeFirst();

        const dailyWorkoutsActivities: any = await db
        .selectFrom('daily_workouts_activities')
        .select([
            db.fn.sum('calories').as('calories'),
        ])
        .where('user_id', '=', userId)
        .where('daily_at', '>=', startOfDay)
        .where('daily_at', '<=', endOfDay)
        .executeTakeFirst();

        const caloriesProtocolBurned = Number(dailyCoachProtocol?.total_calories);
        const caloriesWorkoutsBurned = Number(dailyWorkoutsActivities?.calories);
        const total_calories_burned = caloriesProtocolBurned + caloriesWorkoutsBurned;

        // sum - garantir que os valores sejam sempre não-negativos
        const sum_daily_nutritionist_protocol = daily_nutritionist_protocol.reduce((acc, curr) => {
            // Validar e garantir que os valores sejam números válidos e não-negativos
            const calories = Math.max(0, Number(curr.calories) || 0);
            const protein = Math.max(0, Number(curr.protein) || 0);
            const carbs = Math.max(0, Number(curr.carbs) || 0);
            const fat = Math.max(0, Number(curr.fat) || 0);

            acc.calories += calories;
            acc.protein += protein;
            acc.carbs += carbs;
            acc.fat += fat;
            return acc;
        }, { calories: 0, protein: 0, carbs: 0, fat: 0 });

        /*
        const sum_daily_meals = daily_meals.reduce((acc: any, curr: any) => {
            acc.calories += Number(curr.calories);
            acc.protein += Number(curr.protein);
            acc.carbs += Number(curr.carbs);
            acc.fat += Number(curr.fat);
            return acc;
        }, { calories: 0, protein: 0, carbs: 0, fat: 0 });

        const total = {
            calories: sum_daily_nutritionist_protocol.calories + sum_daily_meals.calories,
            protein: sum_daily_nutritionist_protocol.protein + sum_daily_meals.protein,
            carbs: sum_daily_nutritionist_protocol.carbs + sum_daily_meals.carbs,
            fat: sum_daily_nutritionist_protocol.fat + sum_daily_meals.fat,
        }
        */
       const total = sum_daily_nutritionist_protocol;

        // Se não há refeições registradas (total zerado), garantir que os valores sejam realmente zero
        if (daily_nutritionist_protocol.length === 0 ||
            (total.calories === 0 && total.protein === 0 && total.carbs === 0 && total.fat === 0)) {
            total.calories = 0;
            total.protein = 0;
            total.carbs = 0;
            total.fat = 0;
        }

        const calories_remaining: any = (Number(goals?.goal_calories) || 0) - (Number(total.calories) - Number(total_calories_burned));

        const dataNutritionalSummary = {
            calories: {
                consumed: parseFloat(Number(total.calories).toFixed(2)) || 0,
                burned: parseFloat(Number(total_calories_burned).toFixed(2)) || 0,
                remaining: calories_remaining > 0 ? parseFloat(Number(calories_remaining).toFixed(2)) : 0,
                goal: parseFloat(Number(goals?.goal_calories).toFixed(2)) || 0,
            },
            protein: {
                protein: parseFloat(Number(total.protein).toFixed(2)) || 0,
                goal: parseFloat(Number(goals?.goal_protein).toFixed(2)) || 0,
            },
            fat: {
                fat: parseFloat(Number(total.fat).toFixed(2)) || 0,
                goal: parseFloat(Number(goals?.goal_fat).toFixed(2)) || 0,
            },
            carbs: {
                carbs: parseFloat(Number(total.carbs).toFixed(2)) || 0,
                goal: parseFloat(Number(goals?.goal_carbs).toFixed(2)) || 0,
            },
            fiber: {
                fiber: 0,
                goal: 0,
            },
            water: {
                water: parseFloat(Number(water?.consumed).toFixed(2)) || 0,
                goal: parseFloat(Number(goals?.goal_water).toFixed(2)) || 0,
            },
        }        

        return {
            status: 'success',
            data: dataNutritionalSummary,
        }
    }


    async getDaily(query: any, userId: number) {
        const { date } = query;

        const startOfDay = dayjs(date).startOf('day').toDate();
        const endOfDay = dayjs(date).endOf('day').toDate();

        // sum water of this date
        const water = await db
                .selectFrom('daily_water')
                .where('user_id', '=', userId)
                .where('daily_at', '>=', startOfDay)
                .where('daily_at', '<=', endOfDay)
                // sum consumed
                .select([
                    db.fn.sum('consumed').as('consumed')
                ])
                .groupBy('user_id')
                .executeTakeFirst();

        const waterConsumed = water?.consumed || 0;


        return {
            status: 'success',
            data: {
                activity: {
                    device_source: 'Apple Health',
                    steps: {
                        steps: 0,
                        goal: 0,
                    },
                    active: {
                        minutes: 0,
                        goal: 0,
                    },
                    calories: {  
                        calories: 0,
                        goal: 0,
                    },
                    },
                    heart: {
                        heart: 0,
                        default_min: 60,
                        default_max: 120,
                    },
                    nutritional_summary: {
                        calories: {
                            consumed: 0,
                            burned: 0,
                            remaining: 0,
                            goal: 0
                        },
                        protein: {
                            protein: 0,
                            goal: 0,
                        },
                        fat: {
                            fat: 0,
                            goal: 0,
                        },
                        carbs: {
                            carbs: 0,
                            goal: 0,
                        },
                        fiber: {
                            fiber: 0,
                            goal: 0,
                        },
                    },
                    water: {
                        water: waterConsumed,
                        goal: 0,
                    },
                }
                
            }
            
        }

        getFirstName(name: string) {
            const names = name.split(' ');
            return names[0];
        }

        async getAssessments(userId: number) {
        const user = await db
        .selectFrom('users')
        .where('id', '=', userId)
        .select([
          'name'
        ])
        .groupBy('id')
        .executeTakeFirst();
        
        const assessmentsData = {
            name: user?.name ? this.getFirstName(user.name) : '',
            assessments: {
                days_met_goals: 0,
                attendance: {
                    weekly: 0,
                    monthly: 0,
                    streak: 0,
                    record_streak: 0,
                },
                weekly_diet_progress: {
                    weekly: [
                        {
                            day: 'Seg',
                            completed: 0,
                            total: 0
                        },
                        {
                            day: 'Ter',
                            completed: 0,
                            total: 0
                        },
                        {
                            day: 'Qua',
                            completed: 0,
                            total: 0
                        },
                        {
                            day: 'Qui',
                            completed: 1,
                            total: 1
                        },
                        {
                            day: 'Sex',
                            completed: 0,
                            total: 0
                        },
                        {
                            day: 'Sab',
                            completed: 0,
                            total: 0
                        },
                        {
                            day: 'Dom',
                            completed: 0,
                            total: 1
                        }
                    ],
                    success_rate: 0,
                    complete_meals: 0,
                    perfect_days: 0,
                }
        }
        }

        return {
            status: 'success',
            data: assessmentsData,
        };
    
    }

    // Daily
    async postDailyWater(user_id: number, dailyWaterDto: DailyWaterDto) {
        const { consumed, daily_at } = dailyWaterDto;

        const dailyAt = dayjs(daily_at).startOf('day').toDate();

        const daily = await db
        .insertInto('daily_water')
        .values({
            user_id,
            consumed,
            daily_at: dailyAt,
            created_at: new Date(),
            updated_at: new Date(),
        })
        .execute();

        return {
            "status": "success",
            "data": []
        }
    }

    async getDailyWorkoutsActivities() {
        const dailyWorkoutsActivities = await db
        .selectFrom('workouts_activities')
        .select(['id', 'name'])
        .orderBy('sort_order', 'asc')
        .execute();

        return {
            status: 'success',
            data: dailyWorkoutsActivities,
        };
    }

    // Daily Workouts Activities
    async getDailyWorkoutsActivitiesByDate(userId: number, query: any) {
        const { date_start, date_end } = query;
        // if date_start and date_end are not provided, use current date
        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();
        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();

        const dailyWorkoutsActivities = await db
        .selectFrom('daily_workouts_activities as wa')
        .leftJoin('workouts_activities as w', 'w.id', 'wa.activity_id')
        .where('wa.user_id', '=', userId)
        .where('wa.daily_at', '>=', startOfDay)
        .where('wa.daily_at', '<=', endOfDay)
        .select([
            'wa.id',
            'w.name',
            'wa.activity_time',
            'wa.calories',
        ])
        .execute();

        return {
            status: 'success',
            data: dailyWorkoutsActivities,
        };
    }

    // minutes to HH:MM:SS
    minutesToHHMMSS(minutes: number): string {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        const seconds = 0;
        return `${hours}:${remainingMinutes}:${seconds}`;
    }

    async postDailyWorkoutsActivities(userId: number, dailyWorkoutsActivitiesDto: DailyWorkoutsActivitiesDto) {
        const { id, duration } = dailyWorkoutsActivitiesDto;

        const workoutsActivities = await db
        .selectFrom('workouts_activities')
        .where('id', '=', id)
        .select(['calories'])
        .executeTakeFirst();

        if (!workoutsActivities) {
            return {
                status: 'error',
                message: 'Workouts activity not found',
            };
        }

        // duration in minutes
        const calculated_calories = duration * (workoutsActivities.calories/60);

        const dailyWorkoutsActivities = await db
        .insertInto('daily_workouts_activities')
        .values({
            user_id: userId,
            activity_id: id,
            activity_time: this.minutesToHHMMSS(duration),
            calories: calculated_calories
        })
        .execute();

        return {
            status: 'success',
            data: [],
        };
    }

    async hasActiveProtocolWorkout(userId: number) {
        const today = new Date();

        console.log(`🔍 hasActiveProtocolWorkout: Verificando protocolo ativo para usuário ${userId}`);
        console.log(`📅 Data atual: ${today.toISOString()}`);

        const protocol = await db
            .selectFrom('coach_protocols')
            .selectAll()
            .where('client_id', '=', userId)
            .where('started_at', '<=', today)
            .where('ended_at', 'is', null)
            .orderBy('started_at', 'desc') // Caso existam múltiplos, pega o mais recente
            .executeTakeFirst();

        if (protocol) {
            console.log(`✅ hasActiveProtocolWorkout: Protocolo ativo encontrado - ID: ${protocol.id}, Nome: ${protocol.name}`);
            console.log(`📊 Detalhes: started_at=${protocol.started_at}, ended_at=${protocol.ended_at}`);
            return true;
        } else {
            console.log(`❌ hasActiveProtocolWorkout: Nenhum protocolo ativo encontrado`);
            return false;
        }
    }

    async hasActiveProtocolDiet(userId: number) {
        const today = new Date();

        console.log(`🔍 hasActiveProtocolDiet: Verificando protocolo ativo para usuário ${userId}`);
        console.log(`📅 Data atual: ${today.toISOString()}`);

        // CORREÇÃO CRÍTICA: Usar a mesma query da exibição para garantir consistência absoluta
        const protocol = await db
            .selectFrom('nutritionist_protocols as p')
            .leftJoin('select_options as s', 's.id', 'p.type_id')
            .select(['p.id', 'p.name', 'p.started_at', 'p.ended_at', 's.value_option as type', 'p.type_id'])
            .where('p.client_id', '=', userId)
            .where('p.started_at', '<=', today)
            .where('p.ended_at', 'is', null)
            .orderBy('p.started_at', 'desc')
            .executeTakeFirst();

        if (protocol) {
            console.log(`✅ hasActiveProtocolDiet: Protocolo ativo encontrado - ID: ${protocol.id}, Nome: ${protocol.name}`);
            console.log(`📊 Detalhes: started_at=${protocol.started_at}, ended_at=${protocol.ended_at}, type=${protocol.type || 'sem tipo'}, type_id=${protocol.type_id}`);

            // Aviso se o protocolo não tem type válido, mas ainda considera como ativo
            if (!protocol.type) {
                console.warn(`⚠️ hasActiveProtocolDiet: Protocolo ${protocol.id} não tem type_id válido, mas será considerado ativo`);
            }
            return true;
        } else {
            console.log(`❌ hasActiveProtocolDiet: Nenhum protocolo ativo encontrado`);

            // DEBUG: Verificar se há protocolos órfãos
            const orphanProtocols = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select(['p.id', 'p.name', 'p.type_id', 's.value_option as type'])
                .where('p.client_id', '=', userId)
                .where('p.started_at', '<=', today)
                .where('p.ended_at', 'is', null)
                .where('s.id', 'is', null) // Protocolos com type_id inválido
                .execute();

            if (orphanProtocols.length > 0) {
                console.warn(`⚠️ hasActiveProtocolDiet: Encontrados ${orphanProtocols.length} protocolos órfãos (type_id inválido):`, orphanProtocols);

                // AUTO-CORREÇÃO: Marcar protocolos órfãos como finalizados
                for (const orphan of orphanProtocols) {
                    await db
                        .updateTable('nutritionist_protocols')
                        .set({ ended_at: today, updated_at: today })
                        .where('id', '=', orphan.id)
                        .execute();

                    console.log(`🔧 hasActiveProtocolDiet: Protocolo órfão ${orphan.id} automaticamente finalizado`);
                }
            }

            return false;
        }
    }

    // MÉTODO TEMPORÁRIO PARA CORREÇÃO DE PROTOCOLOS ÓRFÃOS
    async fixOrphanProtocols(userId: number) {
        console.log(`🔧 fixOrphanProtocols: Iniciando correção para usuário ${userId}`);

        const today = new Date();
        const results = {
            orphansFound: 0,
            orphansFixed: 0,
            consistencyRestored: false,
            details: []
        };

        try {
            // 1. Identificar protocolos órfãos
            const orphanProtocols = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select(['p.id', 'p.name', 'p.type_id', 'p.started_at', 'p.ended_at'])
                .where('p.client_id', '=', userId)
                .where('p.ended_at', 'is', null)
                .where('s.id', 'is', null) // type_id inválido
                .execute();

            results.orphansFound = orphanProtocols.length;
            console.log(`🔍 Encontrados ${orphanProtocols.length} protocolos órfãos`);

            if (orphanProtocols.length > 0) {
                // 2. Finalizar protocolos órfãos
                for (const orphan of orphanProtocols) {
                    await db
                        .updateTable('nutritionist_protocols')
                        .set({
                            ended_at: today,
                            updated_at: today
                        })
                        .where('id', '=', orphan.id)
                        .execute();

                    results.orphansFixed++;
                    (results.details as any[]).push({
                        id: orphan.id,
                        name: orphan.name,
                        type_id: orphan.type_id,
                        action: 'finalizado'
                    });

                    console.log(`✅ Protocolo órfão finalizado: ID ${orphan.id}, Nome: ${orphan.name}`);
                }
            }

            // 3. Verificar consistência após correção
            const validationCount = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select(db.fn.count('id').as('count'))
                .where('p.client_id', '=', userId)
                .where('p.started_at', '<=', today)
                .where('p.ended_at', 'is', null)
                .where('s.id', 'is not', null)
                .executeTakeFirst();

            const displayCount = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select(db.fn.count('id').as('count'))
                .where('p.client_id', '=', userId)
                .where('p.started_at', '<=', today)
                .where('p.ended_at', 'is', null)
                .executeTakeFirst();

            results.consistencyRestored = Number(validationCount?.count || 0) === Number(displayCount?.count || 0);

            console.log(`📊 Pós-correção: Validação=${validationCount?.count || 0}, Exibição=${displayCount?.count || 0}`);
            console.log(`✅ Consistência restaurada: ${results.consistencyRestored}`);

            return results;

        } catch (error) {
            console.error(`❌ Erro na correção de protocolos órfãos:`, error);
            throw error;
        }
    }

    // REGRA OTIMIZADA: MANTER APENAS UM PROTOCOLO ATIVO POR VEZ
    async ensureSingleActiveProtocol(userId: number): Promise<{
        activeProtocol: any | null;
        deactivatedCount: number;
        message: string
    }> {
        console.log(`🔧 ensureSingleActiveProtocol: Verificando protocolos ativos para usuário ${userId}`);

        try {
            // Buscar todos os protocolos ativos (ended_at = null)
            const activeProtocols = await db
                .selectFrom('nutritionist_protocols')
                .select(['id', 'name', 'started_at', 'created_at'])
                .where('client_id', '=', userId)
                .where('ended_at', 'is', null)
                .orderBy('started_at', 'desc') // Mais recente primeiro
                .execute();

            console.log(`📊 Encontrados ${activeProtocols.length} protocolos ativos`);

            if (activeProtocols.length === 0) {
                console.log(`✅ Nenhum protocolo ativo encontrado`);
                return {
                    activeProtocol: null,
                    deactivatedCount: 0,
                    message: 'Nenhum protocolo ativo encontrado'
                };
            }

            if (activeProtocols.length === 1) {
                console.log(`✅ Apenas um protocolo ativo encontrado: ${activeProtocols[0].name} (ID: ${activeProtocols[0].id})`);
                return {
                    activeProtocol: activeProtocols[0],
                    deactivatedCount: 0,
                    message: 'Protocolo único ativo mantido'
                };
            }

            // Múltiplos protocolos ativos - manter apenas o mais recente
            const mostRecentProtocol = activeProtocols[0]; // Primeiro da lista ordenada por started_at desc
            const protocolsToDeactivate = activeProtocols.slice(1); // Todos os outros

            console.log(`⚠️ Múltiplos protocolos ativos encontrados (${activeProtocols.length})`);
            console.log(`✅ Mantendo ativo: ${mostRecentProtocol.name} (ID: ${mostRecentProtocol.id}, Started: ${mostRecentProtocol.started_at})`);
            console.log(`❌ Desativando ${protocolsToDeactivate.length} protocolos mais antigos`);

            // Finalizar protocolos mais antigos
            const now = new Date();
            let deactivatedCount = 0;

            for (const protocol of protocolsToDeactivate) {
                try {
                    await db
                        .updateTable('nutritionist_protocols')
                        .set({
                            ended_at: now,
                            updated_at: now
                        })
                        .where('id', '=', protocol.id)
                        .where('client_id', '=', userId)
                        .execute();

                    console.log(`🔄 Protocolo desativado: ${protocol.name} (ID: ${protocol.id})`);
                    deactivatedCount++;
                } catch (updateError) {
                    console.error(`❌ Erro ao desativar protocolo ${protocol.id}:`, updateError);
                }
            }

            console.log(`✅ Regra aplicada com sucesso: ${deactivatedCount} protocolos desativados, 1 mantido ativo`);

            return {
                activeProtocol: mostRecentProtocol,
                deactivatedCount,
                message: `Mantido protocolo mais recente ativo. ${deactivatedCount} protocolos antigos foram finalizados.`
            };

        } catch (error) {
            console.error(`❌ Erro ao aplicar regra de protocolo único:`, error);
            throw error;
        }
    }

    // HISTÓRICO DE PROTOCOLOS DE DIETA - VERSÃO ULTRA SIMPLIFICADA
    async getProtocolsDietHistory(userId: number, options: {
        page?: number;
        limit?: number;
        status?: 'active' | 'finished' | 'all';
        startDate?: string;
        endDate?: string;
        type?: string;
    } = {}) {
        console.log(`📚 getProtocolsDietHistory: Iniciando busca para usuário ${userId}`, options);

        try {
            const {
                page = 1,
                limit = 10,
                status = 'all'
            } = options;

            const offset = (page - 1) * limit;

            console.log(`🔍 Parâmetros processados: page=${page}, limit=${limit}, offset=${offset}, status=${status}`);

            // Query mais simples possível
            console.log(`🔍 Executando query básica para nutritionist_protocols...`);

            const protocols = await db
                .selectFrom('nutritionist_protocols')
                .select([
                    'id',
                    'name',
                    'objective',
                    'started_at',
                    'ended_at',
                    'created_at',
                    'goal_calories',
                    'goal_protein',
                    'goal_carbs',
                    'goal_fat'
                ])
                .where('client_id', '=', userId)
                .orderBy('started_at', 'desc')
                .limit(limit)
                .offset(offset)
                .execute();

            console.log(`✅ Query básica executada. Encontrados ${protocols.length} protocolos`);

            // Processar protocolos de forma simples
            const processedProtocols = protocols.map(protocol => ({
                id: protocol.id,
                name: protocol.name || 'Protocolo sem nome',
                objective: protocol.objective || '',
                started_at: protocol.started_at,
                ended_at: protocol.ended_at,
                created_at: protocol.created_at,
                goal_calories: protocol.goal_calories || 0,
                goal_protein: protocol.goal_protein || 0,
                goal_carbs: protocol.goal_carbs || 0,
                goal_fat: protocol.goal_fat || 0,
                type: 'Dieta',
                status: protocol.ended_at ? 'finished' : 'active',
                duration_days: protocol.ended_at && protocol.started_at
                    ? Math.floor((new Date(protocol.ended_at).getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24))
                    : 0
            }));

            // Filtrar por status se necessário
            const filteredProtocols = status === 'all'
                ? processedProtocols
                : processedProtocols.filter(p => p.status === status);

            console.log(`📊 Protocolos após filtro de status: ${filteredProtocols.length}`);

            // Contar total simples
            const totalResult = await db
                .selectFrom('nutritionist_protocols')
                .select(db.fn.count('id').as('total'))
                .where('client_id', '=', userId)
                .executeTakeFirst();

            const total = Number(totalResult?.total || 0);

            console.log(`📊 Total de protocolos no banco: ${total}`);

            // Estatísticas básicas
            const activeCount = processedProtocols.filter(p => p.status === 'active').length;
            const finishedCount = processedProtocols.filter(p => p.status === 'finished').length;

            const result = {
                protocols: filteredProtocols,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit),
                    hasNext: page < Math.ceil(total / limit),
                    hasPrev: page > 1
                },
                stats: {
                    total: total,
                    active: activeCount,
                    finished: finishedCount,
                    avgDuration: 0,
                    topTypes: [{ type: 'Dieta', count: total }]
                },
                filters: {
                    status,
                    startDate: options.startDate,
                    endDate: options.endDate,
                    type: options.type
                }
            };

            console.log(`✅ Histórico processado com sucesso:`, {
                protocolsCount: result.protocols.length,
                total: result.pagination.total,
                page: result.pagination.page,
                activeCount,
                finishedCount
            });

            return result;

        } catch (error) {
            console.error(`❌ Erro ao buscar histórico de protocolos para usuário ${userId}:`, error);
            console.error(`❌ Stack trace:`, error.stack);
            console.error(`❌ Parâmetros recebidos:`, { userId, options });

            // Retornar uma resposta de erro mais amigável
            throw new HttpException({
                status: 500,
                message: ['Erro interno ao buscar histórico de protocolos.'],
                error: error.message
            }, 500);
        }
    }

    // ESTATÍSTICAS DE PROTOCOLOS DE DIETA
    async getProtocolsDietStats(userId: number) {
        try {
            const today = new Date();

            // Estatísticas simplificadas
            const totalProtocols = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select(db.fn.count('id').as('count'))
                .where('p.client_id', '=', userId)
                .where('s.id', 'is not', null)
                .executeTakeFirst();

            const activeProtocols = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select(db.fn.count('id').as('count'))
                .where('p.client_id', '=', userId)
                .where('p.ended_at', 'is', null)
                .where('p.started_at', '<=', today)
                .where('s.id', 'is not', null)
                .executeTakeFirst();

            const finishedProtocols = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select(db.fn.count('id').as('count'))
                .where('p.client_id', '=', userId)
                .where('p.ended_at', 'is not', null)
                .where('s.id', 'is not', null)
                .executeTakeFirst();

            // Tipos mais usados
            const topTypes = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select([
                    's.value_option as type',
                    db.fn.count('id').as('count')
                ])
                .where('p.client_id', '=', userId)
                .where('s.id', 'is not', null)
                .groupBy('s.value_option')
                .orderBy('count', 'desc')
                .limit(5)
                .execute();

            return {
                total: Number(totalProtocols?.count || 0),
                active: Number(activeProtocols?.count || 0),
                finished: Number(finishedProtocols?.count || 0),
                avgDuration: 0, // Será calculado no frontend se necessário
                topTypes: topTypes.map(t => ({
                    type: t.type,
                    count: Number(t.count)
                }))
            };

        } catch (error) {
            console.error(`❌ Erro ao calcular estatísticas:`, error);
            return {
                total: 0,
                active: 0,
                finished: 0,
                avgDuration: 0,
                topTypes: []
            };
        }
    }

    // FINALIZAR PROTOCOLO DE DIETA
    async finishProtocolDiet(userId: number, protocolId: number) {
        console.log(`🏁 finishProtocolDiet: Finalizando protocolo ${protocolId} para usuário ${userId}`);

        try {
            const today = new Date();

            // Verificar se o protocolo existe e pertence ao usuário
            const protocol = await db
                .selectFrom('nutritionist_protocols')
                .selectAll()
                .where('id', '=', protocolId)
                .where('client_id', '=', userId)
                .where('ended_at', 'is', null)
                .executeTakeFirst();

            if (!protocol) {
                throw new Error('Protocolo não encontrado ou já finalizado');
            }

            // Finalizar protocolo
            const updatedProtocol = await db
                .updateTable('nutritionist_protocols')
                .set({
                    ended_at: today,
                    updated_at: today
                })
                .where('id', '=', protocolId)
                .where('client_id', '=', userId)
                .executeTakeFirst();

            console.log(`✅ Protocolo ${protocolId} finalizado com sucesso`);

            return {
                id: protocolId,
                name: protocol.name,
                ended_at: today,
                duration_days: Math.floor((today.getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24))
            };

        } catch (error) {
            console.error(`❌ Erro ao finalizar protocolo:`, error);
            throw error;
        }
    }

    // DUPLICAR PROTOCOLO DE DIETA - VERSÃO SIMPLIFICADA
    async duplicateProtocolDiet(userId: number, protocolId: number, newStartDate?: string) {
        console.log(`📋 duplicateProtocolDiet: Duplicando protocolo ${protocolId} para usuário ${userId}`);

        try {
            // Verificar se usuário já tem protocolo ativo
            if (await this.hasActiveProtocolDiet(userId)) {
                throw new Error('Você já possui um protocolo ativo. Finalize o protocolo atual antes de duplicar outro.');
            }

            // Buscar protocolo original
            const originalProtocol = await db
                .selectFrom('nutritionist_protocols')
                .selectAll()
                .where('id', '=', protocolId)
                .where('client_id', '=', userId)
                .executeTakeFirst();

            if (!originalProtocol) {
                throw new Error('Protocolo não encontrado');
            }

            const startDate = newStartDate ? new Date(newStartDate) : new Date();
            const today = new Date();

            // Criar novo protocolo baseado no original (apenas dados básicos)
            const newProtocol = await db
                .insertInto('nutritionist_protocols')
                .values({
                    client_id: userId,
                    name: `${originalProtocol.name} (Cópia)`,
                    type_id: originalProtocol.type_id,
                    objective: originalProtocol.objective,
                    initial_weight: originalProtocol.initial_weight,
                    general_notes: originalProtocol.general_notes,
                    goal_calories: originalProtocol.goal_calories,
                    goal_protein: originalProtocol.goal_protein,
                    goal_carbs: originalProtocol.goal_carbs,
                    goal_fat: originalProtocol.goal_fat,
                    goal_water: originalProtocol.goal_water,
                    started_at: startDate,
                    created_at: today,
                    updated_at: today
                })
                .executeTakeFirst();

            const newProtocolId = Number(newProtocol.insertId);
            console.log(`✅ Protocolo duplicado com sucesso. Novo ID: ${newProtocolId}`);

            // Duplicar refeições do protocolo original
            const originalMeals = await db
                .selectFrom('nutritionist_protocols_meals')
                .selectAll()
                .where('protocol_id', '=', protocolId)
                .execute();

            let mealsCount = 0;
            for (const meal of originalMeals) {
                const newMeal = await db
                    .insertInto('nutritionist_protocols_meals')
                    .values({
                        protocol_id: newProtocolId,
                        name: meal.name,
                        day_of_week: meal.day_of_week,
                        meal_time: meal.meal_time,
                        created_at: today,
                        updated_at: today
                    })
                    .executeTakeFirst();

                const newMealId = Number(newMeal.insertId);

                // Duplicar alimentos da refeição
                const originalFoods = await db
                    .selectFrom('nutritionist_protocols_meals_foods')
                    .selectAll()
                    .where('meal_id', '=', meal.id)
                    .execute();

                for (const food of originalFoods) {
                    await db
                        .insertInto('nutritionist_protocols_meals_foods')
                        .values({
                            meal_id: newMealId,
                            food_id: food.food_id,
                            name: food.name,
                            quantity: food.quantity,
                            unit: food.unit,
                            calories: food.calories,
                            protein: food.protein,
                            carbs: food.carbs,
                            fat: food.fat,
                            fiber: food.fiber,
                            created_at: today,
                            updated_at: today
                        })
                        .execute();
                }
                mealsCount++;
            }

            // Duplicar suplementos do protocolo original
            const originalSupplements = await db
                .selectFrom('nutritionist_protocols_supplements')
                .selectAll()
                .where('protocol_id', '=', protocolId)
                .execute();

            let supplementsCount = 0;
            for (const supplement of originalSupplements) {
                await db
                    .insertInto('nutritionist_protocols_supplements')
                    .values({
                        protocol_id: newProtocolId,
                        name: supplement.name,
                        dosage: supplement.dosage,
                        supplement_time: supplement.supplement_time,
                        notes: supplement.notes,
                        created_at: today,
                        updated_at: today
                    })
                    .execute();
                supplementsCount++;
            }

            console.log(`✅ Duplicação completa: ${mealsCount} refeições e ${supplementsCount} suplementos copiados`);

            return {
                id: newProtocolId,
                name: `${originalProtocol.name} (Cópia)`,
                originalId: protocolId,
                started_at: startDate,
                mealsCount,
                supplementsCount
            };

        } catch (error) {
            console.error(`❌ Erro ao duplicar protocolo:`, error);
            throw error;
        }
    }

    async postProtocolsWorkout(userId: number, createProtocolWorkoutDto: CreateProtocolWorkoutDto) {
      const { name, type_id, split, frequency, objective, general_notes, workouts } = createProtocolWorkoutDto;
  
      if (await this.hasActiveProtocolWorkout(userId)) {
          throw new HttpException({
              status: 400,
              message: ['Você já possui um protocolo ativo.'],
          }, 400);
      }
  
      const new_protocol = await db
          .insertInto('coach_protocols')
          .values({
              client_id: userId,
              name,
              type_id,
              split,
              frequency,
              objective,
              general_notes,
              started_at: new Date(),
              ended_at: null,
              created_at: new Date(),
              updated_at: new Date(),
          })
          .executeTakeFirst();
  
      const new_protocol_id = Number(new_protocol.insertId);
  
      if (Array.isArray(workouts)) {
          const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  
          // Usar for...of para garantir a ordem
          for (let index = 0; index < workouts.length; index++) {
              const workout: any = workouts[index];
              if (workout?.exercises && Array.isArray(workout.exercises)) {
                  const workout_name = letters[index];
                  const new_workout = await db
                      .insertInto('coach_protocols_workouts')
                      .values({
                          protocol_id: new_protocol_id,
                          name: `Treino ${workout_name}`,
                      })
                      .executeTakeFirst();
  
                  const new_workout_id = Number(new_workout.insertId);
  
                  // Garantir a ordem dos exercícios também
                  for (const exercise of workout.exercises) {
                      if (exercise.exercise_id) {
                          await db
                              .insertInto('coach_protocols_workouts_exercises')
                              .values({
                                  workout_id: new_workout_id,
                                  exercise_id: exercise.exercise_id,
                                  sets: exercise.sets,
                                  reps: exercise.reps,
                                  rpe: exercise.rpe,
                                  rest_seconds: exercise.rest_seconds,
                                  notes: exercise.notes || null,
                              })
                              .execute();
                      }
                  }
              }
          }
      }
  
      return {
          status: 'success',
          data: [],
      };
  }

  // ============================================================================
  // WORKOUT PROTOCOL MANAGEMENT
  // ============================================================================

  /**
   * Finalizar protocolo de treino
   */
  async finishProtocolWorkout(userId: number, protocolId: number) {
      console.log(`🏁 finishProtocolWorkout: Finalizando protocolo ${protocolId} para usuário ${userId}`);

      try {
          const today = new Date();

          // Verificar se o protocolo existe e pertence ao usuário
          const protocol = await db
              .selectFrom('coach_protocols')
              .selectAll()
              .where('id', '=', protocolId)
              .where('client_id', '=', userId)
              .where('ended_at', 'is', null)
              .executeTakeFirst();

          if (!protocol) {
              throw new Error('Protocolo não encontrado ou já finalizado');
          }

          // Finalizar o protocolo
          await db
              .updateTable('coach_protocols')
              .set({
                  ended_at: today,
                  updated_at: today
              })
              .where('id', '=', protocolId)
              .where('client_id', '=', userId)
              .execute();

          console.log(`✅ Protocolo de treino ${protocolId} finalizado com sucesso`);

          return {
              id: protocolId,
              name: protocol.name,
              ended_at: today,
              duration_days: Math.floor((today.getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24))
          };

      } catch (error) {
          console.error(`❌ Erro ao finalizar protocolo de treino:`, error);
          throw error;
      }
  }

  /**
   * Duplicar protocolo de treino
   */
  async duplicateProtocolWorkout(userId: number, protocolId: number, newStartDate?: string) {
      console.log(`📋 duplicateProtocolWorkout: Duplicando protocolo ${protocolId} para usuário ${userId}`);

      try {
          // Verificar se usuário já tem protocolo ativo
          if (await this.hasActiveProtocolWorkout(userId)) {
              throw new Error('Você já possui um protocolo de treino ativo. Finalize o protocolo atual antes de duplicar outro.');
          }

          // Buscar protocolo original
          const originalProtocol = await db
              .selectFrom('coach_protocols')
              .selectAll()
              .where('id', '=', protocolId)
              .where('client_id', '=', userId)
              .executeTakeFirst();

          if (!originalProtocol) {
              throw new Error('Protocolo não encontrado');
          }

          const startDate = newStartDate ? new Date(newStartDate) : new Date();
          const today = new Date();

          // Criar novo protocolo
          const newProtocol = await db
              .insertInto('coach_protocols')
              .values({
                  client_id: userId,
                  name: `${originalProtocol.name} (Reutilizado)`,
                  objective: originalProtocol.objective,
                  type_id: originalProtocol.type_id,
                  split: originalProtocol.split,
                  frequency: originalProtocol.frequency,
                  started_at: startDate,
                  ended_at: null,
                  created_at: today,
                  updated_at: today
              })
              .executeTakeFirst();

          const newProtocolId = Number(newProtocol.insertId);

          // Duplicar workouts do protocolo
          const originalWorkouts = await db
              .selectFrom('coach_protocols_workouts')
              .selectAll()
              .where('protocol_id', '=', protocolId)
              .execute();

          for (const workout of originalWorkouts) {
              const newWorkout = await db
                  .insertInto('coach_protocols_workouts')
                  .values({
                      protocol_id: newProtocolId,
                      name: workout.name,
                      created_at: today,
                      updated_at: today
                  })
                  .executeTakeFirst();

              const newWorkoutId = Number(newWorkout.insertId);

              // Duplicar exercícios do workout
              const originalExercises = await db
                  .selectFrom('coach_protocols_workouts_exercises')
                  .selectAll()
                  .where('workout_id', '=', workout.id)
                  .execute();

              for (const exercise of originalExercises) {
                  await db
                      .insertInto('coach_protocols_workouts_exercises')
                      .values({
                          workout_id: newWorkoutId,
                          exercise_id: exercise.exercise_id,
                          sets: exercise.sets,
                          reps: exercise.reps,
                          notes: exercise.notes,
                          created_at: today,
                          updated_at: today
                      })
                      .execute();
              }
          }

          console.log(`✅ Protocolo de treino ${protocolId} duplicado com sucesso. Novo ID: ${newProtocolId}`);

          return {
              id: newProtocolId,
              name: `${originalProtocol.name} (Reutilizado)`,
              started_at: startDate,
              original_protocol_id: protocolId
          };

      } catch (error) {
          console.error(`❌ Erro ao duplicar protocolo de treino:`, error);
          throw error;
      }
  }



  // ============================================================================
  // DEBUG METHODS - TEMPORARY FOR DIAGNOSTICS
  // ============================================================================

  /**
   * DEBUG: Verificar dados raw de protocolos no banco
   */
  async debugProtocolsRaw(userId: number) {
      console.log(`🔍 DEBUG: debugProtocolsRaw para usuário ${userId}`);

      try {
          // Buscar todos os protocolos do usuário
          const allProtocols = await db
              .selectFrom('coach_protocols')
              .selectAll()
              .where('client_id', '=', userId)
              .execute();

          console.log(`🔍 DEBUG: Encontrados ${allProtocols.length} protocolos para usuário ${userId}`);

          return {
              userId,
              totalProtocols: allProtocols.length,
              protocols: allProtocols,
              activeProtocols: allProtocols.filter(p => !p.ended_at),
              finishedProtocols: allProtocols.filter(p => p.ended_at),
              tables: {
                  coach_protocols: allProtocols.length
              }
          };
      } catch (error) {
          console.error(`❌ DEBUG: Erro ao buscar protocolos raw:`, error);
          throw error;
      }
  }

  /**
   * DEBUG: Verificar dados raw de treinos completados no banco
   */
  async debugWorkoutsRaw(userId: number) {
      console.log(`🔍 DEBUG: debugWorkoutsRaw para usuário ${userId}`);

      try {
          // Buscar todos os registros de treinos do usuário
          const allWorkouts = await db
              .selectFrom('daily_coach_protocol')
              .selectAll()
              .where('user_id', '=', userId)
              .execute();

          console.log(`🔍 DEBUG: Encontrados ${allWorkouts.length} registros de treinos para usuário ${userId}`);

          const completedWorkouts = allWorkouts.filter(w => w.total_calories > 0);
          const recentWorkouts = allWorkouts.filter(w => {
              if (!w.daily_at) return false;
              const workoutDate = new Date(w.daily_at);
              const thirtyDaysAgo = new Date();
              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
              return workoutDate >= thirtyDaysAgo;
          });

          return {
              userId,
              totalWorkouts: allWorkouts.length,
              completedWorkouts: completedWorkouts.length,
              recentWorkouts: recentWorkouts.length,
              workouts: allWorkouts,
              completedWorkoutsData: completedWorkouts,
              recentWorkoutsData: recentWorkouts,
              tables: {
                  daily_coach_protocol: allWorkouts.length
              }
          };
      } catch (error) {
          console.error(`❌ DEBUG: Erro ao buscar treinos raw:`, error);
          throw error;
      }
  }

  /**
   * Buscar histórico de protocolos de treino
   */
  async getProtocolsWorkoutHistory(userId: number, options: any) {
      console.log(`📚 getProtocolsWorkoutHistory: Buscando histórico para usuário ${userId}`, options);

      try {
          const { page = 1, limit = 10, status = 'all' } = options;
          const offset = (page - 1) * limit;

          // Base query
          let query = db
              .selectFrom('coach_protocols')
              .selectAll()
              .where('client_id', '=', userId)
              .orderBy('created_at', 'desc');

          // Apply status filter
          if (status !== 'all') {
              if (status === 'active') {
                  query = query.where('ended_at', 'is', null);
              } else if (status === 'finished') {
                  query = query.where('ended_at', 'is not', null);
              }
          }

          // Get total count
          const totalResult = await query
              .select(db.fn.count<number>('id').as('total'))
              .executeTakeFirst();
          const total = Number(totalResult?.total || 0);

          // Get paginated results
          const protocols = await query
              .limit(limit)
              .offset(offset)
              .execute();

          // Calculate stats
          const stats = {
              total,
              active: protocols.filter(p => !p.ended_at).length,
              finished: protocols.filter(p => p.ended_at).length,
              avgDuration: 0, // Could calculate based on start/end dates
              topTypes: []
          };

          // Format protocols for frontend
          const formattedProtocols = protocols.map(protocol => ({
              id: protocol.id,
              name: protocol.name,
              objective: protocol.objective,
              started_at: protocol.started_at,
              ended_at: protocol.ended_at,
              created_at: protocol.created_at,
              updated_at: protocol.updated_at,
              type: protocol.type_id?.toString() || 'workout',
              status: protocol.ended_at ? 'finished' : 'active',
              duration_days: protocol.ended_at ?
                  Math.floor((new Date(protocol.ended_at).getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24)) :
                  Math.floor((new Date().getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24)),
              split: protocol.split,
              frequency: protocol.frequency
          }));

          const pagination = {
              page,
              limit,
              total,
              totalPages: Math.ceil(total / limit),
              hasNext: page < Math.ceil(total / limit),
              hasPrev: page > 1
          };

          console.log(`✅ getProtocolsWorkoutHistory: Encontrados ${protocols.length} protocolos`);

          return {
              protocols: formattedProtocols,
              pagination,
              stats,
              filters: {
                  status,
                  startDate: options.startDate,
                  endDate: options.endDate,
                  type: options.type
              }
          };

      } catch (error) {
          console.error(`❌ Erro ao buscar histórico de protocolos de treino:`, error);
          throw error;
      }
  }

  /**
   * Debug: Buscar protocolos raw do banco
   */
  async getProtocolsRawDebug(userId: number) {
      console.log(`🔍 getProtocolsRawDebug: Buscando dados raw para usuário ${userId}`);

      try {
          // Buscar todos os protocolos do usuário
          const protocols = await db
              .selectFrom('coach_protocols')
              .selectAll()
              .where('client_id', '=', userId)
              .orderBy('created_at', 'desc')
              .execute();

          console.log(`🔍 getProtocolsRawDebug: Encontrados ${protocols.length} protocolos`);

          return {
              total: protocols.length,
              protocols: protocols.map(p => ({
                  id: p.id,
                  name: p.name,
                  started_at: p.started_at,
                  ended_at: p.ended_at,
                  status: p.ended_at ? 'finished' : 'active',
                  objective: p.objective,
                  created_at: p.created_at
              }))
          };

      } catch (error) {
          console.error(`❌ Erro ao buscar protocolos raw:`, error);
          throw error;
      }
  }

  // Workout AI
  async generateWorkoutProtocolPrompt(userInfo: any) {
    const goalMapping = {
      "Hipertrofia": 37,
      "Força": 38,
      "Resistência": 39,
      "Emagrecimento": 103
  };

    const goalId = goalMapping[userInfo.goal] || 2; // Default para Hipertrofia
    const frequency = userInfo.timeAvailable ? Math.floor(userInfo.timeAvailable / 60 * 3) : 4; // Calcula frequência baseada no tempo disponível

    return `
    # CONTEXTO OBRIGATÓRIO:
    ## DADOS DO USUÁRIO:
    - Altura: ${userInfo?.height || 'Não informada'} cm
    - Peso: ${userInfo?.weight || 'Não informado'} kg
    - % Gordura: ${userInfo?.bodyFat ? userInfo.bodyFat + '%' : 'Não medida'}
    - Nível de Atividade: ${userInfo?.activityLevel}
    - Objetivo: ${userInfo?.goal} (ID: ${goalId})
    - Nível de Experiência com Musculação: ${userInfo?.experience || 'Não especificado'}
    - Split Preferido: ${userInfo?.splitPreference || 'Não especificado'}
    - Tempo Disponível por Sessão: ${userInfo?.timeAvailable || 60} minutos
    - Equipamentos Disponíveis: ${userInfo?.equipment?.join(', ') || 'Nenhum equipamento específico'}
    - Grupos Musculares Alvo: ${userInfo?.targetMuscles?.join(', ') || 'Todos os grupos musculares'}
    - Restrições: ${userInfo?.restrictions?.join(', ') || 'Nenhuma restrição informada'}
    - Preferências: ${userInfo?.preferences?.join(', ') || 'Nenhuma preferência específica'}

    ${userInfo?.notes ? '## COMENTÁRIOS ADICIONAIS:\n' + userInfo.notes : ''}

    # TAREFA PRINCIPAL:
    Como treinador IA especializado, crie um protocolo de treino personalizado considerando:
    1. Objetivo do usuário (${userInfo?.goal})
    2. Nível de experiência (${userInfo?.experience})
    3. Disponibilidade de tempo (${userInfo?.timeAvailable} minutos por sessão)
    4. Equipamentos disponíveis (${userInfo?.equipment?.join(', ')})
    5. Grupos musculares alvo (${userInfo?.targetMuscles?.join(', ')})

    # DIRETRIZES PARA O PROTOCOLO:
    - Priorize exercícios compostos para usuários intermediários/avançados
    - Inclua exercícios de isolamento conforme necessário
    - Ajuste volume (séries/repetições) baseado no objetivo:
      * Força: 3-6 séries de 1-6 repetições
      * Hipertrofia: 3-5 séries de 6-12 repetições
      * Resistência: 2-4 séries de 12-20+ repetições
    - Defina descanso adequado:
      * Força: 2-5 minutos
      * Hipertrofia: 1-2 minutos
      * Resistência: 30-60 segundos
    - Use nomenclatura padrão de exercícios em português

    # MODELO JSON OBRIGATÓRIO:
    {
        "name": "[Nome criativo baseado no objetivo e split]",
        "type_id": ${goalId},
        "split": "[Split baseado na preferência do usuário]",
        "frequency": ${frequency}, // Frequência semanal de treinos (Apenas número de dias na semana)
        "objective": "Hipertrofia/Força/Resistência/Emagrecimento",
        "general_notes": "[Recomendações personalizadas]",
        "workouts": [
            {
                "name": "Treino [A/B/C/etc]", // Usar letras sequenciais caso seja treino ABC. Aberto para outras convenções dependendo do split
                "exercises": [
                    {
                        "name": "[Nome do Exercício]",
                        "sets": [Número de séries],
                        "reps": [Número de repetições ou intervalo],
                        "rpe": [Escala de 1-10, opcional],
                        "rest_seconds": [Tempo em segundos],
                        "notes": "[Instruções específicas, opcional]"
                    }
                    // Continuar com outros exercícios
                ]
            }
            // Continuar com outros treinos conforme a frequência
        ]
    }

    # EXEMPLO MÍNIMO ESPERADO:
    {
        "name": "Protocolo ABC",
        "type_id": 1,
        "split": "A-B-C",
        "frequency": 4,
        "objective": "Hipertrofia/Força/Resistência/Emagrecimento",
        "general_notes": "Aumentar carga progressivamente a cada semana",
        "workouts": [
            {
                "name": "Treino A",
                "exercises": [
                    {
                        "name": "Supino reto",
                        "sets": 4,
                        "reps": 4,
                        "rpe": 8,
                        "rest_seconds": 180,
                        "notes": "Manter forma estrita, 2s na fase excêntrica"
                    },
                    {
                        "name": "Desenvolvimento com halteres",
                        "sets": 3,
                        "reps": 6,
                        "rest_seconds": 120
                    }
                ]
            }
            // Continuar com outros treinos
        ]
    }

    # REGRAS ESSENCIAIS:
    1. Gerar EXATAMENTE o número de treinos correspondente à frequência semanal
    2. Cobrir TODOS os grupos musculares alvo do usuário
    3. Variar exercícios para evitar monotonia
    4. Usar nomenclatura de exercícios em português (ex: "Agachamento livre" em vez de "Barbell squat")
    5. Incluir pelo menos 1 exercício composto por treino
    6. Ajustar volume total conforme nível de experiência
    7. Manter sessões dentro do tempo disponível (${userInfo?.timeAvailable || 60} minutos)

    Retorne APENAS o JSON válido SEM comentários ou markdown.
    `;
}

async postProtocolsWorkoutAi(userId: number, userInfo: any) {
    if (await this.hasActiveProtocolWorkout(userId)) {
        throw new HttpException({
            status: 400,
            message: ['Você já possui um protocolo ativo.'],
        }, 400);
    }

    try {
        const prompt = await this.generateWorkoutProtocolPrompt(userInfo);
        const response = await this.openai.chat.completions.create({
            model: "gpt-4o-mini", // Optimized model for better performance
            messages: [
                {
                    role: "system",
                    content: "You are a professional fitness coach. Create workout protocols in valid JSON format only, ensuring complete muscle group coverage and exercise safety."
                },
                { role: "user", content: prompt }
            ],
            temperature: 0.1, // Reduced for consistency
            max_tokens: 2048, // Optimized for efficiency
            response_format: { type: "json_object" }
        });

        const aiResponseText: any = response.choices[0].message.content;
        const protocolDataText = this.extractJsonFromString(aiResponseText);
        const protocolData = JSON.parse(protocolDataText);

        if (!protocolData.name || !protocolData.type_id || !protocolData.workouts) {
            throw new Error('Dados essenciais ausentes no protocolo gerado');
        }

        // Iniciar transação
        const result = await db.transaction().execute(async (trx) => {
            // Inserir protocolo
            const new_protocol = await trx
                .insertInto('coach_protocols')
                .values({
                    client_id: userId,
                    name: protocolData.name,
                    type_id: protocolData.type_id,
                    split: protocolData.split,
                    frequency: protocolData.frequency,
                    objective: protocolData.objective,
                    general_notes: protocolData.general_notes || null,
                    started_at: new Date(),
                    ended_at: null,
                    created_at: new Date(),
                    updated_at: new Date(),
                })
                .executeTakeFirst();

            const new_protocol_id = Number(new_protocol.insertId);

            // Inserir treinos e exercícios
            for (let index = 0; index < protocolData.workouts.length; index++) {
                const workout = protocolData.workouts[index];
                const workout_name = workout.name;

                const new_workout = await trx
                    .insertInto('coach_protocols_workouts')
                    .values({
                        protocol_id: new_protocol_id,
                        name: workout_name,
                    })
                    .executeTakeFirst();

                const new_workout_id = Number(new_workout.insertId);

                if (workout.exercises && Array.isArray(workout.exercises)) {
                    await trx
                        .insertInto('coach_protocols_workouts_exercises')
                        .values(workout.exercises.map(exercise => ({
                            workout_id: new_workout_id,
                            exercise_id: null, // Pode ser preenchido posteriormente com IDs de exercícios cadastrados
                            name: exercise.name,
                            sets: exercise.sets,
                            reps: typeof exercise.reps === 'string' ? exercise.reps : exercise.reps.toString(),
                            rpe: exercise.rpe || null,
                            rest_seconds: exercise.rest_seconds || null,
                            notes: exercise.notes || null,
                        })))
                        .execute();
                }
            }

            return new_protocol_id;
        });

        return {
            status: "success",
            data: {
                protocol_id: result,
            },
        };
    } catch (error) {
        console.error('Erro ao processar protocolo de treino:', {
            message: error.message,
            stack: error.stack,
            input: userInfo
        });
        throw new HttpException({
            status: "error",
            message: ['Erro ao processar protocolo de treino.', error.message],
        }, 500);
    }
}


  

    async getActiveProtocolsWorkouts(userId: number) {
        const today = new Date();
        const protocol = await db
            .selectFrom('coach_protocols')
            .selectAll()
            .where('client_id', '=', userId)
            .where('started_at', '<=', today)
            .where('ended_at', 'is', null)
            .orderBy('started_at', 'desc') // Caso existam múltiplos, pega o mais recente
            .executeTakeFirst();
    
        if (!protocol) {
            return {
                status: 'success',
                data: {
                    has_protocol: false,
                }
            };
        }
    
        // Buscar todos os exercícios associados a esse protocolo
        const exercises = await db
            .selectFrom('coach_protocols_workouts as p')
            .leftJoin('coach_protocols_workouts_exercises as pe', 'pe.workout_id', 'p.id')
            .leftJoin('exercises as e', 'e.id', 'pe.exercise_id')
            .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')
            .leftJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')
            .select([
                'p.id',
                'pe.id as pe_id',
                'pe.exercise_id',
                'p.name as workout_name',
                'e.name as exercise_name',
                'pe.name as exercise_name2',
                's.value_option as muscle_group',
                'equipment.value_option as equipment',
                'e.media_url',
                'pe.sets',
                'pe.reps',
                'pe.rpe',
                'pe.rest_seconds',
                'pe.notes',
            ])
            .where('protocol_id', '=', protocol.id)
            // .where('pe.exercise_id', 'is not', null) // Evita exercícios inválidos
            .execute();
    
        // Criar a estrutura de workouts agrupando os exercícios corretamente
        const workouts = exercises.reduce((acc, curr) => {
            const workoutName = curr.workout_name;
    
            if (!acc[workoutName]) {
                acc[workoutName] = {
                    name: workoutName,
                    id: curr.id,
                    exercises: [],
                };
            }
    
            acc[workoutName].exercises.push(curr);
            return acc;
        }, {});

        const workoutsAndExercises = Object.values(workouts).map((workout: any) => ({
            name: workout.name,
            id: workout.id,
            exercises: workout.exercises.map((exercise: any) => ({
                id: exercise.pe_id,
                exercise_id: exercise.exercise_id || null,
                name: exercise.exercise_name || exercise.exercise_name2,
                muscle_group: exercise.muscle_group || null,
                equipment: exercise.equipment || null,
                media_url: exercise.media_url || null,
                sets: exercise.sets,
                reps: exercise.reps,
                rpe: exercise.rpe,
                rest_seconds: exercise.rest_seconds,
                notes: exercise.notes,
            })),
        }));

        const workoutsCompleted = await db
        .selectFrom('daily_coach_protocol')
        .where('user_id', '=', userId)
        .where('protocol_id', '=', protocol.id)
        .select(db.fn.count<number>('id').as('workouts_completed'))
        .executeTakeFirst();

    
        // Montar e retornar a resposta formatada
        return {
            status: 'success',
            data: {
                has_protocol: true,
                id: protocol.id,
                frequency: protocol.frequency,
                name: protocol.name,
                workouts_completed: workoutsCompleted?.workouts_completed || 0,
                general_notes: protocol.general_notes,
                objective: protocol.objective,
                split: protocol.split,
                started_at: protocol.started_at,
                type_id: protocol.type_id,
                workouts: workoutsAndExercises,
            },
        };
    }

    async getProtocolDietById(protocolId: number, userId: number) {
        console.log(`🔍 getProtocolDietById: Buscando protocolo de dieta ID: ${protocolId} (tipo: ${typeof protocolId}) para usuário: ${userId}`);

        try {
            // Buscar o protocolo básico
            const protocol = await db
                .selectFrom('nutritionist_protocols as p')
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select([
                    'p.id',
                    'p.name',
                    'p.objective',
                    'p.initial_weight',
                    'p.general_notes',
                    's.value_option as type',
                    'p.goal_calories as calories',
                    'p.goal_protein as protein',
                    'p.goal_carbs as carbs',
                    'p.goal_fat as fat',
                    'p.goal_water as water',
                    'p.started_at',
                    'p.ended_at',
                    'p.type_id',
                ])
                .where('p.id', '=', protocolId)
                .where('p.client_id', '=', userId)
                .executeTakeFirst();

            console.log(`📋 Protocolo de dieta encontrado:`, protocol ? 'SIM' : 'NÃO');
            if (protocol) {
                console.log(`📊 Detalhes do protocolo: ID=${protocol.id}, Nome="${protocol.name}", Tipo="${protocol.type}"`);
            }

            if (!protocol) {
                console.log(`❌ Protocolo de dieta ${protocolId} não encontrado para usuário ${userId}`);

                // Verificar se o protocolo existe mas para outro usuário
                const protocolExists = await db
                    .selectFrom('nutritionist_protocols')
                    .select(['id', 'client_id'])
                    .where('id', '=', protocolId)
                    .executeTakeFirst();

                if (protocolExists) {
                    console.log(`⚠️ Protocolo ${protocolId} existe mas pertence ao usuário ${protocolExists.client_id}, não ao usuário ${userId}`);
                    throw new HttpException({
                        status: 403,
                        message: ['Acesso negado ao protocolo.'],
                    }, 403);
                } else {
                    console.log(`❌ Protocolo ${protocolId} não existe no banco de dados`);
                    throw new HttpException({
                        status: 404,
                        message: ['Protocolo de dieta não encontrado.'],
                    }, 404);
                }
            }

        // Buscar todas as refeições do protocolo organizadas por dia da semana
        const mealRows = await db
            .selectFrom('nutritionist_protocols_meals as p')
            .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')
            .select([
                'p.id',
                'p.name',
                'p.day_of_week',
                'p.meal_time',
                'f.name as food_name',
                'f.quantity',
                'f.unit',
                'f.calories',
                'f.protein',
                'f.carbs',
                'f.fat',
                'f.fiber',
            ])
            .where('p.protocol_id', '=', protocolId)
            .orderBy('p.day_of_week', 'asc')
            .orderBy('p.meal_time', 'asc')
            .orderBy('p.id', 'asc')
            .execute();

        // Agrupar refeições por dia da semana
        const mealsByDay = new Map();
        mealRows.forEach((row) => {
            const day = row.day_of_week.toLowerCase();
            if (!mealsByDay.has(day)) {
                mealsByDay.set(day, []);
            }

            let meal = mealsByDay.get(day).find((m: any) => m.id === row.id);
            if (!meal) {
                meal = {
                    id: row.id,
                    name: row.name,
                    meal_time: row.meal_time,
                    nutrients: {
                        calories: 0,
                        protein: 0,
                        carbs: 0,
                        fat: 0,
                        fiber: 0,
                    },
                    foods: [],
                };
                mealsByDay.get(day).push(meal);
            }

            if (row.food_name) {
                meal.foods.push({
                    name: row.food_name,
                    unit: row.unit,
                    quantity: parseFloat(Number(row.quantity).toFixed(2)) || 0,
                });
                meal.nutrients.calories += parseFloat(Number(row.calories).toFixed(2)) || 0;
                meal.nutrients.protein += parseFloat(Number(row.protein).toFixed(2)) || 0;
                meal.nutrients.carbs += parseFloat(Number(row.carbs).toFixed(2)) || 0;
                meal.nutrients.fat += parseFloat(Number(row.fat).toFixed(2)) || 0;
                meal.nutrients.fiber += parseFloat(Number(row.fiber).toFixed(2)) || 0;
            }
        });

        // Converter Map para objeto
        const meals = Object.fromEntries(mealsByDay);

        // Buscar suplementos do protocolo
        const supplements = await db
            .selectFrom('nutritionist_protocols_supplements as p')
            .select(['p.name', 'p.dosage', 'p.supplement_time', 'p.notes'])
            .where('protocol_id', '=', protocolId)
            .execute();

            console.log(`✅ Protocolo de dieta ${protocolId} carregado com sucesso`);
            console.log(`📊 Resumo: ${Object.keys(meals).length} dias com refeições, ${supplements.length} suplementos`);

            return {
                id: protocol.id,
                type: protocol.type || 'Dieta',
                name: protocol.name,
                objective: protocol.objective,
                general_notes: protocol.general_notes,
                initial_weight: protocol.initial_weight,
                started_at: protocol.started_at,
                ended_at: protocol.ended_at,
                status: protocol.ended_at ? 'finished' : 'active',
                goals: {
                    calories: parseFloat(Number(protocol.calories).toFixed(2)) || 0,
                    protein: parseFloat(Number(protocol.protein).toFixed(2)) || 0,
                    carbs: parseFloat(Number(protocol.carbs).toFixed(2)) || 0,
                    fat: parseFloat(Number(protocol.fat).toFixed(2)) || 0,
                    water: parseFloat(Number(protocol.water).toFixed(2)) || 0,
                },
                meals,
                supplements: supplements.map((sup) => ({
                    name: sup.name,
                    dosage: sup.dosage,
                    supplement_time: sup.supplement_time,
                    notes: sup.notes,
                })),
            };

        } catch (error) {
            console.error(`❌ Erro ao buscar protocolo de dieta ${protocolId}:`, error);
            console.error(`❌ Stack trace:`, error.stack);
            throw error;
        }
    }

    async getProtocolWorkoutById(protocolId: number, userId: number) {
        console.log(`🔍 Buscando protocolo ID: ${protocolId} para usuário: ${userId}`);

        const protocol = await db
            .selectFrom('coach_protocols')
            .selectAll()
            .where('id', '=', protocolId)
            .where('client_id', '=', userId)
            .executeTakeFirst();

        console.log(`📋 Protocolo encontrado:`, protocol ? 'SIM' : 'NÃO');

        if (!protocol) {
            console.log(`❌ Protocolo ${protocolId} não encontrado para usuário ${userId}`);
            throw new HttpException({
                status: 404,
                message: ['Protocolo não encontrado.'],
            }, 404);
        }



        // Verificar se o protocolo está ativo
        if (protocol.ended_at) {
            console.log(`⚠️ Protocolo ${protocolId} está finalizado`);
        }

        // Primeiro, buscar todos os workouts do protocolo
        const allWorkouts = await db
            .selectFrom('coach_protocols_workouts')
            .select(['id', 'name', 'protocol_id'])
            .where('protocol_id', '=', protocol.id)
            .execute();



        // Depois, buscar todos os exercícios associados a esse protocolo
        const exercises = await db
            .selectFrom('coach_protocols_workouts as p')
            .leftJoin('coach_protocols_workouts_exercises as pe', 'pe.workout_id', 'p.id')
            .leftJoin('exercises as e', 'e.id', 'pe.exercise_id')
            .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')
            .leftJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')
            .select([
                'p.id',
                'pe.id as pe_id',
                'pe.exercise_id',
                'p.name as workout_name',
                'e.name as exercise_name',
                'pe.name as exercise_name2',
                's.value_option as muscle_group',
                'equipment.value_option as equipment',
                'e.media_url',
                'pe.sets',
                'pe.reps',
                'pe.rpe',
                'pe.rest_seconds',
                'pe.notes',
            ])
            .where('protocol_id', '=', protocol.id)
            .execute();



        // Inicializar estrutura de workouts com todos os workouts encontrados
        const workouts = {};
        allWorkouts.forEach(workout => {
            workouts[workout.name] = {
                name: workout.name,
                id: workout.id,
                exercises: [],
            };
        });

        // Adicionar exercícios aos workouts correspondentes
        exercises.forEach(curr => {
            const workoutName = curr.workout_name;
            if (workouts[workoutName] && curr.pe_id) {
                workouts[workoutName].exercises.push(curr);
            }
        });

        const workoutsAndExercises = Object.values(workouts).map((workout: any) => ({
            name: workout.name,
            id: workout.id,
            exercises: workout.exercises.map((exercise: any) => ({
                id: exercise.pe_id,
                exercise_id: exercise.exercise_id || null,
                name: exercise.exercise_name || exercise.exercise_name2,
                muscle_group: exercise.muscle_group || null,
                equipment: exercise.equipment || null,
                media_url: exercise.media_url || null,
                sets: exercise.sets,
                reps: exercise.reps,
                rpe: exercise.rpe,
                rest_seconds: exercise.rest_seconds,
                notes: exercise.notes,
            })),
        }));





        const workoutsCompleted = await db
            .selectFrom('daily_coach_protocol')
            .where('user_id', '=', userId)
            .where('protocol_id', '=', protocol.id)
            .select(db.fn.count<number>('id').as('workouts_completed'))
            .executeTakeFirst();

        return {
            status: 'success',
            data: {
                has_protocol: true,
                id: protocol.id,
                frequency: protocol.frequency,
                name: protocol.name,
                workouts_completed: workoutsCompleted?.workouts_completed || 0,
                general_notes: protocol.general_notes,
                objective: protocol.objective,
                split: protocol.split,
                started_at: protocol.started_at,
                type_id: protocol.type_id,
                workouts: workoutsAndExercises,
            },
        };
    }

    async updateProtocolWorkout(protocolId: number, updateData: any, userId: number) {
        // Verificar se o protocolo existe e pertence ao usuário
        const existingProtocol = await db
            .selectFrom('coach_protocols')
            .select(['id', 'client_id', 'ended_at', 'started_at'])
            .where('id', '=', protocolId)
            .where('client_id', '=', userId)
            .executeTakeFirst();

        if (!existingProtocol) {
            throw new HttpException({
                status: 404,
                message: ['Protocolo não encontrado ou você não tem permissão para editá-lo.'],
            }, 404);
        }

        // Verificar se o protocolo ainda está ativo (não foi finalizado)
        if (existingProtocol.ended_at) {
            throw new HttpException({
                status: 400,
                message: ['Não é possível editar um protocolo que já foi finalizado.'],
            }, 400);
        }

        // Validar dados básicos
        if (!updateData.name || updateData.name.trim().length === 0) {
            throw new HttpException({
                status: 400,
                message: ['Nome do protocolo é obrigatório.'],
            }, 400);
        }

        if (!updateData.objective || updateData.objective.trim().length === 0) {
            throw new HttpException({
                status: 400,
                message: ['Objetivo do protocolo é obrigatório.'],
            }, 400);
        }

        if (!updateData.frequency || updateData.frequency < 1 || updateData.frequency > 7) {
            throw new HttpException({
                status: 400,
                message: ['Frequência deve ser entre 1 e 7 dias por semana.'],
            }, 400);
        }

        try {
            // Atualizar dados básicos do protocolo
            await db
                .updateTable('coach_protocols')
                .set({
                    name: updateData.name,
                    type_id: updateData.type,
                    split: updateData.split,
                    frequency: updateData.frequency,
                    objective: updateData.objective,
                    general_notes: updateData.notes,
                    updated_at: new Date(),
                })
                .where('id', '=', protocolId)
                .execute();

            // Se há workouts para atualizar
            if (updateData.workouts && Array.isArray(updateData.workouts)) {
                // Remover workouts e exercícios existentes
                const existingWorkouts = await db
                    .selectFrom('coach_protocols_workouts')
                    .select(['id'])
                    .where('protocol_id', '=', protocolId)
                    .execute();

                for (const workout of existingWorkouts) {
                    await db
                        .deleteFrom('coach_protocols_workouts_exercises')
                        .where('workout_id', '=', workout.id)
                        .execute();
                }

                await db
                    .deleteFrom('coach_protocols_workouts')
                    .where('protocol_id', '=', protocolId)
                    .execute();

                // Adicionar novos workouts e exercícios
                const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                for (const [index, workout] of updateData.workouts.entries()) {
                    const workoutName = workout.name || `Treino ${letters[index]}`;

                    const newWorkout = await db
                        .insertInto('coach_protocols_workouts')
                        .values({
                            protocol_id: protocolId,
                            name: workoutName,
                            created_at: new Date(),
                            updated_at: new Date(),
                        })
                        .executeTakeFirst();

                    const workoutId = Number(newWorkout.insertId);

                    if (workout.exercises && Array.isArray(workout.exercises)) {
                        for (const exercise of workout.exercises) {
                            // Verificar se é um exercício personalizado (ID começa com 'custom_')
                            const isCustomExercise = typeof exercise.exercise_id === 'string' && 
                                exercise.exercise_id.toString().startsWith('custom_');
                            
                            await db
                                .insertInto('coach_protocols_workouts_exercises')
                                .values({
                                    workout_id: workoutId,
                                    // Para exercícios personalizados, definir exercise_id como null
                                    exercise_id: isCustomExercise ? null : (exercise.exercise?.id || exercise.exercise_id),
                                    name: exercise.name || exercise.exercise_name || exercise.exercise?.name,
                                    sets: exercise.sets,
                                    reps: exercise.reps,
                                    rpe: exercise.rpe,
                                    rest_seconds: exercise.restTime || exercise.rest_seconds,
                                    notes: exercise.notes,
                                })
                                .execute();
                        }
                    }
                }
            }

            return {
                status: 'success',
                message: 'Protocolo atualizado com sucesso',
                data: { id: protocolId }
            };

        } catch (error) {
            console.error('Erro ao atualizar protocolo:', error);
            throw new HttpException({
                status: 500,
                message: ['Erro interno do servidor ao atualizar protocolo.'],
            }, 500);
        }
    }

    async getUserData(userId: number) {
        const user = await db
          .selectFrom('users')
          .where('id', '=', userId)
          .select(['name', 'email', 'photo', 'height', 'weight', 'date_of_birth'])
          .executeTakeFirst();
          return user;
      }

    
          async postProtocolsDiet(userId: number, createProtocolDietDto: CreateProtocolDietDto) {
            console.log(`🚀 postProtocolsDiet: Iniciando criação de protocolo manual para usuário ${userId}`);
            console.log(`📋 Dados recebidos:`, JSON.stringify(createProtocolDietDto, null, 2));

            // Penente: Receber propriedade dos alimentos
            if (await this.hasActiveProtocolDiet(userId)) {
                console.error(`❌ postProtocolsDiet: Usuário ${userId} já possui protocolo ativo`);
                throw new HttpException({
                    status: 400,
                    message: ['Você já possui um protocolo ativo.'],
                }, 400);
            }

            console.log(`✅ postProtocolsDiet: Validação passou - criando protocolo manual para usuário ${userId}`);

            const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createProtocolDietDto;

            const userData = await this.getUserData(userId);
            const initial_weight = userData?.weight || 0;
    
            const new_protocol = await db
              .insertInto('nutritionist_protocols')
              .values({
                name: name,
                type_id: type_id,
                initial_weight: initial_weight,
                objective: objective,
                goal_calories: nutritional_goals.calories,
                goal_protein: nutritional_goals.protein,
                goal_carbs: nutritional_goals.carbs,
                goal_fat: nutritional_goals.fat,
                goal_water: nutritional_goals.water,
                general_notes: general_notes,
                started_at: new Date(),
                client_id: userId,
                created_at: new Date(),
                updated_at: new Date(),
              })
              .executeTakeFirst();
    
            const new_protocol_id = Number(new_protocol.insertId);
    
    
            meals.forEach(async (meal: any) => {
              const new_meal = await db.insertInto('nutritionist_protocols_meals')
              .values({
                protocol_id: new_protocol_id,
                name: meal.name,
                day_of_week: meal.day_of_week,
                meal_time: meal.meal_time,
              })
              .executeTakeFirst();
    
              const new_meal_id = Number(new_meal.insertId);
    
              meal?.foods?.forEach((food: any) => {
                db.insertInto('nutritionist_protocols_meals_foods')
                .values({
                    meal_id: new_meal_id,
                    food_id: food.food_id,
                    name: food.name,
                    quantity: food.quantity,
                    unit: food.unit|| 'g',
                    calories: food.calories || 0,
                    protein: food.protein || 0,
                    carbs: food.carbs || 0,
                    fat: food.fat || 0,
                    fiber: food.fiber || 0,
                  })
                  .execute();
              });
            });
    
            if (supplements && Array.isArray(supplements) && supplements.length > 0) {
              await db
                .insertInto('nutritionist_protocols_supplements')
                .values(supplements.map((supplement) => ({
                  protocol_id: new_protocol_id,
                  name: supplement.name,
                  dosage: supplement.dosage,
                  supplement_time: supplement.supplement_time,
                  notes: supplement.notes,
                })))
                .execute();
            }
    
            return {
              status: 'success',
              data: [],
            };
          }

          // AI
          async generateProtocolPrompt(userInfo: any) {
            const goalMapping = {
              "Emagrecimento": 34,
              "Manutenção": 35,
              "Ganho Muscular": 36
            };
          
            const goalId = goalMapping[userInfo.goal] || 35;
            const mealFrequency = userInfo.mealFrequency || 5; // Default para 5 refeições por dia se não fornecido
          
            return `
            # CONTEXTO OBRIGATÓRIO:
            ## DADOS DO USUÁRIO:
            - Altura: ${userInfo?.height || 'Não informada'} cm
            - Peso Atual: ${userInfo?.weight} kg
            - Peso Alvo: ${userInfo?.targetWeight || 'Não especificado'} kg
            - % Gordura Atual: ${userInfo?.bodyFat ? userInfo.bodyFat + '%' : 'Não medida'}
            - % Gordura Alvo: ${userInfo?.targetBodyFat ? userInfo.targetBodyFat + '%' : 'Não especificada'}
            - Nível de Atividade: ${userInfo?.activityLevel}
            - Objetivo: ${userInfo?.goal} (ID: ${goalId})
            - Preferências: ${userInfo?.dietaryPreferences.join(', ') || 'Nenhuma preferência específica'}
            - Restrições: ${userInfo?.restrictions.join(', ') || 'Nenhuma restrição informada'}
            - Alimentos Preferidos: ${userInfo?.includedFoods.join(', ') || 'Nenhum favorito informado'}
            - Frequência de Refeições: ${mealFrequency} refeições/dia
          
            ${userInfo?.goal === 'Emagrecimento' && userInfo.dietPlan ? '## PLANO ALIMENTAR:\n' + userInfo.dietPlan : ''}
          
            ${userInfo?.notes ? '## COMENTÁRIOS ADICIONAIS:\n' + userInfo.notes : ''}
          
            # TAREFA PRINCIPAL:
            Como nutricionista IA, calcule AUTONOMAMENTE:
            1. Necessidade calórica diária
            2. Distribuição de macronutrientes (proteínas, carboidratos, gorduras)
            3. Recomendações hídricas
            4. Valores nutricionais para cada alimento
            5. As somas dos macronutrientes (proteína, carboidrato, gordura) e calorias provenientes dos alimentos em TODAS as refeições diárias DEVEM bater com os valores especificados no campo "nutritional_goals", com margem máxima de erro de ±5% por dia.
          
            # DIRETRIZES PARA CÁLCULOS:
            - Use a fórmula de Mifflin-St Jeor para o cálculo calórico basal
            - Ajuste para objetivo (déficit/superávit calórico quando aplicável)
            - Considere a distribuição ACEITÁVEL de macronutrientes (AMDR)
            - Os valores nutricionais (calorias, proteína, carboidrato, gordura e fibra) DEVEM ser calculados exclusivamente com base em fontes brasileiras confiáveis (prioritariamente a Tabela TACO e dados da ANVISA)
            - Não invente ou estime valores nutricionais. Se não houver valor na TACO, prefira alimentos com valores disponíveis
            - Considere o peso do alimento na forma em que ele será consumido (ex: arroz cozido, frango grelhado, etc), conforme especificado na TACO
          
            # REQUISITO CRÍTICO:
            - Crie um plano alimentar COMPLETO para TODOS OS 7 DIAS DA SEMANA: monday, tuesday, wednesday, thursday, friday, saturday, sunday
            - Gere EXATAMENTE ${mealFrequency} refeições por dia para CADA dia
            - NÃO corte a resposta antes de completar todos os 7 dias
          
            # MODELO JSON OBRIGATÓRIO:
            {
              "name": "[Nome criativo baseado no objetivo]",
              "type_id": ${goalId},
              "objective": "[Descrição técnica com base nos dados do usuário]",
              "nutritional_goals": {
                "calories": /* Calcular usando fórmulas científicas E validar com a soma das refeições do dia */,
                "protein": /* Proteína em gramas (1.2-2.2g/kg peso) */,
                "carbs": /* Carboidratos em gramas */,
                "fat": /* Gorduras em gramas */,
                "water": /* ml (35ml/kg + ajuste de atividade) */
              },
              "meals": [
                {
                  "name": "[Nome da Refeição]",
                  "day_of_week": "monday/tuesday/.../sunday",
                  "meal_time": "HH:MM:SS",
                  "foods": [
                    {
                      "name": "[Alimento]",
                      "quantity": /* Quantidade numérica SEM UNIDADE */,
                      "unit": "g/ml/...",
                      "calories": /* Valor exato calculado */,
                      "protein": /* g */,
                      "carbs": /* g */,
                      "fat": /* g */,
                      "fiber": /* g */
                    }
                  ]
                }
              ],
              "supplements": [
                {
                  "name": "[Nome Suplemento]",
                  "dosage": "Dose específica",
                  "supplement_time": "Momento de consumo",
                  "notes": "Justificativa técnica"
                }
              ],
              "general_notes": "[Recomendações personalizadas]"
            }
          
            # EXEMPLO MÍNIMO ESPERADO:
            {
              "name": "Exemplo",
              "type_id": ${goalId},
              "objective": "...",
              "nutritional_goals": {"calories": 2500, "protein": 150, "carbs": 150, "fat": 139, "water": 2870},
              "meals": [
                {"name": "Café da Manhã", "day_of_week": "monday", "meal_time": "07:30:00", "foods": [...]},
                {"name": "Lanche", "day_of_week": "monday", "meal_time": "10:30:00", "foods": [...]},
                {"name": "Almoço", "day_of_week": "monday", "meal_time": "13:00:00", "foods": [...]},
                {"name": "Lanche da Tarde", "day_of_week": "monday", "meal_time": "16:00:00", "foods": [...]},
                {"name": "Jantar", "day_of_week": "monday", "meal_time": "19:30:00", "foods": [...]},
                {"name": "Café da Manhã", "day_of_week": "tuesday", "meal_time": "07:30:00", "foods": [...]}
                // ... continuar até sunday com ${mealFrequency} refeições por dia
              ],
              "supplements": [...],
              "general_notes": "..."
            }
          
            # REGRAS ESSENCIAIS:
            1. Números SEM ASPAS ou unidades nos valores (ex: "quantity": 150)
            2. Precisão nutricional: ±5% de margem de erro por dia
            3. Variar alimentos entre dias (mantendo a consistência nutricional): ${userInfo?.foodVariation || 'Não especificado'}
            4. Incluir fibra em TODOS alimentos aplicáveis
            5. Usar alimentos típicos brasileiros
            6. Formato 24h para horários (ex: "14:30:00")
            7. Gerar EXATAMENTE ${mealFrequency} refeições por dia para TODOS os 7 dias
          
            # CRITÉRIO DE PRECISÃO NUTRICIONAL:
            - Todos os valores nutricionais devem ser extraídos ou calculados com base nas informações da Tabela TACO.
            - Nunca invente valores. Não arredonde de forma exagerada.
            - A soma das calorias e dos macronutrientes (proteína, carboidrato, gordura) de TODAS as refeições de um dia deve corresponder aos valores em "nutritional_goals" com no máximo +5% de variação.
            - Essa validação deve ser feita por dia. Exemplo: se o plano indica 2.400 kcal, a soma dos alimentos de segunda-feira pode variar entre 2.400 e 2.520 kcal, mas não menos ou mais que isso.
            - O mesmo se aplica a cada macronutriente separadamente.
            - Não inclua valores arbitrários em "nutritional_goals". Calcule com base no total dos alimentos e ajuste conforme necessário.
          
            Retorne APENAS o JSON válido SEM comentários ou markdown.
            `;
          }
          
          private extractJsonFromString(text: string): string {
            const jsonStart = text.indexOf('{');
            const jsonEnd = text.lastIndexOf('}') + 1;
          
            if (jsonStart === -1 || jsonEnd <= jsonStart) {
              throw new Error('Nenhum JSON válido encontrado na resposta');
            }
          
            const extracted = text.slice(jsonStart, jsonEnd);
          
            try {
              return JSON.stringify(JSON.parse(extracted)); // Tenta validar diretamente
            } catch {
              return JSON.stringify(JSON.parse(jsonrepair(extracted))); // Repara se necessário
            }
          }

            private extractJsonFromStringOld(text: string): string {
              // Etapa 0: Normalização inicial
              let sanitized = text
                .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove todos os caracteres de controle
                .replace(/^\uFEFF/, '') // Remove BOM
                .replace(/[‘’]/g, "'") // Normaliza aspas simples
                .replace(/[“”]/g, '"') // Normaliza aspas duplas
                .replace(/\\n/g, '') // Remove quebras de linha
                .trim();
            
              // Etapa 1: Extrair o bloco JSON
              const jsonStart = sanitized.indexOf('{');
              const jsonEnd = sanitized.lastIndexOf('}') + 1;
              
              if (jsonStart === -1 || jsonEnd <= jsonStart) {
                throw new Error('Nenhum JSON válido encontrado na resposta');
              }
              
              sanitized = sanitized.slice(jsonStart, jsonEnd);
            
              // Etapa 2: Correções específicas para JSON corrompido
              sanitized = sanitized
                // Remove aspas duplicadas em nomes de propriedades
                .replace(/"{2,}/g, '"')
                // Corrige aspas em valores numéricos
                .replace(/":\s*"(\d+)"/g, '": $1')
                // Remove aspas extras antes de valores
                .replace(/""([^"]+)""/g, '"$1"')
                // Corrige tempos no formato "HH":"MM:SS"
                .replace(/"(\d{2})":(\d{2}):(\d{2})"/g, '"$1:$2:$3"')
                // Remove espaços desnecessários
                .replace(/\s+/g, ' ')
                .replace(/\s*([{}[\]:,])\s*/g, '$1');
            
              // Etapa 3: Correções avançadas
              const fixes: Array<[RegExp, string]> = [
                // Garante aspas em propriedades
                [/([{,])\s*([a-zA-Z_]+)\s*:/g, '$1"$2":'],
                // Remove aspas em valores numéricos
                [/":\s*"(\d+(?:\.\d+)?)"/g, '":$1'],
                // Corrige valores booleanos
                [/":\s*"(true|false)"/g, '":$1'],
                // Remove vírgulas extras
                [/,\s*}/g, '}'],
                [/,\s*]/g, ']']
              ];
            
              fixes.forEach(([regex, replacement]) => {
                sanitized = sanitized.replace(regex, replacement);
              });
            
              // Etapa 4: Validação final
              try {
                const parsed = JSON.parse(sanitized);
                return JSON.stringify(parsed); // Garante formato consistente
              } catch (error) {
                console.error('JSON após tentativas de correção:', sanitized.substring(0, 500));
                throw new Error(`Falha na validação do JSON: ${error.message}`);
              }
            }


            async postProtocolsDietAi(userId: number, userInfo: any) {
              console.log(`🤖 postProtocolsDietAi: Iniciando criação de protocolo IA para usuário ${userId}`);
              console.log(`📋 Dados recebidos:`, JSON.stringify(userInfo, null, 2));

              if (await this.hasActiveProtocolDiet(userId)) {
                console.error(`❌ postProtocolsDietAi: Usuário ${userId} já possui protocolo ativo`);
                throw new HttpException({
                  status: 400,
                  message: ['Você já possui um protocolo ativo.'],
                }, 400);
              }

              console.log(`✅ postProtocolsDietAi: Validação passou - criando protocolo IA para usuário ${userId}`);
            
              try {
                const prompt = await this.generateProtocolPrompt(userInfo);
                const response = await this.openai.chat.completions.create({
                  model: "gpt-4o-mini", // Mudança para gpt-4o-mini para melhor performance
                  messages: [
                    {
                      role: "system",
                      content: "You are a professional nutritionist expert in creating personalized diet protocols. Return only valid JSON following the specified format, ensuring a complete plan for all 7 days of the week."
                    },
                    { role: "user", content: prompt }
                  ],
                  temperature: 0.2, // Reduzido para mais consistência
                  max_tokens: 6144, // Reduzido para otimizar performance
                  response_format: { type: "json_object" }
                });
            
                const aiResponseText: any = response.choices[0].message.content;
                const protocolDataText = this.extractJsonFromString(aiResponseText);
                const protocolData = JSON.parse(protocolDataText);
            
                if (!protocolData.name || !protocolData.type_id || !protocolData.nutritional_goals) {
                  throw new Error('Dados essenciais ausentes no protocolo gerado');
                }
            
                const initial_weight = userInfo.weight;
            
                // Iniciar transação
                const result = await db.transaction().execute(async (trx) => {
                  // Atualizar dados do usuário, se necessário
                  let userUpdateData = {
                    ...(userInfo.weight && userInfo.weight > 0 && { weight: userInfo.weight }),
                    ...(userInfo.bodyFat && userInfo.bodyFat > 0 && { bodyfat: userInfo.bodyFat }), // Corrigido 'bodyfat' para 'bodyFat'
                  };
            
                  if (Object.keys(userUpdateData).length > 0) {
                    await trx
                      .updateTable('users')
                      .set(userUpdateData)
                      .where('id', '=', userId)
                      .execute();
                  }
            
                  // Inserir protocolo
                  const new_protocol = await trx
                    .insertInto('nutritionist_protocols')
                    .values({
                      name: protocolData.name,
                      type_id: protocolData.type_id,
                      initial_weight,
                      objective: protocolData.objective,
                      goal_calories: protocolData.nutritional_goals.calories,
                      goal_protein: protocolData.nutritional_goals.protein,
                      goal_carbs: protocolData.nutritional_goals.carbs,
                      goal_fat: protocolData.nutritional_goals.fat,
                      goal_water: protocolData.nutritional_goals.water,
                      general_notes: protocolData.general_notes,
                      started_at: new Date(),
                      client_id: userId,
                      created_at: new Date(),
                      updated_at: new Date(),
                    })
                    .executeTakeFirst();
            
                  const new_protocol_id = Number(new_protocol.insertId);
            
                  // Inserir refeições
                  for (const meal of protocolData.meals || []) {
                    const new_meal = await trx
                      .insertInto('nutritionist_protocols_meals')
                      .values({
                        protocol_id: new_protocol_id,
                        name: meal.name,
                        day_of_week: meal.day_of_week,
                        meal_time: meal.meal_time,
                      })
                      .executeTakeFirst();
            
                    const new_meal_id = Number(new_meal.insertId);
            
                    if (meal.foods && Array.isArray(meal.foods)) {
                      await trx
                        .insertInto('nutritionist_protocols_meals_foods')
                        .values(meal.foods.map(food => ({
                          meal_id: new_meal_id,
                          food_id: null,
                          name: food.name,
                          quantity: food.quantity,
                          unit: food.unit || 'g',
                          calories: food.calories || 0,
                          protein: food.protein || 0,
                          carbs: food.carbs || 0,
                          fat: food.fat || 0,
                          fiber: food.fiber || 0,
                        })))
                        .execute();
                    }
                  }
            
                  // Inserir suplementos
                  if (protocolData.supplements?.length > 0) {
                    await trx
                      .insertInto('nutritionist_protocols_supplements')
                      .values(protocolData.supplements.map((supplement) => ({
                        protocol_id: new_protocol_id,
                        name: supplement.name,
                        dosage: supplement.dosage,
                        supplement_time: supplement.supplement_time,
                        notes: supplement.notes,
                      })))
                      .execute();
                  }
            
                  return new_protocol_id;
                });
            
                return {
                  status: "success",
                  data: {
                    protocol_id: result,
                  },
                };
              } catch (error) {
                console.error('Erro ao processar protocolo:', {
                  message: error.message,
                  stack: error.stack,
                  input: userInfo
                });
                throw new HttpException({
                  status: "error",
                  message: ['Erro ao processar protocolo de dieta.', error.message],
                }, 500);
              }
            }


          

          async getActiveProtocolsDiet(userId: number) {
            console.log(`🔍 getActiveProtocolsDiet: Buscando protocolo ativo para usuário ${userId}`);

            // APLICAR REGRA OTIMIZADA: Garantir apenas um protocolo ativo
            const protocolRule = await this.ensureSingleActiveProtocol(userId);
            console.log('📋 Resultado da regra:', protocolRule.message);

            if (!protocolRule.activeProtocol) {
              console.log('❌ Nenhum protocolo ativo encontrado');
              return {
                status: 'success',
                data: {
                  has_protocol: false,
                  message: protocolRule.message
                },
              };
            }

            // Buscar dados completos do protocolo ativo único
            const protocol = await db
              .selectFrom('nutritionist_protocols as p')
              .leftJoin('select_options as s', 's.id', 'p.type_id')
              .select([
                'p.id',
                'p.name',
                'p.objective',
                'p.initial_weight',
                'p.general_notes',
                's.value_option as type',
                'p.goal_calories as calories',
                'p.goal_protein as protein',
                'p.goal_carbs as carbs',
                'p.goal_fat as fat',
                'p.goal_water as water',
                'p.started_at',
                'p.ended_at'])
              .where('p.id', '=', protocolRule.activeProtocol.id)
              .where('p.client_id', '=', userId)
              .executeTakeFirst();

            if (!protocol) {
              console.log('❌ Erro: protocolo ativo não encontrado após regra');
              return {
                status: 'success',
                data: {
                  has_protocol: false,
                  message: 'Protocolo ativo não encontrado'
                },
              };
            }

            console.log(`✅ Protocolo ativo único confirmado: ${protocol.name} (ID: ${protocol.id})`);
            if (protocolRule.deactivatedCount > 0) {
              console.log(`🔄 ${protocolRule.deactivatedCount} protocolos antigos foram finalizados`);
            }
          
            // Buscar meals e foods associados
            const mealRows = await db
              .selectFrom('nutritionist_protocols_meals as p')
              .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')
              .select([
                'p.id',
                'p.name',
                'p.day_of_week',
                'p.meal_time',
                'f.name as food_name',
                'f.quantity',
                'f.unit',
              ])
              .where('protocol_id', '=', protocol.id)
              .orderBy('p.meal_time', 'asc')
              .orderBy('p.id', 'asc')
              .execute();
          
            // Agrupar os meals por dia da semana
            const mealsByDay = new Map<string, any[]>();
            mealRows.forEach((row) => {
              const day = row.day_of_week.toLowerCase(); // Padronizar como minúsculo, ex: "monday"
              if (!mealsByDay.has(day)) {
                mealsByDay.set(day, []);
              }
          
              // Encontrar ou criar a refeição no dia
              let meal = mealsByDay.get(day)?.find((m) => m.id === row.id);
              if (!meal) {
                meal = {
                  id: row.id,
                  name: row.name,
                  meal_time: row.meal_time,
                  foods: [],
                };
                mealsByDay.get(day)?.push(meal);
              }
          
              // Adicionar alimento, se existir
              if (row.food_name) {
                meal.foods.push({
                  name: row.food_name,
                  unit: row.unit,
                  quantity: parseFloat(Number(row.quantity).toFixed(2)) || 0,
                });
              }
            });
          
            // Converter Map para objeto
            const meals = Object.fromEntries(mealsByDay);
          
            // Buscar supplementos
            const supplements = await db
              .selectFrom('nutritionist_protocols_supplements as p')
              .select(['p.id', 'p.name', 'p.dosage', 'p.supplement_time', 'p.notes'])
              .where('protocol_id', '=', protocol.id)
              .execute();
          
            return {
              status: 'success',
              data: {
                has_protocol: true,
                id: protocol.id,
                type: protocol.type,
                name: protocol.name,
                objective: protocol.objective,
                general_notes: protocol.general_notes,
                initial_weight: parseFloat(Number(protocol.initial_weight).toFixed(2)) || 0,
                started_at: protocol.started_at,
                ended_at: protocol.ended_at,
                goals: {
                  calories: parseFloat(Number(protocol.calories).toFixed(2)) || 0,
                  protein: parseFloat(Number(protocol.protein).toFixed(2)) || 0,
                  carbs: parseFloat(Number(protocol.carbs).toFixed(2)) || 0,
                  fat: parseFloat(Number(protocol.fat).toFixed(2)) || 0,
                  water: parseFloat(Number(protocol.water).toFixed(2)) || 0,
                },
                meals, // Agora é um objeto com dias da semana como chaves
                supplements: supplements.map((sup) => ({
                  name: sup.name,
                  dosage: sup.dosage,
                  supplement_time: sup.supplement_time,
                  notes: sup.notes,
                })),
              },
            };
          }

          async getActiveMealsOfDayWeek(userId: number, query: any) {
            const { date } = query;
            let inputDate: Date = date ? new Date(date) : new Date();
            // Verifica se a data é válida, caso contrário, usa a data atual
            if (isNaN(inputDate.getTime())) {
                inputDate = new Date();
            }

            console.log('🔄 getActiveMealsOfDayWeek: Iniciando busca para usuário', userId);
            console.log('📅 Data consultada:', date);

            const tz = 'America/Sao_Paulo';

            // Usa a data passada ou a data atual no timezone do usuário
            const inputDateTz = dayjs.tz(date || undefined, tz);

            // Agora sim, pega o início e fim do dia corretamente no timezone do usuário
            const startOfDayUtc = inputDateTz.startOf('day').utc().toDate();
            const endOfDayUtc = inputDateTz.endOf('day').utc().toDate();

            // Obtém o nome do dia da semana em inglês e em minúsculas (ex: "monday")
            const dayName = startOfDayUtc.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
            console.log('📅 Dia da semana:', dayName);

            const today = dayjs().tz(tz).toDate();

            // APLICAR REGRA OTIMIZADA: Garantir apenas um protocolo ativo
            console.log('🔧 Aplicando regra de protocolo único ativo...');
            const protocolRule = await this.ensureSingleActiveProtocol(userId);
            console.log('📋 Resultado da regra:', protocolRule.message);

            if (!protocolRule.activeProtocol) {
                console.log('❌ Nenhum protocolo ativo encontrado para usuário', userId);
                return {
                    status: 'success',
                    data: {
                        has_protocol: false,
                        message: protocolRule.message
                    },
                };
            }

            // Buscar dados completos do protocolo ativo
            console.log('🔍 Buscando dados completos do protocolo ativo:', protocolRule.activeProtocol.id);

            const protocolBase = await db
                .selectFrom('nutritionist_protocols as p')
                .select([
                    'p.id',
                    'p.name',
                    'p.objective',
                    'p.initial_weight',
                    'p.general_notes',
                    'p.goal_calories as calories',
                    'p.goal_protein as protein',
                    'p.goal_carbs as carbs',
                    'p.goal_fat as fat',
                    'p.goal_water as water',
                    'p.started_at',
                    'p.ended_at',
                    'p.type_id',
                ])
                .where('p.id', '=', protocolRule.activeProtocol.id)
                .where('p.client_id', '=', userId)
                .executeTakeFirst();

            if (!protocolBase) {
                console.log('❌ Erro: protocolo ativo não encontrado após regra', protocolRule.activeProtocol.id);
                return {
                    status: 'success',
                    data: {
                        has_protocol: false,
                        message: 'Protocolo ativo não encontrado'
                    },
                };
            }

            // Busca o tipo do protocolo separadamente
            let protocolType = 'Dieta'; // valor padrão
            if (protocolBase.type_id) {
                const typeResult = await db
                    .selectFrom('select_options')
                    .select(['value_option'])
                    .where('id', '=', protocolBase.type_id)
                    .executeTakeFirst();

                if (typeResult && typeResult.value_option) {
                    protocolType = typeResult.value_option;
                }
            }

            const protocol = {
                ...protocolBase,
                type: protocolType
            };

            console.log('✅ Protocolo ativo único confirmado:', {
                id: protocol.id,
                name: protocol.name,
                started_at: protocol.started_at,
                type: protocol.type,
                deactivated_count: protocolRule.deactivatedCount
            });

            // Busca as refeições para o dia específico, com LEFT JOIN para verificar se a refeição foi completada
            console.log('🔍 Buscando refeições do protocolo para o dia:', dayName);

            const mealRows: any = await db
                .selectFrom('nutritionist_protocols_meals as p')
                .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')
                .leftJoin('daily_meals as d', (join) =>
                    join
                        .onRef('d.meal_id', '=', 'p.id') // Relaciona meal_id
                        .on('d.user_id', '=', userId) // Filtra pelo usuário
                        .on('d.daily_at', '>=', startOfDayUtc)
                        .on('d.daily_at', '<=', endOfDayUtc) // Filtra pelo dia atual
                )
                .leftJoin('daily_meals_foods as df', 'df.meal_id', 'd.id')
                .select([
                    sql`COALESCE(d.id, p.id)`.as('id'),
                    'p.id as protocol_meal_id',
                    'p.name',
                    'p.meal_time',
                    'd.daily_at',
                    sql`COALESCE(df.name, f.name)`.as('food_name'), // Se df.name for NULL, usa f.name
                    sql`COALESCE(df.quantity, f.quantity)`.as('quantity'), // Se df.quantity for NULL, usa f.quantity
                    sql`COALESCE(df.unit, f.unit)`.as('unit'), // Se df.unit for NULL, usa f.unit
                    sql`COALESCE(df.calories, f.calories)`.as('calories'), // Se df.calories for NULL, usa f.calories
                    sql`COALESCE(df.protein, f.protein)`.as('protein'), // Se df.protein for NULL, usa f.protein
                    sql`COALESCE(df.carbs, f.carbs)`.as('carbs'), // Se df.carbs for NULL, usa f.carbs
                    sql`COALESCE(df.fat, f.fat)`.as('fat'), // Se df.fat for NULL, usa f.fat
                    sql`CASE WHEN d.id IS NOT NULL THEN TRUE ELSE FALSE END`.as('completed'), // Verifica se há registro em daily_meals
                ])
                .where('p.protocol_id', '=', protocol.id)
                .where('p.day_of_week', '=', dayName)
                .orderBy('d.daily_at', 'asc')
                .orderBy('p.meal_time', 'asc')
                .orderBy('p.id', 'asc')
                .execute();

            console.log(`📊 Encontradas ${mealRows.length} linhas de refeições do protocolo para ${dayName}`);

            const mealsOutDiet = await db
                .selectFrom('daily_meals as d')
                .leftJoin('daily_meals_foods as df', 'df.meal_id', 'd.id')
                .select([
                    'd.id',
                    'd.name',                    
                    'd.daily_at',
                    'df.name as food_name',
                    'df.quantity',
                    'df.unit',
                    'df.calories',
                    'df.protein',
                    'df.carbs',
                    'df.fat',
                    'df.fiber',
                ])
                .where('d.user_id', '=', userId)
                .where((eb) =>
                    eb.and([
                        eb(sql`DATE(d.daily_at)`, '=', sql`DATE(${startOfDayUtc})`),
                        eb('d.meal_id', 'is', null),
                    ])
                )
                .orderBy('d.daily_at', 'asc')
                .execute();
                
            const dateTz = (date: any) => {
                return dayjs.tz(date, tz).format('YYYY-MM-DD HH:mm:ss');
            }
            const timeTz = (date: any) => {
                return dayjs.tz(date, tz).format('HH:mm:ss');
            }

            const mealRowsFormatted = mealRows.map((row: any) => ({
                id: row.id,
                protocol_meal_id: row.protocol_meal_id || null,
                name: row.name,
                meal_time: row.daily_at ? timeTz(row.daily_at) : row.meal_time,
                food_name: row.food_name,
                quantity: row.quantity,
                unit: row.unit,
                calories: row.calories,
                protein: row.protein,
                carbs: row.carbs,
                fat: row.fat,
                fiber: row.fiber,
                completed: row.completed,
            }));

            console.log('📋 Refeições do protocolo formatadas:', mealRowsFormatted.length);

            const mealsOutDietFormatted = mealsOutDiet.map((row) => ({
                id: row.id,
                name: row.name,
                meal_time: timeTz(row.daily_at),
                food_name: row.food_name,
                quantity: row.quantity,
                unit: row.unit,
                calories: row.calories,
                protein: row.protein,
                carbs: row.carbs,
                fat: row.fat,
                fiber: row.fiber,
                completed: true,
            }));

            const allMeals = [...mealRowsFormatted, ...mealsOutDietFormatted].map((row) => ({
                id: row.id,
                name: row.name,
                meal_time: row.meal_time,
                food_name: row.food_name,
                quantity: row.quantity,
                unit: row.unit,
                calories: row.calories,
                protein: row.protein,
                carbs: row.carbs,
                fat: row.fat,
                fiber: row.fiber,
                completed: row?.completed ? true : false,
            }));

            
            const allMealsOrder = allMeals.sort((a, b) => {
              return a.meal_time.localeCompare(b.meal_time);
            });

        
            // Agrupa as refeições e seus alimentos
            const mealsMap = new Map();
            for (const row of allMealsOrder) {
                // Usa protocol_meal_id quando disponível para agrupar corretamente refeições do protocolo
                const mealKey = (row as any).protocol_meal_id || row.id;
                let meal = mealsMap.get(mealKey);
                if (!meal) {
                    meal = {
                        id: (row as any).protocol_meal_id || row.id, // Usa protocol_meal_id para refeições do protocolo
                        name: row.name,
                        meal_time: row.meal_time,
                        nutrients: {
                            calories: 0,
                            protein: 0,
                            carbs: 0,
                            fat: 0,
                            fiber: 0,
                        },
                        completed: row.completed ? true : false, // Adiciona o campo "completed"
                        foods: [],
                    };
                    mealsMap.set(mealKey, meal);
                }
                if (row.food_name) {
                    meal.foods.push({
                        name: row.food_name,
                        unit: row.unit,
                        quantity: parseFloat(Number(row.quantity).toFixed(2)) || 0,
                    });
                    meal.nutrients.calories += parseFloat(Number(row.calories).toFixed(2)) || 0;
                    meal.nutrients.protein += parseFloat(Number(row.protein).toFixed(2)) || 0;
                    meal.nutrients.carbs += parseFloat(Number(row.carbs).toFixed(2)) || 0;
                    meal.nutrients.fat += parseFloat(Number(row.fat).toFixed(2)) || 0;
                    meal.nutrients.fiber += parseFloat(Number(row.fiber).toFixed(2)) || 0;
                }
            }
            const meals = Array.from(mealsMap.values());

            console.log(`🍽️ Total de refeições agrupadas: ${meals.length}`);
            console.log('📋 Refeições finais:', meals.map(m => ({ id: m.id, name: m.name, completed: m.completed, foods_count: m.foods.length })));
        
            // Busca os suplementos do protocolo
            const supplements = await db
                .selectFrom('nutritionist_protocols_supplements as p')
                .select(['p.name', 'p.dosage', 'p.supplement_time', 'p.notes'])
                .where('protocol_id', '=', protocol.id)
                .execute();
        
            return {
                status: 'success',
                data: {
                    has_protocol: true,
                    id: protocol.id,
                    type: protocol.type,
                    name: protocol.name,
                    objective: protocol.objective,
                    general_notes: protocol.general_notes,
                    initial_weight: protocol.initial_weight,
                    started_at: protocol.started_at,
                    ended_at: protocol.ended_at,
                    goals: {
                        calories: protocol.calories,
                        protein: protocol.protein,
                        carbs: protocol.carbs,
                        fat: protocol.fat,
                        water: protocol.water,
                    },
                    meals,
                    supplements: supplements.map(sup => ({
                        name: sup.name,
                        dosage: sup.dosage,
                        supplement_time: sup.supplement_time,
                        notes: sup.notes,
                    })),
                },
            };
        }


          async getActiveProtocolsDietMealsDayWeek(userId: number) {
            // Just return the meals for the current day of the week
            const today = new Date();

            // get protocol active id
            const protocol = await db
              .selectFrom('nutritionist_protocols as p')
              .select(['p.id'])
              .where('p.client_id', '=', userId)
              .where('p.started_at', '<=', today)
              .where('p.ended_at', 'is', null)
              .orderBy('p.started_at', 'desc')
              .executeTakeFirst();

              if (!protocol) {
                return {
                  status: 'success',
                  data: {
                    has_protocol: false,
                  },
                };
              }

              const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

              const mealRows = await db
                .selectFrom('nutritionist_protocols_meals as p')
                .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')
                .leftJoin('foods as food', 'food.id', 'f.food_id')
                .select([
                  'p.id',
                  'p.name',
                  'p.day_of_week',
                  'p.meal_time',
                  'food.name as food_name',
                  'f.quantity',
                  'f.unit',
                ])
                .where('protocol_id', '=', protocol.id)
                .where('p.day_of_week', '=', dayOfWeek)
                .orderBy('p.meal_time', 'asc')
                .orderBy('p.id', 'asc')
                .execute();

                const mealsByDay = new Map<string, any[]>();
                mealRows.forEach((row) => {
                  const day = row.day_of_week.toLowerCase(); // Padronizar como minúsculo, ex: "monday"
                  if (!mealsByDay.has(day)) {
                    mealsByDay.set(day, []);
                  }

                  // Encontrar ou criar a refeição no dia
                  let meal = mealsByDay.get(day)?.find((m) => m.id === row.id);
                  if (!meal) {
                    meal = {
                      id: row.id,
                      name: row.name,
                      meal_time: row.meal_time,
                      foods: [],
                    };
                    mealsByDay.get(day)?.push(meal);
                  }

                  // Adicionar alimento, se existir
                  if (row.food_name) {
                    meal.foods.push({
                      name: row.food_name,
                      unit: row.unit,
                      quantity: row.quantity,
                    });
                  }
                });

                const meals = Object.fromEntries(mealsByDay);

                return {
                  status: 'success',
                  data: {
                    has_protocol: true,
                    meals,
                  },
                };
          }
            

          async deleteProtocolDiet(id: number, userId: number) {  
            const protocol = await db
              .selectFrom('nutritionist_protocols')
              .where('id', '=', id)
              .where('client_id', '=', userId)
              .select(['id'])
              .executeTakeFirst();
          
            if (!protocol) {
              return {
                status: 'error',
                message: 'Protocol not found',
              };
            }
          
            // Excluir protocolo
            await db
              .deleteFrom('nutritionist_protocols')
              .where('id', '=', id)
              .execute();
          
            return {
              status: 'success',
              data: [],
              message: 'Protocol deleted successfully',
            };
          }

          async deleteProtocolWorkout(id: number, userId: number) {
            const protocol = await db
              .selectFrom('coach_protocols')
              .where('id', '=', id)
              .where('client_id', '=', userId)
              .select(['id'])
              .executeTakeFirst();
          
            if (!protocol) {
              return {
                status: 'error',
                message: 'Protocol not found',
              };
            }
          
            // Excluir protocolo
            await db
              .deleteFrom('coach_protocols')
              .where('id', '=', id)
              .execute();
          
            return {
              status: 'success',
              data: [],
              message: 'Protocol deleted successfully',
            };
          }

          // Check protocol workout

          // Check protocol diet
          async checkProtocolDiet(userId: number, body: any) {
          const { meal_id } = body;
          let { meal_name, daily_at, meal_foods } = body;

          const tz = 'America/Sao_Paulo';

          const today = dayjs().tz(tz).format('YYYY-MM-DD HH:mm:ss');

          daily_at = (daily_at ? dayjs.tz(daily_at, tz).format('YYYY-MM-DD HH:mm:ss') : today);

          // check if meal_id is already checked (apenas para refeições do protocolo)
          if (meal_id) {
            const isMealChecked = await db
              .selectFrom('daily_meals')
              .select(['id'])
              .where('user_id', '=', userId)
              .where('meal_id', '=', meal_id)
              .where(sql`DATE(daily_at)`, '=', sql`DATE(${daily_at})`)
              .executeTakeFirst();

              if (isMealChecked) {
                return {
                  status: 'error',
                  message: 'Meal already checked',
                };
              }
          }

          let sum: any = { calories: 0, carbs: 0, protein: 0, fat: 0, fiber: 0 };

          let food: any;

          if(meal_id) {
          // get protocol_id meal_time calories carbs protein fat from meal and foods
          food = await db
            .selectFrom('nutritionist_protocols_meals_foods as p')
            .leftJoin('nutritionist_protocols_meals as m', 'm.id', 'p.meal_id')
            .select([
              'm.protocol_id',
              'p.meal_id',
              'p.name',
              'm.name as meal_name',
              'm.meal_time',
              'p.quantity',
              'p.calories',
              'p.carbs',
              'p.protein',
              'p.fat',
              'p.fiber',
            ])
            .where('p.meal_id', '=', meal_id)
            .execute();

            if (!food) {
              throw new HttpException('Meal not found', HttpStatus.NOT_FOUND);
            }

            // sum calories carbs protein fat from food x quantity
            // CORREÇÃO: Verificar se os valores precisam ser convertidos
            sum = food.reduce((acc: any, curr: any) => {
              const quantity = Number(curr.quantity) || 0;
              const calories = Number(curr.calories) || 0;
              const protein = Number(curr.protein) || 0;
              const carbs = Number(curr.carbs) || 0;
              const fat = Number(curr.fat) || 0;
              const fiber = Number(curr.fiber) || 0;

              // CORREÇÃO TEMPORÁRIA: Sempre aplicar conversão por 100g
              // Baseado no comentário original: "quantity is in grams and calories carbs protein fat are in 100g"
              // Vamos assumir que TODOS os valores estão por 100g e precisam ser convertidos
              const conversionFactor = quantity / 100;

              console.log(`[DEBUG] Alimento: ${curr.name || 'N/A'}, Quantidade: ${quantity}g, Calorias originais: ${calories}, Fator: ${conversionFactor}, Calorias convertidas: ${calories * conversionFactor}`);

              acc.calories += parseFloat((calories * conversionFactor).toFixed(2));
              acc.carbs += parseFloat((carbs * conversionFactor).toFixed(2));
              acc.protein += parseFloat((protein * conversionFactor).toFixed(2));
              acc.fat += parseFloat((fat * conversionFactor).toFixed(2));
              acc.fiber += parseFloat((fiber * conversionFactor).toFixed(2));

              return acc;
            }, { calories: 0, carbs: 0, protein: 0, fat: 0, fiber: 0 });
          }
          
          if(!meal_id && meal_foods) {
             sum = meal_foods.reduce((acc: any, curr: any) => {
              acc.calories += parseFloat(Number(curr.calories).toFixed(2));
              acc.carbs += parseFloat(Number(curr.carbs).toFixed(2));
              acc.protein += parseFloat(Number(curr.protein).toFixed(2));
              acc.fat += parseFloat(Number(curr.fat).toFixed(2));
              acc.fiber += parseFloat(Number(curr.fiber).toFixed(2));
              return acc;
            }, { calories: 0, carbs: 0, protein: 0, fat: 0, fiber: 0 });  
          }            

            const mealData: any = {
              user_id: userId,
              protocol_id: (meal_id && food && food.length > 0) ? food[0]?.protocol_id : null,
              meal_id: meal_id || null,
              name: (meal_id && food && food.length > 0) ? food[0]?.meal_name : meal_name,
              calories: sum.calories,
              carbs: sum.carbs,
              protein: sum.protein,
              fat: sum.fat,
              fiber: sum.fiber || 0,
              daily_at,
              created_at: new Date(),
              updated_at: new Date(),
            }

            const dailyMeal = await db
              .insertInto('daily_meals')
              .values(mealData)
              .executeTakeFirst();

            const dailyMealId = Number(dailyMeal.insertId);

            if(meal_foods) {
              await db
                .insertInto('daily_meals_foods')
                .values(meal_foods.map((food: any) => ({
                  meal_id: dailyMealId,
                  food_id: null,
                  name: food.name,
                  quantity: food.quantity,
                  unit: food.unit || 'g',
                  calories: food.calories || 0,
                  protein: food.protein || 0,
                  carbs: food.carbs || 0,
                  fat: food.fat || 0,
                  fiber: food.fiber || 0,
                })))
                .execute();
            }


            return {
              status: 'success',
              data: [],
              message: 'Meal checked successfully',
            };
          }

          async uncheckProtocolDiet(userId: number, body: any) {
            const { id } = body;

            // daily
            await db
              .deleteFrom('daily_meals')
              .where('id', '=', id)
              .where('user_id', '=', userId)
              .execute();

            return {
              status: 'success',
              data: [],
              message: 'Meal unchecked successfully',
            };
          }

          async getDailyCheckedMeal(userId: number, query: any) {
            const { date_start, date_end } = query;
            // if date_start and date_end are not provided, use current date
            const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();
            const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();    
          
            const meals = await db
              .selectFrom('daily_meals as d')
              .leftJoin('nutritionist_protocols_meals as m', 'm.id', 'd.meal_id')
              .select([
                'd.id',
                'd.meal_id',
                'm.name',
                'm.meal_time',
              ])
              .where('d.user_id', '=', userId)
              .where('d.daily_at', '>=', startOfDay)
              .where('d.daily_at', '<=', endOfDay)
              .where('d.meal_id', 'is not', null)
              .execute();
          
            return {
              status: 'success',
              data: meals,
            };
          }

          async getProtocolsWorkoutExercises(userId: number) {
            const today = new Date();
            const protocol = await db
              .selectFrom('coach_protocols as p')
              .select(['p.id'])
              .where('p.client_id', '=', userId)
              .where('p.started_at', '<=', today)
              .where('p.ended_at', 'is', null)
              .orderBy('p.started_at', 'desc')
              .executeTakeFirst();
        
            if (!protocol) {
              return {
                status: 'success',
                data: {
                  has_protocol: false,
                },
              };
            }
        
            const exercises = await db
              .selectFrom('coach_protocols_workouts as p')
              .leftJoin('coach_protocols_workouts_exercises as pe', 'pe.workout_id', 'p.id')
              .leftJoin('exercises as e', 'e.id', 'pe.exercise_id')
              .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')
              .leftJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')
              .select([
                'p.id',
                'pe.id as pe_id',
                'pe.exercise_id',
                // 'pe.split_group',
                'e.name',
                'pe.name as exercise_name',
                's.value_option as muscle_group',
                'equipment.value_option as equipment',
                'e.media_url',
                'pe.sets',
                'pe.reps',
                'pe.rpe',
                'pe.rest_seconds',
                'pe.notes',
              ])
              .where('protocol_id', '=', protocol.id)
              // .where('pe.exercise_id', 'is not', null)
              // .orderBy('p.split_group', 'asc')
              .execute();
        
            // Agrupar os exercícios por split_group
            const workouts = exercises.reduce((acc, curr) => {
              if (!acc[curr.id]) {
                acc[curr.id] = {
                  id: curr.id,
                  exercises: [],
                };
              }
              acc[curr.id].exercises.push(curr);
              return acc;
            }, {});
        
            // Transforma o objeto em um array de workouts
            const workoutsArray = Object.values(workouts);

            const workoutsAndExercises = workoutsArray.map((workout: any) => ({
              id: workout.id,
              exercises: workout.exercises.map((exercise) => ({
                id: exercise.pe_id,
                exercise_id: exercise.exercise_id || null,
                name: exercise.name || exercise.exercise_name,
                muscle_group: exercise.muscle_group || null,
                equipment: exercise.equipment || null,
                media_url: exercise.media_url || null,
                sets: exercise.sets,
                reps: exercise.reps,
                rpe: exercise.rpe,
                rest_seconds: exercise.rest_seconds,
                notes: exercise.notes,
              })),
            }));

        
            return {
              status: 'success',
              data: {
                has_protocol: true,
                id: protocol.id,
                workouts: workoutsAndExercises,
              },
            };
        }

        private convertTimeToHours(time: string): number {
          const [hours, minutes, seconds] = time.split(':').map(Number);
          return hours + minutes / 60 + seconds / 3600;
        }

        async calculateCalories(workoutTime: string, met: number, weight: string | number) {
          const weightKg = typeof weight === 'string' ? parseFloat(weight) : weight;
          const timeInHours = this.convertTimeToHours(workoutTime);
          return met * weightKg * timeInHours * 1.05;
        }

        async postProtocolsWorkoutDaily(userId: number, body: any) {
        const { protocol_id, protocol_workout_id, workout_time, total_weight, met, series } = body;

        const user = await db
          .selectFrom('users')
          .select(['weight'])
          .where('id', '=', userId)
          .executeTakeFirst();

        const weight = user?.weight || 0;

        const total_calories = await this.calculateCalories(workout_time, Number(met), Number(weight));
          
        const seriesData = series.map((serie: any) => ({
          protocol_exercise_id: serie.protocol_exercise_id,
          calories: serie.calories,
          weight: serie.weight,
          reps: serie.reps,
        }));
        
        const dailyProtocolData = {
          user_id: userId,
          protocol_id,
          protocol_workout_id,
          met,
          workout_time,
          total_calories,
          total_weight
        };

        const dailyProtocol = await db
          .insertInto('daily_coach_protocol')
          .values(dailyProtocolData)
          .executeTakeFirst();

        const dailyProtocolId = Number(dailyProtocol.insertId);

        await db
          .insertInto('daily_coach_protocol_series')
          .values(seriesData.map((serie: any) => ({ daily_id: dailyProtocolId, ...serie })))
          .execute();

        return {
          status: 'success',
          data: [],
        };
      }

      async getProtocolsWorkoutDaily(userId: number, query: any) {
        const { page = 1, date_start, date_end } = query;
        const limit = 10;
        const offset = (page - 1) * limit;

        // if date_start and date_end are not provided, use current date
        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();
        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();

      
        let queryResult = db
          .selectFrom('daily_coach_protocol as dcp')
          .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')
          .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')
          .leftJoin('daily_coach_protocol_series as dcps', 'dcps.daily_id', 'dcp.id')
          .leftJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcps.protocol_exercise_id')
          .leftJoin('exercises as e', 'e.id', 'cpwe.exercise_id')
          .leftJoin('select_options as so_type', 'so_type.id', 'cp.type_id')
          .leftJoin('select_options as so_muscle', 'so_muscle.id', 'e.muscle_group_id')
          .select([
            'dcp.created_at as date',
            'cpw.name as workout_name',
            'dcp.workout_time',
            'dcp.total_calories',
            (eb) =>
              eb
                .fn.count(eb.ref('dcps.protocol_exercise_id'))
                .distinct()
                .as('exercise_count'),
            'so_type.value_option as type',
            sql<string>`GROUP_CONCAT(DISTINCT so_muscle.value_option SEPARATOR ', ')`.as('muscle_groups'), // Usando sql diretamente
          ]);

          queryResult = queryResult.where('dcp.user_id', '=', userId);

          if (date_start && date_end) {
            queryResult = queryResult.where('dcp.created_at', '>=', startOfDay).where('dcp.created_at', '<=', endOfDay);
          }

          queryResult = queryResult.groupBy(['dcp.id', 'cpw.name', 'so_type.value_option'])
          .limit(limit)
          .offset(offset)
          .orderBy('dcp.created_at', 'desc');

          const result = await queryResult
          .execute();

          let queryTotalRecordsResult: any = db
          .selectFrom('daily_coach_protocol as dcp')
          .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')
          .where('dcp.user_id', '=', userId);

          if (date_start && date_end) {
            queryTotalRecordsResult = queryTotalRecordsResult.where('dcp.created_at', '>=', startOfDay).where('dcp.created_at', '<=', endOfDay);
          }

          queryTotalRecordsResult = queryTotalRecordsResult.select(db.fn.countAll().as('total'));

          const totalRecordsResult = await queryTotalRecordsResult
          .executeTakeFirstOrThrow();
      
        const totalRecords = parseInt(totalRecordsResult.total, 10);
        const hasMore = totalRecords > page * limit;
      
        return {
          status: 'success',
          data: result.map((row) => ({
            date: row.date,
            workout_name: row.workout_name,
            total_calories: row.total_calories,
            workout_time: row.workout_time,
            exercise_count: Number(row.exercise_count),
            type: row.type || 'N/A',
            muscle_groups: row.muscle_groups || 'N/A', // Garante que não seja undefined
          })),
          pagination: {
            total_records: totalRecords,
            current_page: page,
            limit,
            has_more: hasMore,
          },
        };
      }

      async calculateDaysGoalSequence(userId: number): Promise<number> {
        // Pegar o último dia registrado (o ponto de partida do streak)
        const latestGoal = await db
        .selectFrom('daily_meals_goal')
        .select(['goal_date', 'goal_met'])
        .where('user_id', '=', userId)
        .orderBy('goal_date', 'desc')
        .limit(1)
        .executeTakeFirst();

        // Se não há registros ou o último dia não atingiu a meta, o streak é 0
        if (!latestGoal || !latestGoal.goal_met) {
        return 0;
        }

        let streak = 1; // O último dia conta como 1, se goal_met = TRUE
        const latestDate = new Date(latestGoal.goal_date);
        let previousDate = new Date(latestDate);
        previousDate.setDate(latestDate.getDate() - 1);

        // Verificar o dia anterior para determinar se a sequência continua
        while (true) {
        const previousDateStr = previousDate.toISOString().split('T')[0];

        const previousGoal = await db
          .selectFrom('daily_meals_goal')
          .select('goal_met')
          .where('user_id', '=', userId)
          .where('goal_date', '=', previousDateStr)
          .executeTakeFirst();

        // Se o dia anterior não existe ou a meta não foi atingida, a sequência para
        if (!previousGoal || !previousGoal.goal_met) {
          break;
        }

        streak++;
        previousDate.setDate(previousDate.getDate() - 1); // Continuar verificando o próximo dia anterior
        }

        return streak;
      }

      async getProgressNutritionalDaysGoalSequence(userId: number) {
        const daysGoalSequence = await this.calculateDaysGoalSequence(userId);

        return {
          status: 'success',
          data: {
            days_goal_sequence: daysGoalSequence,
          },
        };
      }

      async postProgressEvaluations(userId: number, body: any, files: { front?: Express.Multer.File, back?: Express.Multer.File, side?: Express.Multer.File }) {
        const { weight, bf } = body;

        // Debug logs
        console.log('Received body:', body);
        console.log('Weight:', weight, 'Type:', typeof weight);
        console.log('BF:', bf, 'Type:', typeof bf);
        console.log('Files:', Object.keys(files).filter(key => files[key]));

        // Convert to numbers and validate
        const weightNum = parseFloat(weight);
        const bfNum = parseFloat(bf);

        if (isNaN(weightNum) || weightNum <= 0 || isNaN(bfNum) || bfNum <= 0) {
          throw new HttpException({
            status: 400,
            message: ['Os campos Peso e Gordura Corporal são obrigatórios e devem ser números válidos maiores que zero.'],
          }, 400);
        }
      
        let frontImage: string | null = null;
        let backImage: string | null = null;
        let sideImage: string | null = null;
      
        const uploadDir = path.join(__dirname, '..', '..', 'storage', 'media', userId.toString(), 'evaluations');
      
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
      
        // Tipos permitidos e dimensões máximas
        const typesAllowed = ['image/jpeg', 'image/png', 'image/jpg'];
        const MAX_DIMENSION = 1080;
      
        for (const key of ['front', 'back', 'side'] as const) {
          const file = files[key];
      
          if (file) {
            if (file.size > 30 * 1024 * 1024) {
              throw new HttpException({ status: 400, message: ['O arquivo deve ter no máximo 30MB.'] }, 400);
            }
      
            if (!typesAllowed.includes(file.mimetype)) {
              throw new HttpException({ status: 400, message: ['O arquivo deve ser uma imagem (jpg, jpeg, png).'] }, 400);
            }
      
            const fileExtension = path.extname(file.originalname);
            const fileName = `${randomUUID()}${fileExtension}`;
            const filePath = path.join(uploadDir, fileName);
      
            const image = sharp(file.buffer);
            const metadata = await image.metadata();
      
            if (metadata.width! > MAX_DIMENSION || metadata.height! > MAX_DIMENSION) {
              const imageResized = await image
                .resize({
                  width: metadata.width! > metadata.height! ? MAX_DIMENSION : undefined,
                  height: metadata.height! > metadata.width! ? MAX_DIMENSION : undefined,
                  fit: 'inside',
                  withoutEnlargement: true
                })
                .jpeg({ quality: 80 })
                .toBuffer();
      
              await fs.promises.writeFile(filePath, imageResized);
            } else {
              await fs.promises.writeFile(filePath, file.buffer);
            }
      
            if (key === 'front') frontImage = fileName;
            if (key === 'back') backImage = fileName;
            if (key === 'side') sideImage = fileName;
          }
        }
      
        const new_evaluation = await db
          .insertInto('evaluations')
          .values({
            user_id: userId,
            weight: weightNum,
            bf: bfNum,
            created_at: new Date(),
            updated_at: new Date(),
          })
          .executeTakeFirst();

        const evaluationId = Number(new_evaluation.insertId);

        // Update user weight and bodyfat
        let userUpdateData = {
          ...(weightNum && weightNum > 0 && { weight: weightNum }),
          ...(bfNum && bfNum > 0 && { bodyfat: bfNum }),
        };

        if (Object.keys(userUpdateData).length > 0) {
          await db
            .updateTable('users')
            .set(userUpdateData)
            .where('id', '=', userId)
            .execute();
        }        
      
        if (frontImage || backImage || sideImage) {
          const imagesToInsert = [
            { name: frontImage, position: 'front' },
            { name: backImage, position: 'back' },
            { name: sideImage, position: 'side' },
          ].filter(img => img.name !== null);
      
          await Promise.all(
            imagesToInsert.map(image =>
              db.insertInto('evaluations_photos')
                .values({
                  evaluation_id: evaluationId,
                  media_type: 'image',
                  media_position: image.position as 'front' | 'back' | 'side',
                  media_url: image.name!,
                })
                .execute()
            )
          );
        }
      
        return { status: 'success', data: [] };
      }

      async postProgressEvaluationsMeasurements(userId: number, body: any) {
        const { shoulders, chest, waist, abdomen, hips, biceps_right, biceps_left, forearm_right, forearm_left, thigh_right, thigh_left, calf_right, calf_left } = body;
        const new_evaluation_measurements = await db
        .insertInto('evaluations_measurements')
        .values({
          user_id: userId,
          shoulders: shoulders,
          chest: chest,
          waist: waist,
          abdomen: abdomen,
          hips: hips,
          biceps_right: biceps_right,
          biceps_left: biceps_left,
          forearm_right: forearm_right,
          forearm_left: forearm_left,
          thigh_right: thigh_right,
          thigh_left: thigh_left,
          calf_right: calf_right,
          calf_left: calf_left,
          created_at: new Date(),
          updated_at: new Date(),
        })
        .executeTakeFirst();

        return {
          status: 'success',
          data: [],
        };

      }

      async getProgressEvaluations(userId: number, query: any) {
        const { page = 1, limit = 5 } = query;
      
        const offset = (page - 1) * limit;
      
        // Consultando as avaliações
        const evaluations = await db
          .selectFrom('evaluations as e')
          .leftJoin('evaluations_photos as ep', 'ep.evaluation_id', 'e.id')
          .select([
            'e.id',
            'e.weight',
            'e.bf',
            'e.created_at',
            'ep.media_url',
            'ep.media_position',
          ])
          .where('e.user_id', '=', userId)
          .orderBy('e.created_at', 'desc')
          .limit(limit)
          .offset(offset)
          .execute();
      
        // Consultando as medições
        const measurements = await db
          .selectFrom('evaluations_measurements as em')
          .select([
            'em.user_id',
            'em.shoulders',
            'em.chest',
            'em.waist',
            'em.abdomen',
            'em.hips',
            'em.biceps_right',
            'em.biceps_left',
            'em.forearm_right',
            'em.forearm_left',
            'em.thigh_right',
            'em.thigh_left',
            'em.calf_right',
            'em.calf_left',
            'em.created_at',
          ])
          .where('em.user_id', '=', userId)
          .orderBy('em.created_at', 'desc')
          .limit(limit)
          .offset(offset)
          .execute();
      
        // Obtendo o total de avaliações
        const totalEvaluations = await db
          .selectFrom('evaluations')
          .where('user_id', '=', userId)
          .select(db.fn.countAll().as('total'))
          .executeTakeFirst();
      
        // Formatando as avaliações
        const formattedEvaluations = evaluations.reduce((acc: any, evaluation: any) => {
          // Verifica se a avaliação já foi processada
          let evaluationData: any = acc.find((item: any) => item.id === evaluation.id);
      
          // Se a avaliação ainda não foi processada, cria um novo item para avaliação
          if (!evaluationData) {
            evaluationData = {
              id: evaluation.id,
              type: 'evaluation', // Adiciona tipo de dado
              weight: evaluation.weight,
              bf: evaluation.bf,
              created_at: evaluation.created_at,
              photos: {
                front: null,
                back: null,
                side: null,
              },
            };
            acc.push(evaluationData);
          }

          const photo_base_url = process.env.STATIC_URL + '/storage/media/' + userId + '/evaluations/';
      
          // Preenche a foto baseada na posição
          if (evaluation.media_position === 'front') {
            evaluationData.photos.front = photo_base_url + evaluation.media_url;
          } else if (evaluation.media_position === 'back') {
            evaluationData.photos.back = photo_base_url + evaluation.media_url;
          } else if (evaluation.media_position === 'side') {
            evaluationData.photos.side = photo_base_url + evaluation.media_url;
          }
      
          return acc;
        }, []);
      
        // Formatando as medições
        const formattedMeasurements = measurements.reduce((acc: any, measurement: any) => {
          // Verifica se a medição já foi processada
          let measurementData: any = acc.find((item: any) => item.created_at === measurement.created_at);
      
          // Se a medição ainda não foi processada, cria um novo item para medição
          if (!measurementData) {
            measurementData = {
              id: measurement.user_id,
              type: 'measurement', // Adiciona tipo de dado
              shoulders: measurement.shoulders,
              chest: measurement.chest,
              waist: measurement.waist,
              abdomen: measurement.abdomen,
              hips: measurement.hips,
              biceps_right: measurement.biceps_right,
              biceps_left: measurement.biceps_left,
              forearm_right: measurement.forearm_right,
              forearm_left: measurement.forearm_left,
              thigh_right: measurement.thigh_right,
              thigh_left: measurement.thigh_left,
              calf_right: measurement.calf_right,
              calf_left: measurement.calf_left,              
              created_at: measurement.created_at,
            };
            acc.push(measurementData);
          }
      
          return acc;
        }, []);
      
        // Juntando as avaliações e medições em um único array
        const allData = [...formattedEvaluations, ...formattedMeasurements];
      
        // Ordenando tudo por data de criação para que fique organizado
        allData.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
        return {
          status: 'success',
          data: allData,
          pagination: {
            page,
            limit,
            total: Number(totalEvaluations?.total),
          },
        };
      }
      

      async calculateCurrentStreak(userId: number): Promise<number> {
        const latestGoal = await db
          .selectFrom('daily_meals_goal')
          .select(['goal_date', 'goal_met'])
          .where('user_id', '=', userId)
          .orderBy('goal_date', 'desc')
          .limit(1)
          .executeTakeFirst();
      
        if (!latestGoal || !latestGoal.goal_met) return 0;
      
        let streak = 1;
        const latestDate = new Date(latestGoal.goal_date);
        let previousDate = new Date(latestDate);
        previousDate.setDate(latestDate.getDate() - 1);
      
        while (true) {
          const previousDateStr = previousDate.toISOString().split('T')[0];
          const previousGoal = await db
            .selectFrom('daily_meals_goal')
            .select('goal_met')
            .where('user_id', '=', userId)
            .where('goal_date', '=', previousDateStr)
            .executeTakeFirst();
      
          if (!previousGoal || !previousGoal.goal_met) break;
      
          streak++;
          previousDate.setDate(previousDate.getDate() - 1);
        }
      
        return streak;
      }

      async calculateRecordStreak(userId: number): Promise<number> {
        // Pegar todos os dias com meta atingida, ordenados por data
        const goals = await db
          .selectFrom('daily_meals_goal')
          .select('goal_date')
          .where('user_id', '=', userId)
          .where('goal_met', '=', true)
          .orderBy('goal_date', 'asc')
          .execute();
      
        if (!goals.length) return 0;
      
        let maxStreak = 0;
        let currentStreak = 1;
      
        for (let i = 1; i < goals.length; i++) {
          const currentDate = new Date(goals[i].goal_date);
          const previousDate = new Date(goals[i - 1].goal_date);
          const diffInDays = (currentDate.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24);
      
          if (diffInDays === 1) {
            // Dias consecutivos
            currentStreak++;
          } else {
            // Sequência quebrada, reiniciar
            maxStreak = Math.max(maxStreak, currentStreak);
            currentStreak = 1;
          }
        }
      
        // Atualizar o máximo com a última sequência
        maxStreak = Math.max(maxStreak, currentStreak);
      
        return maxStreak;
      }

      async calculateWeekAttendance(userId: number, currentDate: Date): Promise<number> {
        // Definir o início e fim da semana atual (segunda a domingo)
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - (currentDate.getDay() + 6) % 7); // Segunda-feira
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // Domingo
      
        const startDateStr = startOfWeek.toISOString().split('T')[0];
        const endDateStr = endOfWeek.toISOString().split('T')[0];
      
        // Contar dias com meta atingida na semana
        const successfulDays = await db
          .selectFrom('daily_meals_goal')
          .select(sql<number>`COUNT(DISTINCT goal_date)`.as('count'))
          .where('user_id', '=', userId)
          .where('goal_date', '>=', startDateStr)
          .where('goal_date', '<=', endDateStr)
          .where('goal_met', '=', true)
          .executeTakeFirst();
      
        const daysWithGoalMet = successfulDays?.count || 0;
        const totalDaysInWeek = 7; // Sempre 7 dias na semana
      
        // Calcular percentual
        return Math.round((daysWithGoalMet / totalDaysInWeek) * 100);
      }

      async calculateMonthAttendance(userId: number, currentDate: Date): Promise<number> {
        // Definir o início e fim do mês atual
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
      
        const startDateStr = startOfMonth.toISOString().split('T')[0];
        const endDateStr = endOfMonth.toISOString().split('T')[0];
      
        // Contar dias com meta atingida no mês
        const successfulDays = await db
          .selectFrom('daily_meals_goal')
          .select(sql<number>`COUNT(DISTINCT goal_date)`.as('count'))
          .where('user_id', '=', userId)
          .where('goal_date', '>=', startDateStr)
          .where('goal_date', '<=', endDateStr)
          .where('goal_met', '=', true)
          .executeTakeFirst();
      
        const daysWithGoalMet = successfulDays?.count || 0;
        const totalDaysInMonth = endOfMonth.getDate(); // Número de dias no mês
      
        // Calcular percentual
        return Math.round((daysWithGoalMet / totalDaysInMonth) * 100);
      }
      
      async getProgressDietWeekly(userId: number) {
          // Obter o protocolo ativo do usuário
          const protocol = await db
            .selectFrom('nutritionist_protocols')
            .where('client_id', '=', userId)
            .where('ended_at', 'is', null)
            .select(['id'])
            .executeTakeFirst();
      
          if (!protocol) {
            return {
              status: 'success',
              data: {
                weekly: [],
                success_rate: 0,
                complete_meals: 0,
                perfect_days: 0,
              },
            };
          }
      
          // Obter o total de refeições esperadas por dia da semana do protocolo
          const protocolMeals = await db
            .selectFrom('nutritionist_protocols_meals')
            .where('protocol_id', '=', protocol.id)
            .select(['day_of_week'])
            .groupBy('day_of_week')
            .select((eb) => eb.fn.count('id').as('meal_count'))
            .execute();
      
          // Tipagem explícita para o resultado
          interface ProtocolMealCount {
            day_of_week: string;
            meal_count: number;
          }
      
          const totalMealsByDay: { [key: string]: number } = {};
          (protocolMeals as ProtocolMealCount[]).forEach((meal) => {
            totalMealsByDay[meal.day_of_week] = meal.meal_count;
          });
      
          // Mapear dias da semana (banco -> interface)
          const dayMapping: { [key: string]: string } = {
            monday: 'Seg',
            tuesday: 'Ter',
            wednesday: 'Qua',
            thursday: 'Qui',
            friday: 'Sex',
            saturday: 'Sab',
            sunday: 'Dom',
          };
      
          // Obter o intervalo da semana atual (segunda a domingo)
          const startOfWeek = dayjs().startOf('isoWeek').toDate();
          const endOfWeek = dayjs().endOf('isoWeek').toDate();
      
          // Contar refeições completadas por dia
          const dailyMeals = await db
            .selectFrom('daily_meals')
            .where('user_id', '=', userId)
            .where('daily_at', '>=', startOfWeek)
            .where('daily_at', '<=', endOfWeek)
            .where('meal_id', 'is not', null)
            .select((eb) => [sql<string>`DATE(daily_at)`.as('day')])
            .execute();
      
          // Mapear os dias da semana
          const weeklyProgress: any[] = [];
          for (let i = 0; i < 7; i++) {
            const currentDate = dayjs(startOfWeek).add(i, 'day').toDate();
            const dayOfWeek = dayjs(currentDate).format('dddd').toLowerCase();
            const mappedDay = dayMapping[dayOfWeek as keyof typeof dayMapping];
            const total = totalMealsByDay[dayOfWeek] || 0;
            const completed = dailyMeals.filter((meal) =>
              dayjs(meal.day).isSame(currentDate, 'day')
            ).length;
      
            weeklyProgress.push({
              day: mappedDay,
              completed,
              total,
            });
          }
      
          // Calcular métricas
          const completeMeals = weeklyProgress.reduce((acc: number, day: any) => acc + day.completed, 0);
          const totalPossibleMeals = weeklyProgress.reduce((acc, day) => acc + day.total, 0);
          const successRate = totalPossibleMeals > 0 ? (completeMeals / totalPossibleMeals) * 100 : 0;
          const perfectDays = weeklyProgress.filter((day) => day.completed === day.total).length;
      
          return {
            status: 'success',
            data: {
              weekly: weeklyProgress,
              success_rate: Number(successRate.toFixed(2)),
              complete_meals: completeMeals,
              perfect_days: perfectDays,
            },
          };
        }

    // Novo método para progresso semanal consolidado (treinos, nutrição, sono, água)
    async getWeeklyProgressConsolidated(userId: number) {
      const startOfWeek = dayjs().startOf('isoWeek').toDate();
      const endOfWeek = dayjs().endOf('isoWeek').toDate();

      console.log('🔄 getWeeklyProgressConsolidated: Buscando dados para usuário', userId);
      console.log('📅 Período:', startOfWeek.toISOString(), 'até', endOfWeek.toISOString());

      try {
        // 1. Dados de nutrição (já implementado)
        const nutritionData = await this.getProgressDietWeekly(userId);

        // 2. Dados de treinos
        const workoutData = await db
          .selectFrom('daily_workouts_activities')
          .where('user_id', '=', userId)
          .where('daily_at', '>=', startOfWeek)
          .where('daily_at', '<=', endOfWeek)
          .select([
            sql`DATE(daily_at)`.as('day'),
            sql`COUNT(*)`.as('completed'),
            sql`SUM(calories)`.as('calories_burned')
          ])
          .groupBy(sql`DATE(daily_at)`)
          .execute();

        // 3. Dados de água
        const waterData = await db
          .selectFrom('daily_water')
          .where('user_id', '=', userId)
          .where('daily_at', '>=', startOfWeek)
          .where('daily_at', '<=', endOfWeek)
          .select([
            sql`DATE(daily_at)`.as('day'),
            sql`SUM(consumed)`.as('total_consumed')
          ])
          .groupBy(sql`DATE(daily_at)`)
          .execute();

            // 4. Dados de sono (mock por enquanto - pode ser integrado com wearables)
            const sleepData: SleepData[] = [];
            for (let i = 0; i < 7; i++) {
              const currentDate = dayjs(startOfWeek).add(i, 'day');
              sleepData.push({
                day: currentDate.format('YYYY-MM-DD'),
                hours: 7 + Math.random() * 2, // 7-9 horas
                quality: Math.floor(Math.random() * 30) + 70 // 70-100%
              });
            }

            // Mapear dados por dia da semana
            const dayMapping = {
              monday: 'Seg',
              tuesday: 'Ter',
              wednesday: 'Qua',
              thursday: 'Qui',
              friday: 'Sex',
              saturday: 'Sáb',
              sunday: 'Dom'
            };

            const weeklyConsolidated: WeeklyConsolidatedData[] = [];
            for (let i = 0; i < 7; i++) {
              const currentDate = dayjs(startOfWeek).add(i, 'day');
              const dayOfWeek = currentDate.format('dddd').toLowerCase();
              const mappedDay = dayMapping[dayOfWeek];
              const dateStr = currentDate.format('YYYY-MM-DD');

              // Encontrar dados para este dia
              const nutritionDay = nutritionData.data.weekly.find(d => d.day === mappedDay) || { completed: 0, total: 0 };
              const workoutDay = workoutData.find(d => dayjs(d.day).format('YYYY-MM-DD') === dateStr) || { completed: 0, calories_burned: 0 };
              const waterDay = waterData.find(d => dayjs(d.day).format('YYYY-MM-DD') === dateStr) || { total_consumed: 0 };
              const sleepDay = sleepData.find(d => d.day === dateStr) || { hours: 0, quality: 0 };

              weeklyConsolidated.push({
                day: mappedDay,
                date: dateStr,
                nutrition: {
                  completed: nutritionDay.completed,
                  total: nutritionDay.total,
                  percentage: nutritionDay.total > 0 ? Math.round((nutritionDay.completed / nutritionDay.total) * 100) : 0
                },
                workout: {
                  completed: Number(workoutDay.completed) || 0,
                  calories_burned: Number(workoutDay.calories_burned) || 0,
                  target: 1, // Meta de 1 treino por dia
                  percentage: Number(workoutDay.completed) > 0 ? 100 : 0
                },
                water: {
                  consumed: Number(waterDay.total_consumed) || 0,
                  target: 2500, // Meta de 2.5L por dia
                  percentage: Math.min(Math.round((Number(waterDay.total_consumed) || 0) / 2500 * 100), 100)
                },
                sleep: {
                  hours: Number(sleepDay.hours) || 0,
                  quality: Number(sleepDay.quality) || 0,
                  target: 8, // Meta de 8 horas
                  percentage: Math.min(Math.round((Number(sleepDay.hours) || 0) / 8 * 100), 100)
                }
              });
            }

            // Calcular estatísticas gerais
            const totalDays = weeklyConsolidated.length;
            const nutritionSuccess = weeklyConsolidated.filter(d => d.nutrition.percentage >= 80).length;
            const workoutSuccess = weeklyConsolidated.filter(d => d.workout.percentage >= 100).length;
            const waterSuccess = weeklyConsolidated.filter(d => d.water.percentage >= 80).length;
            const sleepSuccess = weeklyConsolidated.filter(d => d.sleep.percentage >= 80).length;

            return {
              status: 'success',
              data: {
                weekly: weeklyConsolidated,
                summary: {
                  nutrition: {
                    success_rate: Math.round((nutritionSuccess / totalDays) * 100),
                    days_completed: nutritionSuccess
                  },
                  workout: {
                    success_rate: Math.round((workoutSuccess / totalDays) * 100),
                    days_completed: workoutSuccess,
                    total_calories: weeklyConsolidated.reduce((sum, d) => sum + d.workout.calories_burned, 0)
                  },
                  water: {
                    success_rate: Math.round((waterSuccess / totalDays) * 100),
                    days_completed: waterSuccess,
                    total_consumed: weeklyConsolidated.reduce((sum, d) => sum + d.water.consumed, 0)
                  },
                  sleep: {
                    success_rate: Math.round((sleepSuccess / totalDays) * 100),
                    days_completed: sleepSuccess,
                    average_hours: weeklyConsolidated.reduce((sum, d) => sum + d.sleep.hours, 0) / totalDays,
                    average_quality: weeklyConsolidated.reduce((sum, d) => sum + d.sleep.quality, 0) / totalDays
                  }
                }
              }
            };

          } catch (error) {
            console.error('❌ Erro ao buscar progresso semanal consolidado:', error);
            return {
              status: 'error',
              message: 'Erro ao buscar dados de progresso semanal',
              data: null
            };
          }
        }

      async getProgressDiet(userId: number) {
      }

      async getProgressWeightFat(userId: number, query: any) {
        const { date_start, date_end } = query;

        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();
        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();

        let weightFatQuery = db
        .selectFrom('evaluations')
        .where('user_id', '=', userId);
        if(date_start) {
          weightFatQuery = weightFatQuery
          .where('created_at', '>=', startOfDay);
        }
        if(date_end) {
          weightFatQuery = weightFatQuery
          .where('created_at', '<=', endOfDay);
        }
        const weightFat = await weightFatQuery
        .select([
            'created_at as date',
            'weight',
            'bf',
        ])
        .execute();

        return {
            status: 'success',
            data: weightFat,
        }

      }

      async getProgressStrength(userId: number, query: any) {
        const { date_start, date_end, filter, filter_id } = query;

        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();
        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();

        const strengthQuery = db
        .selectFrom('daily_coach_protocol_series as dcp')
        .innerJoin('daily_coach_protocol as dcp2', 'dcp2.id', 'dcp.daily_id')
        .innerJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcp.protocol_exercise_id')
        .innerJoin('exercises as e', 'e.id', 'cpwe.exercise_id')
        .where('dcp2.user_id', '=', userId)
        .where('dcp2.daily_at', '>=', startOfDay)
        .where('dcp2.daily_at', '<=', endOfDay)
        .select([
            'dcp2.daily_at as date',
            (eb) => eb.fn.sum('dcp.weight').as('weight'),
        ]);

        if (filter === 'muscle_group') {
            strengthQuery
            .innerJoin('select_options as so', 'so.id', 'e.muscle_group_id')
            .where('so.id', '=', filter_id)
        }
        
        if (filter === 'exercise') {
            strengthQuery
            .where('e.id', '=', filter_id)
        }

        const strength = await strengthQuery
        .groupBy('dcp.created_at')
        .execute();

        return {
            status: 'success',
            data: strength,
        }
      }
      
  async getProgressNutritionalAnalysis(userId: number, query: { date_start?: string; date_end?: string }) {
    const { date_start, date_end } = query;

    // Definir intervalo de datas (padrão: semana atual)
    const startOfWeek = date_start
      ? dayjs(date_start).startOf('day').toDate()
      : dayjs().startOf('week').toDate();
    const endOfWeek = date_end
      ? dayjs(date_end).endOf('day').toDate()
      : dayjs().endOf('week').toDate();

    // Consultar as refeições do usuário no intervalo de datas
    const nutritionalDataDaily: any = await db
      .selectFrom('daily_meals as dm')
      .leftJoin('daily_meals_foods as dmf', 'dmf.meal_id', 'dm.id')
      .leftJoin('foods as f', 'f.id', 'dmf.food_id')
      .where('dm.user_id', '=', userId)
      .where('dm.daily_at', '>=', startOfWeek)
      .where('dm.daily_at', '<=', endOfWeek)
      .select((eb) => [
        eb.fn.sum('dmf.calories').as('total_calories'),
        eb.fn.sum('dmf.carbs').as('total_carbs'),
        eb.fn.sum('dmf.protein').as('total_protein'),
        eb.fn.sum('dmf.fat').as('total_fat'),
        eb.fn.count('dm.id').as('meal_count'),
      ])
      .executeTakeFirst();

      // from protocol daily_nutritionist_protocol
      const nutritionalData = await db
      .selectFrom('daily_meals')
      .where('user_id', '=', userId)
      .where('daily_at', '>=', startOfWeek)
      .where('daily_at', '<=', endOfWeek)
      .select((eb) => [
        eb.fn.sum('calories').as('total_calories'),
        eb.fn.sum('carbs').as('total_carbs'),
        eb.fn.sum('protein').as('total_protein'),
        eb.fn.sum('fat').as('total_fat'),
        eb.fn.count('id').as('meal_count'),
      ])
      .executeTakeFirst();


    // Definir interface para o resultado da consulta
    interface NutritionalSummary {
      total_calories: number | null;
      total_carbs: number | null;
      total_protein: number | null;
      total_fat: number | null;
      meal_count: number;
    }

    // Se não houver refeições, retornar valores zerados
    if (!nutritionalData || nutritionalData.meal_count === 0) {
      return {
        status: 'success',
        data: {
          weekly_averages: {
            macronutrient_distribution: {
              calories: 0,
              calories_percent: 100,
              carbs: 0,
              carbs_percent: 0,
              protein: 0,
              protein_percent: 0,
              fat: 0,
              fat_percent: 0,
            },
          },
        },
      };
    }

    // Calcular médias semanais
    const daysInRange = dayjs(endOfWeek).diff(startOfWeek, 'day') + 1;
    const avgCalories = Number(nutritionalData.total_calories || 0) / daysInRange;
    const avgCarbs = Number(nutritionalData.total_carbs || 0) / daysInRange;
    const avgProtein = Number(nutritionalData.total_protein || 0) / daysInRange;
    const avgFat = Number(nutritionalData.total_fat || 0) / daysInRange;

    // Calcular calorias contribuídas por cada macronutriente
    const carbsCalories = avgCarbs * 4; // 1g carboidrato = 4 kcal
    const proteinCalories = avgProtein * 4; // 1g proteína = 4 kcal
    const fatCalories = avgFat * 9; // 1g gordura = 9 kcal
    const totalCaloriesFromMacros = carbsCalories + proteinCalories + fatCalories;

    // Calcular percentuais
    const carbsPercent = totalCaloriesFromMacros > 0 ? (carbsCalories / totalCaloriesFromMacros) * 100 : 0;
    const proteinPercent = totalCaloriesFromMacros > 0 ? (proteinCalories / totalCaloriesFromMacros) * 100 : 0;
    const fatPercent = totalCaloriesFromMacros > 0 ? (fatCalories / totalCaloriesFromMacros) * 100 : 0;

    // Generate micronutrients data based on macronutrient intake
    const micronutrientsData = this.generateMicronutrientsFromMacros({
      calories: Math.round(avgCalories),
      protein: Math.round(avgProtein),
      carbs: Math.round(avgCarbs),
      fat: Math.round(avgFat)
    });

    return {
      status: 'success',
      data: {
        date: new Date().toISOString(),
        totalIntake: micronutrientsData.totalIntake,
        deficiencies: micronutrientsData.deficiencies,
        excesses: micronutrientsData.excesses,
        recommendations: micronutrientsData.recommendations,
        overallScore: micronutrientsData.overallScore,
        improvementAreas: micronutrientsData.improvementAreas,
        weekly_averages: {
          macronutrient_distribution: {
            calories: Math.round(avgCalories),
            calories_percent: 100,
            carbs: Math.round(avgCarbs),
            carbs_percent: Math.round(carbsPercent),
            protein: Math.round(avgProtein),
            protein_percent: Math.round(proteinPercent),
            fat: Math.round(avgFat),
            fat_percent: Math.round(fatPercent),
          },
        },
      },
    };
  }


  
  async getProgressCaloricBalance(userId: number, query: { date_start?: string; date_end?: string }) {
    const { date_start, date_end } = query;
  
    // Definir intervalo de datas (padrão: semana atual)
    const startOfWeek = date_start
      ? new Date(date_start)
      : new Date();
    const endOfWeek = date_end
      ? new Date(date_end)
      : new Date();
  
    startOfWeek.setHours(0, 0, 0, 0);
    endOfWeek.setHours(23, 59, 59, 999);
  
    // Consulta principal para obter o saldo calórico
    const result = await db
      .selectFrom('daily_meals as dnp')
      .leftJoin('daily_workouts_activities as dwa', 'dwa.user_id', 'dnp.user_id')
      .where('dnp.user_id', '=', userId)
      .where('dnp.daily_at', '>=', startOfWeek)
      .where('dnp.daily_at', '<=', endOfWeek)
      .select([
        // db.fn.coalesce(db.fn.date('dnp.created_at'), '').as('date'),
        'dnp.daily_at as date',
        // db.fn.sum('dnp.calories').as('consumed'),
        (eb) => eb.fn.sum(eb.ref('dnp.calories')).as('consumed'),
        // db.fn.sum('dwa.calories').as('burned'),
        (eb) => eb.fn.sum(eb.ref('dwa.calories')).as('burned'),
      ])
      .groupBy('date')
      .orderBy('date', 'asc')
      .execute();
  
    // Calcular médias e taxa metabólica basal
    const totalConsumed = result.reduce((sum, row) => sum + Number(row.consumed || 0), 0);
    const totalBurned = result.reduce((sum, row) => sum + Number(row.burned || 0), 0);
    const averageConsumed = totalConsumed / result.length || 0;
    const averageBurned = totalBurned / result.length || 0;
  
    return {
      status: 'success',
      data: {
        chart: result.map((row: any) => ({
          date: row.date.toISOString().split('T')[0],
          consumed: Number(row.consumed || 0),
          burned: Number(row.burned || 0),
        })),
        average_consumed: averageConsumed,
        average_burned: averageBurned,
        basal_metabolic_rate: 0, // Implementar lógica para calcular a taxa metabólica basal
      },
    };
  }

      async getProgressWorkouts(userId: number, query: any) {
        const { date_start, date_end } = query;
        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();
        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();

        const workouts = await db
        .selectFrom('daily_coach_protocol as dcp')
        .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')
        .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')
        .leftJoin('daily_coach_protocol_series as dcps', 'dcps.daily_id', 'dcp.id')
        .leftJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcps.protocol_exercise_id')
        .leftJoin('exercises as e', 'e.id', 'cpwe.exercise_id')
        .leftJoin('select_options as so_type', 'so_type.id', 'cp.type_id')
        .leftJoin('select_options as so_muscle', 'so_muscle.id', 'e.muscle_group_id')
        .select([
            'dcp.created_at as date',
            'cpw.name as workout_name',
            'dcp.workout_time',
            'dcp.total_calories',
            (eb) =>
              eb
                .fn.count(eb.ref('dcps.protocol_exercise_id'))
                .distinct()
                .as('exercise_count'),
            'so_type.value_option as type',
            sql<string>`GROUP_CONCAT(DISTINCT so_muscle.value_option SEPARATOR ', ')`.as('muscle_groups'), // Usando sql diretamente
          ])
        .where('dcp.user_id', '=', userId)
        .where('dcp.created_at', '>=', startOfDay)
        .where('dcp.created_at', '<=', endOfDay)
        .groupBy(['dcp.id', 'cpw.name', 'so_type.value_option'])
        .execute();

        const workoutsMapped = workouts.map((item: any) => {
            return {
                date: item.date,
                workout_name: item.workout_name,
                total_calories: item.total_calories,
                workout_time: item.workout_time,
                exercise_count: Number(item.exercise_count),
                type: item.type || 'N/A',
                muscle_groups: item.muscle_groups || 'N/A', // Garante que não seja undefined
            }
        })

        const workoutsAvarage = workoutsMapped.reduce((acc: any, item: any) => {
            acc.total_calories += item.total_calories;
            acc.workout_time += item.workout_time;
            return acc;
        }, { total_calories: 0, workout_time: 0 })

        return {
            status: 'success',
            data: {
                workouts: workoutsMapped,
                minutes: workoutsAvarage.workout_time,
                calories: workoutsAvarage.total_calories,
                chart: workoutsMapped,
            }
        }
      }

      async getProgressWorkoutsAnalysis(userId: number, query: any) {
        const { date_start, date_end } = query;
      
        // Definir intervalo de datas (padrão: semana atual)
        const startOfWeek = date_start
          ? new Date(date_start)
          : new Date();
        const endOfWeek = date_end
          ? new Date(date_end)
          : new Date();
      
        startOfWeek.setHours(0, 0, 0, 0);
        endOfWeek.setHours(23, 59, 59, 999);
      
        // Consulta principal para obter os dados dos treinos
        const result = await db
          .selectFrom('daily_coach_protocol as dcp')
          .where('dcp.user_id', '=', userId)
          .where('dcp.daily_at', '>=', startOfWeek)
          .where('dcp.daily_at', '<=', endOfWeek)
          .select([
            // db.fn.count('dcp.id').as('workouts'),
            (eb) => eb.fn.count(eb.ref('dcp.id')).as('workouts'),
            // db.fn.sum('dcp.total_calories').as('calories'),
            (eb) => eb.fn.sum(eb.ref('dcp.total_calories')).as('calories'),
            // db.fn.coalesce(db.fn.date('dcp.created_at'), '').as('date'),
            'dcp.daily_at as date',
            // db.fn.sum('dcp.workout_time').as('minutes'),
            (eb) => eb.fn.sum(eb.ref('dcp.workout_time')).as('minutes'),
          ])
          .groupBy('date')
          .orderBy('date', 'asc')
          .execute();
      
        // Calcular totais
        const totalWorkouts = result.reduce((sum, row) => sum + Number(row.workouts || 0), 0);
        const totalMinutes = result.reduce((sum, row) => sum + Number(row.minutes || 0), 0);
        const totalCalories = result.reduce((sum, row) => sum + Number(row.calories || 0), 0);
      
        // Montar o gráfico
        const chartData = result.map((row: any) => ({
          label: row.date.toISOString().split('T')[0],
          training_volume: Number(row.minutes || 0),
        }));
      
        return {
          status: 'success',
          data: {
            workouts: totalWorkouts,
            minutes: totalMinutes,
            calories: totalCalories,
            chart: chartData,
          },
        };
      }

      
        async getProgressCompleteAnalysis(userId: number, query: { date_start?: string; date_end?: string }) {
          const { date_start, date_end } = query;
      
          // Definir intervalo de datas (padrão: mês atual)
          const startOfPeriod = date_start
            ? dayjs(date_start).startOf('day').toDate()
            : dayjs().startOf('month').toDate();
          const endOfPeriod = date_end
            ? dayjs(date_end).endOf('day').toDate()
            : dayjs().endOf('month').toDate();
      
          // 1. Calcular horário mais frequente dos treinos
          const workoutTimes = await db
            .selectFrom('daily_coach_protocol')
            .where('user_id', '=', userId)
            .where('workout_time', '>=', startOfPeriod)
            .where('workout_time', '<=', endOfPeriod)
            .select((eb) => [
              sql<string>`HOUR(workout_time)`.as('hour'),
              eb.fn.count('id').as('count'),
            ])
            .groupBy('hour')
            .orderBy('count', 'desc')
            .executeTakeFirst();
          const mostFrequentTime = workoutTimes ? `${String(workoutTimes.hour).padStart(2, '0')}:00` : '00:00';
      
          // 2. Calcular intervalo médio entre treinos
          const allWorkouts = await db
            .selectFrom('daily_coach_protocol')
            .where('user_id', '=', userId)
            .where('workout_time', '>=', startOfPeriod)
            .where('workout_time', '<=', endOfPeriod)
            .select('workout_time')
            .orderBy('workout_time', 'asc')
            .execute();
          let averageInterval = 0;
          if (allWorkouts.length > 1) {
            const intervals: number[] = [];
            for (let i = 1; i < allWorkouts.length; i++) {
              const diff = dayjs(allWorkouts[i].workout_time).diff(allWorkouts[i - 1].workout_time, 'hour', true);
              intervals.push(diff);
            }
            averageInterval = intervals.length > 0 ? intervals.reduce((a, b) => a + b, 0) / intervals.length : 0;
          }
          const averageIntervalFormatted = averageInterval.toFixed(1) + 'h';
      
          // 3. Calcular primeira e última refeição
          const mealTimes = await db
            .selectFrom('daily_meals_foods as df')
            .innerJoin('daily_meals as dm', 'dm.id', 'df.meal_id')
            .where('dm.user_id', '=', userId)
            .where('dm.daily_at', '>=', startOfPeriod)
            .where('dm.daily_at', '<=', endOfPeriod)
            .select(sql<Date>`HOUR(dm.daily_at)`.as('meal_time')) // hour of daily_at
            .execute();
          const sortedMealTimes = mealTimes.map((m) => m.meal_time).sort((a: any, b: any) => a - b);
          const firstMeal = sortedMealTimes.length > 0 ? dayjs(sortedMealTimes[0]).format('HH:mm') : '00:00';
          const lastMeal = sortedMealTimes.length > 0 ? dayjs(sortedMealTimes[sortedMealTimes.length - 1]).format('HH:mm') : '00:00';
      
          // 4. Calcular faixas de horário dinâmicas (timeSlots) com base nos dados
          const totalDays = dayjs(endOfPeriod).diff(startOfPeriod, 'day') + 1;
          const mealHours = mealTimes.map((m) => dayjs(m.meal_time).hour());
          const minHour = mealHours.length > 0 ? Math.min(...mealHours) : 0;
          const maxHour = mealHours.length > 0 ? Math.max(...mealHours) : 23;
      
          // Criar faixas dinâmicas (ex.: dividir em 6 faixas entre minHour e maxHour)
          const numSlots = 6; // Número de faixas desejadas (ajustável)
          const range = maxHour - minHour;
          const slotSize = range > 0 ? Math.ceil(range / numSlots) : 1;
          const timeSlots: { time: string; start: number; end: number }[] = [];
          for (let i = 0; i < numSlots; i++) {
            const start = minHour + i * slotSize;
            const end = Math.min(start + slotSize - 1, 23);
            timeSlots.push({
              time: `${String(start).padStart(2, '0')}:00`,
              start,
              end,
            });
          }
      
          // Calcular frequência por horário
          const frequencyByTime = await Promise.all(
            timeSlots.map(async (slot) => {
              const daysWithMeal = await db
                .selectFrom('daily_meals_foods as df')
                .innerJoin('daily_meals as dm', 'dm.id', 'df.meal_id')
                .where('dm.user_id', '=', userId)
                .where('dm.daily_at', '>=', startOfPeriod)
                .where('dm.daily_at', '<=', endOfPeriod)
                .where((eb) =>
                  eb.and([
                    eb(sql<number>`HOUR(dm.daily_at)`, '>=', slot.start),
                    eb(sql<number>`HOUR(dm.daily_at)`, '<=', slot.end),
                  ])
                )
                .select((eb) => [sql<string>`DATE(dm.daily_at)`.as('day')])
                .distinct()
                .execute();
              const percent = totalDays > 0 ? (daysWithMeal.length / totalDays) * 100 : 0;
              return { time: slot.time, percent: Math.round(percent) };
            })
          );
      
          // 5. Buscar grupos musculares dinamicamente
          const muscleGroupsData = await db
            .selectFrom('select_options')
            .where('area_key', '=', 'muscle_group')
            .select(['id', 'value_option as name'])
            .execute();
          const muscleGroups = muscleGroupsData.reduce((acc: any, group: any) => {
            acc[group.id] = group.name;
            return acc;
          }, {} as { [key: number]: string });
      
          // Calcular distribuição de volume por grupo muscular
          const volumeDistribution = await db
            .selectFrom('daily_coach_protocol_series')
            .innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id')
            .innerJoin('coach_protocols_workouts_exercises' , 'coach_protocols_workouts_exercises.id', 'daily_coach_protocol_series.protocol_exercise_id')
            .innerJoin('exercises', 'exercises.id', 'coach_protocols_workouts_exercises.exercise_id')
            .where('daily_coach_protocol.user_id', '=', userId)
            .where('daily_coach_protocol.workout_time', '>=', startOfPeriod)
            .where('daily_coach_protocol.workout_time', '<=', endOfPeriod)
            .select((eb: any) => [
              'exercises.muscle_group_id',
              eb.fn.sum('daily_coach_protocol_series.calories').as('total_calories'),
            ])
            .groupBy('exercises.muscle_group_id')
            .execute();
      
          const totalCalories = volumeDistribution.reduce((sum, d) => sum + Number(d.total_calories), 0);
          const volumeDistributionFormatted = volumeDistribution.map((d) => ({
            muscle_group: muscleGroups[d.muscle_group_id] || 'Outros',
            percent: totalCalories > 0 ? Math.round((Number(d.total_calories) / totalCalories) * 100) : 0,
          }));
      
          // 6. Retornar os dados
          return {
            status: 'success',
            data: {
              most_frequent_time: mostFrequentTime,
              average_interval: averageIntervalFormatted,
              first_meal: firstMeal,
              last_meal: lastMeal,
              frequency_by_time: frequencyByTime,
              volume_distribution: volumeDistributionFormatted,
            },
          };
        }

      async getProgressVolumeDistribution(userId: number, query: any) {
        const { date_start, date_end } = query;
        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();
        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();


        const volumeDistribution = await db
        .selectFrom('daily_coach_protocol_series as dcp')
        .innerJoin('daily_coach_protocol as dcp2', 'dcp2.id', 'dcp.daily_id')
        .innerJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcp.protocol_exercise_id')
        .innerJoin('exercises as e', 'e.id', 'cpwe.exercise_id') 
        .innerJoin('select_options as so', 'so.id', 'e.muscle_group_id')
        .where('dcp2.user_id', '=', userId)
        .where('dcp2.daily_at', '>=', startOfDay)
        .where('dcp2.daily_at', '<=', endOfDay)
        .select([
            'so.value_option as muscle_group',
            'dcp.weight',
        ])
        .groupBy('so.value_option')
        .execute();

        const volumeDistributionMapped = volumeDistribution.reduce((acc: any, item: any) => {
            // label and calc percent on weight of group {label, percent}
            const percent = item.weight / volumeDistribution.reduce((acc: any, item: any) => acc + item.weight, 0) * 100;
            acc.push({
                label: item.muscle_group,
                weight: item.weight,
                percent: Math.round(percent),
            })
            return acc;
        }, [])

        




        return {
            status: 'success',
            data: volumeDistributionMapped,
        }
      }

      async getProgressAttendance(userId: number) {
        const today = dayjs().startOf('day').toDate();
    
        // 1. Calcular assiduidade da semana
        const startOfWeek = dayjs().startOf('week').toDate();
        const activeDaysWeek = await db
          .selectFrom('daily_coach_protocol')
          .where('user_id', '=', userId)
          .where('workout_time', '>=', startOfWeek)
          .where('workout_time', '<=', today)
          .select((eb) => [sql<string>`DATE(workout_time)`.as('day')])
          .execute();
        const totalDaysWeek = dayjs(today).diff(startOfWeek, 'day') + 1;
        const weekAttendance = totalDaysWeek > 0 ? (activeDaysWeek.length / totalDaysWeek) * 100 : 0;
    
        // 2. Calcular assiduidade do mês
        const startOfMonth = dayjs().startOf('month').toDate();
        const activeDaysMonth = await db
          .selectFrom('daily_coach_protocol')
          .where('user_id', '=', userId)
          .where('workout_time', '>=', startOfMonth)
          .where('workout_time', '<=', today)
          .select((eb) => [sql<string>`DATE(workout_time)`.as('day')])
          .execute();
        const totalDaysMonth = dayjs(today).diff(startOfMonth, 'day') + 1;
        const monthAttendance = totalDaysMonth > 0 ? (activeDaysMonth.length / totalDaysMonth) * 100 : 0;
    
        // 3. Calcular streak atual
        const allActiveDays = await db
          .selectFrom('daily_coach_protocol')
          .where('user_id', '=', userId)
          .select((eb) => [sql<string>`DATE(workout_time)`.as('day')])
          .orderBy('workout_time', 'desc')
          .execute();
        const activeDaysSet = new Set(allActiveDays.map((d) => d.day));
        let currentStreak = 0;
        let currentDate = today;
        while (activeDaysSet.has(dayjs(currentDate).format('YYYY-MM-DD'))) {
          currentStreak++;
          currentDate = dayjs(currentDate).subtract(1, 'day').toDate();
        }
    
        // 4. Calcular recorde de streak
        let maxStreak = 0;
        let tempStreak = 0;
        const sortedActiveDays = allActiveDays.map((d) => dayjs(d.day).toDate()).sort((a, b) => a.getTime() - b.getTime());
        for (let i = 0; i < sortedActiveDays.length; i++) {
          tempStreak = 1;
          let j = i + 1;
          while (j < sortedActiveDays.length && dayjs(sortedActiveDays[j]).diff(sortedActiveDays[j - 1], 'day') === 1) {
            tempStreak++;
            j++;
          }
          maxStreak = Math.max(maxStreak, tempStreak);
          i = j - 1; // Pular dias já contados
        }
    
        // 5. Retornar os dados
        return {
          status: 'success',
          data: {
            week: Math.round(weekAttendance),
            month: Math.round(monthAttendance),
            streak: currentStreak,
            record_streak: maxStreak,
          },
        };
      }

      // AI
      async getActiveMacroUserInfo(userId: number) {
        const user = await db
        .selectFrom('nutritionist_protocols as p')
        .select([
          'p.goal_calories as calories',
          'p.goal_protein as protein',
          'p.goal_carbs as carbs',
          'p.goal_fat as fat',
        ])
        .where('p.client_id', '=', userId)
        .where('p.ended_at', 'is', null)
        .executeTakeFirst();

        const text = `
          Calorias: ${user?.calories} kcal
          Proteína: ${user?.protein} g
          Carboidratos: ${user?.carbs} g
          Gorduras: ${user?.fat} g
        `;

        return text;
      }     


      async getPromptTextAi(text: string, userId: number) {
        // Prompt otimizado e mais conciso
        return `Gere 3 recomendações de alimentos baseado em: ${text}

Retorne JSON válido:
{ "recommendations": [
  [{"name": "alimento", "quantity": 100, "unit": "g", "calories": 150, "protein": 20, "carbs": 10, "fat": 5, "fiber": 2}]
]}

Regras:
1. Alimentos brasileiros típicos
2. Valores nutricionais precisos
3. Quantidades realistas`;
      }

      async getMealData(meal_id: number) {
        const mealData = await db
        .selectFrom('nutritionist_protocols_meals as m')
        .leftJoin('nutritionist_protocols_meals_foods as mf', 'mf.meal_id', 'm.id')
        .where('m.id', '=', meal_id)
        .select([
            'm.id as meal_id',
            'm.name as meal_name',
            'mf.name as food_name',
            'mf.quantity as food_quantity',
            'mf.unit as food_unit',
            'mf.calories as food_calories',
            'mf.protein as food_protein',
            'mf.carbs as food_carbs',
            'mf.fat as food_fat',
            'mf.fiber as food_fiber',
        ])
        .execute();
        
        const mealText = mealData.map((item: any) => {
            return `
            Refeição: ${item.meal_name}
            Alimento: ${item.food_name}
            Quantidade: ${item.food_quantity} ${item.food_unit}
            Calorias: ${item.food_calories}
            Proteína: ${item.food_protein}
            Carboidratos: ${item.food_carbs}
            Gorduras: ${item.food_fat}
            Fibra: ${item.food_fiber}
            `;
        }).join('\n');

        return mealText;
      }

      async getPromptTextReplacementAi(mealData: any) {
        const { meal_id } = mealData;

        const mealText = await this.getMealData(meal_id);

        return `
          Com base na refeição abaixo, gere 3 recomendações de refeições com alimentos alternativos, mantendo a mesma quantidade e valores nutricionais.

          # REFEIÇÃO:
          ${mealText}

          Retorne APENAS o JSON válido SEM comentários ou markdown.

          OBRIGATÓRIO: O JSON DEVE SEGUIR O FORMATO EXATO DE ESTRUTURA ABAIXO, NÃO ACRESCENTE OU DIMINUA OS CAMPOS

          # MODELO JSON OBRIGATÓRIO:
          { recommendations: [
          [
            {
              "name": "[Nome do alimento]",
              "quantity": /* Quantidade numérica SEM UNIDADE */,
              "unit": "g/ml/...",
              "calories": /* Valor exato calculado */,
              "protein": /* g */,
              "carbs": /* g */,
              "fat": /* g */,
              "fiber": /* g */
            },...
          ],...
          ]}

          # REGRAS ESSENCIAIS:
          1. Números SEM ASPAS ou unidades nos valores (ex: "quantity": 150)
          2. Precisão nutricional: ±5% de margem de erro
          3. Usar alimentos típicos brasileiros          
        `;
      }

      async getPromptTextRecognitionAi(text: string, userId: number) {
        return `Identifique alimentos em: ${text}

Retorne JSON:
{ "recommendations": [
  [{"name": "alimento", "quantity": 100, "unit": "g", "calories": 150, "protein": 20, "carbs": 10, "fat": 5, "fiber": 2}]
]}`;
      }

      async getPromptImage() {
        return `
          Com base na imagem, identifique os alimentos e suas quantidades.

          Retorne APENAS o JSON válido SEM comentários ou markdown.

          OBRIGATÓRIO: O JSON DEVE SEGUIR O FORMATO EXATO DE ESTRUTURA ABAIXO, NÃO ACRESCENTE OU DIMINUA OS CAMPOS

          # MODELO JSON OBRIGATÓRIO:
          { recommendations: [
          [
            {
              "name": "[Nome do alimento]",
              "quantity": /* Quantidade numérica SEM UNIDADE */,
              "unit": "g/ml/...",
              "calories": /* Valor exato calculado */,
              "protein": /* g */,
              "carbs": /* g */,
              "fat": /* g */,
              "fiber": /* g */
            },...
          ],...
          ]}

          # REGRAS ESSENCIAIS:
          1. Números SEM ASPAS ou unidades nos valores (ex: "quantity": 150)
          2. Precisão nutricional: ±5% de margem de erro
          3. Usar alimentos típicos brasileiros     
        `;
      }

      async getPromptAudio() {
        return `
          Com base na transcrição do áudio, identifique os alimentos e suas quantidades.

          Retorne APENAS o JSON válido SEM comentários ou markdown.

          OBRIGATÓRIO: O JSON DEVE SEGUIR O FORMATO EXATO DE ESTRUTURA ABAIXO, NÃO ACRESCENTE OU DIMINUA OS CAMPOS

          # MODELO JSON OBRIGATÓRIO:
          { recommendations: [
          [
            {
              "name": "[Nome do alimento]",
              "quantity": /* Quantidade numérica SEM UNIDADE */,
              "unit": "g/ml/...",
              "calories": /* Valor exato calculado */,
              "protein": /* g */,
              "carbs": /* g */,
              "fat": /* g */,
              "fiber": /* g */
            },...
          ],...
          ]}

          # REGRAS ESSENCIAIS:
          1. Números SEM ASPAS ou unidades nos valores (ex: "quantity": 150)
          2. Precisão nutricional: ±5% de margem de erro
          3. Usar alimentos típicos brasileiros     
        `;
      }


      async processTextAi(text: string, mealData: any, userId: number) {
        if (!text || typeof text !== 'string' || text.trim() === '') {
          if(mealData.meal_type !== 'replacement') {
            throw new Error('Texto está indefinido ou vazio');
          }
        }
        
        let prompt: any;
        switch(mealData.meal_type) {
          case 'new':
            prompt = await this.getPromptTextAi(text, userId);
            break;
          case 'recognition':
            prompt = await this.getPromptTextRecognitionAi(text, userId);
            break;
          case 'replacement':
            prompt = await this.getPromptTextReplacementAi(mealData);
            break;
          default:
            throw new Error('Tipo de refeição inválido');
        }

        const response: any = await this.openai.chat.completions.create({
          model: "gpt-4o-mini", // Modelo otimizado para velocidade
          messages: [
            {
              role: "system",
              content: "Nutricionista expert. Retorne apenas JSON válido."
            },
            { role: "user", content: prompt }
          ],
          temperature: 0.1, // Reduzido para máxima consistência e velocidade
          max_tokens: 1024, // Drasticamente reduzido para otimizar performance
          response_format: { type: "json_object" }
        });
    
        
        const aiResponseText: any = response.choices[0].message.content;

        return aiResponseText;
      }

      async processImageAi(base64: string) {
        // Validação de entrada
        if (!base64 || typeof base64 !== 'string' || base64.trim() === '') {
          throw new Error('Base64 da imagem está indefinido ou vazio');
        }

        // gpt-4o , gpt-4o-mini or gpt-4-turbo


      const completion = await this.openai.chat.completions.create({
          model: "gpt-4o-mini",
          messages: [
            { role: "system", content: "Você é um assistente que analisa imagens e fornece respostas detalhadas." },
            {
              role: "user",
              content: [
                  {
                      type: "image_url",
                      image_url: {
                          url: base64,
                      },
                  },
                  {
                      type: "text",
                      text: await this.getPromptImage(),
                  },
              ],
          }],
      });

      return completion.choices[0].message.content;


      /*
      // Validação de formato Base64 e tipo de mídia
      const base64Match = base64.match(/^data:(image\/\w+);base64,(.+)$/);
      if (!base64Match || base64Match.length < 3) {
          throw new Error('Formato Base64 inválido ou tipo de mídia não suportado');
      }

      const [, mimeType, dataStr] = base64Match;
      const validImageTypes = ['image/png', 'image/jpeg', 'image/gif', 'image/webp'];

      if (!validImageTypes.includes(mimeType)) {
          throw new Error(`Tipo de imagem não suportado: ${mimeType}`);
      }

      // Conversão e validação dos dados Base64
      let buffer;
      try {
          buffer = Buffer.from(dataStr, 'base64');
      } catch (error) {
          throw new Error('Dados Base64 inválidos');
      }

      // Criar o diretório se não existir
      const uploadDir = path.join('__temp');
      await fs.promises.mkdir(uploadDir, { recursive: true });
    
      // Gerar nome aleatório para o arquivo
      const fileExt = mimeType.split('/')[1];
      const randomName = `image_${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = path.join(uploadDir, randomName);

      try {
          await fsPromises.writeFile(filePath, buffer);
      } catch (error) {
          throw new Error(`Erro ao salvar arquivo de imagem: ${error.message}`);
      }

      const fileUrl = process.env.STATIC_URL + '/temp/' + randomName;
      // const fileUrl = 'https://static.mysnapfit.com.br/temp/image_1743474479214-xdwhn1klom.jpeg';

        const response: any = await this.openai.chat.completions.create({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: 'Você é um assistente que analisa imagens e fornece respostas detalhadas.',
            },
            {
              role: 'user',
              content: [
                {
                  type: 'image_url',
                  image_url: { url: fileUrl },
                },
                {
                  type: 'text',
                  text: await this.getPromptImage(),
                },
              ],
            },
          ],
        });

        // Remover o arquivo temporário após o processamento
        fs.unlinkSync(filePath);

        const aiResponseText: any = response.choices[0].message.content;

        console.log(aiResponseText);

        return aiResponseText;
        */
      }

      async processAudioAi(base64: string, userId: number) {
        try {
          // Validar entrada
          if (!base64 || typeof base64 !== 'string' || base64.trim() === '') {
            throw new Error('Base64 do áudio está indefinido ou vazio');
          }
      
          // Validar formato Base64
          const base64Match = base64.match(/^data:(audio\/\w+);base64,(.+)$/);
          if (!base64Match || base64Match.length < 3) {
            throw new Error('Formato Base64 inválido ou tipo de mídia não suportado');
          }
      
          const [, mimeType, dataStr] = base64Match;
          const openAiAudiosAllowedTypes = ['flac', 'm4a', 'mp3', 'mp4', 'mpeg', 'mpga', 'oga', 'ogg', 'wav', 'webm'];
      
          const fileExt = mimeType.split('/')[1];
      
          if (!openAiAudiosAllowedTypes.includes(fileExt)) {
            throw new Error(`Tipo de áudio não suportado: ${mimeType}. Formatos aceitos: ${openAiAudiosAllowedTypes.join(', ')}`);
          }
      
          // Converter Base64 para Buffer
          let buffer: Buffer;
          try {
            buffer = Buffer.from(dataStr, 'base64');
          } catch (error) {
            throw new Error('Dados Base64 inválidos');
          }
      
          // Criar diretório temporário
          const uploadDir = path.join('__temp');
          await fsExtra.ensureDir(uploadDir);
      
          // Gerar nome aleatório para o arquivo
          const randomName = `audio_${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
          const filePath = path.join(uploadDir, randomName);
      
          // Salvar arquivo
          await fs.promises.writeFile(filePath, buffer);

          // Testar se o arquivo foi salvo corretamente
          if (!fs.existsSync(filePath) || fs.statSync(filePath).size === 0) {
            throw new Error('Arquivo salvo está vazio ou inválido.');
          }

          // console.log(`Arquivo salvo em: ${filePath}`);

      
          // Enviar para a API da OpenAI
          /*
          const response = await this.openai.audio.transcriptions.create({
            file: fs.createReadStream(filePath),
            model: 'gpt-4o-transcribe',            
          });
          */
          
          const fileBlob = new Blob([buffer], { type: mimeType });
          const file = new File([fileBlob], randomName, { type: mimeType });   
          
          
          const form = new FormData();
          form.append('file', file);
          form.append('model', 'whisper-1');
          
          const config = {
              headers: {
                  'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
                  'Content-Type': 'multipart/form-data',
                  // ...axios.defaults.headers.common,
              },
          };
          const response = await axios.post('https://api.openai.com/v1/audio/transcriptions', form, config);

      
          // Remover arquivo temporário após o processamento
          await fs.promises.unlink(filePath);
      
          const transcription = response.data.text;
      
          const mealData = {
            meal_type: 'recognition',
          };
      
          return await this.processTextAi(transcription, mealData, userId);
        } catch (error) {
          console.error('Erro ao processar áudio:', error);
          return { error: 'Falha ao processar o áudio' };
        }

      }

      async processAudioAiOld(base64: string, userId: number) {
        try {
          // Validate input
        if (!base64 || typeof base64 !== 'string' || base64.trim() === '') {
          throw new Error('Base64 do áudio está indefinido ou vazio');
      }



      // Split and validate Base64 format
      const base64Match = base64.match(/^data:(audio\/\w+);base64,(.+)$/);
      if (!base64Match || base64Match.length < 3) {
          throw new Error('Formato Base64 inválido ou tipo de mídia não suportado');
      }


      const [, mimeType, dataStr] = base64Match;
      const validAudioTypes = ['audio/mp3', 'audio/mpeg', 'audio/webm', 'audio/wav', 'audio/ogg'];
      

      if (!validAudioTypes.includes(mimeType)) {
          throw new Error(`Tipo de áudio não suportado: ${mimeType}`);
      }

      // Convert and validate Base64 data
      let buffer: Buffer;

      try {
          buffer = Buffer.from(dataStr, 'base64');
      } catch (error) {
          throw new Error('Dados Base64 inválidos');
      }



      // Criar o diretório se não existir
      const uploadDir = path.join('__temp');
      await fs.promises.mkdir(uploadDir, { recursive: true });
    
      // Gerar nome aleatório para o arquivo
      const fileExt = mimeType.split('/')[1];
      const randomName = `audio_${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = path.join(uploadDir, randomName);

      let needsConversion = false;

      // Write file asynchronously

      try {
          await fsPromises.writeFile(filePath, buffer);
      } catch (error) {
          throw new Error(`Erro ao salvar arquivo de áudio: ${error.message}`);
      }

          const openAiAudiosAllowedTypes = ['flac', 'm4a', 'mp3', 'mp4', 'mpeg', 'mpga', 'oga', 'ogg', 'wav', 'webm'];
          let randomName2 = '';
          let filePath2 = '';

          if (!openAiAudiosAllowedTypes.includes(fileExt)) {
            // convert to webm
            randomName2 = `audio_${Date.now()}-${Math.random().toString(36).substring(2)}.webm`;
            const convertedFilePath = path.join(uploadDir, randomName2);
            await sharp(buffer).toFile(convertedFilePath);
            filePath2 = convertedFilePath;
          }

          const getFileUrl = (!needsConversion) ? filePath : filePath2;

          // const fileUrl = process.env.STATIC_URL + '/temp/' + randomName;
          // const fileUrl = 'https://static.mysnapfit.com.br/temp/audio_1743464604190-vhjxfkuazep.webm';
      
          // Enviar para a OpenAI Whisper API
          const response = await this.openai.audio.transcriptions.create({
            file: fs.createReadStream(getFileUrl),
            model: 'gpt-4o-transcribe',
          });
      
          // Remover o arquivo temporário após o processamento
          fs.unlinkSync(filePath);
          if (needsConversion) {
            fs.unlinkSync(filePath2);
          }
      
          const transcription = response.text;

          const mealData = {
            meal_type: 'recognition',
          }
          
          return await this.processTextAi(transcription, mealData, userId);
        } catch (error) {
          console.error('Erro ao processar áudio:', error);
          return { error: 'Falha ao processar o áudio' };
        }
      }


      async getFoodsSuggestions(userId: number, query: any, body: any) {
        console.log('🔄 getFoodsSuggestions chamado:', { userId, query, body });

        const mealType = query.meal_type || 'new'; // new, recognition, replacement
        const mealId = query.meal_id || null;

        console.log('🔄 Parâmetros processados:', { mealType, mealId });

        const mealData = {
          meal_type: mealType,
          meal_id: mealId,
        }

        const getType = body.type;
        const content = body.content;
        let response: any;

        console.log('🔄 Processando tipo:', getType);

        if (getType === 'text') {
          response = await this.processTextAi(content, mealData, userId);
        } else if (getType === 'image') {
          response = await this.processImageAi(content);
        } else if (getType === 'audio') {
          response = await this.processAudioAi(content, userId);
        } else {
          throw new Error('Tipo de mídia não suportado');
        }

        console.log('🔄 Resposta da IA:', response);

        // const textJson = JSON.parse(response);
        const textJson = JSON.parse(this.extractJsonFromString(response));

        console.log('🔄 JSON extraído:', textJson);

        let recommendations = textJson?.recommendations || [];

        console.log('🔄 Recommendations antes do processamento:', recommendations);

        if(Array.isArray(recommendations) && recommendations?.length > 0) {
          recommendations = recommendations.map((item: any) => {
            // Verificar se item é um array antes de chamar map
            if (!Array.isArray(item)) {
              console.warn('⚠️ Item não é um array:', item);
              return item;
            }
            return item.map((item2: any) => {
              return {
                ...item2,
                quantity: parseFloat(Number(item2.quantity).toFixed(2)),
                calories: parseFloat(Number(item2.calories).toFixed(2)),
                protein: parseFloat(Number(item2.protein).toFixed(2)),
                carbs: parseFloat(Number(item2.carbs).toFixed(2)),
                fat: parseFloat(Number(item2.fat).toFixed(2)),
                fiber: parseFloat(Number(item2.fiber).toFixed(2)),
              }
            })
          })
        }

        console.log('🔄 Recommendations após processamento:', recommendations);

        const result = {
          status: 'success',
          data: recommendations || [],
        };

        console.log('🔄 Resultado final:', result);

        return result;
      }

      // User Options
      async getUserOptions(userId: number) {
        let affiliate: any = false;
        let admin: any = false;

        // check if is affiliate
        const isAffiliate = await db.selectFrom('affiliates')
        .where('user_id', '=', userId)
        .select('status')
        .executeTakeFirst();

        if (isAffiliate) {
          affiliate = isAffiliate.status;

          if(affiliate === 'inactive') {
            affiliate = false;
          }
        }

        // check if is admin
        const isAdmin = await db.selectFrom('users_roles')
        .where('user_id', '=', userId)
        .where('role_id', '=', 1)
        .select('user_id')
        .executeTakeFirst();

        if (isAdmin) {
          admin = true;
        }

        return {
          status: 'success',
          data: {
            affiliate,
            admin,
          },
        };        
      }


      // Affiliates
      async createAffiliate(body: any, userId: number) {
        const { aff_ref } = body;

        // check if user is already an affiliate
        const isAffiliate = await db.selectFrom('affiliates')
        .where('user_id', '=', userId)
        .select('id')
        .executeTakeFirst();

        if (isAffiliate) {
          return {
            status: 'success',
            data: [],
          };
        }
        
        let aff_id = null;
        if (aff_ref) {
          const affData: any = await db.selectFrom('affiliate_links')
          .where('invite', '=', aff_ref.toLowerCase().trim())
          .where('link_type', '=', 'signup_affiliate')
          .select(['user_id'])
          .executeTakeFirst();
    
          if (affData) {
            aff_id = affData.user_id;
          }
        }

        const affiliateData: any = {
          status: 'pending',
          user_id: userId,
          ref_user_id: aff_id,
          invite: aff_ref ? aff_ref.toLowerCase().trim() : null,
          is_master: null,
          created_at: new Date(),
          updated_at: new Date(),
        }

        const newAffiliate = await db.insertInto('affiliates')
        .values(affiliateData)
        .executeTakeFirstOrThrow();

        return {
          status: 'success',
          data: [],
        }
      }

      // Plans
      // Checkout
      async subscribePlan(userId: number, planId: number) {
        const plan = await db.selectFrom('plans_payments_providers')
        .where('id', '=', planId)
        .select(['payment_provider_external_id'])
        .executeTakeFirstOrThrow();

        const stripePlanId = plan.payment_provider_external_id;

        // user data
        const user = await db.selectFrom('users')
        .where('id', '=', userId)
        .select(['email', 'stripeId'])
        .executeTakeFirstOrThrow();

        // create or retreive customer by stripeId
        let customerId = user.stripeId;

        if (!customerId) {
          const customer = await this.stripe.customers.create(
            {
              // @ts-ignore
              email: user.email,
              metadata: {
                userId: userId.toString(),
              },
            }
          );

          customerId = customer.id;

          await db.updateTable('users')
          .set({
            stripeId: customerId,
          })
          .where('id', '=', userId)
          .execute();
        }

        // @ts-ignore
        const session = await this.stripe.checkout.sessions.create({
          payment_method_types: ['card'],
          mode: 'subscription',
          client_reference_id: userId.toString(),
          customer: customerId,
          success_url: process.env.STRIPE_SUCCESS_URL,
          cancel_url: process.env.STRIPE_CANCEL_URL,
          line_items: [
            {
              price: stripePlanId,
              quantity: 1,
            },
          ],
        });

        return {
          status: 'success',
          data: {
            url: session.url,
          },
        };
      }

      // Plans
      async getAllPlans(userId: number) {
        // check if user has active subscription
        const hasActiveSubscription = await db.selectFrom('users_subscriptions')
        .where('user_id', '=', userId)
        .where('status', '=', 'active')
        .select('plan_id')
        .executeTakeFirst();

        // Get plans from web
        const plans = await db.selectFrom('plans as p1')
        .leftJoin('plans_payments_providers as p2', 'p1.id', 'p2.plan_id')
        .leftJoin('payment_providers as p3', 'p2.payment_provider_id', 'p3.id')
        .select([
          'p1.id', 'p1.is_active', 'p1.name', 'p1.description',
          sql`COALESCE(p2.price, p1.price)`.as('price'),
          sql`COALESCE(p2.currency, p1.currency)`.as('currency'),
          sql`COALESCE(p2.snaptokens, p1.snaptokens)`.as('snaptokens'),
          'p2.id as config_id',
        ])
        .where('p2.platform', '=', 'web')
        .where('p1.deleted_at', 'is', null)
        .where('p2.deleted_at', 'is', null)
        .execute();
        
        // return plans and selected if has active subscription
        return {
          status: 'success',
          data: plans.map((plan: any) => {
            return {
              ...plan,
              selected: hasActiveSubscription ? plan.id === hasActiveSubscription.plan_id : false,
            };
          }),
        };
      }

    // Subscription methods
    async getMySubscriptions(userId: number) {
        const subscriptions = await db
            .selectFrom('users_subscriptions as us')
            .leftJoin('plans as p', 'p.id', 'us.plan_id')
            .leftJoin('payment_providers as pp', 'pp.id', 'us.plan_payment_provider_id')
            .where('us.user_id', '=', userId)
            .select([
                'us.id',
                'us.status',
                'us.start_date',
                'us.end_date',
                'us.next_billing_date',
                'us.cancel_at_period_end',
                'us.is_trial',
                'us.trial_start_date',
                'us.trial_end_date',
                'us.price',
                'us.currency',
                'p.name as plan_name',
                'p.description as plan_description',
                'pp.name as payment_provider',
            ])
            .orderBy('us.created_at', 'desc')
            .execute();

        return {
            status: 'success',
            data: subscriptions,
        };
    }

    async cancelSubscription(subscriptionId: number, userId: number) {
        // Verificar se a assinatura pertence ao usuário
        const subscription = await db
            .selectFrom('users_subscriptions')
            .where('id', '=', subscriptionId)
            .where('user_id', '=', userId)
            .select(['id', 'status', 'payment_provider_external_id'])
            .executeTakeFirst();

        if (!subscription) {
            throw new HttpException({
                status: 404,
                message: ['Assinatura não encontrada.'],
            }, 404);
        }

        if (subscription.status === 'canceled') {
            throw new HttpException({
                status: 400,
                message: ['Assinatura já está cancelada.'],
            }, 400);
        }

        // Atualizar status para cancelamento no final do período
        await db
            .updateTable('users_subscriptions')
            .set({
                cancel_at_period_end: true,
                updated_at: new Date(),
            })
            .where('id', '=', subscriptionId)
            .execute();

        return {
            status: 'success',
            message: 'Assinatura será cancelada no final do período atual.',
        };
    }

    async cancelSubscriptionImmediately(subscriptionId: number, userId: number) {
        // Verificar se a assinatura pertence ao usuário
        const subscription = await db
            .selectFrom('users_subscriptions')
            .where('id', '=', subscriptionId)
            .where('user_id', '=', userId)
            .select(['id', 'status'])
            .executeTakeFirst();

        if (!subscription) {
            throw new HttpException({
                status: 404,
                message: ['Assinatura não encontrada.'],
            }, 404);
        }

        if (subscription.status === 'canceled') {
            throw new HttpException({
                status: 400,
                message: ['Assinatura já está cancelada.'],
            }, 400);
        }

        // Cancelar imediatamente
        await db
            .updateTable('users_subscriptions')
            .set({
                status: 'canceled',
                end_date: new Date(),
                updated_at: new Date(),
            })
            .where('id', '=', subscriptionId)
            .execute();

        return {
            status: 'success',
            message: 'Assinatura cancelada imediatamente.',
        };
    }

    // Transaction methods
    async getMyTransactions(userId: number) {
        const transactions = await db
            .selectFrom('transactions as t')
            .leftJoin('payment_providers as pp', 'pp.id', 't.payment_provider_id')
            .where('t.user_id', '=', userId)
            .select([
                't.id',
                't.provider_transaction_id',
                't.amount',
                't.currency',
                't.status',
                't.source_type',
                't.source_id',
                't.created_at',
                'pp.name as payment_provider',
            ])
            .orderBy('t.created_at', 'desc')
            .execute();

        return {
            status: 'success',
            data: transactions,
        };
    }

    async getMyTransactionDetails(transactionId: number, userId: number) {
        const transaction = await db
            .selectFrom('transactions as t')
            .leftJoin('payment_providers as pp', 'pp.id', 't.payment_provider_id')
            .leftJoin('users_subscriptions as us', 'us.id', 't.source_id')
            .leftJoin('plans as p', 'p.id', 'us.plan_id')
            .where('t.id', '=', transactionId)
            .where('t.user_id', '=', userId)
            .select([
                't.id',
                't.provider_transaction_id',
                't.amount',
                't.currency',
                't.status',
                't.source_type',
                't.source_id',
                't.created_at',
                'pp.name as payment_provider',
                'p.name as plan_name',
                'p.description as plan_description',
                'us.start_date as subscription_start_date',
                'us.end_date as subscription_end_date',
            ])
            .executeTakeFirst();

        if (!transaction) {
            throw new HttpException({
                status: 404,
                message: ['Transação não encontrada.'],
            }, 404);
        }

        return {
            status: 'success',
            data: transaction,
        };
    }

    // Endpoint geral de progresso (compatibilidade com frontend)
    async getProgress(userId: number, query: any) {
        try {
            // Buscar dados de avaliações recentes da tabela 'evaluations'
            const evaluations = await db
                .selectFrom('evaluations')
                .where('user_id', '=', userId)
                .orderBy('created_at', 'desc')
                .limit(10)
                .select([
                    'id',
                    'weight',
                    'bf as body_fat_percentage',
                    'created_at'
                ])
                .execute();

            // Buscar fotos das avaliações
            const evaluationIds = evaluations.map(e => e.id);
            let photos: any[] = [];
            if (evaluationIds.length > 0) {
                photos = await db
                    .selectFrom('evaluations_photos')
                    .where('evaluation_id', 'in', evaluationIds)
                    .select([
                        'evaluation_id',
                        'media_position',
                        'media_url'
                    ])
                    .execute();
            }

            // Transformar dados para formato esperado pelo frontend
            const progressData = evaluations.map(evaluation => {
                const evalPhotos = photos.filter(p => p.evaluation_id === evaluation.id);
                const photoMap: any = {};
                evalPhotos.forEach(photo => {
                    photoMap[photo.media_position] = photo.media_url;
                });

                return {
                    weight: evaluation.weight,
                    bodyFat: evaluation.body_fat_percentage,
                    date: evaluation.created_at,
                    photos: photoMap
                };
            });

            return {
                status: 'success',
                data: progressData
            };
        } catch (error) {
            console.error('Error getting progress data:', error);
            return {
                status: 'success',
                data: []
            };
        }
    }

    // Análise nutricional semanal
    async getAnalyticsNutritionWeekly(userId: number, query: any) {
        try {
            // Buscar dados nutricionais dos últimos 7 dias
            const startOfDay = dayjs().subtract(7, 'days').startOf('day').toDate();
            const endOfDay = dayjs().endOf('day').toDate();

            // Buscar dados das refeições diárias
            const mealsData = await db
                .selectFrom('daily_meals')
                .where('user_id', '=', userId)
                .where('daily_at', '>=', startOfDay)
                .where('daily_at', '<=', endOfDay)
                .select([
                    'daily_at as date',
                    'calories',
                    'protein',
                    'carbs',
                    'fat'
                ])
                .orderBy('daily_at', 'asc')
                .execute();

            return {
                status: 'success',
                data: mealsData
            };
        } catch (error) {
            console.error('Error getting weekly nutrition analytics:', error);
            return {
                status: 'success',
                data: []
            };
        }
    }

    // Análise de treinos semanal
    async getAnalyticsWorkoutsWeekly(userId: number, query: any) {
        try {
            // Buscar dados de treinos dos últimos 7 dias
            const startOfDay = dayjs().subtract(7, 'days').startOf('day').toDate();
            const endOfDay = dayjs().endOf('day').toDate();

            const workoutData = await db
                .selectFrom('daily_workouts_activities')
                .where('user_id', '=', userId)
                .where('daily_at', '>=', startOfDay)
                .where('daily_at', '<=', endOfDay)
                .select([
                    'daily_at as date',
                    'calories as caloriesBurned'
                ])
                .orderBy('daily_at', 'asc')
                .execute();

            return {
                status: 'success',
                data: workoutData
            };
        } catch (error) {
            console.error('Error getting weekly workout analytics:', error);
            return {
                status: 'success',
                data: []
            };
        }
    }

    // Evolução de força
    async getStrengthEvolution(userId: number, query: any) {
        try {
            // Por enquanto, retornar dados vazios até implementarmos a consulta correta
            // TODO: Implementar consulta real quando a estrutura do banco estiver clara
            return {
                status: 'success',
                data: {}
            };
        } catch (error) {
            console.error('Error getting strength evolution:', error);
            return {
                status: 'success',
                data: {}
            };
        }
    }

    // Aderência semanal
    async getProgressAdherence(userId: number, query: any) {
        try {
            const { week } = query;

            // If week is provided, calculate start and end dates for that week
            let startOfDay: Date;
            let endOfDay: Date;

            if (week) {
                const weekDate = dayjs(week);
                startOfDay = weekDate.startOf('week').toDate();
                endOfDay = weekDate.endOf('week').toDate();
            } else {
                // Default to current week
                startOfDay = dayjs().startOf('week').toDate();
                endOfDay = dayjs().endOf('week').toDate();
            }

            // For string date comparisons (goal_date is string type)
            const startDateString = dayjs(startOfDay).format('YYYY-MM-DD');
            const endDateString = dayjs(endOfDay).format('YYYY-MM-DD');

            // Get diet adherence data from daily_meals_goal table
            const dietGoalData = await db
                .selectFrom('daily_meals_goal')
                .where('user_id', '=', userId)
                .where('goal_date', '>=', startDateString)
                .where('goal_date', '<=', endDateString)
                .select([
                    'goal_date as date',
                    'meals_completed',
                    'meals'
                ])
                .execute();

            // Get workout adherence data - count activities per day
            const workoutData = await db
                .selectFrom('daily_workouts_activities')
                .where('user_id', '=', userId)
                .where('daily_at', '>=', startOfDay)
                .where('daily_at', '<=', endOfDay)
                .select([
                    'daily_at as date',
                    db.fn.count('id').as('activities_count')
                ])
                .groupBy('daily_at')
                .execute();

            // Get coach protocol data for workout targets
            const coachProtocolData = await db
                .selectFrom('daily_coach_protocol')
                .where('user_id', '=', userId)
                .where('daily_at', '>=', startOfDay)
                .where('daily_at', '<=', endOfDay)
                .select([
                    'daily_at as date',
                    db.fn.count('id').as('planned_workouts')
                ])
                .groupBy('daily_at')
                .execute();

            // Calculate adherence percentages
            const totalDietMeals = dietGoalData.reduce((sum, day) => sum + (day.meals || 0), 0);
            const completedDietMeals = dietGoalData.reduce((sum, day) => sum + (day.meals_completed || 0), 0);
            const dietPercentage = totalDietMeals > 0 ? (completedDietMeals / totalDietMeals) * 100 : 0;

            const totalWorkoutActivities = workoutData.reduce((sum, day) => sum + Number(day.activities_count || 0), 0);
            const totalPlannedWorkouts = coachProtocolData.reduce((sum, day) => sum + Number(day.planned_workouts || 0), 0);
            const workoutPercentage = totalPlannedWorkouts > 0 ? (totalWorkoutActivities / totalPlannedWorkouts) * 100 :
                                    totalWorkoutActivities > 0 ? 100 : 0; // If no planned workouts but activities exist, 100%

            // Get water data from daily_water table
            const waterData = await db
                .selectFrom('daily_water')
                .where('user_id', '=', userId)
                .where('daily_at', '>=', startOfDay)
                .where('daily_at', '<=', endOfDay)
                .select([
                    'daily_at as date',
                    'consumed as glasses_consumed',
                    'consumed as glasses_goal'
                ])
                .execute();

            const totalWaterGlasses = waterData.reduce((sum, day) => sum + (day.glasses_goal || 8), 0);
            const completedWaterGlasses = waterData.reduce((sum, day) => sum + (day.glasses_consumed || 0), 0);
            const waterPercentage = totalWaterGlasses > 0 ? (completedWaterGlasses / totalWaterGlasses) * 100 : 0;

            const overallPercentage = (dietPercentage + workoutPercentage + waterPercentage) / 3;

            return {
                status: 'success',
                data: {
                    diet: {
                        completed: completedDietMeals,
                        total: totalDietMeals,
                        percentage: Math.round(dietPercentage * 10) / 10
                    },
                    workout: {
                        completed: totalWorkoutActivities,
                        total: totalPlannedWorkouts || totalWorkoutActivities, // Use planned workouts or actual activities as total
                        percentage: Math.round(workoutPercentage * 10) / 10
                    },
                    water: {
                        completed: completedWaterGlasses,
                        total: totalWaterGlasses,
                        percentage: Math.round(waterPercentage * 10) / 10
                    },
                    overall: {
                        percentage: Math.round(overallPercentage * 10) / 10
                    }
                }
            };
        } catch (error) {
            console.error('Error getting progress adherence:', error);
            return {
                status: 'success',
                data: {
                    diet: { completed: 0, total: 0, percentage: 0 },
                    workout: { completed: 0, total: 0, percentage: 0 },
                    water: { completed: 0, total: 0, percentage: 0 },
                    overall: { percentage: 0 }
                }
            };
        }
    }

    // WORKOUT SESSION METHODS

    /**
     * Start a workout session from an active protocol
     */
    async startWorkout(userId: number, body: any) {
        try {
            const { protocolId, templateId, exercises, date } = body;

            console.log('🏋️ startWorkout: Starting workout session for user:', userId);
            console.log('📋 Protocol ID:', protocolId);
            console.log('📅 Date:', date);

            // Get active protocol if protocolId not provided
            let workoutProtocolId = protocolId;
            if (!workoutProtocolId) {
                const activeProtocol = await this.getActiveProtocolsWorkouts(userId);
                if (activeProtocol.data.has_protocol) {
                    workoutProtocolId = activeProtocol.data.id;
                } else {
                    throw new Error('No active workout protocol found');
                }
            }

            // Ensure protocolId is a number
            workoutProtocolId = Number(workoutProtocolId);
            if (isNaN(workoutProtocolId)) {
                throw new Error('Invalid protocol ID');
            }

            // Verify protocol exists and belongs to user
            const protocol = await db
                .selectFrom('coach_protocols')
                .selectAll()
                .where('id', '=', workoutProtocolId)
                .where('client_id', '=', userId)
                .executeTakeFirst();

            if (!protocol) {
                throw new Error('Workout protocol not found');
            }

            // Get the first workout from the protocol if no specific workout selected
            const protocolWorkouts = await db
                .selectFrom('coach_protocols_workouts')
                .selectAll()
                .where('protocol_id', '=', workoutProtocolId)
                .orderBy('id', 'asc')
                .execute();

            if (protocolWorkouts.length === 0) {
                throw new Error('No workouts found in protocol');
            }

            // Use the first workout for now (can be enhanced to select specific workout)
            const selectedWorkout = protocolWorkouts[0];

            // Create workout session record
            const workoutSession = await db
                .insertInto('daily_coach_protocol')
                .values({
                    user_id: userId,
                    protocol_id: workoutProtocolId,
                    protocol_workout_id: selectedWorkout.id,
                    workout_time: new Date(), // Set to current time when workout starts
                    total_calories: 0, // Will be calculated when completed
                    total_weight: 0, // Will be calculated when completed
                    met: 6.0, // Default MET value for strength training
                    daily_at: new Date(date),
                    created_at: new Date(),
                    updated_at: new Date()
                })
                .executeTakeFirst();

            const sessionId = Number(workoutSession.insertId);

            console.log('✅ Workout session created with ID:', sessionId);

            return {
                status: 'success',
                data: {
                    sessionId,
                    protocolId: workoutProtocolId,
                    workoutId: selectedWorkout.id,
                    workoutName: selectedWorkout.name,
                    message: 'Workout session started successfully'
                }
            };

        } catch (error) {
            console.error('❌ Error starting workout:', error);
            throw new HttpException({
                status: 'error',
                message: [error.message || 'Failed to start workout session']
            }, 400);
        }
    }

    /**
     * Get workouts by date
     */
    async getWorkoutsByDate(userId: number, query: any) {
        try {
            const { date } = query;
            const targetDate = date ? new Date(date) : new Date();

            console.log('📅 getWorkoutsByDate: Getting workouts for date:', targetDate);

            const workouts = await db
                .selectFrom('daily_coach_protocol as dcp')
                .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')
                .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')
                .select([
                    'dcp.id as session_id',
                    'dcp.protocol_id',
                    'dcp.protocol_workout_id',
                    'cpw.name as workout_name',
                    'cp.name as protocol_name',
                    'dcp.workout_time',
                    'dcp.total_calories',
                    'dcp.total_weight',
                    'dcp.daily_at',
                    'dcp.created_at'
                ])
                .where('dcp.user_id', '=', userId)
                .where('dcp.daily_at', '>=', dayjs(targetDate).startOf('day').toDate())
                .where('dcp.daily_at', '<=', dayjs(targetDate).endOf('day').toDate())
                .orderBy('dcp.created_at', 'desc')
                .execute();

            return {
                status: 'success',
                data: workouts
            };

        } catch (error) {
            console.error('❌ Error getting workouts by date:', error);
            return {
                status: 'success',
                data: []
            };
        }
    }

    /**
     * Get workout history with pagination
     */
    async getWorkoutHistory(userId: number, query: any) {
        try {
            const { period = 'month', page = 1, limit = 10 } = query;
            const offset = (page - 1) * limit;

            console.log('📚 getWorkoutHistory: Getting workout history for period:', period);

            // Calculate date range based on period
            let startDate: Date;
            let endDate = new Date();

            switch (period) {
                case 'week':
                case '7d':
                    startDate = dayjs().subtract(7, 'days').startOf('day').toDate();
                    break;
                case 'month':
                case '30d':
                    startDate = dayjs().subtract(30, 'days').startOf('day').toDate();
                    break;
                case '3months':
                case '90d':
                    startDate = dayjs().subtract(90, 'days').startOf('day').toDate();
                    break;
                default:
                    startDate = dayjs().subtract(30, 'days').startOf('day').toDate();
            }

            const workouts = await db
                .selectFrom('daily_coach_protocol as dcp')
                .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')
                .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')
                .select([
                    'dcp.id as session_id',
                    'dcp.protocol_id',
                    'dcp.protocol_workout_id',
                    'cpw.name as workout_name',
                    'cp.name as protocol_name',
                    'dcp.workout_time',
                    'dcp.total_calories',
                    'dcp.total_weight',
                    'dcp.daily_at as date',
                    'dcp.created_at',
                    'cp.objective',
                    'cp.split',
                    'cp.started_at as protocol_started_at',
                    'cp.ended_at as protocol_ended_at',
                    'cp.frequency as protocol_frequency'
                ])
                .where('dcp.user_id', '=', userId)
                .where('dcp.daily_at', '>=', startDate)
                .where('dcp.daily_at', '<=', endDate)
                // Show all workouts - let frontend handle completion status
                // .where('dcp.total_calories', '>', 0) // Temporarily removed to show all workouts
                .orderBy('dcp.daily_at', 'desc')
                .limit(limit)
                .offset(offset)
                .execute();

            // Get total count for pagination
            const totalResult = await db
                .selectFrom('daily_coach_protocol as dcp')
                .select(db.fn.countAll().as('total'))
                .where('dcp.user_id', '=', userId)
                .where('dcp.daily_at', '>=', startDate)
                .where('dcp.daily_at', '<=', endDate)
                // Count all workouts - let frontend handle filtering
                // .where('dcp.total_calories', '>', 0) // Temporarily removed
                .executeTakeFirst();

            const total = Number(totalResult?.total || 0);

            // Calculate summary statistics
            const totalWorkouts = workouts.length;
            const totalVolume = workouts.reduce((sum, w) => sum + (w.total_weight || 0), 0);
            const averageIntensity = totalWorkouts > 0 ?
                workouts.reduce((sum, w) => sum + (w.total_calories || 0), 0) / totalWorkouts : 0;

            return {
                status: 'success',
                data: {
                    workouts: workouts.map(w => {
                        // Calculate duration in minutes from workout_time to now (if completed)
                        let durationMinutes = 45; // Default duration
                        if (w.workout_time && w.total_calories > 0) {
                            // If workout is completed, assume it took 45 minutes (we can enhance this later)
                            durationMinutes = 45;
                        }

                        // Determine protocol status
                        const isActiveProtocol = !w.protocol_ended_at;
                        const protocolStatus = isActiveProtocol ? 'active' : 'completed';

                        return {
                            ...w,
                            // More robust completion logic - consider multiple factors
                            completed: (w.total_calories && w.total_calories > 0) || (w.total_weight && w.total_weight > 0) || w.workout_time !== null,
                            duration: durationMinutes, // Duration in minutes
                            workout_start_time: w.workout_time, // Keep original time for reference
                            // Protocol information
                            protocol_status: protocolStatus,
                            is_current_protocol: isActiveProtocol,
                            protocol_type: 'workout' // Since this is workout history
                        };
                    }),
                    totalWorkouts,
                    totalVolume,
                    averageIntensity,
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit)
                    }
                }
            };

        } catch (error) {
            console.error('❌ Error getting workout history:', error);
            return {
                status: 'success',
                data: {
                    workouts: [],
                    totalWorkouts: 0,
                    totalVolume: 0,
                    averageIntensity: 0,
                    pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
                }
            };
        }
    }

    /**
     * Get workout session details by session ID
     */
    async getWorkoutSession(userId: number, sessionId: string) {
        try {


            // Get session details with joins
            const session = await db
                .selectFrom('daily_coach_protocol as dcp')
                .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')
                .leftJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')
                .select([
                    'dcp.id as session_id',
                    'dcp.protocol_id',
                    'dcp.protocol_workout_id as workout_id',
                    'cpw.name as workout_name',
                    'cp.name as protocol_name',
                    'dcp.workout_time',
                    'dcp.total_calories',
                    'dcp.total_weight',
                    'dcp.daily_at',
                    'dcp.created_at',
                    'cp.objective',
                    'cp.split'
                ])
                .where('dcp.id', '=', Number(sessionId))
                .where('dcp.user_id', '=', userId)
                .executeTakeFirst();

            if (!session) {
                throw new Error('Workout session not found');
            }

            // Get exercises with proper joins (handles both linked exercises and custom exercises)
            const exercises = await db
                .selectFrom('coach_protocols_workouts_exercises as cpwe')
                .leftJoin('exercises as e', 'e.id', 'cpwe.exercise_id')
                .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')
                .select([
                    'cpwe.id',
                    'e.name as exercise_name',
                    'cpwe.name as custom_name',
                    'cpwe.sets',
                    'cpwe.reps',
                    'cpwe.rpe',
                    'cpwe.rest_seconds',
                    's.value_option as muscle_group',
                    'cpwe.notes'
                ])
                .where('cpwe.workout_id', '=', session.workout_id)
                .execute();

            // Format the response to match what ActiveWorkoutPage expects
            const workoutData = {
                id: session.workout_id,
                name: session.workout_name,
                muscle_groups: exercises.map(e => e.muscle_group).filter((v, i, a) => a.indexOf(v) === i).join(', '),
                exercises: exercises.map(exercise => ({
                    id: exercise.id,
                    name: exercise.exercise_name || exercise.custom_name || 'Exercício sem nome',
                    sets: exercise.sets,
                    reps: exercise.reps,
                    rpe: exercise.rpe,
                    rest_seconds: exercise.rest_seconds,
                    muscle_group: exercise.muscle_group,
                    notes: exercise.notes
                }))
            };

            return {
                status: 'success',
                data: {
                    session: {
                        id: session.session_id,
                        protocol_id: session.protocol_id,
                        workout_id: session.workout_id,
                        workout_name: session.workout_name,
                        protocol_name: session.protocol_name,
                        workout_time: session.workout_time,
                        total_calories: session.total_calories,
                        total_weight: session.total_weight,
                        date: session.daily_at,
                        created_at: session.created_at,
                        objective: session.objective,
                        split: session.split
                    },
                    workout: workoutData,
                    // Format as protocol structure for compatibility
                    has_protocol: true,
                    id: session.protocol_id,
                    name: session.protocol_name,
                    workouts: [workoutData]
                }
            };

        } catch (error) {
            console.error('❌ Error getting workout session:', error);
            throw new HttpException({
                status: 'error',
                message: [error.message || 'Failed to get workout session']
            }, 400);
        }
    }

    /**
     * Complete a workout session
     */
    async completeWorkout(userId: number, workoutId: string, completionData?: any) {
        try {
            console.log('✅ completeWorkout: Completing workout session:', workoutId);
            console.log('📊 completeWorkout: Completion data:', completionData);

            // Validate workoutId
            if (!workoutId || isNaN(Number(workoutId))) {
                throw new Error('Invalid workout ID provided');
            }

            // Find the workout session
            const session = await db
                .selectFrom('daily_coach_protocol')
                .selectAll()
                .where('id', '=', Number(workoutId))
                .where('user_id', '=', userId)
                .executeTakeFirst();

            if (!session) {
                throw new Error(`Workout session not found for ID: ${workoutId} and user: ${userId}`);
            }

            console.log('📋 completeWorkout: Found session:', {
                id: session.id,
                protocol_id: session.protocol_id,
                workout_time: session.workout_time,
                user_id: session.user_id
            });

            // Calculate workout duration
            let workoutDuration = 45; // Default 45 minutes

            // Use duration from completion data if provided
            if (completionData?.duration && completionData.duration > 0) {
                workoutDuration = Math.round(completionData.duration / 60); // Convert seconds to minutes
                console.log('📊 Using duration from completion data:', workoutDuration, 'minutes');
            } else if (session.workout_time && session.workout_time.getTime() > new Date('2020-01-01').getTime()) {
                // Only calculate from workout_time if it's a reasonable date (after 2020)
                workoutDuration = Math.round((new Date().getTime() - session.workout_time.getTime()) / (1000 * 60));
                console.log('📊 Calculated duration from workout_time:', workoutDuration, 'minutes');
            } else {
                console.log('📊 Using default duration:', workoutDuration, 'minutes');
            }

            // Get user weight for calorie calculation
            const user = await db
                .selectFrom('users')
                .select(['weight'])
                .where('id', '=', userId)
                .executeTakeFirst();

            const userWeight = user?.weight || 70; // Default 70kg if not set

            // Calculate calories burned (MET * weight * time in hours)
            const met = session.met || 6.0; // Default MET for strength training
            const caloriesBurned = Math.round(met * userWeight * (Number(workoutDuration) / 60));

            // Update the session with completion data
            console.log('💪 completeWorkout: Updating session with completion data');
            console.log('📊 Workout duration:', workoutDuration, 'minutes');
            console.log('🔥 Calories burned:', caloriesBurned);

            await db
                .updateTable('daily_coach_protocol')
                .set({
                    total_calories: caloriesBurned,
                    updated_at: new Date()
                    // Keep the original workout_time as the start time
                    // Don't modify workout_time since it represents when the workout started
                })
                .where('id', '=', Number(workoutId))
                .where('user_id', '=', userId)
                .execute();

            console.log('✅ completeWorkout: Session updated successfully');

            console.log('✅ Workout session completed successfully');

            return {
                status: 'success',
                data: {
                    sessionId: workoutId,
                    duration: workoutDuration,
                    caloriesBurned,
                    message: 'Workout completed successfully'
                }
            };

        } catch (error) {
            console.error('❌ Error completing workout:', error);
            throw new HttpException({
                status: 'error',
                message: [error.message || 'Failed to complete workout session']
            }, 400);
        }
    }

    // Helper method to generate micronutrients data from macronutrients
    private generateMicronutrientsFromMacros(macroData: any) {
        const { calories, protein, carbs, fat } = macroData;

        // Generate realistic micronutrient data based on macronutrient intake
        const micronutrients = [
            {
                micronutrientId: 'vitamin_d',
                name: 'Vitamina D',
                currentIntake: Math.max(5, Math.min(15, calories / 200)), // 5-15 mcg based on calories
                recommendedIntake: 10,
                unit: 'mcg',
                status: calories > 1800 ? 'adequate' : 'deficient',
                percentage: Math.min(100, (calories / 2000) * 80)
            },
            {
                micronutrientId: 'iron',
                name: 'Ferro',
                currentIntake: Math.max(8, Math.min(18, protein / 10)), // Based on protein intake
                recommendedIntake: 14,
                unit: 'mg',
                status: protein > 100 ? 'adequate' : 'low',
                percentage: Math.min(100, (protein / 120) * 90)
            },
            {
                micronutrientId: 'vitamin_b12',
                name: 'Vitamina B12',
                currentIntake: Math.max(1.5, Math.min(3, protein / 50)), // Based on protein
                recommendedIntake: 2.4,
                unit: 'mcg',
                status: protein > 80 ? 'adequate' : 'deficient',
                percentage: Math.min(100, (protein / 100) * 85)
            },
            {
                micronutrientId: 'calcium',
                name: 'Cálcio',
                currentIntake: Math.max(600, Math.min(1200, calories * 0.5)), // Based on overall intake
                recommendedIntake: 1000,
                unit: 'mg',
                status: calories > 2000 ? 'adequate' : 'low',
                percentage: Math.min(100, (calories / 2200) * 95)
            },
            {
                micronutrientId: 'vitamin_c',
                name: 'Vitamina C',
                currentIntake: Math.max(40, Math.min(120, carbs / 2)), // Based on carbs (fruits/vegetables)
                recommendedIntake: 90,
                unit: 'mg',
                status: carbs > 150 ? 'adequate' : 'low',
                percentage: Math.min(100, (carbs / 180) * 88)
            },
            {
                micronutrientId: 'omega_3',
                name: 'Ômega-3',
                currentIntake: Math.max(0.8, Math.min(2.5, fat / 30)), // Based on fat intake
                recommendedIntake: 1.6,
                unit: 'g',
                status: fat > 50 ? 'adequate' : 'deficient',
                percentage: Math.min(100, (fat / 60) * 92)
            }
        ];

        const deficiencies = micronutrients.filter(m => m.status === 'deficient' || m.status === 'low');
        const excesses = micronutrients.filter(m => m.status === 'excess');

        const recommendations = deficiencies.map(micro => ({
            micronutrientId: micro.micronutrientId,
            recommendation: `Aumentar consumo de ${micro.name}`,
            foods: this.getRecommendedFoods(micro.micronutrientId),
            priority: micro.status === 'deficient' ? 'high' : 'medium'
        }));

        const overallScore = Math.round(
            micronutrients.reduce((sum, micro) => sum + micro.percentage, 0) / micronutrients.length
        );

        return {
            totalIntake: micronutrients,
            deficiencies,
            excesses,
            recommendations,
            overallScore,
            improvementAreas: deficiencies.map(d => d.name).slice(0, 3)
        };
    }

    private getRecommendedFoods(micronutrientId: string): string[] {
        const foodMap: { [key: string]: string[] } = {
            'vitamin_d': ['Salmão', 'Sardinha', 'Ovos', 'Cogumelos'],
            'iron': ['Carne vermelha', 'Feijão', 'Espinafre', 'Lentilha'],
            'vitamin_b12': ['Carne', 'Peixe', 'Ovos', 'Laticínios'],
            'calcium': ['Leite', 'Queijo', 'Iogurte', 'Brócolis'],
            'vitamin_c': ['Laranja', 'Morango', 'Kiwi', 'Pimentão'],
            'omega_3': ['Salmão', 'Sardinha', 'Nozes', 'Linhaça']
        };

        return foodMap[micronutrientId] || ['Alimentos variados'];
    }
}
