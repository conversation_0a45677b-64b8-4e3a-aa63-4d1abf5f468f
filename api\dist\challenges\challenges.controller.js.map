{"version": 3, "sources": ["../../src/challenges/challenges.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Body, \n  Param, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { ChallengesService } from './challenges.service';\n\n@Controller('challenges')\n@UseGuards(JwtAuthGuard)\nexport class ChallengesController {\n  constructor(private readonly challengesService: ChallengesService) {}\n\n  @Get()\n  async getChallenges(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.challengesService.getChallenges(userId, query);\n  }\n\n  @Get('my')\n  async getMyChallenges(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.challengesService.getMyChallenges(userId);\n  }\n\n  @Get(':id')\n  async getChallenge(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.challengesService.getChallenge(Number(id), userId);\n  }\n\n  @Post()\n  async createChallenge(@Body() challengeData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.challengesService.createChallenge(challengeData, userId);\n  }\n\n  @Post(':id/join')\n  async joinChallenge(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.challengesService.joinChallenge(Number(id), userId);\n  }\n\n  @Post(':id/leave')\n  async leaveChallenge(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.challengesService.leaveChallenge(Number(id), userId);\n  }\n\n  @Get(':id/leaderboard')\n  async getChallengeLeaderboard(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.challengesService.getChallengeLeaderboard(Number(id), userId);\n  }\n\n  @Post(':id/progress')\n  async updateChallengeProgress(@Param('id') id: string, @Body() progressData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.challengesService.updateChallengeProgress(Number(id), userId, progressData);\n  }\n}\n"], "names": ["ChallengesController", "getChallenges", "req", "query", "userId", "user", "challengesService", "getMyChallenges", "getChallenge", "id", "Number", "createChallenge", "challengeData", "joinChallenge", "leaveChallenge", "getChallengeLeaderboard", "updateChallengeProgress", "progressData", "constructor"], "mappings": ";;;;+BAeaA;;;eAAAA;;;wBANN;8BACsB;mCACK;;;;;;;;;;;;;;;AAI3B,IAAA,AAAMA,uBAAN,MAAMA;IAGX,MACMC,cAAc,AAAWC,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC5D,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACL,aAAa,CAACG,QAAQD;IACtD;IAEA,MACMI,gBAAgB,AAAWL,GAAQ,EAAE;QACzC,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACC,eAAe,CAACH;IAChD;IAEA,MACMI,aAAa,AAAaC,EAAU,EAAE,AAAWP,GAAQ,EAAE;QAC/D,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACE,YAAY,CAACE,OAAOD,KAAKL;IACzD;IAEA,MACMO,gBAAgB,AAAQC,aAAkB,EAAE,AAAWV,GAAQ,EAAE;QACrE,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACK,eAAe,CAACC,eAAeR;IAC/D;IAEA,MACMS,cAAc,AAAaJ,EAAU,EAAE,AAAWP,GAAQ,EAAE;QAChE,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACO,aAAa,CAACH,OAAOD,KAAKL;IAC1D;IAEA,MACMU,eAAe,AAAaL,EAAU,EAAE,AAAWP,GAAQ,EAAE;QACjE,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACQ,cAAc,CAACJ,OAAOD,KAAKL;IAC3D;IAEA,MACMW,wBAAwB,AAAaN,EAAU,EAAE,AAAWP,GAAQ,EAAE;QAC1E,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACS,uBAAuB,CAACL,OAAOD,KAAKL;IACpE;IAEA,MACMY,wBAAwB,AAAaP,EAAU,EAAE,AAAQQ,YAAiB,EAAE,AAAWf,GAAQ,EAAE;QACrG,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACU,uBAAuB,CAACN,OAAOD,KAAKL,QAAQa;IAC5E;IAhDAC,YAAY,AAAiBZ,iBAAoC,CAAE;aAAtCA,oBAAAA;IAAuC;AAiDtE"}