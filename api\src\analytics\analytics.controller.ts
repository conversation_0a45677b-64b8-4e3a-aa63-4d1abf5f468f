import { 
  Controller, 
  Get, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AnalyticsService } from './analytics.service';

@Controller('analytics')
@UseGuards(JwtAuthGuard)
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('dashboard')
  async getDashboardAnalytics(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getDashboardAnalytics(userId, query);
  }

  @Get('progress/summary')
  async getProgressSummary(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getProgressSummary(userId, query);
  }

  @Get('nutrition/trends')
  async getNutritionTrends(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getNutritionTrends(userId, query);
  }

  @Get('workout/trends')
  async getWorkoutTrends(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getWorkoutTrends(userId, query);
  }

  @Get('body/composition')
  async getBodyCompositionAnalysis(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getBodyCompositionAnalysis(userId, query);
  }

  @Get('habits/analysis')
  async getHabitsAnalysis(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getHabitsAnalysis(userId, query);
  }

  @Get('goals/tracking')
  async getGoalsTracking(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getGoalsTracking(userId, query);
  }

  @Get('social/stats')
  async getSocialStats(@Request() req: any) {
    const userId = req.user.userId;
    return this.analyticsService.getSocialStats(userId);
  }

  @Get('gamification/stats')
  async getGamificationStats(@Request() req: any) {
    const userId = req.user.userId;
    return this.analyticsService.getGamificationStats(userId);
  }

  @Get('reports/weekly')
  async getWeeklyReport(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getWeeklyReport(userId, query);
  }

  @Get('reports/monthly')
  async getMonthlyReport(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getMonthlyReport(userId, query);
  }

  @Get('insights/ai')
  async getAIInsights(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getAIInsights(userId, query);
  }

  @Get('predictions/weight')
  async getWeightPredictions(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getWeightPredictions(userId, query);
  }

  @Get('benchmarks/comparison')
  async getBenchmarkComparison(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.analyticsService.getBenchmarkComparison(userId, query);
  }
}
