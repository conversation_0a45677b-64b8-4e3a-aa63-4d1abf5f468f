{"version": 3, "sources": ["../../src/admin/admin.service.ts"], "sourcesContent": ["import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';\nimport { db } from '../database';\nimport { GetAllUsersQueryDto } from './get-users-query.dto';\n// import * as dayjs from 'dayjs';\nconst dayjs = require('dayjs');\nimport { CreateUserDto } from './dto/create-user.dto';\nimport * as bcrypt from 'bcryptjs';\nimport { UpdateUserDto } from './dto/update-user.dto';\nimport { GetAllFoodsQueryDto } from './dto/get-all-foods-query.dto';\nimport { CreateFoodDto } from './dto/create-food.dto';\nimport { UpdateFoodDto } from './dto/update-food.dto';\nimport { CreateExerciseDto } from './dto/create-exercise.dto';\nimport { GetAllExercisesQueryDto } from './dto/get-all-exercises-query.dto';\nimport { sql } from 'kysely';\nimport { CreatePlanDto } from './dto/plans/create-plan.dto';\nimport { UpdatePlanDto } from './dto/plans/update-plan.dto';\nimport { ConfigPlanDto } from './dto/plans/config-plan.dto';\nimport { Stripe } from 'stripe';\n\n@Injectable()\nexport class AdminService {\n      private stripe: Stripe;\n\n      constructor() {\n        // @ts-ignore\n        this.stripe = new Stripe(process.env.STRIPE_SK, {\n          // apiVersion: '2025-03-31',\n        });\n      }\n\n      formatDatetime(datetime: any): string {\n            return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');\n      }\n\n      async getStats() {\n        // Contagem de usuários com role_id = 4 (usuários comuns)\n        const totalUsers = await db\n          .selectFrom('users_roles')\n          .where('role_id', '=', 4)\n          .select(db.fn.count<number>('user_id').as('count'))\n          .executeTakeFirst();\n\n        // Contagem de usuários com role_id = 2 (nutritionistas) ou role_id = 3 (coaches)\n        const totalProfessionals = await db\n          .selectFrom('users_roles')\n          .where('role_id', 'in', [2, 3])\n          .select(db.fn.count<number>('user_id').as('count'))\n          .executeTakeFirst();\n\n        // Contagem de assinaturas ativas\n        const activeSubscriptions = await db\n          .selectFrom('users_subscriptions')\n          .where('status', '=', 'active')\n          .where('deleted_at', 'is', null)\n          .select(db.fn.count<number>('id').as('count'))\n          .executeTakeFirst();\n\n        // Cálculo da receita mensal (transações pagas no mês atual)\n        const currentDate = new Date();\n        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);\n        const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);\n\n        const monthlyRevenue = await db\n          .selectFrom('transactions')\n          .where('status', '=', 'paid')\n          .where('created_at', '>=', firstDayOfMonth)\n          .where('created_at', '<=', lastDayOfMonth)\n          .select(db.fn.sum<number>('amount').as('total'))\n          .executeTakeFirst();\n\n        // Retornar as estatísticas\n        return {\n          status: 'success',\n          data:\n            {\n              total_users: Number(totalUsers?.count || 0),\n              total_professionals: Number(totalProfessionals?.count || 0),\n              active_subscriptions: Number(activeSubscriptions?.count || 0),\n              monthly_revenue: Number(monthlyRevenue?.total || 0),\n            },\n        };\n      }\n\n      async getAllUsers(query: GetAllUsersQueryDto) {\n        const { q, role_id } = query;\n        let { page = 1, limit = 100 } = query;\n        const offset = (page - 1) * limit;\n\n        let queryBuilder = db\n          .selectFrom('users')\n          .leftJoin('users_roles', 'users.id', 'users_roles.user_id')\n          .leftJoin('roles', 'users_roles.role_id', 'roles.id')\n          .select([\n            'users.id',\n            'users.name',\n            'users.email',\n            'users.photo',\n            'users.created_at',\n            db.fn<string>('group_concat', ['roles.name']).as('roles'),\n          ])\n          .groupBy('users.id')\n          .orderBy('users.created_at', 'desc');\n\n        if (q) {\n          queryBuilder = queryBuilder.where((eb) =>\n            eb.or([\n              eb('users.name', 'like', `%${q}%`),\n              eb('users.email', 'like', `%${q}%`),\n            ])\n          );\n        }\n\n        if (role_id) {\n          queryBuilder = queryBuilder.where('roles.id', '=', role_id);\n        }\n\n        const [data, total] = await Promise.all([\n          queryBuilder.limit(limit).offset(offset).execute(),\n          db.selectFrom('users').select(db.fn.countAll().as('total')).executeTakeFirst(),\n        ]);\n\n        return {\n          status: 'success',\n          data: data.map((row) => ({\n            id: row.id,\n            name: row.name,\n            email: row.email,\n            roles: row.roles ? row.roles.split(', ') : [],\n            photo: row.photo,\n            createdAt: this.formatDatetime(row.created_at),\n          })),\n          pagination: {\n            page,\n            limit,\n            total: Number(total?.total),\n          },\n        };\n      }\n\n\n      async createUser(createUserDto: CreateUserDto) {\n        const { name, email, password, role_id, photo } = createUserDto;\n\n        // Verifica se o e-mail já está cadastrado\n        const existingUser = await db\n          .selectFrom('users')\n          .where('email', '=', email)\n          .select('id')\n          .executeTakeFirst();\n\n        if (existingUser) {\n          throw new ConflictException('E-mail already exists');\n        }\n\n        // Criptografa a senha\n        const hashedPassword = await bcrypt.hash(password, 10);\n\n        // Insere o novo usuário no banco de dados\n        const result = await db\n          .insertInto('users')\n          .values({\n            name,\n            email,\n            password: hashedPassword, // Armazena a senha criptografada\n            photo,\n            created_at: new Date(),\n            updated_at: new Date(),\n          })\n          .executeTakeFirst();\n\n        // Obtém o ID do usuário recém-inserido\n        const userId: any = result.insertId;\n\n        // Associa o usuário ao papel (role)\n        await db\n          .insertInto('users_roles')\n          .values({\n            user_id: userId,\n            role_id,\n          })\n          .execute();\n\n        return {\n          status: 'success',\n          data: [],\n        };\n      }\n\n\n      async updateUser(id: number, updateUserDto: UpdateUserDto) {\n        const { name, email, password, role_id, photo } = updateUserDto;\n\n        // Verifica se o usuário existe\n        const existingUser = await db\n          .selectFrom('users')\n          .where('id', '=', id)\n          .select(['id', 'email'])\n          .executeTakeFirst();\n\n        if (!existingUser) {\n          throw new NotFoundException('User not found');\n        }\n\n        // Verifica se o e-mail já está em uso por outro usuário\n        if (email !== existingUser.email) {\n          const emailExists = await db\n            .selectFrom('users')\n            .where('email', '=', email)\n            .select('id')\n            .executeTakeFirst();\n\n          if (emailExists) {\n            throw new ConflictException('E-mail already exists');\n          }\n        }\n\n        // Prepara os valores para atualização\n        const updateValues: any = {\n          name,\n          email,\n          photo,\n          updated_at: new Date(),\n        };\n\n        // Criptografa a nova senha, se fornecida\n        if (password) {\n          const hashedPassword = await bcrypt.hash(password, 10);\n          updateValues.password = hashedPassword;\n        }\n\n        // Atualiza o usuário no banco de dados\n        await db\n          .updateTable('users')\n          .set(updateValues)\n          .where('id', '=', id)\n          .execute();\n\n        // Atualiza o papel (role) do usuário na tabela intermediária\n        await db\n          .updateTable('users_roles')\n          .set({ role_id })\n          .where('user_id', '=', id)\n          .execute();\n\n        // Retorna o usuário atualizado\n        const updatedUser = await db\n          .selectFrom('users')\n          .where('id', '=', id)\n          .select(['id', 'name', 'email', 'photo', 'created_at', 'updated_at'])\n          .executeTakeFirstOrThrow();\n\n        return {\n          status: 'success',\n          data: updatedUser,\n        };\n      }\n\n      async deleteUser(id: number) {\n        // Verifica se o usuário existe\n        const existingUser = await db\n          .selectFrom('users')\n          .where('id', '=', id)\n          .select('id')\n          .executeTakeFirst();\n\n        if (!existingUser) {\n          throw new NotFoundException('User not found');\n        }\n\n        // Remove as roles associadas ao usuário\n        await db\n          .deleteFrom('users_roles')\n          .where('user_id', '=', id)\n          .execute();\n\n        // Remove o usuário\n        await db\n          .deleteFrom('users')\n          .where('id', '=', id)\n          .execute();\n\n        return {\n          status: 'success',\n          message: 'User deleted successfully',\n        };\n      }\n\n      async getSelectOptions(type: string) {\n        const options =  await db\n          .selectFrom('select_options')\n          .select(['id', 'value_option'])\n          .where('area_key', '=', type)\n          .orderBy(['sort_order', 'value_option'])\n          .execute();\n\n          return {\n            status: 'success',\n            data: options,\n          }\n      }\n\n\n      async getAllFoods(query: GetAllFoodsQueryDto): Promise<any> {\n        const { name, category_id, page = 1, limit = 100 } = query;\n\n        // Cria o construtor de consulta base\n        let queryBuilder = db\n          .selectFrom('foods')\n          .innerJoin('select_options as category', 'category.id', 'foods.category_id')\n          .select([\n            'foods.id',\n            'foods.name',\n            'foods.quantity',\n            'foods.unit as serving_unit',\n            // (qb: any) => qb.ref('serving_unit.value_option').as('serving_unit'), // Referência explícita\n            'foods.calories',\n            'foods.protein',\n            'foods.carbs',\n            'foods.fat',\n            (qb: any) => qb.ref('category.value_option').as('category'), // Referência explícita\n          ]);\n\n        // Aplica filtro por nome, se fornecido\n        if (name) {\n          queryBuilder = queryBuilder.where('foods.name', 'like', `%${name}%`);\n        }\n\n        // Aplica filtro por categoria, se fornecido\n        if (category_id) {\n          queryBuilder = queryBuilder.where('foods.category_id', '=', category_id);\n        }\n\n        // Cria uma nova consulta para calcular o total de registros\n        const totalRecordsQuery = db\n          .selectFrom('foods')\n          .innerJoin('select_options as category', 'category.id', 'foods.category_id');\n\n        // Reaplica os mesmos filtros na nova consulta\n        if (name) {\n          totalRecordsQuery.where('foods.name', 'like', `%${name}%`);\n        }\n        if (category_id) {\n          totalRecordsQuery.where('foods.category_id', '=', category_id);\n        }\n\n        // Calcula o total de registros correspondentes aos filtros\n        const totalRecords = await totalRecordsQuery\n          .select(db.fn.countAll().as('count'))\n          .executeTakeFirst();\n\n        const total = Number(totalRecords?.count || 0);\n\n        // Aplica paginação na consulta principal\n        const offset = (page - 1) * limit;\n        queryBuilder = queryBuilder.limit(limit).offset(offset);\n\n        // Executa a consulta para obter os alimentos paginados\n        const foods = await queryBuilder.execute();\n\n        // Retorna os dados com informações de paginação\n        return {\n          status: 'success',\n          data: foods,\n          pagination: {\n            page: page,\n            limit: limit,\n            total: total,\n            totalPages: Math.ceil(total / limit),\n          },\n        };\n      }\n\n      async searchFoods(query: { q: string; limit?: number }): Promise<any> {\n        const { q, limit = 20 } = query;\n\n        if (!q || q.trim().length === 0) {\n          return {\n            status: 'success',\n            data: [],\n          };\n        }\n\n        const foods = await db\n          .selectFrom('foods')\n          .innerJoin('select_options as category', 'category.id', 'foods.category_id')\n          .select([\n            'foods.id',\n            'foods.name',\n            'foods.quantity',\n            'foods.unit as serving_unit',\n            'foods.calories',\n            'foods.protein',\n            'foods.carbs',\n            'foods.fat',\n            'foods.fiber',\n            (qb: any) => qb.ref('category.value_option').as('category'),\n          ])\n          .where('foods.name', 'like', `%${q.trim()}%`)\n          .limit(limit)\n          .execute();\n\n        return {\n          status: 'success',\n          data: foods,\n        };\n      }\n\n      async createFood(createFoodDto: CreateFoodDto) {\n        const {\n          name,\n          category_id,\n          quantity,\n          unit,\n          calories,\n          protein,\n          carbs,\n          fat,\n          fiber,\n        } = createFoodDto;\n\n        // Verifica se a categoria existe\n        const categoryExists = await db\n          .selectFrom('select_options')\n          .where('id', '=', category_id)\n          .select('id')\n          .executeTakeFirst();\n\n        if (!categoryExists) {\n          throw new ConflictException('Category does not exist');\n        }\n\n\n        // Insere o novo alimento no banco de dados\n        const result = await db\n          .insertInto('foods')\n          .values({\n            name,\n            category_id,\n            quantity,\n            unit,\n            calories,\n            protein,\n            carbs,\n            fat,\n            fiber,\n            created_at: new Date(),\n            updated_at: new Date(),\n          })\n          // .returning(['id', 'name', 'serving', 'calories'])\n          .executeTakeFirstOrThrow();\n\n        return {\n          status: 'success',\n          data: [],\n        };\n      }\n\n\n  async updateFood(id: number, updateFoodDto: UpdateFoodDto) {\n    const {\n      name,\n      category_id,\n      quantity,\n      unit,\n      calories,\n      protein,\n      carbs,\n      fat,\n      fiber,\n    } = updateFoodDto;\n\n    // Verifica se o alimento existe\n    const existingFood = await db\n      .selectFrom('foods')\n      .where('id', '=', id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!existingFood) {\n      throw new NotFoundException('Food not found');\n    }\n\n    // Verifica se a categoria existe\n    const categoryExists = await db\n      .selectFrom('select_options')\n      .where('id', '=', category_id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!categoryExists) {\n      throw new ConflictException('Category does not exist');\n    }\n\n    // Atualiza o alimento no banco de dados\n    const result = await db\n      .updateTable('foods')\n      .set({\n        name,\n        category_id,\n        quantity,\n        unit,\n        calories,\n        protein,\n        carbs,\n        fat,\n        fiber,\n        updated_at: new Date(),\n      })\n      .where('id', '=', id)\n      // .returning(['id', 'name', 'serving', 'calories'])\n      .executeTakeFirstOrThrow();\n\n    return {\n      status: 'success',\n      data: [],\n    };\n  }\n\n  async deleteFood(id: number) {\n    // Verifica se o alimento existe\n    const existingFood = await db\n      .selectFrom('foods')\n      .where('id', '=', id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!existingFood) {\n      throw new NotFoundException('Food not found');\n    }\n\n    // Exclui o alimento do banco de dados\n    await db\n      .deleteFrom('foods')\n      .where('id', '=', id)\n      .execute();\n\n    return {\n      status: 'success',\n      message: 'Food deleted successfully',\n    };\n  }\n\n  // Exercícios\n  async getAllExercises(query: GetAllExercisesQueryDto) {\n    const { name, muscle_group_id, page = 1, limit = 100 } = query;\n\n    // Passo 1: Buscar os exercícios principais\n    let queryBuilder = db\n      .selectFrom('exercises as e')\n      .innerJoin('select_options as muscle_group', 'muscle_group.id', 'e.muscle_group_id')\n      .innerJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')\n      .select([\n        'e.id',\n        'e.name',\n        (qb) => qb.ref('muscle_group.value_option').as('muscle_group'),\n        (qb) => qb.ref('equipment.value_option').as('equipment'),\n      ])\n      .where('e.deleted_at', 'is', null);\n\n    // Aplica filtro por nome\n    if (name) {\n      queryBuilder = queryBuilder.where('e.name', 'like', `%${name}%`);\n    }\n\n    // Aplica filtro por grupo muscular\n    if (muscle_group_id) {\n      queryBuilder = queryBuilder.where('e.muscle_group_id', '=', muscle_group_id);\n    }\n\n    // Calcula o total de registros correspondentes aos filtros\n    let totalRecordsQuery = db\n      .selectFrom('exercises as e')\n      .innerJoin('select_options as muscle_group', 'muscle_group.id', 'e.muscle_group_id')\n      .innerJoin('select_options as equipment', 'equipment.id', 'e.equipment_id');\n\n    if (name) {\n      totalRecordsQuery = totalRecordsQuery.where('e.name', 'like', `%${name}%`);\n    }\n    if (muscle_group_id) {\n      totalRecordsQuery = totalRecordsQuery.where('e.muscle_group_id', '=', muscle_group_id);\n    }\n\n    const totalRecords = await totalRecordsQuery\n      .select(db.fn.countAll().as('count'))\n      .where('e.deleted_at', 'is', null)\n      .executeTakeFirst();\n\n    const total = Number(totalRecords?.count || 0);\n\n    // Aplica paginação\n    const offset = (page - 1) * limit;\n    queryBuilder = queryBuilder\n    .limit(limit).offset(offset);\n\n    // Executa a consulta principal\n    const exercises = await queryBuilder.execute();\n\n    // Se não houver exercícios, retorna uma resposta vazia\n    if (exercises.length === 0) {\n      return {\n        status: 'success',\n        data: [],\n        pagination: {\n          page,\n          limit,\n          total,\n          totalPages: Math.ceil(total / limit),\n        },\n      };\n    }\n\n    // Passo 2: Buscar os IDs dos exercícios\n    const exerciseIds = exercises.map((exercise) => exercise.id);\n\n    // Passo 3: Buscar os itens relacionados\n    const relatedItems = await db\n      .selectFrom('exercises_items')\n      .where('exercise_id', 'in', exerciseIds)\n      .select(['exercise_id', 'key_item', 'value_item'])\n      .execute();\n\n    // Passo 4: Organizar os dados finais\n    const formattedExercises = exercises.map((exercise) => {\n      const targetMuscles = relatedItems\n        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'target_muscles')\n        .map((item) => item.value_item);\n\n      const synergisticMuscles = relatedItems\n        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'synergistic_muscles')\n        .map((item) => item.value_item);\n\n      const instructions = relatedItems\n        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'instructions')\n        .map((item) => item.value_item);\n\n      const tips = relatedItems\n        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'tips')\n        .map((item) => item.value_item);\n\n      return {\n        id: exercise.id,\n        name: exercise.name,\n        muscle_group: exercise.muscle_group,\n        equipment: exercise.equipment,\n        target_muscles: targetMuscles,\n        synergistic_muscles: synergisticMuscles,\n        instructions: instructions,\n        tips: tips,\n      };\n    });\n\n    return {\n      status: 'success',\n      data: formattedExercises,\n      pagination: {\n        page,\n        limit,\n        total,\n        totalPages: Math.ceil(total / limit),\n      },\n    };\n  }\n\n\n  async createExercise(createExerciseDto: CreateExerciseDto) {\n    const {\n      name,\n      muscle_group_id,\n      equipment_id,\n      media_url,\n      target_muscles,\n      synergistic_muscles,\n      instructions,\n      tips,\n    } = createExerciseDto;\n\n    // Verifica se o grupo muscular existe\n    const muscleGroupExists = await db\n      .selectFrom('select_options')\n      .where('id', '=', muscle_group_id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!muscleGroupExists) {\n      throw new ConflictException('Muscle group does not exist');\n    }\n\n    // Verifica se o equipamento existe\n    const equipmentExists = await db\n      .selectFrom('select_options')\n      .where('id', '=', equipment_id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!equipmentExists) {\n      throw new ConflictException('Equipment does not exist');\n    }\n\n    // Insere o novo exercício na tabela `exercises`\n    const exerciseResult = await db\n      .insertInto('exercises')\n      .values({\n        name,\n        muscle_group_id,\n        equipment_id,\n        media_url: media_url || null,\n        created_at: new Date(),\n        updated_at: new Date(),\n      })\n      // .returning(['id'])\n      .executeTakeFirstOrThrow();\n\n      const exerciseId = Number(exerciseResult.insertId);\n\n    // Insere os itens relacionados na tabela `exercises_items`\n    const itemsToInsert: any[] = [];\n\n    // Target muscles\n    target_muscles?.forEach((muscle) => {\n      itemsToInsert.push({\n        exercise_id: exerciseId,\n        key_item: 'target_muscles',\n        value_item: muscle,\n      });\n    });\n\n    // Synergistic muscles\n    synergistic_muscles?.forEach((muscle) => {\n      itemsToInsert.push({\n        exercise_id: exerciseId,\n        key_item: 'synergistic_muscles',\n        value_item: muscle,\n      });\n    });\n\n    // Instructions\n    instructions?.forEach((instruction, index) => {\n      itemsToInsert.push({\n        exercise_id: exerciseId,\n        key_item: `instructions`,\n        value_item: instruction,\n      });\n    });\n\n    // Tips (opcional)\n    if (tips) {\n      tips.forEach((tip, index) => {\n        itemsToInsert.push({\n          exercise_id: exerciseId,\n          key_item: `tips`,\n          value_item: tip,\n        });\n      });\n    }\n\n    // Insere todos os itens na tabela `exercises_items`\n    if (itemsToInsert.length > 0) {\n      await db.insertInto('exercises_items').values(itemsToInsert).execute();\n    }\n\n    return {\n      status: 'success',\n      data: { id: exerciseId, name },\n    };\n  }\n\n  async deleteExercise(id: number) {\n    // Verifica se o exercício existe\n    const existingExercise = await db\n      .selectFrom('exercises')\n      .where('id', '=', id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!existingExercise) {\n      throw new NotFoundException('Exercise not found');\n    }\n\n    // Exclui o exercício do banco de dados\n    await db\n      .deleteFrom('exercises')\n      .where('id', '=', id)\n      .execute();\n\n    return {\n      status: 'success',\n      message: 'Exercise deleted successfully',\n    };\n  }\n\n  // Plans management\n  async getAllPlans() {\n    const plans = await db\n     .selectFrom('plans as p1')\n     .leftJoin('plans_payments_providers as p2', 'p1.id', 'p2.plan_id')\n     .leftJoin('payment_providers as p3', 'p2.payment_provider_id', 'p3.id')\n     .select([\n      'p1.id', 'p1.is_active', 'p1.name', 'p1.description',\n      'p1.price', 'p1.currency', 'p1.snaptokens',\n      sql`COALESCE(p2.price, p1.price)`.as('provider_price'),\n      sql`COALESCE(p2.currency, p1.currency)`.as('provider_currency'),\n      sql`COALESCE(p2.snaptokens, p1.snaptokens)`.as('provider_snaptokens'),\n      'p2.id as config_id',\n      'p1.frequency', 'p1.interval_value', 'p1.is_active', 'p1.role_id', 'p1.user_id', 'p1.snaptokens', 'p1.allows_trial', 'p1.trial_period_days', 'p1.affiliate_master_commission_percent', 'p1.affiliate_commission_percent', 'p1.created_at',\n      'p2.platform', 'p2.payment_provider_external_id',\n      'p3.name as payment_provider_name',\n     ])\n     .where('p1.deleted_at', 'is', null)\n     .where('p2.deleted_at', 'is', null)\n     .execute();\n\n     const getPeriod = (frequency: string, interval_value: number): string => {\n      // 'monthly' | 'quarterly' | 'semiannual' | 'annual'\n      if (frequency === 'monthly' && interval_value === 1) {\n        return 'monthly';\n      } else if (frequency === 'monthly' && interval_value === 3) {\n        return 'quarterly';\n      } else if (frequency === 'monthly' && interval_value === 6) {\n        return 'semiannual';\n      } else if (frequency === 'annually' && interval_value === 1) {\n        return 'annual';\n      } else {\n        return 'other';\n      }\n    }\n\n    // Agrupar os providers por plano\n    const groupedPlans = plans.reduce<Record<number, any>>((acc, plan) => {\n      const { id, payment_provider_name, platform, payment_provider_external_id, provider_price, provider_currency, provider_snaptokens } = plan;\n\n      if (!acc[id]) {\n        acc[id] = {\n          id,\n          is_active: plan.is_active,\n          name: plan.name,\n          description: plan.description,\n          price: Number(plan.price),\n          period: getPeriod(plan.frequency, plan.interval_value),\n          snaptokens: plan.snaptokens,\n          affiliate_master_commission_percent: Number(plan.affiliate_master_commission_percent),\n          affiliate_commission_percent: Number(plan.affiliate_commission_percent),\n          payment_config: []\n        };\n      }\n\n      // Se houver provider, adicione ao array\n      if (payment_provider_name) {\n        acc[id].payment_config.push({\n          config_id: plan.config_id,\n          payment_provider: payment_provider_name.replace(/\\s+/g, '_').toLowerCase(),\n          platform,\n          price: Number(provider_price),\n\n          // currency: provider_currency,\n          snaptokens: provider_snaptokens,\n          payment_provider_external_id,\n        });\n      }\n\n      return acc;\n    }, {});\n\n    // Converter para array\n    const formattedPlans = Object.values(groupedPlans);\n\n    return {\n      status:'success',\n      data: formattedPlans,\n    };\n  }\n\n  async createPlan(createPlanDto: CreatePlanDto, userId: number) {\n    const user_id = userId;\n    const role_id_users = 4;\n    const currency = 'BRL';\n    const allows_trial = null;\n    const trial_period_days = null;\n\n    const {\n      name,\n      description,\n      price,\n      period,\n      isActive,\n      snaptokens,\n      affiliate_master_commission_percent,\n      affiliate_commission_percent\n    } = createPlanDto;\n\n    const is_active = (isActive === true) ? 1 : null;\n\n    const getPeriod = (period: string): { frequency: 'daily' | 'weekly' | 'monthly' | 'annually'; interval_value: number } => {\n      if (period === 'monthly') {\n        return { frequency: 'monthly', interval_value: 1 };\n      } else if (period === 'quarterly') {\n        return { frequency: 'monthly', interval_value: 3 };\n      } else if (period === 'semiannual') {\n        return { frequency: 'monthly', interval_value: 6 };\n      } else if (period === 'annually') {\n        return { frequency: 'annually', interval_value: 1 };\n      } else {\n        return { frequency: 'monthly', interval_value: 1 };\n      }\n    }\n\n    const { frequency, interval_value }: { frequency: 'daily' | 'weekly' | 'monthly' | 'annually'; interval_value: number } = getPeriod(period);\n\n    // Insere o novo plano na tabela `plans`\n    const planResult = await db\n      .insertInto('plans')\n      .values({\n        name,\n        description,\n        price,\n        currency,\n        frequency,\n        interval_value,\n        is_active,\n        role_id: role_id_users,\n        user_id,\n        snaptokens,\n        allows_trial,\n        trial_period_days,\n        affiliate_master_commission_percent,\n        affiliate_commission_percent,\n        created_at: new Date(),\n        updated_at: new Date(),\n      })\n      // .returning(['id'])\n      .executeTakeFirstOrThrow();\n\n      const planId = Number(planResult.insertId);\n\n    return {\n      status: 'success',\n      data: { id: planId, name },\n    };\n  }\n\n  async updatePlan(id: number, updatePlanDto: UpdatePlanDto) {\n    const {\n      name,\n      description,\n      price,\n      currency,\n      frequency,\n      interval_value,\n      isActive,\n      role_id,\n      user_id,\n      snaptokens,\n      allows_trial,\n      trial_period_days,\n      affiliate_master_commission_percent,\n      affiliate_commission_percent\n    } = updatePlanDto;\n\n    const is_active = (isActive === true) ? 1 : null;\n\n    // Atualiza o plano na tabela `plans`\n    const planResult = await db\n      .updateTable('plans')\n      .set({\n        name,\n        description,\n        price,\n        currency,\n        frequency,\n        interval_value,\n        is_active,\n        role_id,\n        user_id,\n        snaptokens,\n        allows_trial,\n        trial_period_days,\n        affiliate_master_commission_percent,\n        affiliate_commission_percent,\n        updated_at: new Date(),\n      })\n      .where('id', '=', id)\n      // .returning(['id'])\n      .executeTakeFirstOrThrow();\n\n    return {\n      status: 'success',\n      data: { id, name },\n    };\n  }\n\n  async configPlan(id: number, configPlanDto: ConfigPlanDto) {\n    console.log('configPlanDto', configPlanDto);\n\n\n    const payment_provider_ids = {\n      'stripe': 1,\n      'google_play_billing': 2,\n      'apple_iap': 3,\n    };\n\n    const payment_provider_id = payment_provider_ids[configPlanDto.payment_provider];\n\n    const {\n      platform,\n      price,\n      // currency,\n      snaptokens,\n      payment_provider_external_id,\n    } = configPlanDto;\n\n    const currency = 'BRL';\n\n    // Verifica se o provedor de pagamento existe\n    const existingPaymentProvider = await db\n      .selectFrom('payment_providers')\n      .where('id', '=', payment_provider_id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!existingPaymentProvider) {\n      throw new NotFoundException('Payment provider not found');\n    }\n\n    // Verifica se o provedor já está configurado para o plano\n    const existingPlanConfig = await db\n      .selectFrom('plans_payments_providers')\n      .where('plan_id', '=', id)\n      .where('payment_provider_id', '=', payment_provider_id)\n      .where('platform', '=', platform)\n      .select('id')\n      .executeTakeFirst();\n\n    if (existingPlanConfig) {\n      // Atualiza a configuração do plano para o provedor de pagamento\n      const planConfigResult = await db\n        .updateTable('plans_payments_providers')\n        .set({\n          price,\n          currency,\n          snaptokens,\n          payment_provider_external_id,\n          updated_at: new Date(),\n        })\n        .where('id', '=', existingPlanConfig.id)\n        .executeTakeFirstOrThrow();\n    }\n\n    // Insere ou atualiza a configuração do plano para o provedor de pagamento\n    if (!existingPlanConfig) {\n      const planConfigResult = await db\n      .insertInto('plans_payments_providers')\n      .values({\n        plan_id: id,\n        platform,\n        payment_provider_id,\n        price,\n        currency,\n        snaptokens,\n        payment_provider_external_id,\n        created_at: new Date(),\n        updated_at: new Date(),\n      })\n      .executeTakeFirstOrThrow();\n    }\n\n    return {\n      status: 'success',\n      data: { id, payment_provider_id },\n    };\n  }\n\n  async deletePlan(id: number) {\n    // Verifica se o plano existe\n    const existingPlan = await db\n      .selectFrom('plans')\n      .where('id', '=', id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!existingPlan) {\n      throw new NotFoundException('Plan not found');\n    }\n\n    // Atualiza o deleted_at do plano\n    await db\n      .updateTable('plans')\n      .set({\n        deleted_at: new Date(),\n      })\n      .where('id', '=', id)\n      .execute();\n\n    return {\n      status: 'success',\n      message: 'Plan deleted successfully',\n    };\n  }\n\n  async deletePlanConfig(id: number) {\n    // Verifica se a configuração do plano existe\n    const existingPlanConfig = await db\n      .selectFrom('plans_payments_providers')\n      .where('id', '=', id)\n      .select('id')\n      .executeTakeFirst();\n\n    if (!existingPlanConfig) {\n      throw new NotFoundException('Plan config not found');\n    }\n\n    // Exclui a configuração do plano\n    await db\n      .updateTable('plans_payments_providers')\n      .set({\n        deleted_at: new Date(),\n      })\n      .where('id', '=', id)\n      .execute();\n\n    return {\n      status: 'success',\n      message: 'Plan config deleted successfully',\n    };\n  }\n\n  async getAllSubscriptions(query: any) {\n    const { q, status } = query;\n    let { page = 1, limit = 100 } = query;\n    const offset = (page - 1) * limit;\n\n    let queryBuilder = db\n      .selectFrom('users_subscriptions')\n      .leftJoin('users', 'users_subscriptions.user_id', 'users.id')\n      .leftJoin('plans', 'users_subscriptions.plan_id', 'plans.id')\n      .leftJoin('plans_payments_providers', 'users_subscriptions.plan_payment_provider_id', 'plans_payments_providers.id')\n      .leftJoin('payment_providers', 'plans_payments_providers.payment_provider_id', 'payment_providers.id')\n      .select([\n        'users_subscriptions.id',\n        'users_subscriptions.status',\n        'users_subscriptions.platform',\n        'users_subscriptions.price',\n        'users_subscriptions.currency',\n        'users_subscriptions.start_date',\n        'users_subscriptions.end_date',\n        'users_subscriptions.next_billing_date',\n        'users_subscriptions.cancel_at_period_end',\n        'users_subscriptions.is_trial',\n        'users_subscriptions.trial_start_date',\n        'users_subscriptions.trial_end_date',\n        'users_subscriptions.snaptokens',\n        'users_subscriptions.created_at',\n        'users.name as user_name',\n        'users.email as user_email',\n        'plans.name as plan_name',\n        'payment_providers.name as payment_provider_name',\n      ])\n      .where('users_subscriptions.deleted_at', 'is', null)\n      .orderBy('users_subscriptions.created_at', 'desc');\n\n    if (q) {\n      queryBuilder = queryBuilder.where((eb) =>\n        eb.or([\n          eb('users.name', 'like', `%${q}%`),\n          eb('users.email', 'like', `%${q}%`)\n        ])\n      );\n    }\n\n    if (status) {\n      queryBuilder = queryBuilder.where('users_subscriptions.status', '=', status);\n    }\n\n    const [data, total] = await Promise.all([\n      queryBuilder.limit(Number(limit)).offset(Number(offset)).execute(),\n      db.selectFrom('users_subscriptions').select(db.fn.countAll().as('total')).executeTakeFirst(),\n    ]);\n\n    return {\n      status: 'success',\n      data: data.map((row) => ({\n        id: row.id,\n        user_name: row.user_name,\n        user_email: row.user_email,\n        status: row.status,\n        platform: row.platform,\n        price: row.price,\n        currency: row.currency,\n        start_date: row.start_date,\n        end_date: row.end_date,\n        next_billing_date: row.next_billing_date,\n        cancel_at_period_end: row.cancel_at_period_end,\n        is_trial: row.is_trial,\n        trial_start_date: row.trial_start_date,\n        trial_end_date: row.trial_end_date,\n        snaptokens: row.snaptokens,\n        created_at: row.created_at,\n        plan_name: row.plan_name,\n        payment_provider_name: row.payment_provider_name,\n      })),\n      pagination: {\n        page,\n        limit,\n        total: Number(total?.total),\n      },\n    };\n  }\n\n  async getAllTransactions(query: any) {\n    const { q, status } = query;\n    let { page = 1, limit = 100 } = query;\n    const offset = (page - 1) * limit;\n\n    let queryBuilder = db\n      .selectFrom('transactions')\n      .leftJoin('users', 'transactions.user_id', 'users.id')\n      .leftJoin('payment_providers', 'transactions.payment_provider_id', 'payment_providers.id')\n      .select([\n        'transactions.id',\n        'transactions.provider_transaction_id',\n        'transactions.amount',\n        'transactions.currency',\n        'transactions.status',\n        'transactions.source_type',\n        'transactions.source_id',\n        'transactions.created_at',\n        'users.name as user_name',\n        'users.email as user_email',\n        'payment_providers.name as payment_provider_name',\n      ])\n      .where('transactions.deleted_at', 'is', null)\n      .orderBy('transactions.created_at', 'desc');\n\n    if (q) {\n      queryBuilder = queryBuilder.where((eb) =>\n        eb.or([\n          eb('users.name', 'like', `%${q}%`),\n          eb('users.email', 'like', `%${q}%`)\n        ])\n      );\n    }\n\n    if (status) {\n      queryBuilder = queryBuilder.where('transactions.status', '=', status);\n    }\n\n    const [data, total] = await Promise.all([\n      queryBuilder.limit(Number(limit)).offset(Number(offset)).execute(),\n      db.selectFrom('transactions').select(db.fn.countAll().as('total')).executeTakeFirst(),\n    ]);\n\n    return {\n      status: 'success',\n      data: data.map((row) => ({\n        id: row.id,\n        user_name: row.user_name,\n        user_email: row.user_email,\n        provider_transaction_id: row.provider_transaction_id,\n        amount: row.amount,\n        currency: row.currency,\n        status: row.status,\n        source_type: row.source_type,\n        source_id: row.source_id,\n        created_at: row.created_at,\n        payment_provider_name: row.payment_provider_name,\n      })),\n      pagination: {\n        page,\n        limit,\n        total: Number(total?.total),\n      },\n    };\n  }\n\n  // Affiliates\nasync getAffiliates(query: any) {\n    const { q, status, is_master } = query;\n    let { page = 1, limit = 100 } = query;\n    const offset = (page - 1) * limit;\n\n    let queryBuilder = db\n      .selectFrom('affiliates')\n      .leftJoin('users', 'affiliates.user_id', 'users.id')\n      .select([\n        'users.id',\n        'affiliates.status',\n        'affiliates.is_master',\n        'affiliates.accepted_at',\n        'affiliates.created_at',\n        'users.name as name',\n        'users.email as email'\n      ])\n      .where('affiliates.deleted_at', 'is', null)\n      .orderBy('affiliates.created_at', 'desc');\n\n    if (q) {\n      queryBuilder = queryBuilder.where((eb) =>\n        eb.or([\n          eb('users.name', 'like', `%${q}%`),\n          eb('users.email', 'like', `%${q}%`)\n        ])\n      );\n    }\n\n    if (status) {\n      queryBuilder = queryBuilder.where('affiliates.status', '=', status);\n    }\n\n    if (is_master) {\n      queryBuilder = queryBuilder.where('affiliates.is_master', '=', is_master);\n    }\n\n    const [data, total] = await Promise.all([\n      queryBuilder.limit(Number(limit)).offset(Number(offset)).execute(),\n      db.selectFrom('affiliates').select(db.fn.countAll().as('total')).executeTakeFirst(),\n    ]);\n\n    return {\n      status: 'success',\n      data: data.map((row) => ({\n        id: row.id,\n        status: row.status,\n        name: row.name,\n        email: row.email,\n        is_master: row.is_master,\n        accepted_at: row.accepted_at,\n        created_at: row.created_at,\n      })),\n      pagination: {\n        page,\n        limit,\n        total: Number(total?.total),\n      },\n    };\n  }\n\n\n\n      async createAffiliateAccount(email: string, name: string, affiliateId?: string): Promise<string> {\n      const account = await this.stripe.accounts.create({\n        type: 'express',\n        country: 'BR',\n        email: email,\n        business_type: 'individual',\n        individual: {\n          first_name: name.split(' ')[0],\n          last_name: name.split(' ').slice(1).join(' '),\n        },\n        metadata: { affiliateId: affiliateId || '' },\n      });\n      return account.id;\n      }\n\n    async updateAffiliate(id: number, updateAffiliateDto: any) {\n      const { status, is_master } = updateAffiliateDto;\n\n      // Verifica se o afiliado existe\n      const existingAffiliate = await db\n        .selectFrom('affiliates')\n        .where('user_id', '=', id)\n        .select(['id', 'accepted_at'])\n        .executeTakeFirst();\n\n      if (!existingAffiliate) {\n        throw new NotFoundException('Affiliate not found');\n      }\n\n      const isNew = !existingAffiliate.accepted_at;\n      let acceptedIfActive = status === 'active' ? new Date() : null;\n      let accepted_at = existingAffiliate.accepted_at ? existingAffiliate.accepted_at : acceptedIfActive;\n\n      // Atualiza o afiliado\n      await db\n        .updateTable('affiliates')\n        .set({\n          status,\n          is_master: is_master ? 1 : null,\n          accepted_at,\n          updated_at: new Date(),\n        })\n        .where('user_id', '=', id)\n        .execute();\n\n\n      if (isNew && status === 'active') {\n        const affData = await db.selectFrom('affiliates')\n        .leftJoin('users', 'users.id', 'affiliates.user_id')\n        .select(['users.id', 'users.email', 'users.name'])\n        .where('affiliates.user_id', '=', id)\n        .executeTakeFirst();\n\n        if (affData) {\n          const name: string = affData.name || '';\n          const email: string = affData.email || '';\n          const account = await this.createAffiliateAccount(email, name, id.toString());\n\n          if(account) {\n            await db\n            .updateTable('affiliates')\n            .set({\n              stripeId: account,\n              updated_at: new Date(),\n            })\n            .where('user_id', '=', id)\n            .execute();\n          }\n        }\n\n      }\n\n      return {\n        status: 'success',\n        data: [],\n      };\n    }\n\n    // Webhook Stripe\n    async webhook(body: any) {\n      try {\n        // Verificar se o body é válido\n        if (!body || !body.type || !body.data || !body.data.object) {\n          console.error('Webhook body inválido:', body);\n          return {\n            status: 'error',\n            message: 'Webhook body inválido',\n          };\n        }\n\n        const event = body;\n        const eventType = event.type;\n\n        // Verificar a assinatura do webhook (em produção)\n        // const signature = request.headers['stripe-signature'];\n        // const event = this.stripe.webhooks.constructEvent(\n        //   request.body,\n        //   signature,\n        //   process.env.STRIPE_WEBHOOK_SECRET\n        // );\n\n        // console.log(`Webhook received: ${eventType}`);\n        // console.log('Webhook data:', JSON.stringify(event.data.object, null, 2));\n\n        try {\n          switch (eventType) {\n            // Eventos de assinatura\n            case 'customer.subscription.created':\n              await this.handleSubscriptionCreated(event.data.object);\n              break;\n            case 'customer.subscription.updated':\n              await this.handleSubscriptionUpdated(event.data.object);\n              break;\n            case 'customer.subscription.deleted':\n              await this.handleSubscriptionDeleted(event.data.object);\n              break;\n            case 'customer.subscription.trial_will_end':\n              await this.handleSubscriptionTrialWillEnd(event.data.object);\n              break;\n\n            // Eventos de pagamento\n            case 'invoice.created':\n              await this.handleInvoiceCreated(event.data.object);\n              break;\n            case 'invoice.paid':\n              await this.handleInvoicePaid(event.data.object);\n              break;\n            case 'invoice.payment_failed':\n              await this.handleInvoicePaymentFailed(event.data.object);\n              break;\n            case 'charge.succeeded':\n              await this.handleChargeSucceeded(event.data.object);\n              break;\n            case 'charge.refunded':\n              await this.handleChargeRefunded(event.data.object);\n              break;\n            case 'checkout.session.completed':\n              await this.handleCheckoutSessionCompleted(event.data.object);\n              break;\n            case 'payment_intent.succeeded':\n              await this.handlePaymentIntentSucceeded(event.data.object);\n              break;\n            case 'subscription_schedule.created':\n              await this.handleSubscriptionScheduleCreated(event.data.object);\n              break;\n\n            default:\n              console.log(`Evento não tratado: ${eventType}`);\n          }\n        } catch (handlerError) {\n          console.error(`Erro ao processar evento ${eventType}:`, handlerError);\n          return {\n            status: 'error',\n            message: `Erro ao processar evento ${eventType}: ${handlerError.message}`,\n          };\n        }\n\n        return {\n          status: 'success',\n          message: 'Webhook processado com sucesso',\n        };\n      } catch (error) {\n        console.error('Erro ao processar webhook:', error);\n        return {\n          status: 'error',\n          message: `Webhook error: ${error.message}`,\n        };\n      }\n    }\n\n    // Manipuladores de eventos de assinatura\n    private async handleSubscriptionCreated(subscription: any) {\n      try {\n        const customerId = subscription.customer;\n        const user = await this.getUserByStripeCustomerId(customerId);\n\n        if (!user) {\n          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);\n          return;\n        }\n\n        console.log(`Processando criação de assinatura para o usuário ${user.id}, customer_id: ${customerId}, subscription_id: ${subscription.id}, status: ${subscription.status}`);\n\n        // Verificar se a assinatura já existe no banco de dados\n        const existingSubscription = await db\n          .selectFrom('users_subscriptions')\n          .where('payment_provider_external_id', '=', subscription.id)\n          .select(['id', 'status'])\n          .executeTakeFirst();\n\n        if (existingSubscription) {\n          console.log(`Assinatura ${subscription.id} já existe no banco de dados com status '${existingSubscription.status}'. Atualizando...`);\n\n          // Forçar status 'active' se a assinatura da Stripe estiver ativa\n          if (subscription.status === 'active' && existingSubscription.status !== 'active') {\n            await db\n              .updateTable('users_subscriptions')\n              .set({\n                status: 'active',\n                updated_at: new Date(),\n              })\n              .where('id', '=', existingSubscription.id)\n              .execute();\n\n            console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);\n          }\n\n          return existingSubscription.id;\n        }\n\n        const planPaymentProviderId = await this.getPlanPaymentProviderIdByPriceId(subscription.items.data[0].price.id);\n\n        if (!planPaymentProviderId) {\n          console.error(`Configuração de plano não encontrada para o price_id: ${subscription.items.data[0].price.id}`);\n          return;\n        }\n\n        const planInfo = await this.getPlanInfoByPlanPaymentProviderId(planPaymentProviderId.id);\n\n        if (!planInfo) {\n          console.error(`Informações do plano não encontradas para o plan_payment_provider_id: ${planPaymentProviderId.id}`);\n          return;\n        }\n\n        // IMPORTANTE: Sempre definir o status como 'active' se a assinatura estiver ativa na Stripe\n        let status: 'pending' | 'active' | 'canceled' | 'paused' | 'expired';\n\n        if (subscription.status === 'active') {\n          status = 'active';\n          console.log(`Assinatura ${subscription.id} está com status 'active' no Stripe, definindo como 'active' no banco de dados`);\n        } else if (subscription.status === 'canceled') {\n          status = 'canceled';\n        } else if (subscription.status === 'past_due') {\n          status = 'paused';\n        } else if (subscription.status === 'unpaid') {\n          status = 'paused';\n        } else if (subscription.status === 'trialing') {\n          status = 'active'; // Consideramos trial como ativo\n        } else if (subscription.status === 'incomplete') {\n          status = 'active'; // MUDANÇA IMPORTANTE: Definir como 'active' mesmo se estiver 'incomplete'\n          console.log(`Assinatura ${subscription.id} está com status 'incomplete' no Stripe, mas definindo como 'active' no banco de dados`);\n        } else {\n          status = 'active'; // Default para active em caso de status desconhecido\n          console.log(`Assinatura ${subscription.id} está com status '${subscription.status}' no Stripe, definindo como 'active' no banco de dados`);\n        }\n\n        // Verificar se é um trial\n        const isTrial = subscription.trial_end !== null;\n\n        // Garantir que temos uma data de início válida\n        let startDate: Date;\n        if (subscription.current_period_start) {\n          startDate = new Date(subscription.current_period_start * 1000);\n        } else if (subscription.created) {\n          startDate = new Date(subscription.created * 1000);\n        } else {\n          startDate = new Date(); // Usar a data atual como fallback\n        }\n\n        // Garantir que temos uma data de próximo faturamento válida\n        let nextBillingDate: Date | null = null;\n        if (subscription.current_period_end) {\n          nextBillingDate = new Date(subscription.current_period_end * 1000);\n        }\n\n        // Inserir na tabela users_subscriptions\n        const subscriptionResult = await db\n          .insertInto('users_subscriptions')\n          .values({\n            user_id: user.id,\n            plan_id: planInfo.plan_id,\n            plan_payment_provider_id: planPaymentProviderId.id,\n            payment_provider_external_id: subscription.id,\n            platform: planPaymentProviderId.platform || 'web',\n            status,\n            price: Number(subscription.items.data[0].price.unit_amount) / 100, // Convertendo de centavos para reais\n            currency: subscription.currency.toUpperCase(),\n            start_date: startDate,\n            end_date: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : null,\n            next_billing_date: nextBillingDate,\n            cancel_at_period_end: subscription.cancel_at_period_end || false,\n            is_trial: isTrial,\n            trial_start_date: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,\n            trial_end_date: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,\n            snaptokens: planInfo.snaptokens,\n            created_at: new Date(),\n            updated_at: new Date(),\n          })\n          .executeTakeFirst();\n\n        console.log(`Assinatura criada com sucesso para o usuário ${user.id} com status '${status}'`);\n\n        return subscriptionResult && 'insertId' in subscriptionResult ? Number(subscriptionResult.insertId) : null;\n      } catch (error) {\n        console.error('Erro ao processar criação de assinatura:', error);\n        throw error;\n      }\n    }\n\n    private async handleSubscriptionUpdated(subscription: any) {\n      try {\n        // Buscar a assinatura existente\n        const existingSubscription = await db\n          .selectFrom('users_subscriptions')\n          .where('payment_provider_external_id', '=', subscription.id)\n          .select(['id', 'user_id', 'plan_id'])\n          .executeTakeFirst();\n\n        if (!existingSubscription) {\n          console.error(`Assinatura não encontrada para atualização: ${subscription.id}`);\n          return;\n        }\n\n        // Determinar o status da assinatura\n        let status: 'pending' | 'active' | 'canceled' | 'paused' | 'expired' = 'pending';\n        if (subscription.status === 'active') {\n          status = 'active';\n        } else if (subscription.status === 'canceled') {\n          status = 'canceled';\n        } else if (subscription.status === 'past_due') {\n          status = 'paused';\n        } else if (subscription.status === 'unpaid') {\n          status = 'paused';\n        } else if (subscription.status === 'trialing') {\n          status = 'active'; // Consideramos trial como ativo\n        } else if (subscription.status === 'incomplete_expired') {\n          status = 'expired';\n        } else if (subscription.status === 'incomplete') {\n          // Mantém como pending até que o pagamento seja processado\n          status = 'pending';\n          console.log(`Assinatura ${subscription.id} está com status 'incomplete' no Stripe, mantendo como 'pending' no banco de dados`);\n        }\n\n        // Garantir que temos uma data de próximo faturamento válida\n        let nextBillingDate: Date | null = null;\n        if (subscription.current_period_end) {\n          nextBillingDate = new Date(subscription.current_period_end * 1000);\n        }\n\n        // Atualizar a assinatura\n        await db\n          .updateTable('users_subscriptions')\n          .set({\n            status,\n            next_billing_date: nextBillingDate,\n            end_date: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : null,\n            cancel_at_period_end: subscription.cancel_at_period_end || false,\n            updated_at: new Date(),\n          })\n          .where('id', '=', existingSubscription.id)\n          .execute();\n\n        console.log(`Assinatura ${existingSubscription.id} atualizada com sucesso para status '${status}'`);\n      } catch (error) {\n        console.error('Erro ao processar atualização de assinatura:', error);\n        throw error;\n      }\n    }\n\n    private async handleSubscriptionDeleted(subscription: any) {\n      try {\n        // Buscar a assinatura existente\n        const existingSubscription = await db\n          .selectFrom('users_subscriptions')\n          .where('payment_provider_external_id', '=', subscription.id)\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (!existingSubscription) {\n          console.error(`Assinatura não encontrada para exclusão: ${subscription.id}`);\n          return;\n        }\n\n        // Atualizar a assinatura para cancelada\n        const status: 'canceled' = 'canceled';\n        await db\n          .updateTable('users_subscriptions')\n          .set({\n            status,\n            end_date: new Date(),\n            updated_at: new Date(),\n          })\n          .where('id', '=', existingSubscription.id)\n          .execute();\n\n        console.log(`Assinatura ${existingSubscription.id} marcada como cancelada`);\n      } catch (error) {\n        console.error('Erro ao processar exclusão de assinatura:', error);\n        throw error;\n      }\n    }\n\n    private async handleSubscriptionTrialWillEnd(subscription: any) {\n      try {\n        // Buscar a assinatura existente\n        const existingSubscription = await db\n          .selectFrom('users_subscriptions')\n          .where('payment_provider_external_id', '=', subscription.id)\n          .select(['id', 'user_id'])\n          .executeTakeFirst();\n\n        if (!existingSubscription) {\n          console.error(`Assinatura não encontrada para notificação de fim de trial: ${subscription.id}`);\n          return;\n        }\n\n        // Aqui você pode implementar lógica para notificar o usuário que o trial está acabando\n        console.log(`Trial da assinatura ${existingSubscription.id} está prestes a terminar para o usuário ${existingSubscription.user_id}`);\n      } catch (error) {\n        console.error('Erro ao processar notificação de fim de trial:', error);\n        throw error;\n      }\n    }\n\n    // Manipuladores de eventos de pagamento\n    private async handleInvoiceCreated(invoice: any) {\n      console.log('Evento invoice.created recebido em handleInvoiceCreated:', invoice.id);\n\n      try {\n        // Verificar se a fatura está relacionada a uma assinatura\n        if (!invoice.subscription) {\n          console.log(`Fatura ${invoice.id} não está relacionada a uma assinatura`);\n          return;\n        }\n\n        const customerId = invoice.customer;\n        const user = await this.getUserByStripeCustomerId(customerId);\n\n        if (!user) {\n          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);\n          return;\n        }\n\n        // Buscar a assinatura relacionada\n        let subscription = await db\n          .selectFrom('users_subscriptions')\n          .where('payment_provider_external_id', '=', invoice.subscription)\n          .select(['id', 'plan_id', 'plan_payment_provider_id'])\n          .executeTakeFirst();\n\n        // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado\n        // Vamos tentar criar a assinatura com base na fatura\n        if (!subscription) {\n          console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);\n\n          try {\n            // Obter a assinatura do Stripe\n            const stripeSubscription = await this.stripe.subscriptions.retrieve(invoice.subscription);\n\n            // Criar a assinatura no banco de dados\n            await this.handleSubscriptionCreated(stripeSubscription);\n\n            // Buscar a assinatura novamente\n            subscription = await db\n              .selectFrom('users_subscriptions')\n              .where('payment_provider_external_id', '=', invoice.subscription)\n              .select(['id', 'plan_id', 'plan_payment_provider_id'])\n              .executeTakeFirst();\n          } catch (error) {\n            console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);\n          }\n        }\n\n        if (!subscription) {\n          console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);\n          return;\n        }\n\n        // Buscar o provedor de pagamento\n        const paymentProvider = await db\n          .selectFrom('plans_payments_providers as ppp')\n          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')\n          .where('ppp.id', '=', subscription.plan_payment_provider_id)\n          .select(['pp.id as payment_provider_id'])\n          .executeTakeFirst();\n\n        if (!paymentProvider) {\n          console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);\n          return;\n        }\n\n        // Verificar se já existe uma transação para esta fatura\n        const existingTransaction = await db\n          .selectFrom('transactions')\n          .where('provider_transaction_id', '=', invoice.id)\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (existingTransaction) {\n          console.log(`Transação já existe para a fatura ${invoice.id}`);\n          return;\n        }\n\n        // Registrar a transação com status pendente\n        await db\n          .insertInto('transactions')\n          .values({\n            user_id: user.id,\n            provider_transaction_id: invoice.id,\n            payment_provider_id: paymentProvider.payment_provider_id,\n            amount: Number(invoice.amount_due) / 100, // Convertendo de centavos para reais\n            currency: invoice.currency.toUpperCase(),\n            status: 'pending', // Status inicial da fatura\n            source_type: 'subscription', // Tipo correto para assinaturas\n            source_id: subscription.id,\n            created_at: new Date(),\n            updated_at: new Date(),\n          })\n          .execute();\n\n        console.log(`Transação pendente registrada com sucesso para a fatura ${invoice.id}`);\n      } catch (error) {\n        console.error('Erro ao processar criação de fatura:', error);\n        throw error;\n      }\n    }\n    private async handleInvoicePaid(invoice: any) {\n  console.log('Evento invoice.paid recebido em handleInvoicePaid:', invoice.id);\n  try {\n    const customerId = invoice.customer;\n    const user = await this.getUserByStripeCustomerId(customerId);\n\n    if (!user) {\n      console.error(`Usuário não encontrado para o customer_id: ${customerId}`);\n      return;\n    }\n\n    console.log(`Processando fatura paga para o usuário ${user.id}, customer_id: ${customerId}, invoice_id: ${invoice.id}`);\n\n    // Extrair subscription_id do parent.subscription_details (novo formato)\n    let subscriptionId = null;\n    if (invoice.parent?.subscription_details?.subscription) {\n      subscriptionId = invoice.parent.subscription_details.subscription;\n      console.log(`Subscription ID extraído do parent: ${subscriptionId}`);\n    } else if (invoice.subscription) {\n      // Fallback para o formato antigo\n      subscriptionId = invoice.subscription;\n      console.log(`Subscription ID extraído do campo subscription: ${subscriptionId}`);\n    }\n\n    if (!subscriptionId) {\n      console.error(`Nenhum subscription_id encontrado na fatura: ${invoice.id}`);\n      return;\n    }\n\n    // Buscar a assinatura relacionada\n    console.log(`Buscar assinatura relacionada à fatura ${invoice.id}: ${subscriptionId}`);\n    let subscription = await db\n      .selectFrom('users_subscriptions')\n      .where('payment_provider_external_id', '=', subscriptionId)\n      .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])\n      .executeTakeFirst();\n\n    // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado\n    // Vamos tentar criar a assinatura com base na fatura\n    if (!subscription && subscriptionId) {\n      console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);\n\n      try {\n        // Obter a assinatura do Stripe\n        const stripeSubscription = await this.stripe.subscriptions.retrieve(subscriptionId);\n        console.log(`Assinatura obtida da Stripe: ${stripeSubscription.id}, status: ${stripeSubscription.status}`);\n\n        // Criar a assinatura no banco de dados\n        const newSubscriptionId = await this.handleSubscriptionCreated(stripeSubscription);\n\n        if (newSubscriptionId) {\n          // Buscar a assinatura novamente\n          subscription = await db\n            .selectFrom('users_subscriptions')\n            .where('id', '=', Number(newSubscriptionId))\n            .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])\n            .executeTakeFirst();\n        } else {\n          // Tentar buscar por payment_provider_external_id\n          subscription = await db\n            .selectFrom('users_subscriptions')\n            .where('payment_provider_external_id', '=', subscriptionId)\n            .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])\n            .executeTakeFirst();\n        }\n      } catch (error) {\n        console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);\n      }\n    }\n\n    if (!subscription) {\n      console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);\n      return;\n    }\n\n    console.log(`Assinatura encontrada: ${subscription.id}, status atual: ${subscription.status}`);\n\n    // Buscar o provedor de pagamento\n    const paymentProvider = await db\n      .selectFrom('plans_payments_providers as ppp')\n      .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')\n      .where('ppp.id', '=', subscription.plan_payment_provider_id)\n      .select(['pp.id as payment_provider_id'])\n      .executeTakeFirst();\n\n    if (!paymentProvider) {\n      console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);\n      return;\n    }\n\n    // Verificar se já existe uma transação para esta fatura\n    let existingTransaction = await db\n      .selectFrom('transactions')\n      .where('provider_transaction_id', '=', invoice.id)\n      .select(['id', 'status'])\n      .executeTakeFirst();\n\n    let transactionId: number | null = null;\n\n    if (existingTransaction) {\n      // Atualizar a transação existente apenas se não estiver paga\n      if (existingTransaction.status !== 'paid') {\n        await db\n          .updateTable('transactions')\n          .set({\n            amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais\n            status: 'paid',\n            updated_at: new Date(),\n          })\n          .where('id', '=', existingTransaction.id)\n          .execute();\n\n        console.log(`Transação ${existingTransaction.id} atualizada para status 'paid'`);\n      } else {\n        console.log(`Transação ${existingTransaction.id} já está com status 'paid'`);\n      }\n      transactionId = existingTransaction.id;\n    } else {\n      // Registrar uma nova transação\n      const result = await db\n        .insertInto('transactions')\n        .values({\n          user_id: user.id,\n          provider_transaction_id: invoice.id,\n          payment_provider_id: paymentProvider.payment_provider_id,\n          amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais\n          currency: invoice.currency.toUpperCase(),\n          status: 'paid',\n          source_type: 'subscription', // Tipo correto para assinaturas\n          source_id: subscription.id,\n          created_at: new Date(),\n          updated_at: new Date(),\n        })\n        .executeTakeFirst();\n\n      console.log(`Nova transação registrada com sucesso para a fatura ${invoice.id}`);\n\n      if (result && result.insertId) {\n        transactionId = Number(result.insertId);\n\n        // Buscar a transação recém-criada para ter certeza\n        existingTransaction = await db\n          .selectFrom('transactions')\n          .where('id', '=', transactionId)\n          .select(['id', 'status'])\n          .executeTakeFirst();\n\n        console.log(`Transação criada com ID: ${transactionId}`);\n      }\n    }\n\n    // Atualizar o status da assinatura para ativo\n    console.log(`Status atual da assinatura ${subscription.id}: ${subscription.status}`);\n\n    if (subscription.status !== 'active') {\n      await db\n        .updateTable('users_subscriptions')\n        .set({\n          status: 'active',\n          updated_at: new Date(),\n        })\n        .where('id', '=', subscription.id)\n        .execute();\n\n      console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);\n\n      // Verificar se a atualização foi bem-sucedida\n      const updatedSubscription = await db\n        .selectFrom('users_subscriptions')\n        .where('id', '=', subscription.id)\n        .select(['status'])\n        .executeTakeFirst();\n\n      console.log(`Status da assinatura ${subscription.id} após atualização: ${updatedSubscription?.status}`);\n    } else {\n      console.log(`Assinatura ${subscription.id} já está com status 'active', não é necessário atualizar`);\n    }\n\n    console.log(`Transação registrada e assinatura processada com sucesso para a fatura ${invoice.id}`);\n\n    // Registrar comissões de afiliados\n    // Verificar se a fatura está paga usando o status correto\n    if (invoice.status === 'paid' && transactionId) {\n      console.log(`Fatura ${invoice.id} está paga, registrando comissões...`);\n\n      // Chamar o método específico para registrar comissões\n      await this.registerAffiliateCommissions(\n        user.id,\n        subscription.plan_id,\n        transactionId,\n        Number(invoice.amount_paid) / 100,\n        invoice.currency.toUpperCase()\n      );\n\n      console.log(`Comissões registradas com sucesso para a fatura ${invoice.id}`);\n    } else {\n      console.log(`Fatura ${invoice.id} não está paga ou não tem transação associada, não processando comissões`);\n    }\n\n  } catch (error) {\n    console.error('Erro ao processar pagamento de fatura:', error);\n    throw error;\n  }\n}\n\n    private async handleInvoicePaidOld(invoice: any) {\n      console.log('Evento invoice.paid recebido em handleInvoicePaid:', invoice.id);\n      try {\n        const customerId = invoice.customer;\n        const user = await this.getUserByStripeCustomerId(customerId);\n\n        if (!user) {\n          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);\n          return;\n        }\n\n        console.log(`Processando fatura paga para o usuário ${user.id}, customer_id: ${customerId}, invoice_id: ${invoice.id}`);\n\n        // Buscar a assinatura relacionada\n        console.log(`Buscar assinatura relacionada à fatura ${invoice.id}: ${invoice.subscription}`);\n        console.log(invoice);\n        let subscription = await db\n          .selectFrom('users_subscriptions')\n          .where('payment_provider_external_id', '=', invoice.subscription)\n          .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])\n          .executeTakeFirst();\n          \n\n        // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado\n        // Vamos tentar criar a assinatura com base na fatura\n        if (!subscription && invoice.subscription) {\n          console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);\n\n          try {\n            // Obter a assinatura do Stripe\n            const stripeSubscription = await this.stripe.subscriptions.retrieve(invoice.subscription);\n            console.log(`Assinatura obtida da Stripe: ${stripeSubscription.id}, status: ${stripeSubscription.status}`);\n\n            // Criar a assinatura no banco de dados\n            const subscriptionId = await this.handleSubscriptionCreated(stripeSubscription);\n\n            if (subscriptionId) {\n              // Buscar a assinatura novamente\n              subscription = await db\n                .selectFrom('users_subscriptions')\n                .where('id', '=', Number(subscriptionId))\n                .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])\n                .executeTakeFirst();\n            } else {\n              // Tentar buscar por payment_provider_external_id\n              subscription = await db\n                .selectFrom('users_subscriptions')\n                .where('payment_provider_external_id', '=', invoice.subscription)\n                .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])\n                .executeTakeFirst();\n            }\n          } catch (error) {\n            console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);\n          }\n        }\n\n        if (!subscription) {\n          console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);\n          return;\n        }\n\n        console.log(`Assinatura encontrada: ${subscription.id}, status atual: ${subscription.status}`);\n\n        // IMPORTANTE: Sempre atualizar o status para 'active' quando a fatura for paga\n        if (subscription.status !== 'active') {\n          await db\n            .updateTable('users_subscriptions')\n            .set({\n              status: 'active',\n              updated_at: new Date(),\n            })\n            .where('id', '=', subscription.id)\n            .execute();\n\n          console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);\n\n          // Verificar se a atualização foi bem-sucedida\n          const updatedSubscription = await db\n            .selectFrom('users_subscriptions')\n            .where('id', '=', subscription.id)\n            .select(['status'])\n            .executeTakeFirst();\n\n          console.log(`Status da assinatura ${subscription.id} após atualização: ${updatedSubscription?.status}`);\n        }\n\n        // Buscar o provedor de pagamento\n        const paymentProvider = await db\n          .selectFrom('plans_payments_providers as ppp')\n          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')\n          .where('ppp.id', '=', subscription.plan_payment_provider_id)\n          .select(['pp.id as payment_provider_id'])\n          .executeTakeFirst();\n\n        if (!paymentProvider) {\n          console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);\n          return;\n        }\n\n        // Verificar se já existe uma transação para esta fatura\n        let existingTransaction = await db\n          .selectFrom('transactions')\n          .where('provider_transaction_id', '=', invoice.id)\n          .select(['id', 'status'])\n          .executeTakeFirst();\n\n        let transactionId: number | null = null;\n\n        if (existingTransaction) {\n          // Atualizar a transação existente apenas se não estiver paga\n          if (existingTransaction.status !== 'paid') {\n            await db\n              .updateTable('transactions')\n              .set({\n                amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais\n                status: 'paid',\n                updated_at: new Date(),\n              })\n              .where('id', '=', existingTransaction.id)\n              .execute();\n\n            console.log(`Transação ${existingTransaction.id} atualizada para status 'paid'`);\n          } else {\n            console.log(`Transação ${existingTransaction.id} já está com status 'paid'`);\n          }\n          transactionId = existingTransaction.id;\n        } else {\n          // Registrar uma nova transação\n          const result = await db\n            .insertInto('transactions')\n            .values({\n              user_id: user.id,\n              provider_transaction_id: invoice.id,\n              payment_provider_id: paymentProvider.payment_provider_id,\n              amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais\n              currency: invoice.currency.toUpperCase(),\n              status: 'paid',\n              source_type: 'subscription', // Tipo correto para assinaturas\n              source_id: subscription.id,\n              created_at: new Date(),\n              updated_at: new Date(),\n            })\n            .executeTakeFirst();\n\n          console.log(`Nova transação registrada com sucesso para a fatura ${invoice.id}`);\n\n          if (result && result.insertId) {\n            transactionId = Number(result.insertId);\n\n            // Buscar a transação recém-criada para ter certeza\n            existingTransaction = await db\n              .selectFrom('transactions')\n              .where('id', '=', transactionId)\n              .select(['id', 'status'])\n              .executeTakeFirst();\n\n            console.log(`Transação criada com ID: ${transactionId}`);\n          }\n        }\n\n        // Verificar o status atual da assinatura\n        const currentSubscription = await db\n          .selectFrom('users_subscriptions')\n          .where('id', '=', subscription.id)\n          .select(['status'])\n          .executeTakeFirst();\n\n        // Atualizar o status da assinatura para ativo apenas se não estiver já ativo\n        if (currentSubscription) {\n          console.log(`Status atual da assinatura ${subscription.id}: ${currentSubscription.status}`);\n\n          if (currentSubscription.status !== 'active') {\n            const status: 'active' = 'active';\n            await db\n              .updateTable('users_subscriptions')\n              .set({\n                status,\n                updated_at: new Date(),\n              })\n              .where('id', '=', subscription.id)\n              .execute();\n\n            console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);\n\n            // Verificar se a atualização foi bem-sucedida\n            const updatedSubscription = await db\n              .selectFrom('users_subscriptions')\n              .where('id', '=', subscription.id)\n              .select(['status'])\n              .executeTakeFirst();\n\n            console.log(`Status da assinatura ${subscription.id} após atualização: ${updatedSubscription?.status}`);\n          } else {\n            console.log(`Assinatura ${subscription.id} já está com status 'active', não é necessário atualizar`);\n          }\n        } else {\n          console.error(`Não foi possível encontrar a assinatura ${subscription.id} para atualizar o status`);\n        }\n\n        console.log(`Transação registrada e assinatura processada com sucesso para a fatura ${subscription.id}`);\n\n        // Registrar comissões de afiliados\n        if(invoice.paid && transactionId){\n          console.log(`Fatura ${invoice.id} está paga, registrando comissões...`);\n\n          // Chamar o método específico para registrar comissões\n          await this.registerAffiliateCommissions(\n            user.id,\n            subscription.plan_id,\n            transactionId,\n            Number(invoice.amount_paid) / 100,\n            invoice.currency.toUpperCase()\n          );\n\n          console.log(`Comissões registradas com sucesso para a fatura ${invoice.id}`);\n        } else {\n          console.log(`Fatura ${invoice.id} não está paga ou não tem transação associada, não processando comissões`);\n        }\n\n\n\n\n      } catch (error) {\n        console.error('Erro ao processar pagamento de fatura:', error);\n        throw error;\n      }\n    }\n\n    private async getAffiliatesData(aff_id: number) {\n      console.log(`Buscando dados do afiliado com ID: ${aff_id}`);\n\n      if (!aff_id) {\n        console.log('ID do afiliado é nulo ou indefinido');\n        return {\n          affiliate: null,\n          affiliate_master: null,\n          affiliate_master_user_id: null\n        };\n      }\n\n      // Verificar se o usuário existe\n      const user = await db.selectFrom('users')\n        .where('id', '=', aff_id)\n        .select(['id', 'email'])\n        .executeTakeFirst();\n\n      if (!user) {\n        console.log(`Usuário com ID ${aff_id} não encontrado`);\n        return {\n          affiliate: null,\n          affiliate_master: null,\n          affiliate_master_user_id: null\n        };\n      }\n\n      console.log(`Usuário afiliado encontrado: ID=${user.id}, Email=${user.email}`);\n\n      // Buscar o registro de afiliado\n      const affiliate = await db.selectFrom('affiliates')\n        .where('user_id', '=', aff_id)\n        .select(['id', 'stripeId', 'ref_user_id', 'user_id', 'status'])\n        .executeTakeFirst();\n\n      if (!affiliate) {\n        console.log(`Registro de afiliado para o usuário ${aff_id} não encontrado. Criando registro...`);\n\n        // Criar um registro de afiliado para este usuário\n        try {\n          await db.insertInto('affiliates')\n            .values({\n              status: 'active',\n              user_id: aff_id,\n              ref_user_id: 0, // Valor padrão para ref_user_id\n              invite: '', // Valor padrão para invite\n              created_at: new Date(),\n              updated_at: new Date(),\n            })\n            .executeTakeFirst();\n\n          console.log(`Registro de afiliado criado com sucesso para o usuário ${aff_id}`);\n\n          return {\n            affiliate: null, // Sem conta Stripe ainda\n            affiliate_master: null,\n            affiliate_master_user_id: null\n          };\n        } catch (error) {\n          console.error(`Erro ao criar registro de afiliado para o usuário ${aff_id}:`, error);\n          return {\n            affiliate: null,\n            affiliate_master: null,\n            affiliate_master_user_id: null\n          };\n        }\n      }\n\n      console.log(`Afiliado encontrado: ${JSON.stringify(affiliate)}`);\n\n      // Verificar se o afiliado está ativo\n      if (affiliate.status !== 'active') {\n        console.log(`Afiliado ${aff_id} não está ativo (status: ${affiliate.status}). Atualizando para ativo...`);\n\n        // Atualizar o status para ativo\n        try {\n          await db.updateTable('affiliates')\n            .set({\n              status: 'active',\n              updated_at: new Date(),\n            })\n            .where('id', '=', affiliate.id)\n            .execute();\n\n          console.log(`Status do afiliado ${aff_id} atualizado para 'active'`);\n        } catch (error) {\n          console.error(`Erro ao atualizar status do afiliado ${aff_id}:`, error);\n        }\n      }\n\n      let affiliate_master: any = null;\n      if(affiliate.ref_user_id) {\n        affiliate_master = await db.selectFrom('affiliates')\n          .where('user_id', '=', affiliate.ref_user_id)\n          .select(['id', 'stripeId', 'user_id', 'status'])\n          .executeTakeFirst();\n\n        console.log(`Afiliado master encontrado: ${JSON.stringify(affiliate_master)}`);\n\n        // Verificar se o afiliado master está ativo\n        if (affiliate_master && affiliate_master.status !== 'active') {\n          console.log(`Afiliado master ${affiliate.ref_user_id} não está ativo (status: ${affiliate_master.status}). Atualizando para ativo...`);\n\n          // Atualizar o status para ativo\n          try {\n            await db.updateTable('affiliates')\n              .set({\n                status: 'active',\n                updated_at: new Date(),\n              })\n              .where('id', '=', affiliate_master.id)\n              .execute();\n\n            console.log(`Status do afiliado master ${affiliate.ref_user_id} atualizado para 'active'`);\n          } catch (error) {\n            console.error(`Erro ao atualizar status do afiliado master ${affiliate.ref_user_id}:`, error);\n          }\n        }\n      }\n\n      return {\n        affiliate: affiliate?.stripeId || null,\n        affiliate_master: affiliate_master?.stripeId || null,\n        affiliate_master_user_id: affiliate.ref_user_id || null\n      }\n    }\n\n    private async getPlanComissionsData(plan_id: number) {\n      console.log(`Buscando dados de comissão para o plano com ID: ${plan_id}`);\n\n      if (!plan_id) {\n        console.log('ID do plano é nulo ou indefinido');\n        return {\n          affiliate: 0,\n          affiliate_percent: 0,\n          affiliate_master: 0,\n          affiliate_master_percent: 0\n        };\n      }\n\n      const comission = await db.selectFrom('plans')\n        .where('id', '=', plan_id)\n        .select(['price','affiliate_master_commission_percent', 'affiliate_commission_percent'])\n        .executeTakeFirst();\n\n      if (!comission) {\n        console.log(`Plano com ID ${plan_id} não encontrado. Usando valores padrão.`);\n        return {\n          affiliate: 0,\n          affiliate_percent: 10, // Valor padrão de 10%\n          affiliate_master: 0,\n          affiliate_master_percent: 5 // Valor padrão de 5%\n        };\n      }\n\n      console.log(`Dados do plano encontrados: ${JSON.stringify(comission)}`);\n\n      // Verificar se os percentuais de comissão estão definidos\n      const affiliatePercent = comission.affiliate_commission_percent ? Number(comission.affiliate_commission_percent) : 10; // Valor padrão de 10%\n      const affiliateMasterPercent = comission.affiliate_master_commission_percent ? Number(comission.affiliate_master_commission_percent) : 5; // Valor padrão de 5%\n\n      // Calcular os valores de comissão\n      const price = Number(comission.price || 0);\n      const affiliateValue = price * (affiliatePercent / 100);\n      const affiliateMasterValue = price * (affiliateMasterPercent / 100);\n\n      console.log(`Valores de comissão calculados:\n        - Afiliado: ${affiliateValue} (${affiliatePercent}%)\n        - Afiliado Master: ${affiliateMasterValue} (${affiliateMasterPercent}%)`);\n\n      return {\n          affiliate: affiliateValue,\n          affiliate_percent: affiliatePercent,\n          affiliate_master: affiliateMasterValue,\n          affiliate_master_percent: affiliateMasterPercent,\n      }\n    }\n\n    private async handleInvoicePaymentFailed(invoice: any) {\n      try {\n        const customerId = invoice.customer;\n        const user = await this.getUserByStripeCustomerId(customerId);\n\n        if (!user) {\n          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);\n          return;\n        }\n\n        // Buscar a assinatura relacionada\n        let subscription = await db\n          .selectFrom('users_subscriptions')\n          .where('payment_provider_external_id', '=', invoice.subscription)\n          .select(['id', 'plan_id', 'plan_payment_provider_id'])\n          .executeTakeFirst();\n\n        // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado\n        // Vamos tentar criar a assinatura com base na fatura\n        if (!subscription && invoice.subscription) {\n          console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);\n\n          try {\n            // Obter a assinatura do Stripe\n            const stripeSubscription = await this.stripe.subscriptions.retrieve(invoice.subscription);\n\n            // Criar a assinatura no banco de dados\n            await this.handleSubscriptionCreated(stripeSubscription);\n\n            // Buscar a assinatura novamente\n            subscription = await db\n              .selectFrom('users_subscriptions')\n              .where('payment_provider_external_id', '=', invoice.subscription)\n              .select(['id', 'plan_id', 'plan_payment_provider_id'])\n              .executeTakeFirst();\n          } catch (error) {\n            console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);\n          }\n        }\n\n        if (!subscription) {\n          console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);\n          return;\n        }\n\n        // Buscar o provedor de pagamento\n        const paymentProvider = await db\n          .selectFrom('plans_payments_providers as ppp')\n          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')\n          .where('ppp.id', '=', subscription.plan_payment_provider_id)\n          .select(['pp.id as payment_provider_id'])\n          .executeTakeFirst();\n\n        if (!paymentProvider) {\n          console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);\n          return;\n        }\n\n        // Verificar se já existe uma transação para esta fatura\n        const existingTransaction = await db\n          .selectFrom('transactions')\n          .where('provider_transaction_id', '=', invoice.id)\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (existingTransaction) {\n          // Atualizar a transação existente\n          await db\n            .updateTable('transactions')\n            .set({\n              amount: Number(invoice.amount_due) / 100, // Convertendo de centavos para reais\n              status: 'failed',\n              updated_at: new Date(),\n            })\n            .where('id', '=', existingTransaction.id)\n            .execute();\n\n          console.log(`Transação ${existingTransaction.id} atualizada para status 'failed'`);\n        } else {\n          // Registrar uma nova transação falha\n          await db\n            .insertInto('transactions')\n            .values({\n              user_id: user.id,\n              provider_transaction_id: invoice.id,\n              payment_provider_id: paymentProvider.payment_provider_id,\n              amount: Number(invoice.amount_due) / 100, // Convertendo de centavos para reais\n              currency: invoice.currency.toUpperCase(),\n              status: 'failed',\n              source_type: 'subscription', // Tipo correto para assinaturas\n              source_id: subscription.id,\n              created_at: new Date(),\n              updated_at: new Date(),\n            })\n            .execute();\n\n          console.log(`Nova transação falha registrada para a fatura ${invoice.id}`);\n        }\n\n        // Atualizar o status da assinatura para pausada\n        const status: 'paused' = 'paused';\n        await db\n          .updateTable('users_subscriptions')\n          .set({\n            status,\n            updated_at: new Date(),\n          })\n          .where('id', '=', subscription.id)\n          .execute();\n\n        console.log(`Falha de pagamento registrada para a fatura ${invoice.id}`);\n      } catch (error) {\n        console.error('Erro ao processar falha de pagamento:', error);\n        throw error;\n      }\n    }\n\n    // Manipulador para pagamentos bem-sucedidos (não relacionados a assinaturas)\n    private async handleChargeSucceeded(charge: any) {\n      try {\n        // Verificar se a cobrança está relacionada a uma fatura\n        if (charge.invoice) {\n          console.log(`Cobrança ${charge.id} está relacionada a uma fatura. Será processada pelo evento invoice.paid`);\n          return;\n        }\n\n        const customerId = charge.customer;\n        if (!customerId) {\n          console.error(`Cobrança ${charge.id} não tem customer_id associado`);\n          return;\n        }\n\n        const user = await this.getUserByStripeCustomerId(customerId);\n\n        if (!user) {\n          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);\n          return;\n        }\n\n        // Buscar o provedor de pagamento (Stripe)\n        const paymentProvider = await db\n          .selectFrom('payment_providers')\n          .where('name', '=', 'Stripe')\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (!paymentProvider) {\n          console.error('Provedor de pagamento Stripe não encontrado');\n          return;\n        }\n\n        // Verificar se já existe uma transação para esta cobrança\n        const existingTransaction = await db\n          .selectFrom('transactions')\n          .where('provider_transaction_id', '=', charge.id)\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (existingTransaction) {\n          console.log(`Transação já existe para a cobrança ${charge.id}`);\n          return;\n        }\n\n        // Registrar a transação\n        await db\n          .insertInto('transactions')\n          .values({\n            user_id: user.id,\n            provider_transaction_id: charge.id,\n            payment_provider_id: paymentProvider.id,\n            amount: Number(charge.amount) / 100, // Convertendo de centavos para reais\n            currency: charge.currency.toUpperCase(),\n            status: 'paid',\n            source_type: 'invoice_item', // Compra avulsa\n            source_id: 0, // Não está associado a uma assinatura\n            created_at: new Date(charge.created * 1000),\n            updated_at: new Date(),\n          })\n          .execute();\n\n        console.log(`Transação avulsa registrada com sucesso para a cobrança ${charge.id}`);\n      } catch (error) {\n        console.error('Erro ao processar cobrança bem-sucedida:', error);\n        throw error;\n      }\n    }\n\n    // Manipulador para reembolsos\n    private async handleChargeRefunded(charge: any) {\n      try {\n        // Buscar a transação relacionada\n        const transaction = await db\n          .selectFrom('transactions')\n          .where('provider_transaction_id', '=', charge.id)\n          .select(['id', 'user_id', 'payment_provider_id', 'source_type', 'source_id'])\n          .executeTakeFirst();\n\n        if (!transaction) {\n          console.error(`Transação não encontrada para a cobrança: ${charge.id}`);\n          return;\n        }\n\n        // Verificar se o reembolso é total ou parcial\n        const isFullRefund = charge.refunded;\n\n        if (isFullRefund) {\n          // Atualizar o status da transação para reembolsada\n          await db\n            .updateTable('transactions')\n            .set({\n              status: 'refunded',\n              updated_at: new Date(),\n            })\n            .where('id', '=', transaction.id)\n            .execute();\n\n          console.log(`Transação ${transaction.id} marcada como reembolsada`);\n\n          // Se for uma assinatura, atualizar o status da assinatura para cancelada\n          if (transaction.source_type === 'subscription' && transaction.source_id) {\n            const status: 'canceled' = 'canceled';\n            await db\n              .updateTable('users_subscriptions')\n              .set({\n                status,\n                end_date: new Date(),\n                updated_at: new Date(),\n              })\n              .where('id', '=', transaction.source_id)\n              .execute();\n\n            console.log(`Assinatura ${transaction.source_id} marcada como cancelada devido a reembolso`);\n          }\n        } else {\n          // Para reembolsos parciais, podemos criar uma nova transação com status 'refunded'\n          // e o valor negativo do reembolso\n          const refundAmount = Number(charge.amount_refunded) / 100;\n\n          await db\n            .insertInto('transactions')\n            .values({\n              user_id: transaction.user_id,\n              provider_transaction_id: `${charge.id}_refund`,\n              payment_provider_id: transaction.payment_provider_id,\n              amount: -refundAmount, // Valor negativo para indicar reembolso\n              currency: charge.currency.toUpperCase(),\n              status: 'refunded',\n              source_type: transaction.source_type,\n              source_id: transaction.source_id,\n              created_at: new Date(),\n              updated_at: new Date(),\n            })\n            .execute();\n\n          console.log(`Reembolso parcial registrado para a transação ${transaction.id}`);\n        }\n      } catch (error) {\n        console.error('Erro ao processar reembolso:', error);\n        throw error;\n      }\n    }\n\n    // Manipulador para pagamentos bem-sucedidos via PaymentIntent\n    private async handlePaymentIntentSucceeded(paymentIntent: any) {\n      try {\n        console.log(`PaymentIntent ${paymentIntent.id} foi bem-sucedido`);\n\n        // Verificar se o PaymentIntent está relacionado a uma assinatura\n        // Isso pode ser verificado através dos metadados ou da fatura associada\n        if (paymentIntent.invoice) {\n          try {\n            // Buscar a fatura para obter a assinatura relacionada\n            const invoice = await this.stripe.invoices.retrieve(paymentIntent.invoice);\n\n            // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente\n            if (invoice.subscription) {\n              // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente\n              console.log(`PaymentIntent ${paymentIntent.id} está relacionado à assinatura ${invoice.subscription}`);\n\n              // Buscar a assinatura no banco de dados\n              const subscription = await db\n                .selectFrom('users_subscriptions')\n                // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente\n                .where('payment_provider_external_id', '=', invoice.subscription)\n                .select(['id', 'status'])\n                .executeTakeFirst();\n\n              if (subscription) {\n                // Se a assinatura estiver com status pendente, atualizá-la para ativa\n                if (subscription.status === 'pending') {\n                  const status: 'active' = 'active';\n                  await db\n                    .updateTable('users_subscriptions')\n                    .set({\n                      status,\n                      updated_at: new Date(),\n                    })\n                    .where('id', '=', subscription.id)\n                    .execute();\n\n                  console.log(`Assinatura ${subscription.id} ativada após pagamento bem-sucedido via PaymentIntent ${paymentIntent.id}`);\n                }\n              }\n            }\n          } catch (error) {\n            console.error(`Erro ao processar PaymentIntent relacionado à fatura: ${error.message}`);\n          }\n        }\n      } catch (error) {\n        console.error('Erro ao processar PaymentIntent bem-sucedido:', error);\n        throw error;\n      }\n    }\n\n    // Manipulador para agendamentos de assinatura\n    private async handleSubscriptionScheduleCreated(schedule: any) {\n      try {\n        console.log(`SubscriptionSchedule ${schedule.id} foi criado`);\n\n        // Se o agendamento tiver uma assinatura atual, verificar e atualizar seu status\n        if (schedule.subscription) {\n          // Buscar a assinatura no banco de dados\n          const subscription = await db\n            .selectFrom('users_subscriptions')\n            .where('payment_provider_external_id', '=', schedule.subscription)\n            .select(['id', 'status'])\n            .executeTakeFirst();\n\n          if (subscription && subscription.status === 'pending') {\n            // Obter a assinatura do Stripe para verificar seu status atual\n            const stripeSubscription = await this.stripe.subscriptions.retrieve(schedule.subscription);\n\n            if (stripeSubscription.status === 'active') {\n              const status: 'active' = 'active';\n              await db\n                .updateTable('users_subscriptions')\n                .set({\n                  status,\n                  updated_at: new Date(),\n                })\n                .where('id', '=', subscription.id)\n                .execute();\n\n              console.log(`Assinatura ${subscription.id} ativada após criação de agendamento`);\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Erro ao processar criação de agendamento de assinatura:', error);\n        throw error;\n      }\n    }\n\n    // Manipulador para sessões de checkout concluídas\n    private async handleCheckoutSessionCompleted(session: any) {\n      try {\n        // Verificar se a sessão está relacionada a uma assinatura\n        if (session.mode === 'subscription') {\n          console.log(`Sessão de checkout ${session.id} é para uma assinatura. Verificando se precisamos atualizar o status...`);\n\n          // Se tiver subscription, vamos verificar e atualizar o status da assinatura\n          if (session.subscription) {\n            try {\n              // Buscar a assinatura no banco de dados\n              const subscription = await db\n                .selectFrom('users_subscriptions')\n                .where('payment_provider_external_id', '=', session.subscription)\n                .select(['id', 'status', 'user_id'])\n                .executeTakeFirst();\n\n              if (subscription) {\n                // Se a assinatura estiver com status diferente de ativo, atualizá-la\n                if (subscription.status !== 'active') {\n                  const status: 'active' = 'active';\n                  await db\n                    .updateTable('users_subscriptions')\n                    .set({\n                      status,\n                      updated_at: new Date(),\n                    })\n                    .where('id', '=', subscription.id)\n                    .execute();\n\n                  console.log(`Assinatura ${subscription.id} ativada após conclusão do checkout`);\n                }\n\n                // Verificar se já existe uma transação para esta sessão\n                const existingTransaction = await db\n                  .selectFrom('transactions')\n                  .where('provider_transaction_id', '=', session.id)\n                  .select(['id'])\n                  .executeTakeFirst();\n\n                if (!existingTransaction) {\n                  // Buscar o provedor de pagamento (Stripe)\n                  const paymentProvider = await db\n                    .selectFrom('payment_providers')\n                    .where('name', '=', 'Stripe')\n                    .select(['id'])\n                    .executeTakeFirst();\n\n                  if (paymentProvider) {\n                    // Registrar a transação como assinatura\n                    await db\n                      .insertInto('transactions')\n                      .values({\n                        user_id: subscription.user_id,\n                        provider_transaction_id: session.id,\n                        payment_provider_id: paymentProvider.id,\n                        amount: Number(session.amount_total) / 100, // Convertendo de centavos para reais\n                        currency: session.currency.toUpperCase(),\n                        status: 'paid',\n                        source_type: 'subscription', // Tipo correto para assinaturas\n                        source_id: subscription.id, // ID da assinatura\n                        created_at: new Date(session.created * 1000),\n                        updated_at: new Date(),\n                      })\n                      .execute();\n\n                    console.log(`Transação de assinatura registrada com sucesso para a sessão de checkout ${session.id}`);\n                  }\n                }\n              }\n            } catch (error) {\n              console.error(`Erro ao atualizar status da assinatura após checkout: ${error.message}`);\n            }\n          }\n\n          return;\n        }\n\n        const customerId = session.customer;\n        if (!customerId) {\n          console.error(`Sessão de checkout ${session.id} não tem customer_id associado`);\n          return;\n        }\n\n        const user = await this.getUserByStripeCustomerId(customerId);\n\n        if (!user) {\n          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);\n          return;\n        }\n\n        // Buscar o provedor de pagamento (Stripe)\n        const paymentProvider = await db\n          .selectFrom('payment_providers')\n          .where('name', '=', 'Stripe')\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (!paymentProvider) {\n          console.error('Provedor de pagamento Stripe não encontrado');\n          return;\n        }\n\n        // Verificar se já existe uma transação para esta sessão\n        const existingTransaction = await db\n          .selectFrom('transactions')\n          .where('provider_transaction_id', '=', session.id)\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (existingTransaction) {\n          console.log(`Transação já existe para a sessão de checkout ${session.id}`);\n          return;\n        }\n\n        // Registrar a transação\n        await db\n          .insertInto('transactions')\n          .values({\n            user_id: user.id,\n            provider_transaction_id: session.id,\n            payment_provider_id: paymentProvider.id,\n            amount: Number(session.amount_total) / 100, // Convertendo de centavos para reais\n            currency: session.currency.toUpperCase(),\n            status: 'paid',\n            source_type: 'invoice_item', // Compra avulsa\n            source_id: 0, // Não está associado a uma assinatura\n            created_at: new Date(session.created * 1000),\n            updated_at: new Date(),\n          })\n          .execute();\n\n        console.log(`Transação avulsa registrada com sucesso para a sessão de checkout ${session.id}`);\n      } catch (error) {\n        console.error('Erro ao processar sessão de checkout concluída:', error);\n        throw error;\n      }\n    }\n\n    // Métodos auxiliares\n    private async getUserByStripeCustomerId(customerId: string) {\n      const user = await db\n        .selectFrom('users')\n        .where('stripeId', '=', customerId)\n        .select(['id', 'email', 'aff_id'])\n        .executeTakeFirst();\n\n      if (user) {\n        console.log(`Usuário encontrado para o customer_id ${customerId}: ID=${user.id}, Email=${user.email}, Aff_ID=${user.aff_id}`);\n      } else {\n        console.log(`Nenhum usuário encontrado para o customer_id ${customerId}`);\n      }\n\n      return user;\n    }\n\n    private async getPlanPaymentProviderIdByPriceId(priceId: string) {\n      return db\n        .selectFrom('plans_payments_providers')\n        .where('payment_provider_external_id', '=', priceId)\n        .select(['id', 'plan_id', 'platform'])\n        .executeTakeFirst();\n    }\n\n    private async getPlanInfoByPlanPaymentProviderId(planPaymentProviderId: number) {\n      return db\n        .selectFrom('plans_payments_providers as ppp')\n        .innerJoin('plans as p', 'ppp.plan_id', 'p.id')\n        .where('ppp.id', '=', planPaymentProviderId)\n        .select(['p.id as plan_id', 'p.snaptokens'])\n        .executeTakeFirst();\n    }\n\n    // Método específico para registrar comissões\n    private async registerAffiliateCommissions(userId: number, planId: number, transactionId: number, invoiceAmount: number, currency: string) {\n      try {\n        console.log(`Registrando comissões para usuário ${userId}, plano ${planId}, transação ${transactionId}`);\n\n        // Buscar o usuário para obter o aff_id\n        const user = await db\n          .selectFrom('users')\n          .where('id', '=', userId)\n          .select(['id', 'email', 'aff_id'])\n          .executeTakeFirst();\n\n        if (!user) {\n          console.error(`Usuário ${userId} não encontrado`);\n          return;\n        }\n\n        console.log(`Usuário encontrado: ID=${user.id}, Email=${user.email}, Aff_ID=${user.aff_id || 'não definido'}`);\n\n        // Verificar se já existe comissão registrada para esta transação\n        const existingCommission = await db\n          .selectFrom('affiliate_commissions')\n          .where('transaction_id', '=', transactionId)\n          .select(['id'])\n          .executeTakeFirst();\n\n        if (existingCommission) {\n          console.log(`Comissão já registrada para a transação ${transactionId}, excluindo para recriar`);\n\n          // Excluir comissões existentes para recriar\n          await db\n            .deleteFrom('affiliate_commissions')\n            .where('transaction_id', '=', transactionId)\n            .execute();\n\n          console.log(`Comissões existentes para a transação ${transactionId} foram excluídas`);\n        }\n\n        // Obter dados do afiliado\n        const aff_id = user.aff_id || 0;\n        const affiliates = await this.getAffiliatesData(aff_id);\n        console.log(`Dados dos afiliados: ${JSON.stringify(affiliates)}`);\n\n        // Obter dados de comissão do plano\n        const comissions = await this.getPlanComissionsData(planId);\n        console.log(`Dados de comissão do plano ${planId}: ${JSON.stringify(comissions)}`);\n\n        if (!comissions) {\n          console.log(`Não foi possível obter dados de comissões para o plano ${planId}`);\n          return;\n        }\n\n        // Registrar comissão para o afiliado nível 1\n        if (aff_id > 0) {\n          try {\n            console.log(`Registrando comissão para afiliado ${aff_id}`);\n\n            const result = await db\n              .insertInto('affiliate_commissions')\n              .values({\n                aff_level: 1,\n                aff_user_id: aff_id,\n                user_id: userId,\n                plan_id: planId,\n                transaction_id: transactionId,\n                commission_percent: Number(comissions.affiliate_percent || 0),\n                commission_value: Number(comissions.affiliate || 0),\n                status: 'pending',\n                metadata: JSON.stringify({\n                  transaction_amount: invoiceAmount,\n                  currency: currency,\n                }),\n                created_at: new Date(),\n                updated_at: new Date(),\n              })\n              .executeTakeFirst();\n\n            console.log(`Comissão registrada para afiliado ${aff_id}:`, result);\n\n            // Verificar se a comissão foi registrada\n            if (result && 'insertId' in result) {\n              const commissionId = Number(result.insertId);\n              console.log(`Comissão registrada com ID: ${commissionId}`);\n\n              // Verificar se a comissão existe no banco de dados\n              const commission = await db\n                .selectFrom('affiliate_commissions')\n                .where('id', '=', commissionId)\n                .select(['id', 'status'])\n                .executeTakeFirst();\n\n              if (commission) {\n                console.log(`Comissão ${commissionId} verificada no banco de dados com status: ${commission.status}`);\n              } else {\n                console.error(`Comissão ${commissionId} não encontrada no banco de dados após inserção`);\n              }\n            }\n          } catch (error) {\n            console.error(`Erro ao registrar comissão para afiliado ${aff_id}:`, error);\n          }\n        }\n\n        // Registrar comissão para o afiliado master (nível 2)\n        if (affiliates.affiliate_master_user_id) {\n          try {\n            console.log(`Registrando comissão para afiliado master ${affiliates.affiliate_master_user_id}`);\n\n            const result = await db\n              .insertInto('affiliate_commissions')\n              .values({\n                aff_level: 2,\n                aff_user_id: affiliates.affiliate_master_user_id,\n                user_id: userId,\n                plan_id: planId,\n                transaction_id: transactionId,\n                commission_percent: Number(comissions.affiliate_master_percent || 0),\n                commission_value: Number(comissions.affiliate_master || 0),\n                status: 'pending',\n                metadata: JSON.stringify({\n                  transaction_amount: invoiceAmount,\n                  currency: currency,\n                }),\n                created_at: new Date(),\n                updated_at: new Date(),\n              })\n              .executeTakeFirst();\n\n            console.log(`Comissão registrada para afiliado master ${affiliates.affiliate_master_user_id}:`, result);\n\n            // Verificar se a comissão foi registrada\n            if (result && 'insertId' in result) {\n              const commissionId = Number(result.insertId);\n              console.log(`Comissão master registrada com ID: ${commissionId}`);\n\n              // Verificar se a comissão existe no banco de dados\n              const commission = await db\n                .selectFrom('affiliate_commissions')\n                .where('id', '=', commissionId)\n                .select(['id', 'status'])\n                .executeTakeFirst();\n\n              if (commission) {\n                console.log(`Comissão master ${commissionId} verificada no banco de dados com status: ${commission.status}`);\n              } else {\n                console.error(`Comissão master ${commissionId} não encontrada no banco de dados após inserção`);\n              }\n            }\n          } catch (error) {\n            console.error(`Erro ao registrar comissão para afiliado master ${affiliates.affiliate_master_user_id}:`, error);\n          }\n        }\n\n        console.log(`Processamento de comissões concluído para a transação ${transactionId}`);\n      } catch (error) {\n        console.error(`Erro ao registrar comissões:`, error);\n      }\n    }\n\n    // Método para obter as assinaturas de um usuário\n    async getUserSubscriptions(userId: number) {\n      try {\n        const subscriptions = await db\n          .selectFrom('users_subscriptions as us')\n          .innerJoin('plans as p', 'us.plan_id', 'p.id')\n          .innerJoin('plans_payments_providers as ppp', 'us.plan_payment_provider_id', 'ppp.id')\n          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')\n          .where('us.user_id', '=', userId)\n          .where('us.deleted_at', 'is', null)\n          .select([\n            'us.id',\n            'us.status',\n            'us.platform',\n            'us.price',\n            'us.currency',\n            'us.start_date',\n            'us.end_date',\n            'us.next_billing_date',\n            'us.cancel_at_period_end',\n            'us.is_trial',\n            'us.trial_start_date',\n            'us.trial_end_date',\n            'us.snaptokens',\n            'us.created_at',\n            'p.name as plan_name',\n            'p.description as plan_description',\n            'pp.name as payment_provider_name',\n          ])\n          .orderBy('us.created_at', 'desc')\n          .execute();\n\n        return {\n          status: 'success',\n          data: subscriptions.map(subscription => ({\n            id: subscription.id,\n            status: subscription.status,\n            platform: subscription.platform,\n            price: Number(subscription.price),\n            currency: subscription.currency,\n            start_date: this.formatDatetime(subscription.start_date),\n            end_date: subscription.end_date ? this.formatDatetime(subscription.end_date) : null,\n            next_billing_date: subscription.next_billing_date ? this.formatDatetime(subscription.next_billing_date) : null,\n            cancel_at_period_end: subscription.cancel_at_period_end,\n            is_trial: subscription.is_trial,\n            trial_start_date: subscription.trial_start_date ? this.formatDatetime(subscription.trial_start_date) : null,\n            trial_end_date: subscription.trial_end_date ? this.formatDatetime(subscription.trial_end_date) : null,\n            snaptokens: subscription.snaptokens,\n            created_at: this.formatDatetime(subscription.created_at),\n            plan_name: subscription.plan_name,\n            plan_description: subscription.plan_description,\n            payment_provider: subscription.payment_provider_name,\n          })),\n        };\n      } catch (error) {\n        console.error('Erro ao obter assinaturas do usuário:', error);\n        throw error;\n      }\n    }\n\n    // Método para obter as transações de um usuário\n    async getUserTransactions(userId: number) {\n      try {\n        const transactions = await db\n          .selectFrom('transactions as t')\n          .innerJoin('payment_providers as pp', 't.payment_provider_id', 'pp.id')\n          .where('t.user_id', '=', userId)\n          .where('t.deleted_at', 'is', null)\n          .select([\n            't.id',\n            't.provider_transaction_id',\n            't.amount',\n            't.currency',\n            't.status',\n            't.source_type',\n            't.source_id',\n            't.created_at',\n            'pp.name as payment_provider_name',\n          ])\n          .orderBy('t.created_at', 'desc')\n          .execute();\n\n        return {\n          status: 'success',\n          data: transactions.map(transaction => ({\n            id: transaction.id,\n            provider_transaction_id: transaction.provider_transaction_id,\n            amount: Number(transaction.amount),\n            currency: transaction.currency,\n            status: transaction.status,\n            source_type: transaction.source_type,\n            source_id: transaction.source_id,\n            created_at: this.formatDatetime(transaction.created_at),\n            payment_provider: transaction.payment_provider_name,\n          })),\n        };\n      } catch (error) {\n        console.error('Erro ao obter transações do usuário:', error);\n        throw error;\n      }\n    }\n\n    // Método para obter detalhes de uma transação específica\n    async getTransactionDetails(transactionId: number, userId: number) {\n      try {\n        // Buscar a transação\n        const transaction = await db\n          .selectFrom('transactions as t')\n          .innerJoin('payment_providers as pp', 't.payment_provider_id', 'pp.id')\n          .where('t.id', '=', transactionId)\n          .where('t.user_id', '=', userId)\n          .where('t.deleted_at', 'is', null)\n          .select([\n            't.id',\n            't.provider_transaction_id',\n            't.amount',\n            't.currency',\n            't.status',\n            't.source_type',\n            't.source_id',\n            't.created_at',\n            't.updated_at',\n            'pp.name as payment_provider_name',\n          ])\n          .executeTakeFirst();\n\n        if (!transaction) {\n          return {\n            status: 'error',\n            message: 'Transação não encontrada ou não pertence ao usuário',\n          };\n        }\n\n        // Buscar informações adicionais com base no source_type\n        let sourceDetails: any = null;\n        if (transaction.source_type === 'subscription' && transaction.source_id) {\n          // Buscar detalhes da assinatura\n          const subscription = await db\n            .selectFrom('users_subscriptions as us')\n            .innerJoin('plans as p', 'us.plan_id', 'p.id')\n            .where('us.id', '=', transaction.source_id)\n            .select([\n              'us.id',\n              'us.status',\n              'us.platform',\n              'us.price',\n              'us.currency',\n              'us.start_date',\n              'us.end_date',\n              'p.name as plan_name',\n              'p.description as plan_description',\n            ])\n            .executeTakeFirst();\n\n          if (subscription) {\n            sourceDetails = {\n              type: 'subscription',\n              id: subscription.id,\n              status: subscription.status,\n              plan_name: subscription.plan_name,\n              plan_description: subscription.plan_description,\n              price: Number(subscription.price),\n              currency: subscription.currency,\n              start_date: this.formatDatetime(subscription.start_date),\n              end_date: subscription.end_date ? this.formatDatetime(subscription.end_date) : null,\n            };\n          }\n        } else if (transaction.source_type === 'invoice_item') {\n          // Para itens avulsos, podemos buscar informações adicionais se necessário\n          sourceDetails = {\n            type: 'invoice_item',\n            description: 'Compra avulsa',\n          };\n        }\n\n        // Buscar informações do Stripe se disponível\n        let stripeDetails: any = null;\n        if (transaction.provider_transaction_id && transaction.payment_provider_name === 'Stripe') {\n          try {\n            // Verificar se é uma fatura ou uma cobrança\n            if (transaction.provider_transaction_id.startsWith('in_')) {\n              // É uma fatura\n              const invoice = await this.stripe.invoices.retrieve(transaction.provider_transaction_id);\n              stripeDetails = {\n                invoice_number: invoice.number,\n                invoice_url: invoice.hosted_invoice_url,\n                pdf_url: invoice.invoice_pdf,\n                // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente\n                payment_intent: invoice.payment_intent,\n                billing_reason: invoice.billing_reason,\n              };\n            } else if (transaction.provider_transaction_id.startsWith('ch_')) {\n              // É uma cobrança\n              const charge = await this.stripe.charges.retrieve(transaction.provider_transaction_id);\n              stripeDetails = {\n                receipt_url: charge.receipt_url,\n                payment_method: charge.payment_method_details?.type,\n                payment_intent: charge.payment_intent,\n                description: charge.description,\n              };\n            }\n          } catch (stripeError) {\n            console.error('Erro ao obter detalhes do Stripe:', stripeError);\n            // Continuamos mesmo sem os detalhes do Stripe\n          }\n        }\n\n        return {\n          status: 'success',\n          data: {\n            id: transaction.id,\n            provider_transaction_id: transaction.provider_transaction_id,\n            amount: Number(transaction.amount),\n            currency: transaction.currency,\n            status: transaction.status,\n            source_type: transaction.source_type,\n            source_id: transaction.source_id,\n            created_at: this.formatDatetime(transaction.created_at),\n            updated_at: this.formatDatetime(transaction.updated_at),\n            payment_provider: transaction.payment_provider_name,\n            source_details: sourceDetails,\n            provider_details: stripeDetails,\n          },\n        };\n      } catch (error) {\n        console.error('Erro ao obter detalhes da transação:', error);\n        throw error;\n      }\n    }\n\n    // Método para cancelar uma assinatura\n    async cancelSubscription(subscriptionId: number, userId: number) {\n      try {\n        // Verificar se a assinatura existe e pertence ao usuário\n        const subscription = await db\n          .selectFrom('users_subscriptions')\n          .where('id', '=', subscriptionId)\n          .where('user_id', '=', userId)\n          .where('deleted_at', 'is', null)\n          .select(['payment_provider_external_id', 'status'])\n          .executeTakeFirst();\n\n        if (!subscription) {\n          return {\n            status: 'error',\n            message: 'Assinatura não encontrada ou não pertence ao usuário',\n          };\n        }\n\n        // Verificar se a assinatura já está cancelada\n        if (subscription.status === 'canceled') {\n          return {\n            status: 'error',\n            message: 'Assinatura já está cancelada',\n          };\n        }\n\n        // Cancelar a assinatura no Stripe\n        if (subscription.payment_provider_external_id) {\n          try {\n            await this.stripe.subscriptions.update(subscription.payment_provider_external_id, {\n              cancel_at_period_end: true,\n            });\n          } catch (stripeError) {\n            console.error('Erro ao cancelar assinatura no Stripe:', stripeError);\n            // Continuamos mesmo com erro no Stripe para atualizar nosso banco\n          }\n        }\n\n        // Atualizar a assinatura no banco de dados\n        await db\n          .updateTable('users_subscriptions')\n          .set({\n            cancel_at_period_end: true,\n            updated_at: new Date(),\n          })\n          .where('id', '=', subscriptionId)\n          .execute();\n\n        return {\n          status: 'success',\n          message: 'Assinatura será cancelada ao final do período atual',\n        };\n      } catch (error) {\n        console.error('Erro ao cancelar assinatura:', error);\n        throw error;\n      }\n    }\n\n    // Método para cancelar uma assinatura imediatamente\n    async cancelSubscriptionImmediately(subscriptionId: number, userId: number) {\n      try {\n        // Verificar se a assinatura existe e pertence ao usuário\n        const subscription = await db\n          .selectFrom('users_subscriptions')\n          .where('id', '=', subscriptionId)\n          .where('user_id', '=', userId)\n          .where('deleted_at', 'is', null)\n          .select(['payment_provider_external_id', 'status'])\n          .executeTakeFirst();\n\n        if (!subscription) {\n          return {\n            status: 'error',\n            message: 'Assinatura não encontrada ou não pertence ao usuário',\n          };\n        }\n\n        // Verificar se a assinatura já está cancelada\n        if (subscription.status === 'canceled') {\n          return {\n            status: 'error',\n            message: 'Assinatura já está cancelada',\n          };\n        }\n\n        // Cancelar a assinatura no Stripe imediatamente\n        if (subscription.payment_provider_external_id) {\n          try {\n            await this.stripe.subscriptions.cancel(subscription.payment_provider_external_id);\n          } catch (stripeError) {\n            console.error('Erro ao cancelar assinatura no Stripe:', stripeError);\n            // Continuamos mesmo com erro no Stripe para atualizar nosso banco\n          }\n        }\n\n        // Atualizar a assinatura no banco de dados\n        const status: 'canceled' = 'canceled';\n        await db\n          .updateTable('users_subscriptions')\n          .set({\n            status,\n            end_date: new Date(),\n            updated_at: new Date(),\n          })\n          .where('id', '=', subscriptionId)\n          .execute();\n\n        return {\n          status: 'success',\n          message: 'Assinatura cancelada com sucesso',\n        };\n      } catch (error) {\n        console.error('Erro ao cancelar assinatura imediatamente:', error);\n        throw error;\n      }\n    }\n\n  // Método para registrar comissões manualmente para uma assinatura\n  async registerCommissionsForSubscription(subscriptionId: number) {\n    try {\n      console.log(`Registrando comissões manualmente para a assinatura ${subscriptionId}`);\n\n      // Buscar a assinatura\n      const subscription = await db\n        .selectFrom('users_subscriptions')\n        .where('id', '=', subscriptionId)\n        .select(['id', 'user_id', 'plan_id', 'price', 'currency'])\n        .executeTakeFirst();\n\n      if (!subscription) {\n        console.error(`Assinatura ${subscriptionId} não encontrada`);\n        return {\n          status: 'error',\n          message: 'Assinatura não encontrada'\n        };\n      }\n\n      // Buscar a transação mais recente para esta assinatura\n      const transaction = await db\n        .selectFrom('transactions')\n        .where('source_id', '=', subscriptionId)\n        .where('source_type', '=', 'subscription')\n        .where('status', '=', 'paid')\n        .orderBy('id', 'desc')\n        .select(['id', 'amount', 'currency'])\n        .executeTakeFirst();\n\n      if (!transaction) {\n        console.error(`Nenhuma transação encontrada para a assinatura ${subscriptionId}`);\n\n        // Criar uma transação para esta assinatura\n        const newTransaction = await db\n          .insertInto('transactions')\n          .values({\n            user_id: subscription.user_id,\n            provider_transaction_id: `manual_${Date.now()}`,\n            payment_provider_id: 1, // Stripe\n            amount: Number(subscription.price || 0),\n            currency: subscription.currency || 'BRL',\n            status: 'paid',\n            source_type: 'subscription',\n            source_id: subscription.id,\n            created_at: new Date(),\n            updated_at: new Date(),\n          })\n          .executeTakeFirst();\n\n        if (newTransaction && 'insertId' in newTransaction) {\n          const transactionId = Number(newTransaction.insertId);\n          console.log(`Nova transação criada com ID: ${transactionId}`);\n\n          // Registrar comissões\n          await this.registerAffiliateCommissions(\n            subscription.user_id,\n            subscription.plan_id,\n            transactionId,\n            Number(subscription.price || 0),\n            subscription.currency || 'BRL'\n          );\n\n          return {\n            status: 'success',\n            message: 'Comissões registradas com sucesso',\n            data: {\n              transaction_id: transactionId\n            }\n          };\n        } else {\n          console.error(`Erro ao criar transação para a assinatura ${subscriptionId}`);\n          return {\n            status: 'error',\n            message: 'Erro ao criar transação'\n          };\n        }\n      } else {\n        console.log(`Transação encontrada: ${transaction.id}`);\n\n        // Registrar comissões\n        await this.registerAffiliateCommissions(\n          subscription.user_id,\n          subscription.plan_id,\n          transaction.id,\n          Number(transaction.amount || 0),\n          transaction.currency || 'BRL'\n        );\n\n        return {\n          status: 'success',\n          message: 'Comissões registradas com sucesso',\n          data: {\n            transaction_id: transaction.id\n          }\n        };\n      }\n    } catch (error) {\n      console.error('Erro ao registrar comissões:', error);\n      return {\n        status: 'error',\n        message: 'Erro ao registrar comissões'\n      };\n    }\n  }\n}\n"], "names": ["AdminService", "dayjs", "require", "formatDatetime", "datetime", "format", "getStats", "totalUsers", "db", "selectFrom", "where", "select", "fn", "count", "as", "executeTakeFirst", "totalProfessionals", "activeSubscriptions", "currentDate", "Date", "firstDayOfMonth", "getFullYear", "getMonth", "lastDayOfMonth", "monthlyRevenue", "sum", "status", "data", "total_users", "Number", "total_professionals", "active_subscriptions", "monthly_revenue", "total", "getAllUsers", "query", "q", "role_id", "page", "limit", "offset", "queryBuilder", "leftJoin", "groupBy", "orderBy", "eb", "or", "Promise", "all", "execute", "countAll", "map", "row", "id", "name", "email", "roles", "split", "photo", "createdAt", "created_at", "pagination", "createUser", "createUserDto", "password", "existingUser", "ConflictException", "hashedPassword", "bcrypt", "hash", "result", "insertInto", "values", "updated_at", "userId", "insertId", "user_id", "updateUser", "updateUserDto", "NotFoundException", "emailExists", "updateValues", "updateTable", "set", "updatedUser", "executeTakeFirstOrThrow", "deleteUser", "deleteFrom", "message", "getSelectOptions", "type", "options", "getAllFoods", "category_id", "innerJoin", "qb", "ref", "totalRecordsQuery", "totalRecords", "foods", "totalPages", "Math", "ceil", "searchFoods", "trim", "length", "createFood", "createFoodDto", "quantity", "unit", "calories", "protein", "carbs", "fat", "fiber", "categoryExists", "updateFood", "updateFoodDto", "existingFood", "deleteFood", "getAllExercises", "muscle_group_id", "exercises", "exerciseIds", "exercise", "relatedItems", "formattedExercises", "targetMuscles", "filter", "item", "exercise_id", "key_item", "value_item", "synergisticMuscles", "instructions", "tips", "muscle_group", "equipment", "target_muscles", "synergistic_muscles", "createExercise", "createExerciseDto", "equipment_id", "media_url", "muscleGroupExists", "equipmentExists", "exerciseResult", "exerciseId", "itemsToInsert", "for<PERSON>ach", "muscle", "push", "instruction", "index", "tip", "deleteExercise", "existingExercise", "getAllPlans", "plans", "sql", "get<PERSON><PERSON><PERSON>", "frequency", "interval_value", "groupedPlans", "reduce", "acc", "plan", "payment_provider_name", "platform", "payment_provider_external_id", "provider_price", "provider_currency", "provider_snaptokens", "is_active", "description", "price", "period", "<PERSON><PERSON><PERSON>", "affiliate_master_commission_percent", "affiliate_commission_percent", "payment_config", "config_id", "payment_provider", "replace", "toLowerCase", "formattedPlans", "Object", "createPlan", "createPlanDto", "role_id_users", "currency", "allows_trial", "trial_period_days", "isActive", "planResult", "planId", "updatePlan", "updatePlanDto", "configPlan", "configPlanDto", "console", "log", "payment_provider_ids", "payment_provider_id", "existingPaymentProvider", "existingPlanConfig", "planConfigResult", "plan_id", "deletePlan", "existingPlan", "deleted_at", "deletePlanConfig", "getAllSubscriptions", "user_name", "user_email", "start_date", "end_date", "next_billing_date", "cancel_at_period_end", "is_trial", "trial_start_date", "trial_end_date", "plan_name", "getAllTransactions", "provider_transaction_id", "amount", "source_type", "source_id", "getAffiliates", "is_master", "accepted_at", "createAffiliateAccount", "affiliateId", "account", "stripe", "accounts", "create", "country", "business_type", "individual", "first_name", "last_name", "slice", "join", "metadata", "updateAffiliate", "updateAffiliateDto", "existingAffiliate", "isNew", "acceptedIfActive", "affData", "toString", "stripeId", "webhook", "body", "object", "error", "event", "eventType", "handleSubscriptionCreated", "handleSubscriptionUpdated", "handleSubscriptionDeleted", "handleSubscriptionTrialWillEnd", "handleInvoiceCreated", "handleInvoicePaid", "handleInvoicePaymentFailed", "handleChargeSucceeded", "handleChargeRefunded", "handleCheckoutSessionCompleted", "handlePaymentIntentSucceeded", "handleSubscriptionScheduleCreated", "handlerError", "subscription", "customerId", "customer", "user", "getUserByStripeCustomerId", "existingSubscription", "planPaymentProviderId", "getPlanPaymentProviderIdByPriceId", "items", "planInfo", "getPlanInfoByPlanPaymentProviderId", "isTrial", "trial_end", "startDate", "current_period_start", "created", "nextBillingDate", "current_period_end", "subscriptionResult", "plan_payment_provider_id", "unit_amount", "toUpperCase", "cancel_at", "trial_start", "invoice", "stripeSubscription", "subscriptions", "retrieve", "paymentProvider", "existingTransaction", "amount_due", "subscriptionId", "parent", "subscription_details", "newSubscriptionId", "transactionId", "amount_paid", "updatedSubscription", "registerAffiliateCommissions", "handleInvoicePaidOld", "currentSubscription", "paid", "getAffiliatesData", "aff_id", "affiliate", "affiliate_master", "affiliate_master_user_id", "ref_user_id", "invite", "JSON", "stringify", "getPlanComissionsData", "affiliate_percent", "affiliate_master_percent", "comission", "affiliatePercent", "affiliateMasterPercent", "affiliateValue", "affiliateMasterValue", "charge", "transaction", "isFullRefund", "refunded", "refundAmount", "amount_refunded", "paymentIntent", "invoices", "schedule", "session", "mode", "amount_total", "priceId", "invoiceAmount", "existingCommission", "affiliates", "comissions", "aff_level", "aff_user_id", "transaction_id", "commission_percent", "commission_value", "transaction_amount", "commissionId", "commission", "getUserSubscriptions", "plan_description", "getUserTransactions", "transactions", "getTransactionDetails", "sourceDetails", "stripeDetails", "startsWith", "invoice_number", "number", "invoice_url", "hosted_invoice_url", "pdf_url", "invoice_pdf", "payment_intent", "billing_reason", "charges", "receipt_url", "payment_method", "payment_method_details", "stripeError", "source_details", "provider_details", "cancelSubscription", "update", "cancelSubscriptionImmediately", "cancel", "registerCommissionsForSubscription", "newTransaction", "now", "constructor", "Stripe", "process", "env", "STRIPE_SK"], "mappings": ";;;;+BAoBaA;;;eAAAA;;;wBApBoD;0BAC9C;kEAKK;wBAOJ;wBAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdvB,kCAAkC;AAClC,MAAMC,QAAQC,QAAQ;AAgBf,IAAA,AAAMF,eAAN,MAAMA;IAUPG,eAAeC,QAAa,EAAU;QAChC,OAAOH,MAAMG,UAAUC,MAAM,CAAC;IACpC;IAEA,MAAMC,WAAW;QACf,yDAAyD;QACzD,MAAMC,aAAa,MAAMC,YAAE,CACxBC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAK,GACtBC,MAAM,CAACH,YAAE,CAACI,EAAE,CAACC,KAAK,CAAS,WAAWC,EAAE,CAAC,UACzCC,gBAAgB;QAEnB,iFAAiF;QACjF,MAAMC,qBAAqB,MAAMR,YAAE,CAChCC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,MAAM;YAAC;YAAG;SAAE,EAC7BC,MAAM,CAACH,YAAE,CAACI,EAAE,CAACC,KAAK,CAAS,WAAWC,EAAE,CAAC,UACzCC,gBAAgB;QAEnB,iCAAiC;QACjC,MAAME,sBAAsB,MAAMT,YAAE,CACjCC,UAAU,CAAC,uBACXC,KAAK,CAAC,UAAU,KAAK,UACrBA,KAAK,CAAC,cAAc,MAAM,MAC1BC,MAAM,CAACH,YAAE,CAACI,EAAE,CAACC,KAAK,CAAS,MAAMC,EAAE,CAAC,UACpCC,gBAAgB;QAEnB,4DAA4D;QAC5D,MAAMG,cAAc,IAAIC;QACxB,MAAMC,kBAAkB,IAAID,KAAKD,YAAYG,WAAW,IAAIH,YAAYI,QAAQ,IAAI;QACpF,MAAMC,iBAAiB,IAAIJ,KAAKD,YAAYG,WAAW,IAAIH,YAAYI,QAAQ,KAAK,GAAG;QAEvF,MAAME,iBAAiB,MAAMhB,YAAE,CAC5BC,UAAU,CAAC,gBACXC,KAAK,CAAC,UAAU,KAAK,QACrBA,KAAK,CAAC,cAAc,MAAMU,iBAC1BV,KAAK,CAAC,cAAc,MAAMa,gBAC1BZ,MAAM,CAACH,YAAE,CAACI,EAAE,CAACa,GAAG,CAAS,UAAUX,EAAE,CAAC,UACtCC,gBAAgB;QAEnB,2BAA2B;QAC3B,OAAO;YACLW,QAAQ;YACRC,MACE;gBACEC,aAAaC,OAAOtB,YAAYM,SAAS;gBACzCiB,qBAAqBD,OAAOb,oBAAoBH,SAAS;gBACzDkB,sBAAsBF,OAAOZ,qBAAqBJ,SAAS;gBAC3DmB,iBAAiBH,OAAOL,gBAAgBS,SAAS;YACnD;QACJ;IACF;IAEA,MAAMC,YAAYC,KAA0B,EAAE;QAC5C,MAAM,EAAEC,CAAC,EAAEC,OAAO,EAAE,GAAGF;QACvB,IAAI,EAAEG,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAChC,MAAMK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,IAAIE,eAAejC,YAAE,CAClBC,UAAU,CAAC,SACXiC,QAAQ,CAAC,eAAe,YAAY,uBACpCA,QAAQ,CAAC,SAAS,uBAAuB,YACzC/B,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACAH,YAAE,CAACI,EAAE,CAAS,gBAAgB;gBAAC;aAAa,EAAEE,EAAE,CAAC;SAClD,EACA6B,OAAO,CAAC,YACRC,OAAO,CAAC,oBAAoB;QAE/B,IAAIR,GAAG;YACLK,eAAeA,aAAa/B,KAAK,CAAC,CAACmC,KACjCA,GAAGC,EAAE,CAAC;oBACJD,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;oBACjCS,GAAG,eAAe,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;iBACnC;QAEL;QAEA,IAAIC,SAAS;YACXI,eAAeA,aAAa/B,KAAK,CAAC,YAAY,KAAK2B;QACrD;QAEA,MAAM,CAACV,MAAMM,MAAM,GAAG,MAAMc,QAAQC,GAAG,CAAC;YACtCP,aAAaF,KAAK,CAACA,OAAOC,MAAM,CAACA,QAAQS,OAAO;YAChDzC,YAAE,CAACC,UAAU,CAAC,SAASE,MAAM,CAACH,YAAE,CAACI,EAAE,CAACsC,QAAQ,GAAGpC,EAAE,CAAC,UAAUC,gBAAgB;SAC7E;QAED,OAAO;YACLW,QAAQ;YACRC,MAAMA,KAAKwB,GAAG,CAAC,CAACC,MAAS,CAAA;oBACvBC,IAAID,IAAIC,EAAE;oBACVC,MAAMF,IAAIE,IAAI;oBACdC,OAAOH,IAAIG,KAAK;oBAChBC,OAAOJ,IAAII,KAAK,GAAGJ,IAAII,KAAK,CAACC,KAAK,CAAC,QAAQ,EAAE;oBAC7CC,OAAON,IAAIM,KAAK;oBAChBC,WAAW,IAAI,CAACxD,cAAc,CAACiD,IAAIQ,UAAU;gBAC/C,CAAA;YACAC,YAAY;gBACVvB;gBACAC;gBACAN,OAAOJ,OAAOI,OAAOA;YACvB;QACF;IACF;IAGA,MAAM6B,WAAWC,aAA4B,EAAE;QAC7C,MAAM,EAAET,IAAI,EAAEC,KAAK,EAAES,QAAQ,EAAE3B,OAAO,EAAEqB,KAAK,EAAE,GAAGK;QAElD,0CAA0C;QAC1C,MAAME,eAAe,MAAMzD,YAAE,CAC1BC,UAAU,CAAC,SACXC,KAAK,CAAC,SAAS,KAAK6C,OACpB5C,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAIkD,cAAc;YAChB,MAAM,IAAIC,yBAAiB,CAAC;QAC9B;QAEA,sBAAsB;QACtB,MAAMC,iBAAiB,MAAMC,UAAOC,IAAI,CAACL,UAAU;QAEnD,0CAA0C;QAC1C,MAAMM,SAAS,MAAM9D,YAAE,CACpB+D,UAAU,CAAC,SACXC,MAAM,CAAC;YACNlB;YACAC;YACAS,UAAUG;YACVT;YACAE,YAAY,IAAIzC;YAChBsD,YAAY,IAAItD;QAClB,GACCJ,gBAAgB;QAEnB,uCAAuC;QACvC,MAAM2D,SAAcJ,OAAOK,QAAQ;QAEnC,oCAAoC;QACpC,MAAMnE,YAAE,CACL+D,UAAU,CAAC,eACXC,MAAM,CAAC;YACNI,SAASF;YACTrC;QACF,GACCY,OAAO;QAEV,OAAO;YACLvB,QAAQ;YACRC,MAAM,EAAE;QACV;IACF;IAGA,MAAMkD,WAAWxB,EAAU,EAAEyB,aAA4B,EAAE;QACzD,MAAM,EAAExB,IAAI,EAAEC,KAAK,EAAES,QAAQ,EAAE3B,OAAO,EAAEqB,KAAK,EAAE,GAAGoB;QAElD,+BAA+B;QAC/B,MAAMb,eAAe,MAAMzD,YAAE,CAC1BC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC;YAAC;YAAM;SAAQ,EACtBI,gBAAgB;QAEnB,IAAI,CAACkD,cAAc;YACjB,MAAM,IAAIc,yBAAiB,CAAC;QAC9B;QAEA,wDAAwD;QACxD,IAAIxB,UAAUU,aAAaV,KAAK,EAAE;YAChC,MAAMyB,cAAc,MAAMxE,YAAE,CACzBC,UAAU,CAAC,SACXC,KAAK,CAAC,SAAS,KAAK6C,OACpB5C,MAAM,CAAC,MACPI,gBAAgB;YAEnB,IAAIiE,aAAa;gBACf,MAAM,IAAId,yBAAiB,CAAC;YAC9B;QACF;QAEA,sCAAsC;QACtC,MAAMe,eAAoB;YACxB3B;YACAC;YACAG;YACAe,YAAY,IAAItD;QAClB;QAEA,yCAAyC;QACzC,IAAI6C,UAAU;YACZ,MAAMG,iBAAiB,MAAMC,UAAOC,IAAI,CAACL,UAAU;YACnDiB,aAAajB,QAAQ,GAAGG;QAC1B;QAEA,uCAAuC;QACvC,MAAM3D,YAAE,CACL0E,WAAW,CAAC,SACZC,GAAG,CAACF,cACJvE,KAAK,CAAC,MAAM,KAAK2C,IACjBJ,OAAO;QAEV,6DAA6D;QAC7D,MAAMzC,YAAE,CACL0E,WAAW,CAAC,eACZC,GAAG,CAAC;YAAE9C;QAAQ,GACd3B,KAAK,CAAC,WAAW,KAAK2C,IACtBJ,OAAO;QAEV,+BAA+B;QAC/B,MAAMmC,cAAc,MAAM5E,YAAE,CACzBC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC;YAAC;YAAM;YAAQ;YAAS;YAAS;YAAc;SAAa,EACnE0E,uBAAuB;QAE1B,OAAO;YACL3D,QAAQ;YACRC,MAAMyD;QACR;IACF;IAEA,MAAME,WAAWjC,EAAU,EAAE;QAC3B,+BAA+B;QAC/B,MAAMY,eAAe,MAAMzD,YAAE,CAC1BC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACkD,cAAc;YACjB,MAAM,IAAIc,yBAAiB,CAAC;QAC9B;QAEA,wCAAwC;QACxC,MAAMvE,YAAE,CACL+E,UAAU,CAAC,eACX7E,KAAK,CAAC,WAAW,KAAK2C,IACtBJ,OAAO;QAEV,mBAAmB;QACnB,MAAMzC,YAAE,CACL+E,UAAU,CAAC,SACX7E,KAAK,CAAC,MAAM,KAAK2C,IACjBJ,OAAO;QAEV,OAAO;YACLvB,QAAQ;YACR8D,SAAS;QACX;IACF;IAEA,MAAMC,iBAAiBC,IAAY,EAAE;QACnC,MAAMC,UAAW,MAAMnF,YAAE,CACtBC,UAAU,CAAC,kBACXE,MAAM,CAAC;YAAC;YAAM;SAAe,EAC7BD,KAAK,CAAC,YAAY,KAAKgF,MACvB9C,OAAO,CAAC;YAAC;YAAc;SAAe,EACtCK,OAAO;QAER,OAAO;YACLvB,QAAQ;YACRC,MAAMgE;QACR;IACJ;IAGA,MAAMC,YAAYzD,KAA0B,EAAgB;QAC1D,MAAM,EAAEmB,IAAI,EAAEuC,WAAW,EAAEvD,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAErD,qCAAqC;QACrC,IAAIM,eAAejC,YAAE,CAClBC,UAAU,CAAC,SACXqF,SAAS,CAAC,8BAA8B,eAAe,qBACvDnF,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA,+FAA+F;YAC/F;YACA;YACA;YACA;YACA,CAACoF,KAAYA,GAAGC,GAAG,CAAC,yBAAyBlF,EAAE,CAAC;SACjD;QAEH,uCAAuC;QACvC,IAAIwC,MAAM;YACRb,eAAeA,aAAa/B,KAAK,CAAC,cAAc,QAAQ,CAAC,CAAC,EAAE4C,KAAK,CAAC,CAAC;QACrE;QAEA,4CAA4C;QAC5C,IAAIuC,aAAa;YACfpD,eAAeA,aAAa/B,KAAK,CAAC,qBAAqB,KAAKmF;QAC9D;QAEA,4DAA4D;QAC5D,MAAMI,oBAAoBzF,YAAE,CACzBC,UAAU,CAAC,SACXqF,SAAS,CAAC,8BAA8B,eAAe;QAE1D,8CAA8C;QAC9C,IAAIxC,MAAM;YACR2C,kBAAkBvF,KAAK,CAAC,cAAc,QAAQ,CAAC,CAAC,EAAE4C,KAAK,CAAC,CAAC;QAC3D;QACA,IAAIuC,aAAa;YACfI,kBAAkBvF,KAAK,CAAC,qBAAqB,KAAKmF;QACpD;QAEA,2DAA2D;QAC3D,MAAMK,eAAe,MAAMD,kBACxBtF,MAAM,CAACH,YAAE,CAACI,EAAE,CAACsC,QAAQ,GAAGpC,EAAE,CAAC,UAC3BC,gBAAgB;QAEnB,MAAMkB,QAAQJ,OAAOqE,cAAcrF,SAAS;QAE5C,yCAAyC;QACzC,MAAM2B,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAC5BE,eAAeA,aAAaF,KAAK,CAACA,OAAOC,MAAM,CAACA;QAEhD,uDAAuD;QACvD,MAAM2D,QAAQ,MAAM1D,aAAaQ,OAAO;QAExC,gDAAgD;QAChD,OAAO;YACLvB,QAAQ;YACRC,MAAMwE;YACNtC,YAAY;gBACVvB,MAAMA;gBACNC,OAAOA;gBACPN,OAAOA;gBACPmE,YAAYC,KAAKC,IAAI,CAACrE,QAAQM;YAChC;QACF;IACF;IAEA,MAAMgE,YAAYpE,KAAoC,EAAgB;QACpE,MAAM,EAAEC,CAAC,EAAEG,QAAQ,EAAE,EAAE,GAAGJ;QAE1B,IAAI,CAACC,KAAKA,EAAEoE,IAAI,GAAGC,MAAM,KAAK,GAAG;YAC/B,OAAO;gBACL/E,QAAQ;gBACRC,MAAM,EAAE;YACV;QACF;QAEA,MAAMwE,QAAQ,MAAM3F,YAAE,CACnBC,UAAU,CAAC,SACXqF,SAAS,CAAC,8BAA8B,eAAe,qBACvDnF,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,CAACoF,KAAYA,GAAGC,GAAG,CAAC,yBAAyBlF,EAAE,CAAC;SACjD,EACAJ,KAAK,CAAC,cAAc,QAAQ,CAAC,CAAC,EAAE0B,EAAEoE,IAAI,GAAG,CAAC,CAAC,EAC3CjE,KAAK,CAACA,OACNU,OAAO;QAEV,OAAO;YACLvB,QAAQ;YACRC,MAAMwE;QACR;IACF;IAEA,MAAMO,WAAWC,aAA4B,EAAE;QAC7C,MAAM,EACJrD,IAAI,EACJuC,WAAW,EACXe,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,KAAK,EACN,GAAGP;QAEJ,iCAAiC;QACjC,MAAMQ,iBAAiB,MAAM3G,YAAE,CAC5BC,UAAU,CAAC,kBACXC,KAAK,CAAC,MAAM,KAAKmF,aACjBlF,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACoG,gBAAgB;YACnB,MAAM,IAAIjD,yBAAiB,CAAC;QAC9B;QAGA,2CAA2C;QAC3C,MAAMI,SAAS,MAAM9D,YAAE,CACpB+D,UAAU,CAAC,SACXC,MAAM,CAAC;YACNlB;YACAuC;YACAe;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAtD,YAAY,IAAIzC;YAChBsD,YAAY,IAAItD;QAClB,EACA,oDAAoD;SACnDkE,uBAAuB;QAE1B,OAAO;YACL3D,QAAQ;YACRC,MAAM,EAAE;QACV;IACF;IAGJ,MAAMyF,WAAW/D,EAAU,EAAEgE,aAA4B,EAAE;QACzD,MAAM,EACJ/D,IAAI,EACJuC,WAAW,EACXe,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,KAAK,EACN,GAAGG;QAEJ,gCAAgC;QAChC,MAAMC,eAAe,MAAM9G,YAAE,CAC1BC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACuG,cAAc;YACjB,MAAM,IAAIvC,yBAAiB,CAAC;QAC9B;QAEA,iCAAiC;QACjC,MAAMoC,iBAAiB,MAAM3G,YAAE,CAC5BC,UAAU,CAAC,kBACXC,KAAK,CAAC,MAAM,KAAKmF,aACjBlF,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACoG,gBAAgB;YACnB,MAAM,IAAIjD,yBAAiB,CAAC;QAC9B;QAEA,wCAAwC;QACxC,MAAMI,SAAS,MAAM9D,YAAE,CACpB0E,WAAW,CAAC,SACZC,GAAG,CAAC;YACH7B;YACAuC;YACAe;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAzC,YAAY,IAAItD;QAClB,GACCT,KAAK,CAAC,MAAM,KAAK2C,GAClB,oDAAoD;SACnDgC,uBAAuB;QAE1B,OAAO;YACL3D,QAAQ;YACRC,MAAM,EAAE;QACV;IACF;IAEA,MAAM4F,WAAWlE,EAAU,EAAE;QAC3B,gCAAgC;QAChC,MAAMiE,eAAe,MAAM9G,YAAE,CAC1BC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACuG,cAAc;YACjB,MAAM,IAAIvC,yBAAiB,CAAC;QAC9B;QAEA,sCAAsC;QACtC,MAAMvE,YAAE,CACL+E,UAAU,CAAC,SACX7E,KAAK,CAAC,MAAM,KAAK2C,IACjBJ,OAAO;QAEV,OAAO;YACLvB,QAAQ;YACR8D,SAAS;QACX;IACF;IAEA,aAAa;IACb,MAAMgC,gBAAgBrF,KAA8B,EAAE;QACpD,MAAM,EAAEmB,IAAI,EAAEmE,eAAe,EAAEnF,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAEzD,2CAA2C;QAC3C,IAAIM,eAAejC,YAAE,CAClBC,UAAU,CAAC,kBACXqF,SAAS,CAAC,kCAAkC,mBAAmB,qBAC/DA,SAAS,CAAC,+BAA+B,gBAAgB,kBACzDnF,MAAM,CAAC;YACN;YACA;YACA,CAACoF,KAAOA,GAAGC,GAAG,CAAC,6BAA6BlF,EAAE,CAAC;YAC/C,CAACiF,KAAOA,GAAGC,GAAG,CAAC,0BAA0BlF,EAAE,CAAC;SAC7C,EACAJ,KAAK,CAAC,gBAAgB,MAAM;QAE/B,yBAAyB;QACzB,IAAI4C,MAAM;YACRb,eAAeA,aAAa/B,KAAK,CAAC,UAAU,QAAQ,CAAC,CAAC,EAAE4C,KAAK,CAAC,CAAC;QACjE;QAEA,mCAAmC;QACnC,IAAImE,iBAAiB;YACnBhF,eAAeA,aAAa/B,KAAK,CAAC,qBAAqB,KAAK+G;QAC9D;QAEA,2DAA2D;QAC3D,IAAIxB,oBAAoBzF,YAAE,CACvBC,UAAU,CAAC,kBACXqF,SAAS,CAAC,kCAAkC,mBAAmB,qBAC/DA,SAAS,CAAC,+BAA+B,gBAAgB;QAE5D,IAAIxC,MAAM;YACR2C,oBAAoBA,kBAAkBvF,KAAK,CAAC,UAAU,QAAQ,CAAC,CAAC,EAAE4C,KAAK,CAAC,CAAC;QAC3E;QACA,IAAImE,iBAAiB;YACnBxB,oBAAoBA,kBAAkBvF,KAAK,CAAC,qBAAqB,KAAK+G;QACxE;QAEA,MAAMvB,eAAe,MAAMD,kBACxBtF,MAAM,CAACH,YAAE,CAACI,EAAE,CAACsC,QAAQ,GAAGpC,EAAE,CAAC,UAC3BJ,KAAK,CAAC,gBAAgB,MAAM,MAC5BK,gBAAgB;QAEnB,MAAMkB,QAAQJ,OAAOqE,cAAcrF,SAAS;QAE5C,mBAAmB;QACnB,MAAM2B,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAC5BE,eAAeA,aACdF,KAAK,CAACA,OAAOC,MAAM,CAACA;QAErB,+BAA+B;QAC/B,MAAMkF,YAAY,MAAMjF,aAAaQ,OAAO;QAE5C,uDAAuD;QACvD,IAAIyE,UAAUjB,MAAM,KAAK,GAAG;YAC1B,OAAO;gBACL/E,QAAQ;gBACRC,MAAM,EAAE;gBACRkC,YAAY;oBACVvB;oBACAC;oBACAN;oBACAmE,YAAYC,KAAKC,IAAI,CAACrE,QAAQM;gBAChC;YACF;QACF;QAEA,wCAAwC;QACxC,MAAMoF,cAAcD,UAAUvE,GAAG,CAAC,CAACyE,WAAaA,SAASvE,EAAE;QAE3D,wCAAwC;QACxC,MAAMwE,eAAe,MAAMrH,YAAE,CAC1BC,UAAU,CAAC,mBACXC,KAAK,CAAC,eAAe,MAAMiH,aAC3BhH,MAAM,CAAC;YAAC;YAAe;YAAY;SAAa,EAChDsC,OAAO;QAEV,qCAAqC;QACrC,MAAM6E,qBAAqBJ,UAAUvE,GAAG,CAAC,CAACyE;YACxC,MAAMG,gBAAgBF,aACnBG,MAAM,CAAC,CAACC,OAASA,KAAKC,WAAW,KAAKN,SAASvE,EAAE,IAAI4E,KAAKE,QAAQ,KAAK,kBACvEhF,GAAG,CAAC,CAAC8E,OAASA,KAAKG,UAAU;YAEhC,MAAMC,qBAAqBR,aACxBG,MAAM,CAAC,CAACC,OAASA,KAAKC,WAAW,KAAKN,SAASvE,EAAE,IAAI4E,KAAKE,QAAQ,KAAK,uBACvEhF,GAAG,CAAC,CAAC8E,OAASA,KAAKG,UAAU;YAEhC,MAAME,eAAeT,aAClBG,MAAM,CAAC,CAACC,OAASA,KAAKC,WAAW,KAAKN,SAASvE,EAAE,IAAI4E,KAAKE,QAAQ,KAAK,gBACvEhF,GAAG,CAAC,CAAC8E,OAASA,KAAKG,UAAU;YAEhC,MAAMG,OAAOV,aACVG,MAAM,CAAC,CAACC,OAASA,KAAKC,WAAW,KAAKN,SAASvE,EAAE,IAAI4E,KAAKE,QAAQ,KAAK,QACvEhF,GAAG,CAAC,CAAC8E,OAASA,KAAKG,UAAU;YAEhC,OAAO;gBACL/E,IAAIuE,SAASvE,EAAE;gBACfC,MAAMsE,SAAStE,IAAI;gBACnBkF,cAAcZ,SAASY,YAAY;gBACnCC,WAAWb,SAASa,SAAS;gBAC7BC,gBAAgBX;gBAChBY,qBAAqBN;gBACrBC,cAAcA;gBACdC,MAAMA;YACR;QACF;QAEA,OAAO;YACL7G,QAAQ;YACRC,MAAMmG;YACNjE,YAAY;gBACVvB;gBACAC;gBACAN;gBACAmE,YAAYC,KAAKC,IAAI,CAACrE,QAAQM;YAChC;QACF;IACF;IAGA,MAAMqG,eAAeC,iBAAoC,EAAE;QACzD,MAAM,EACJvF,IAAI,EACJmE,eAAe,EACfqB,YAAY,EACZC,SAAS,EACTL,cAAc,EACdC,mBAAmB,EACnBL,YAAY,EACZC,IAAI,EACL,GAAGM;QAEJ,sCAAsC;QACtC,MAAMG,oBAAoB,MAAMxI,YAAE,CAC/BC,UAAU,CAAC,kBACXC,KAAK,CAAC,MAAM,KAAK+G,iBACjB9G,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACiI,mBAAmB;YACtB,MAAM,IAAI9E,yBAAiB,CAAC;QAC9B;QAEA,mCAAmC;QACnC,MAAM+E,kBAAkB,MAAMzI,YAAE,CAC7BC,UAAU,CAAC,kBACXC,KAAK,CAAC,MAAM,KAAKoI,cACjBnI,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACkI,iBAAiB;YACpB,MAAM,IAAI/E,yBAAiB,CAAC;QAC9B;QAEA,gDAAgD;QAChD,MAAMgF,iBAAiB,MAAM1I,YAAE,CAC5B+D,UAAU,CAAC,aACXC,MAAM,CAAC;YACNlB;YACAmE;YACAqB;YACAC,WAAWA,aAAa;YACxBnF,YAAY,IAAIzC;YAChBsD,YAAY,IAAItD;QAClB,EACA,qBAAqB;SACpBkE,uBAAuB;QAExB,MAAM8D,aAAatH,OAAOqH,eAAevE,QAAQ;QAEnD,2DAA2D;QAC3D,MAAMyE,gBAAuB,EAAE;QAE/B,iBAAiB;QACjBV,gBAAgBW,QAAQ,CAACC;YACvBF,cAAcG,IAAI,CAAC;gBACjBrB,aAAaiB;gBACbhB,UAAU;gBACVC,YAAYkB;YACd;QACF;QAEA,sBAAsB;QACtBX,qBAAqBU,QAAQ,CAACC;YAC5BF,cAAcG,IAAI,CAAC;gBACjBrB,aAAaiB;gBACbhB,UAAU;gBACVC,YAAYkB;YACd;QACF;QAEA,eAAe;QACfhB,cAAce,QAAQ,CAACG,aAAaC;YAClCL,cAAcG,IAAI,CAAC;gBACjBrB,aAAaiB;gBACbhB,UAAU,CAAC,YAAY,CAAC;gBACxBC,YAAYoB;YACd;QACF;QAEA,kBAAkB;QAClB,IAAIjB,MAAM;YACRA,KAAKc,OAAO,CAAC,CAACK,KAAKD;gBACjBL,cAAcG,IAAI,CAAC;oBACjBrB,aAAaiB;oBACbhB,UAAU,CAAC,IAAI,CAAC;oBAChBC,YAAYsB;gBACd;YACF;QACF;QAEA,oDAAoD;QACpD,IAAIN,cAAc3C,MAAM,GAAG,GAAG;YAC5B,MAAMjG,YAAE,CAAC+D,UAAU,CAAC,mBAAmBC,MAAM,CAAC4E,eAAenG,OAAO;QACtE;QAEA,OAAO;YACLvB,QAAQ;YACRC,MAAM;gBAAE0B,IAAI8F;gBAAY7F;YAAK;QAC/B;IACF;IAEA,MAAMqG,eAAetG,EAAU,EAAE;QAC/B,iCAAiC;QACjC,MAAMuG,mBAAmB,MAAMpJ,YAAE,CAC9BC,UAAU,CAAC,aACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAAC6I,kBAAkB;YACrB,MAAM,IAAI7E,yBAAiB,CAAC;QAC9B;QAEA,uCAAuC;QACvC,MAAMvE,YAAE,CACL+E,UAAU,CAAC,aACX7E,KAAK,CAAC,MAAM,KAAK2C,IACjBJ,OAAO;QAEV,OAAO;YACLvB,QAAQ;YACR8D,SAAS;QACX;IACF;IAEA,mBAAmB;IACnB,MAAMqE,cAAc;QAClB,MAAMC,QAAQ,MAAMtJ,YAAE,CACpBC,UAAU,CAAC,eACXiC,QAAQ,CAAC,kCAAkC,SAAS,cACpDA,QAAQ,CAAC,2BAA2B,0BAA0B,SAC9D/B,MAAM,CAAC;YACP;YAAS;YAAgB;YAAW;YACpC;YAAY;YAAe;YAC3BoJ,IAAAA,WAAG,CAAA,CAAC,4BAA4B,CAAC,CAACjJ,EAAE,CAAC;YACrCiJ,IAAAA,WAAG,CAAA,CAAC,kCAAkC,CAAC,CAACjJ,EAAE,CAAC;YAC3CiJ,IAAAA,WAAG,CAAA,CAAC,sCAAsC,CAAC,CAACjJ,EAAE,CAAC;YAC/C;YACA;YAAgB;YAAqB;YAAgB;YAAc;YAAc;YAAiB;YAAmB;YAAwB;YAA0C;YAAmC;YAC1N;YAAe;YACf;SACA,EACAJ,KAAK,CAAC,iBAAiB,MAAM,MAC7BA,KAAK,CAAC,iBAAiB,MAAM,MAC7BuC,OAAO;QAER,MAAM+G,YAAY,CAACC,WAAmBC;YACrC,oDAAoD;YACpD,IAAID,cAAc,aAAaC,mBAAmB,GAAG;gBACnD,OAAO;YACT,OAAO,IAAID,cAAc,aAAaC,mBAAmB,GAAG;gBAC1D,OAAO;YACT,OAAO,IAAID,cAAc,aAAaC,mBAAmB,GAAG;gBAC1D,OAAO;YACT,OAAO,IAAID,cAAc,cAAcC,mBAAmB,GAAG;gBAC3D,OAAO;YACT,OAAO;gBACL,OAAO;YACT;QACF;QAEA,iCAAiC;QACjC,MAAMC,eAAeL,MAAMM,MAAM,CAAsB,CAACC,KAAKC;YAC3D,MAAM,EAAEjH,EAAE,EAAEkH,qBAAqB,EAAEC,QAAQ,EAAEC,4BAA4B,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE,GAAGN;YAEtI,IAAI,CAACD,GAAG,CAAChH,GAAG,EAAE;gBACZgH,GAAG,CAAChH,GAAG,GAAG;oBACRA;oBACAwH,WAAWP,KAAKO,SAAS;oBACzBvH,MAAMgH,KAAKhH,IAAI;oBACfwH,aAAaR,KAAKQ,WAAW;oBAC7BC,OAAOlJ,OAAOyI,KAAKS,KAAK;oBACxBC,QAAQhB,UAAUM,KAAKL,SAAS,EAAEK,KAAKJ,cAAc;oBACrDe,YAAYX,KAAKW,UAAU;oBAC3BC,qCAAqCrJ,OAAOyI,KAAKY,mCAAmC;oBACpFC,8BAA8BtJ,OAAOyI,KAAKa,4BAA4B;oBACtEC,gBAAgB,EAAE;gBACpB;YACF;YAEA,wCAAwC;YACxC,IAAIb,uBAAuB;gBACzBF,GAAG,CAAChH,GAAG,CAAC+H,cAAc,CAAC7B,IAAI,CAAC;oBAC1B8B,WAAWf,KAAKe,SAAS;oBACzBC,kBAAkBf,sBAAsBgB,OAAO,CAAC,QAAQ,KAAKC,WAAW;oBACxEhB;oBACAO,OAAOlJ,OAAO6I;oBAEd,+BAA+B;oBAC/BO,YAAYL;oBACZH;gBACF;YACF;YAEA,OAAOJ;QACT,GAAG,CAAC;QAEJ,uBAAuB;QACvB,MAAMoB,iBAAiBC,OAAOlH,MAAM,CAAC2F;QAErC,OAAO;YACLzI,QAAO;YACPC,MAAM8J;QACR;IACF;IAEA,MAAME,WAAWC,aAA4B,EAAElH,MAAc,EAAE;QAC7D,MAAME,UAAUF;QAChB,MAAMmH,gBAAgB;QACtB,MAAMC,WAAW;QACjB,MAAMC,eAAe;QACrB,MAAMC,oBAAoB;QAE1B,MAAM,EACJ1I,IAAI,EACJwH,WAAW,EACXC,KAAK,EACLC,MAAM,EACNiB,QAAQ,EACRhB,UAAU,EACVC,mCAAmC,EACnCC,4BAA4B,EAC7B,GAAGS;QAEJ,MAAMf,YAAY,AAACoB,aAAa,OAAQ,IAAI;QAE5C,MAAMjC,YAAY,CAACgB;YACjB,IAAIA,WAAW,WAAW;gBACxB,OAAO;oBAAEf,WAAW;oBAAWC,gBAAgB;gBAAE;YACnD,OAAO,IAAIc,WAAW,aAAa;gBACjC,OAAO;oBAAEf,WAAW;oBAAWC,gBAAgB;gBAAE;YACnD,OAAO,IAAIc,WAAW,cAAc;gBAClC,OAAO;oBAAEf,WAAW;oBAAWC,gBAAgB;gBAAE;YACnD,OAAO,IAAIc,WAAW,YAAY;gBAChC,OAAO;oBAAEf,WAAW;oBAAYC,gBAAgB;gBAAE;YACpD,OAAO;gBACL,OAAO;oBAAED,WAAW;oBAAWC,gBAAgB;gBAAE;YACnD;QACF;QAEA,MAAM,EAAED,SAAS,EAAEC,cAAc,EAAE,GAAuFF,UAAUgB;QAEpI,wCAAwC;QACxC,MAAMkB,aAAa,MAAM1L,YAAE,CACxB+D,UAAU,CAAC,SACXC,MAAM,CAAC;YACNlB;YACAwH;YACAC;YACAe;YACA7B;YACAC;YACAW;YACAxI,SAASwJ;YACTjH;YACAqG;YACAc;YACAC;YACAd;YACAC;YACAvH,YAAY,IAAIzC;YAChBsD,YAAY,IAAItD;QAClB,EACA,qBAAqB;SACpBkE,uBAAuB;QAExB,MAAM8G,SAAStK,OAAOqK,WAAWvH,QAAQ;QAE3C,OAAO;YACLjD,QAAQ;YACRC,MAAM;gBAAE0B,IAAI8I;gBAAQ7I;YAAK;QAC3B;IACF;IAEA,MAAM8I,WAAW/I,EAAU,EAAEgJ,aAA4B,EAAE;QACzD,MAAM,EACJ/I,IAAI,EACJwH,WAAW,EACXC,KAAK,EACLe,QAAQ,EACR7B,SAAS,EACTC,cAAc,EACd+B,QAAQ,EACR5J,OAAO,EACPuC,OAAO,EACPqG,UAAU,EACVc,YAAY,EACZC,iBAAiB,EACjBd,mCAAmC,EACnCC,4BAA4B,EAC7B,GAAGkB;QAEJ,MAAMxB,YAAY,AAACoB,aAAa,OAAQ,IAAI;QAE5C,qCAAqC;QACrC,MAAMC,aAAa,MAAM1L,YAAE,CACxB0E,WAAW,CAAC,SACZC,GAAG,CAAC;YACH7B;YACAwH;YACAC;YACAe;YACA7B;YACAC;YACAW;YACAxI;YACAuC;YACAqG;YACAc;YACAC;YACAd;YACAC;YACA1G,YAAY,IAAItD;QAClB,GACCT,KAAK,CAAC,MAAM,KAAK2C,GAClB,qBAAqB;SACpBgC,uBAAuB;QAE1B,OAAO;YACL3D,QAAQ;YACRC,MAAM;gBAAE0B;gBAAIC;YAAK;QACnB;IACF;IAEA,MAAMgJ,WAAWjJ,EAAU,EAAEkJ,aAA4B,EAAE;QACzDC,QAAQC,GAAG,CAAC,iBAAiBF;QAG7B,MAAMG,uBAAuB;YAC3B,UAAU;YACV,uBAAuB;YACvB,aAAa;QACf;QAEA,MAAMC,sBAAsBD,oBAAoB,CAACH,cAAcjB,gBAAgB,CAAC;QAEhF,MAAM,EACJd,QAAQ,EACRO,KAAK,EACL,YAAY;QACZE,UAAU,EACVR,4BAA4B,EAC7B,GAAG8B;QAEJ,MAAMT,WAAW;QAEjB,6CAA6C;QAC7C,MAAMc,0BAA0B,MAAMpM,YAAE,CACrCC,UAAU,CAAC,qBACXC,KAAK,CAAC,MAAM,KAAKiM,qBACjBhM,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAAC6L,yBAAyB;YAC5B,MAAM,IAAI7H,yBAAiB,CAAC;QAC9B;QAEA,0DAA0D;QAC1D,MAAM8H,qBAAqB,MAAMrM,YAAE,CAChCC,UAAU,CAAC,4BACXC,KAAK,CAAC,WAAW,KAAK2C,IACtB3C,KAAK,CAAC,uBAAuB,KAAKiM,qBAClCjM,KAAK,CAAC,YAAY,KAAK8J,UACvB7J,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI8L,oBAAoB;YACtB,gEAAgE;YAChE,MAAMC,mBAAmB,MAAMtM,YAAE,CAC9B0E,WAAW,CAAC,4BACZC,GAAG,CAAC;gBACH4F;gBACAe;gBACAb;gBACAR;gBACAhG,YAAY,IAAItD;YAClB,GACCT,KAAK,CAAC,MAAM,KAAKmM,mBAAmBxJ,EAAE,EACtCgC,uBAAuB;QAC5B;QAEA,0EAA0E;QAC1E,IAAI,CAACwH,oBAAoB;YACvB,MAAMC,mBAAmB,MAAMtM,YAAE,CAChC+D,UAAU,CAAC,4BACXC,MAAM,CAAC;gBACNuI,SAAS1J;gBACTmH;gBACAmC;gBACA5B;gBACAe;gBACAb;gBACAR;gBACA7G,YAAY,IAAIzC;gBAChBsD,YAAY,IAAItD;YAClB,GACCkE,uBAAuB;QAC1B;QAEA,OAAO;YACL3D,QAAQ;YACRC,MAAM;gBAAE0B;gBAAIsJ;YAAoB;QAClC;IACF;IAEA,MAAMK,WAAW3J,EAAU,EAAE;QAC3B,6BAA6B;QAC7B,MAAM4J,eAAe,MAAMzM,YAAE,CAC1BC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAACkM,cAAc;YACjB,MAAM,IAAIlI,yBAAiB,CAAC;QAC9B;QAEA,iCAAiC;QACjC,MAAMvE,YAAE,CACL0E,WAAW,CAAC,SACZC,GAAG,CAAC;YACH+H,YAAY,IAAI/L;QAClB,GACCT,KAAK,CAAC,MAAM,KAAK2C,IACjBJ,OAAO;QAEV,OAAO;YACLvB,QAAQ;YACR8D,SAAS;QACX;IACF;IAEA,MAAM2H,iBAAiB9J,EAAU,EAAE;QACjC,6CAA6C;QAC7C,MAAMwJ,qBAAqB,MAAMrM,YAAE,CAChCC,UAAU,CAAC,4BACXC,KAAK,CAAC,MAAM,KAAK2C,IACjB1C,MAAM,CAAC,MACPI,gBAAgB;QAEnB,IAAI,CAAC8L,oBAAoB;YACvB,MAAM,IAAI9H,yBAAiB,CAAC;QAC9B;QAEA,iCAAiC;QACjC,MAAMvE,YAAE,CACL0E,WAAW,CAAC,4BACZC,GAAG,CAAC;YACH+H,YAAY,IAAI/L;QAClB,GACCT,KAAK,CAAC,MAAM,KAAK2C,IACjBJ,OAAO;QAEV,OAAO;YACLvB,QAAQ;YACR8D,SAAS;QACX;IACF;IAEA,MAAM4H,oBAAoBjL,KAAU,EAAE;QACpC,MAAM,EAAEC,CAAC,EAAEV,MAAM,EAAE,GAAGS;QACtB,IAAI,EAAEG,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAChC,MAAMK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,IAAIE,eAAejC,YAAE,CAClBC,UAAU,CAAC,uBACXiC,QAAQ,CAAC,SAAS,+BAA+B,YACjDA,QAAQ,CAAC,SAAS,+BAA+B,YACjDA,QAAQ,CAAC,4BAA4B,gDAAgD,+BACrFA,QAAQ,CAAC,qBAAqB,gDAAgD,wBAC9E/B,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAD,KAAK,CAAC,kCAAkC,MAAM,MAC9CkC,OAAO,CAAC,kCAAkC;QAE7C,IAAIR,GAAG;YACLK,eAAeA,aAAa/B,KAAK,CAAC,CAACmC,KACjCA,GAAGC,EAAE,CAAC;oBACJD,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;oBACjCS,GAAG,eAAe,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;iBACnC;QAEL;QAEA,IAAIV,QAAQ;YACVe,eAAeA,aAAa/B,KAAK,CAAC,8BAA8B,KAAKgB;QACvE;QAEA,MAAM,CAACC,MAAMM,MAAM,GAAG,MAAMc,QAAQC,GAAG,CAAC;YACtCP,aAAaF,KAAK,CAACV,OAAOU,QAAQC,MAAM,CAACX,OAAOW,SAASS,OAAO;YAChEzC,YAAE,CAACC,UAAU,CAAC,uBAAuBE,MAAM,CAACH,YAAE,CAACI,EAAE,CAACsC,QAAQ,GAAGpC,EAAE,CAAC,UAAUC,gBAAgB;SAC3F;QAED,OAAO;YACLW,QAAQ;YACRC,MAAMA,KAAKwB,GAAG,CAAC,CAACC,MAAS,CAAA;oBACvBC,IAAID,IAAIC,EAAE;oBACVgK,WAAWjK,IAAIiK,SAAS;oBACxBC,YAAYlK,IAAIkK,UAAU;oBAC1B5L,QAAQ0B,IAAI1B,MAAM;oBAClB8I,UAAUpH,IAAIoH,QAAQ;oBACtBO,OAAO3H,IAAI2H,KAAK;oBAChBe,UAAU1I,IAAI0I,QAAQ;oBACtByB,YAAYnK,IAAImK,UAAU;oBAC1BC,UAAUpK,IAAIoK,QAAQ;oBACtBC,mBAAmBrK,IAAIqK,iBAAiB;oBACxCC,sBAAsBtK,IAAIsK,oBAAoB;oBAC9CC,UAAUvK,IAAIuK,QAAQ;oBACtBC,kBAAkBxK,IAAIwK,gBAAgB;oBACtCC,gBAAgBzK,IAAIyK,cAAc;oBAClC5C,YAAY7H,IAAI6H,UAAU;oBAC1BrH,YAAYR,IAAIQ,UAAU;oBAC1BkK,WAAW1K,IAAI0K,SAAS;oBACxBvD,uBAAuBnH,IAAImH,qBAAqB;gBAClD,CAAA;YACA1G,YAAY;gBACVvB;gBACAC;gBACAN,OAAOJ,OAAOI,OAAOA;YACvB;QACF;IACF;IAEA,MAAM8L,mBAAmB5L,KAAU,EAAE;QACnC,MAAM,EAAEC,CAAC,EAAEV,MAAM,EAAE,GAAGS;QACtB,IAAI,EAAEG,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAChC,MAAMK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,IAAIE,eAAejC,YAAE,CAClBC,UAAU,CAAC,gBACXiC,QAAQ,CAAC,SAAS,wBAAwB,YAC1CA,QAAQ,CAAC,qBAAqB,oCAAoC,wBAClE/B,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAD,KAAK,CAAC,2BAA2B,MAAM,MACvCkC,OAAO,CAAC,2BAA2B;QAEtC,IAAIR,GAAG;YACLK,eAAeA,aAAa/B,KAAK,CAAC,CAACmC,KACjCA,GAAGC,EAAE,CAAC;oBACJD,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;oBACjCS,GAAG,eAAe,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;iBACnC;QAEL;QAEA,IAAIV,QAAQ;YACVe,eAAeA,aAAa/B,KAAK,CAAC,uBAAuB,KAAKgB;QAChE;QAEA,MAAM,CAACC,MAAMM,MAAM,GAAG,MAAMc,QAAQC,GAAG,CAAC;YACtCP,aAAaF,KAAK,CAACV,OAAOU,QAAQC,MAAM,CAACX,OAAOW,SAASS,OAAO;YAChEzC,YAAE,CAACC,UAAU,CAAC,gBAAgBE,MAAM,CAACH,YAAE,CAACI,EAAE,CAACsC,QAAQ,GAAGpC,EAAE,CAAC,UAAUC,gBAAgB;SACpF;QAED,OAAO;YACLW,QAAQ;YACRC,MAAMA,KAAKwB,GAAG,CAAC,CAACC,MAAS,CAAA;oBACvBC,IAAID,IAAIC,EAAE;oBACVgK,WAAWjK,IAAIiK,SAAS;oBACxBC,YAAYlK,IAAIkK,UAAU;oBAC1BU,yBAAyB5K,IAAI4K,uBAAuB;oBACpDC,QAAQ7K,IAAI6K,MAAM;oBAClBnC,UAAU1I,IAAI0I,QAAQ;oBACtBpK,QAAQ0B,IAAI1B,MAAM;oBAClBwM,aAAa9K,IAAI8K,WAAW;oBAC5BC,WAAW/K,IAAI+K,SAAS;oBACxBvK,YAAYR,IAAIQ,UAAU;oBAC1B2G,uBAAuBnH,IAAImH,qBAAqB;gBAClD,CAAA;YACA1G,YAAY;gBACVvB;gBACAC;gBACAN,OAAOJ,OAAOI,OAAOA;YACvB;QACF;IACF;IAEA,aAAa;IACf,MAAMmM,cAAcjM,KAAU,EAAE;QAC5B,MAAM,EAAEC,CAAC,EAAEV,MAAM,EAAE2M,SAAS,EAAE,GAAGlM;QACjC,IAAI,EAAEG,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAChC,MAAMK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,IAAIE,eAAejC,YAAE,CAClBC,UAAU,CAAC,cACXiC,QAAQ,CAAC,SAAS,sBAAsB,YACxC/B,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAD,KAAK,CAAC,yBAAyB,MAAM,MACrCkC,OAAO,CAAC,yBAAyB;QAEpC,IAAIR,GAAG;YACLK,eAAeA,aAAa/B,KAAK,CAAC,CAACmC,KACjCA,GAAGC,EAAE,CAAC;oBACJD,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;oBACjCS,GAAG,eAAe,QAAQ,CAAC,CAAC,EAAET,EAAE,CAAC,CAAC;iBACnC;QAEL;QAEA,IAAIV,QAAQ;YACVe,eAAeA,aAAa/B,KAAK,CAAC,qBAAqB,KAAKgB;QAC9D;QAEA,IAAI2M,WAAW;YACb5L,eAAeA,aAAa/B,KAAK,CAAC,wBAAwB,KAAK2N;QACjE;QAEA,MAAM,CAAC1M,MAAMM,MAAM,GAAG,MAAMc,QAAQC,GAAG,CAAC;YACtCP,aAAaF,KAAK,CAACV,OAAOU,QAAQC,MAAM,CAACX,OAAOW,SAASS,OAAO;YAChEzC,YAAE,CAACC,UAAU,CAAC,cAAcE,MAAM,CAACH,YAAE,CAACI,EAAE,CAACsC,QAAQ,GAAGpC,EAAE,CAAC,UAAUC,gBAAgB;SAClF;QAED,OAAO;YACLW,QAAQ;YACRC,MAAMA,KAAKwB,GAAG,CAAC,CAACC,MAAS,CAAA;oBACvBC,IAAID,IAAIC,EAAE;oBACV3B,QAAQ0B,IAAI1B,MAAM;oBAClB4B,MAAMF,IAAIE,IAAI;oBACdC,OAAOH,IAAIG,KAAK;oBAChB8K,WAAWjL,IAAIiL,SAAS;oBACxBC,aAAalL,IAAIkL,WAAW;oBAC5B1K,YAAYR,IAAIQ,UAAU;gBAC5B,CAAA;YACAC,YAAY;gBACVvB;gBACAC;gBACAN,OAAOJ,OAAOI,OAAOA;YACvB;QACF;IACF;IAII,MAAMsM,uBAAuBhL,KAAa,EAAED,IAAY,EAAEkL,WAAoB,EAAmB;QACjG,MAAMC,UAAU,MAAM,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;YAChDlJ,MAAM;YACNmJ,SAAS;YACTtL,OAAOA;YACPuL,eAAe;YACfC,YAAY;gBACVC,YAAY1L,KAAKG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC9BwL,WAAW3L,KAAKG,KAAK,CAAC,KAAKyL,KAAK,CAAC,GAAGC,IAAI,CAAC;YAC3C;YACAC,UAAU;gBAAEZ,aAAaA,eAAe;YAAG;QAC7C;QACA,OAAOC,QAAQpL,EAAE;IACjB;IAEF,MAAMgM,gBAAgBhM,EAAU,EAAEiM,kBAAuB,EAAE;QACzD,MAAM,EAAE5N,MAAM,EAAE2M,SAAS,EAAE,GAAGiB;QAE9B,gCAAgC;QAChC,MAAMC,oBAAoB,MAAM/O,YAAE,CAC/BC,UAAU,CAAC,cACXC,KAAK,CAAC,WAAW,KAAK2C,IACtB1C,MAAM,CAAC;YAAC;YAAM;SAAc,EAC5BI,gBAAgB;QAEnB,IAAI,CAACwO,mBAAmB;YACtB,MAAM,IAAIxK,yBAAiB,CAAC;QAC9B;QAEA,MAAMyK,QAAQ,CAACD,kBAAkBjB,WAAW;QAC5C,IAAImB,mBAAmB/N,WAAW,WAAW,IAAIP,SAAS;QAC1D,IAAImN,cAAciB,kBAAkBjB,WAAW,GAAGiB,kBAAkBjB,WAAW,GAAGmB;QAElF,sBAAsB;QACtB,MAAMjP,YAAE,CACL0E,WAAW,CAAC,cACZC,GAAG,CAAC;YACHzD;YACA2M,WAAWA,YAAY,IAAI;YAC3BC;YACA7J,YAAY,IAAItD;QAClB,GACCT,KAAK,CAAC,WAAW,KAAK2C,IACtBJ,OAAO;QAGV,IAAIuM,SAAS9N,WAAW,UAAU;YAChC,MAAMgO,UAAU,MAAMlP,YAAE,CAACC,UAAU,CAAC,cACnCiC,QAAQ,CAAC,SAAS,YAAY,sBAC9B/B,MAAM,CAAC;gBAAC;gBAAY;gBAAe;aAAa,EAChDD,KAAK,CAAC,sBAAsB,KAAK2C,IACjCtC,gBAAgB;YAEjB,IAAI2O,SAAS;gBACX,MAAMpM,OAAeoM,QAAQpM,IAAI,IAAI;gBACrC,MAAMC,QAAgBmM,QAAQnM,KAAK,IAAI;gBACvC,MAAMkL,UAAU,MAAM,IAAI,CAACF,sBAAsB,CAAChL,OAAOD,MAAMD,GAAGsM,QAAQ;gBAE1E,IAAGlB,SAAS;oBACV,MAAMjO,YAAE,CACP0E,WAAW,CAAC,cACZC,GAAG,CAAC;wBACHyK,UAAUnB;wBACVhK,YAAY,IAAItD;oBAClB,GACCT,KAAK,CAAC,WAAW,KAAK2C,IACtBJ,OAAO;gBACV;YACF;QAEF;QAEA,OAAO;YACLvB,QAAQ;YACRC,MAAM,EAAE;QACV;IACF;IAEA,iBAAiB;IACjB,MAAMkO,QAAQC,IAAS,EAAE;QACvB,IAAI;YACF,+BAA+B;YAC/B,IAAI,CAACA,QAAQ,CAACA,KAAKpK,IAAI,IAAI,CAACoK,KAAKnO,IAAI,IAAI,CAACmO,KAAKnO,IAAI,CAACoO,MAAM,EAAE;gBAC1DvD,QAAQwD,KAAK,CAAC,0BAA0BF;gBACxC,OAAO;oBACLpO,QAAQ;oBACR8D,SAAS;gBACX;YACF;YAEA,MAAMyK,QAAQH;YACd,MAAMI,YAAYD,MAAMvK,IAAI;YAE5B,kDAAkD;YAClD,yDAAyD;YACzD,qDAAqD;YACrD,kBAAkB;YAClB,eAAe;YACf,sCAAsC;YACtC,KAAK;YAEL,iDAAiD;YACjD,4EAA4E;YAE5E,IAAI;gBACF,OAAQwK;oBACN,wBAAwB;oBACxB,KAAK;wBACH,MAAM,IAAI,CAACC,yBAAyB,CAACF,MAAMtO,IAAI,CAACoO,MAAM;wBACtD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACK,yBAAyB,CAACH,MAAMtO,IAAI,CAACoO,MAAM;wBACtD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACM,yBAAyB,CAACJ,MAAMtO,IAAI,CAACoO,MAAM;wBACtD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACO,8BAA8B,CAACL,MAAMtO,IAAI,CAACoO,MAAM;wBAC3D;oBAEF,uBAAuB;oBACvB,KAAK;wBACH,MAAM,IAAI,CAACQ,oBAAoB,CAACN,MAAMtO,IAAI,CAACoO,MAAM;wBACjD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACS,iBAAiB,CAACP,MAAMtO,IAAI,CAACoO,MAAM;wBAC9C;oBACF,KAAK;wBACH,MAAM,IAAI,CAACU,0BAA0B,CAACR,MAAMtO,IAAI,CAACoO,MAAM;wBACvD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACW,qBAAqB,CAACT,MAAMtO,IAAI,CAACoO,MAAM;wBAClD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACY,oBAAoB,CAACV,MAAMtO,IAAI,CAACoO,MAAM;wBACjD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACa,8BAA8B,CAACX,MAAMtO,IAAI,CAACoO,MAAM;wBAC3D;oBACF,KAAK;wBACH,MAAM,IAAI,CAACc,4BAA4B,CAACZ,MAAMtO,IAAI,CAACoO,MAAM;wBACzD;oBACF,KAAK;wBACH,MAAM,IAAI,CAACe,iCAAiC,CAACb,MAAMtO,IAAI,CAACoO,MAAM;wBAC9D;oBAEF;wBACEvD,QAAQC,GAAG,CAAC,CAAC,oBAAoB,EAAEyD,WAAW;gBAClD;YACF,EAAE,OAAOa,cAAc;gBACrBvE,QAAQwD,KAAK,CAAC,CAAC,yBAAyB,EAAEE,UAAU,CAAC,CAAC,EAAEa;gBACxD,OAAO;oBACLrP,QAAQ;oBACR8D,SAAS,CAAC,yBAAyB,EAAE0K,UAAU,EAAE,EAAEa,aAAavL,OAAO,EAAE;gBAC3E;YACF;YAEA,OAAO;gBACL9D,QAAQ;gBACR8D,SAAS;YACX;QACF,EAAE,OAAOwK,OAAO;YACdxD,QAAQwD,KAAK,CAAC,8BAA8BA;YAC5C,OAAO;gBACLtO,QAAQ;gBACR8D,SAAS,CAAC,eAAe,EAAEwK,MAAMxK,OAAO,EAAE;YAC5C;QACF;IACF;IAEA,yCAAyC;IACzC,MAAc2K,0BAA0Ba,YAAiB,EAAE;QACzD,IAAI;YACF,MAAMC,aAAaD,aAAaE,QAAQ;YACxC,MAAMC,OAAO,MAAM,IAAI,CAACC,yBAAyB,CAACH;YAElD,IAAI,CAACE,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,2CAA2C,EAAEiB,YAAY;gBACxE;YACF;YAEAzE,QAAQC,GAAG,CAAC,CAAC,iDAAiD,EAAE0E,KAAK9N,EAAE,CAAC,eAAe,EAAE4N,WAAW,mBAAmB,EAAED,aAAa3N,EAAE,CAAC,UAAU,EAAE2N,aAAatP,MAAM,EAAE;YAE1K,wDAAwD;YACxD,MAAM2P,uBAAuB,MAAM7Q,YAAE,CAClCC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAKsQ,aAAa3N,EAAE,EAC1D1C,MAAM,CAAC;gBAAC;gBAAM;aAAS,EACvBI,gBAAgB;YAEnB,IAAIsQ,sBAAsB;gBACxB7E,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,yCAAyC,EAAEgO,qBAAqB3P,MAAM,CAAC,iBAAiB,CAAC;gBAEnI,iEAAiE;gBACjE,IAAIsP,aAAatP,MAAM,KAAK,YAAY2P,qBAAqB3P,MAAM,KAAK,UAAU;oBAChF,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;wBACHzD,QAAQ;wBACR+C,YAAY,IAAItD;oBAClB,GACCT,KAAK,CAAC,MAAM,KAAK2Q,qBAAqBhO,EAAE,EACxCJ,OAAO;oBAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,gCAAgC,CAAC;gBAC7E;gBAEA,OAAOgO,qBAAqBhO,EAAE;YAChC;YAEA,MAAMiO,wBAAwB,MAAM,IAAI,CAACC,iCAAiC,CAACP,aAAaQ,KAAK,CAAC7P,IAAI,CAAC,EAAE,CAACoJ,KAAK,CAAC1H,EAAE;YAE9G,IAAI,CAACiO,uBAAuB;gBAC1B9E,QAAQwD,KAAK,CAAC,CAAC,sDAAsD,EAAEgB,aAAaQ,KAAK,CAAC7P,IAAI,CAAC,EAAE,CAACoJ,KAAK,CAAC1H,EAAE,EAAE;gBAC5G;YACF;YAEA,MAAMoO,WAAW,MAAM,IAAI,CAACC,kCAAkC,CAACJ,sBAAsBjO,EAAE;YAEvF,IAAI,CAACoO,UAAU;gBACbjF,QAAQwD,KAAK,CAAC,CAAC,sEAAsE,EAAEsB,sBAAsBjO,EAAE,EAAE;gBACjH;YACF;YAEA,4FAA4F;YAC5F,IAAI3B;YAEJ,IAAIsP,aAAatP,MAAM,KAAK,UAAU;gBACpCA,SAAS;gBACT8K,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,8EAA8E,CAAC;YAC3H,OAAO,IAAI2N,aAAatP,MAAM,KAAK,YAAY;gBAC7CA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,YAAY;gBAC7CA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,UAAU;gBAC3CA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,YAAY;gBAC7CA,SAAS,UAAU,gCAAgC;YACrD,OAAO,IAAIsP,aAAatP,MAAM,KAAK,cAAc;gBAC/CA,SAAS,UAAU,0EAA0E;gBAC7F8K,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,sFAAsF,CAAC;YACnI,OAAO;gBACL3B,SAAS,UAAU,qDAAqD;gBACxE8K,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,kBAAkB,EAAE2N,aAAatP,MAAM,CAAC,sDAAsD,CAAC;YAC3I;YAEA,0BAA0B;YAC1B,MAAMiQ,UAAUX,aAAaY,SAAS,KAAK;YAE3C,+CAA+C;YAC/C,IAAIC;YACJ,IAAIb,aAAac,oBAAoB,EAAE;gBACrCD,YAAY,IAAI1Q,KAAK6P,aAAac,oBAAoB,GAAG;YAC3D,OAAO,IAAId,aAAae,OAAO,EAAE;gBAC/BF,YAAY,IAAI1Q,KAAK6P,aAAae,OAAO,GAAG;YAC9C,OAAO;gBACLF,YAAY,IAAI1Q,QAAQ,kCAAkC;YAC5D;YAEA,4DAA4D;YAC5D,IAAI6Q,kBAA+B;YACnC,IAAIhB,aAAaiB,kBAAkB,EAAE;gBACnCD,kBAAkB,IAAI7Q,KAAK6P,aAAaiB,kBAAkB,GAAG;YAC/D;YAEA,wCAAwC;YACxC,MAAMC,qBAAqB,MAAM1R,YAAE,CAChC+D,UAAU,CAAC,uBACXC,MAAM,CAAC;gBACNI,SAASuM,KAAK9N,EAAE;gBAChB0J,SAAS0E,SAAS1E,OAAO;gBACzBoF,0BAA0Bb,sBAAsBjO,EAAE;gBAClDoH,8BAA8BuG,aAAa3N,EAAE;gBAC7CmH,UAAU8G,sBAAsB9G,QAAQ,IAAI;gBAC5C9I;gBACAqJ,OAAOlJ,OAAOmP,aAAaQ,KAAK,CAAC7P,IAAI,CAAC,EAAE,CAACoJ,KAAK,CAACqH,WAAW,IAAI;gBAC9DtG,UAAUkF,aAAalF,QAAQ,CAACuG,WAAW;gBAC3C9E,YAAYsE;gBACZrE,UAAUwD,aAAasB,SAAS,GAAG,IAAInR,KAAK6P,aAAasB,SAAS,GAAG,QAAQ;gBAC7E7E,mBAAmBuE;gBACnBtE,sBAAsBsD,aAAatD,oBAAoB,IAAI;gBAC3DC,UAAUgE;gBACV/D,kBAAkBoD,aAAauB,WAAW,GAAG,IAAIpR,KAAK6P,aAAauB,WAAW,GAAG,QAAQ;gBACzF1E,gBAAgBmD,aAAaY,SAAS,GAAG,IAAIzQ,KAAK6P,aAAaY,SAAS,GAAG,QAAQ;gBACnF3G,YAAYwG,SAASxG,UAAU;gBAC/BrH,YAAY,IAAIzC;gBAChBsD,YAAY,IAAItD;YAClB,GACCJ,gBAAgB;YAEnByL,QAAQC,GAAG,CAAC,CAAC,6CAA6C,EAAE0E,KAAK9N,EAAE,CAAC,aAAa,EAAE3B,OAAO,CAAC,CAAC;YAE5F,OAAOwQ,sBAAsB,cAAcA,qBAAqBrQ,OAAOqQ,mBAAmBvN,QAAQ,IAAI;QACxG,EAAE,OAAOqL,OAAO;YACdxD,QAAQwD,KAAK,CAAC,4CAA4CA;YAC1D,MAAMA;QACR;IACF;IAEA,MAAcI,0BAA0BY,YAAiB,EAAE;QACzD,IAAI;YACF,gCAAgC;YAChC,MAAMK,uBAAuB,MAAM7Q,YAAE,CAClCC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAKsQ,aAAa3N,EAAE,EAC1D1C,MAAM,CAAC;gBAAC;gBAAM;gBAAW;aAAU,EACnCI,gBAAgB;YAEnB,IAAI,CAACsQ,sBAAsB;gBACzB7E,QAAQwD,KAAK,CAAC,CAAC,4CAA4C,EAAEgB,aAAa3N,EAAE,EAAE;gBAC9E;YACF;YAEA,oCAAoC;YACpC,IAAI3B,SAAmE;YACvE,IAAIsP,aAAatP,MAAM,KAAK,UAAU;gBACpCA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,YAAY;gBAC7CA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,YAAY;gBAC7CA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,UAAU;gBAC3CA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,YAAY;gBAC7CA,SAAS,UAAU,gCAAgC;YACrD,OAAO,IAAIsP,aAAatP,MAAM,KAAK,sBAAsB;gBACvDA,SAAS;YACX,OAAO,IAAIsP,aAAatP,MAAM,KAAK,cAAc;gBAC/C,0DAA0D;gBAC1DA,SAAS;gBACT8K,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,kFAAkF,CAAC;YAC/H;YAEA,4DAA4D;YAC5D,IAAI2O,kBAA+B;YACnC,IAAIhB,aAAaiB,kBAAkB,EAAE;gBACnCD,kBAAkB,IAAI7Q,KAAK6P,aAAaiB,kBAAkB,GAAG;YAC/D;YAEA,yBAAyB;YACzB,MAAMzR,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;gBACHzD;gBACA+L,mBAAmBuE;gBACnBxE,UAAUwD,aAAasB,SAAS,GAAG,IAAInR,KAAK6P,aAAasB,SAAS,GAAG,QAAQ;gBAC7E5E,sBAAsBsD,aAAatD,oBAAoB,IAAI;gBAC3DjJ,YAAY,IAAItD;YAClB,GACCT,KAAK,CAAC,MAAM,KAAK2Q,qBAAqBhO,EAAE,EACxCJ,OAAO;YAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAE4E,qBAAqBhO,EAAE,CAAC,qCAAqC,EAAE3B,OAAO,CAAC,CAAC;QACpG,EAAE,OAAOsO,OAAO;YACdxD,QAAQwD,KAAK,CAAC,gDAAgDA;YAC9D,MAAMA;QACR;IACF;IAEA,MAAcK,0BAA0BW,YAAiB,EAAE;QACzD,IAAI;YACF,gCAAgC;YAChC,MAAMK,uBAAuB,MAAM7Q,YAAE,CAClCC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAKsQ,aAAa3N,EAAE,EAC1D1C,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI,CAACsQ,sBAAsB;gBACzB7E,QAAQwD,KAAK,CAAC,CAAC,yCAAyC,EAAEgB,aAAa3N,EAAE,EAAE;gBAC3E;YACF;YAEA,wCAAwC;YACxC,MAAM3B,SAAqB;YAC3B,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;gBACHzD;gBACA8L,UAAU,IAAIrM;gBACdsD,YAAY,IAAItD;YAClB,GACCT,KAAK,CAAC,MAAM,KAAK2Q,qBAAqBhO,EAAE,EACxCJ,OAAO;YAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAE4E,qBAAqBhO,EAAE,CAAC,uBAAuB,CAAC;QAC5E,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,6CAA6CA;YAC3D,MAAMA;QACR;IACF;IAEA,MAAcM,+BAA+BU,YAAiB,EAAE;QAC9D,IAAI;YACF,gCAAgC;YAChC,MAAMK,uBAAuB,MAAM7Q,YAAE,CAClCC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAKsQ,aAAa3N,EAAE,EAC1D1C,MAAM,CAAC;gBAAC;gBAAM;aAAU,EACxBI,gBAAgB;YAEnB,IAAI,CAACsQ,sBAAsB;gBACzB7E,QAAQwD,KAAK,CAAC,CAAC,4DAA4D,EAAEgB,aAAa3N,EAAE,EAAE;gBAC9F;YACF;YAEA,uFAAuF;YACvFmJ,QAAQC,GAAG,CAAC,CAAC,oBAAoB,EAAE4E,qBAAqBhO,EAAE,CAAC,wCAAwC,EAAEgO,qBAAqBzM,OAAO,EAAE;QACrI,EAAE,OAAOoL,OAAO;YACdxD,QAAQwD,KAAK,CAAC,kDAAkDA;YAChE,MAAMA;QACR;IACF;IAEA,wCAAwC;IACxC,MAAcO,qBAAqBiC,OAAY,EAAE;QAC/ChG,QAAQC,GAAG,CAAC,4DAA4D+F,QAAQnP,EAAE;QAElF,IAAI;YACF,0DAA0D;YAC1D,IAAI,CAACmP,QAAQxB,YAAY,EAAE;gBACzBxE,QAAQC,GAAG,CAAC,CAAC,OAAO,EAAE+F,QAAQnP,EAAE,CAAC,sCAAsC,CAAC;gBACxE;YACF;YAEA,MAAM4N,aAAauB,QAAQtB,QAAQ;YACnC,MAAMC,OAAO,MAAM,IAAI,CAACC,yBAAyB,CAACH;YAElD,IAAI,CAACE,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,2CAA2C,EAAEiB,YAAY;gBACxE;YACF;YAEA,kCAAkC;YAClC,IAAID,eAAe,MAAMxQ,YAAE,CACxBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAK8R,QAAQxB,YAAY,EAC/DrQ,MAAM,CAAC;gBAAC;gBAAM;gBAAW;aAA2B,EACpDI,gBAAgB;YAEnB,8GAA8G;YAC9G,qDAAqD;YACrD,IAAI,CAACiQ,cAAc;gBACjBxE,QAAQC,GAAG,CAAC,CAAC,yCAAyC,EAAE+F,QAAQnP,EAAE,CAAC,oCAAoC,CAAC;gBAExG,IAAI;oBACF,+BAA+B;oBAC/B,MAAMoP,qBAAqB,MAAM,IAAI,CAAC/D,MAAM,CAACgE,aAAa,CAACC,QAAQ,CAACH,QAAQxB,YAAY;oBAExF,uCAAuC;oBACvC,MAAM,IAAI,CAACb,yBAAyB,CAACsC;oBAErC,gCAAgC;oBAChCzB,eAAe,MAAMxQ,YAAE,CACpBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAK8R,QAAQxB,YAAY,EAC/DrQ,MAAM,CAAC;wBAAC;wBAAM;wBAAW;qBAA2B,EACpDI,gBAAgB;gBACrB,EAAE,OAAOiP,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,oCAAoC,EAAEA,MAAMxK,OAAO,EAAE;gBACtE;YACF;YAEA,IAAI,CAACwL,cAAc;gBACjBxE,QAAQwD,KAAK,CAAC,CAAC,yCAAyC,EAAEwC,QAAQnP,EAAE,CAAC,0BAA0B,CAAC;gBAChG;YACF;YAEA,iCAAiC;YACjC,MAAMuP,kBAAkB,MAAMpS,YAAE,CAC7BC,UAAU,CAAC,mCACXqF,SAAS,CAAC,2BAA2B,2BAA2B,SAChEpF,KAAK,CAAC,UAAU,KAAKsQ,aAAamB,wBAAwB,EAC1DxR,MAAM,CAAC;gBAAC;aAA+B,EACvCI,gBAAgB;YAEnB,IAAI,CAAC6R,iBAAiB;gBACpBpG,QAAQwD,KAAK,CAAC,CAAC,wDAAwD,EAAEgB,aAAa3N,EAAE,EAAE;gBAC1F;YACF;YAEA,wDAAwD;YACxD,MAAMwP,sBAAsB,MAAMrS,YAAE,CACjCC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAK8R,QAAQnP,EAAE,EAChD1C,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI8R,qBAAqB;gBACvBrG,QAAQC,GAAG,CAAC,CAAC,kCAAkC,EAAE+F,QAAQnP,EAAE,EAAE;gBAC7D;YACF;YAEA,4CAA4C;YAC5C,MAAM7C,YAAE,CACL+D,UAAU,CAAC,gBACXC,MAAM,CAAC;gBACNI,SAASuM,KAAK9N,EAAE;gBAChB2K,yBAAyBwE,QAAQnP,EAAE;gBACnCsJ,qBAAqBiG,gBAAgBjG,mBAAmB;gBACxDsB,QAAQpM,OAAO2Q,QAAQM,UAAU,IAAI;gBACrChH,UAAU0G,QAAQ1G,QAAQ,CAACuG,WAAW;gBACtC3Q,QAAQ;gBACRwM,aAAa;gBACbC,WAAW6C,aAAa3N,EAAE;gBAC1BO,YAAY,IAAIzC;gBAChBsD,YAAY,IAAItD;YAClB,GACC8B,OAAO;YAEVuJ,QAAQC,GAAG,CAAC,CAAC,wDAAwD,EAAE+F,QAAQnP,EAAE,EAAE;QACrF,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,wCAAwCA;YACtD,MAAMA;QACR;IACF;IACA,MAAcQ,kBAAkBgC,OAAY,EAAE;QAChDhG,QAAQC,GAAG,CAAC,sDAAsD+F,QAAQnP,EAAE;QAC5E,IAAI;YACF,MAAM4N,aAAauB,QAAQtB,QAAQ;YACnC,MAAMC,OAAO,MAAM,IAAI,CAACC,yBAAyB,CAACH;YAElD,IAAI,CAACE,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,2CAA2C,EAAEiB,YAAY;gBACxE;YACF;YAEAzE,QAAQC,GAAG,CAAC,CAAC,uCAAuC,EAAE0E,KAAK9N,EAAE,CAAC,eAAe,EAAE4N,WAAW,cAAc,EAAEuB,QAAQnP,EAAE,EAAE;YAEtH,wEAAwE;YACxE,IAAI0P,iBAAiB;YACrB,IAAIP,QAAQQ,MAAM,EAAEC,sBAAsBjC,cAAc;gBACtD+B,iBAAiBP,QAAQQ,MAAM,CAACC,oBAAoB,CAACjC,YAAY;gBACjExE,QAAQC,GAAG,CAAC,CAAC,oCAAoC,EAAEsG,gBAAgB;YACrE,OAAO,IAAIP,QAAQxB,YAAY,EAAE;gBAC/B,iCAAiC;gBACjC+B,iBAAiBP,QAAQxB,YAAY;gBACrCxE,QAAQC,GAAG,CAAC,CAAC,gDAAgD,EAAEsG,gBAAgB;YACjF;YAEA,IAAI,CAACA,gBAAgB;gBACnBvG,QAAQwD,KAAK,CAAC,CAAC,6CAA6C,EAAEwC,QAAQnP,EAAE,EAAE;gBAC1E;YACF;YAEA,kCAAkC;YAClCmJ,QAAQC,GAAG,CAAC,CAAC,uCAAuC,EAAE+F,QAAQnP,EAAE,CAAC,EAAE,EAAE0P,gBAAgB;YACrF,IAAI/B,eAAe,MAAMxQ,YAAE,CACxBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAKqS,gBAC3CpS,MAAM,CAAC;gBAAC;gBAAM;gBAAW;gBAA4B;aAAS,EAC9DI,gBAAgB;YAEnB,8GAA8G;YAC9G,qDAAqD;YACrD,IAAI,CAACiQ,gBAAgB+B,gBAAgB;gBACnCvG,QAAQC,GAAG,CAAC,CAAC,yCAAyC,EAAE+F,QAAQnP,EAAE,CAAC,oCAAoC,CAAC;gBAExG,IAAI;oBACF,+BAA+B;oBAC/B,MAAMoP,qBAAqB,MAAM,IAAI,CAAC/D,MAAM,CAACgE,aAAa,CAACC,QAAQ,CAACI;oBACpEvG,QAAQC,GAAG,CAAC,CAAC,6BAA6B,EAAEgG,mBAAmBpP,EAAE,CAAC,UAAU,EAAEoP,mBAAmB/Q,MAAM,EAAE;oBAEzG,uCAAuC;oBACvC,MAAMwR,oBAAoB,MAAM,IAAI,CAAC/C,yBAAyB,CAACsC;oBAE/D,IAAIS,mBAAmB;wBACrB,gCAAgC;wBAChClC,eAAe,MAAMxQ,YAAE,CACpBC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKmB,OAAOqR,oBACxBvS,MAAM,CAAC;4BAAC;4BAAM;4BAAW;4BAA4B;yBAAS,EAC9DI,gBAAgB;oBACrB,OAAO;wBACL,iDAAiD;wBACjDiQ,eAAe,MAAMxQ,YAAE,CACpBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAKqS,gBAC3CpS,MAAM,CAAC;4BAAC;4BAAM;4BAAW;4BAA4B;yBAAS,EAC9DI,gBAAgB;oBACrB;gBACF,EAAE,OAAOiP,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,oCAAoC,EAAEA,MAAMxK,OAAO,EAAE;gBACtE;YACF;YAEA,IAAI,CAACwL,cAAc;gBACjBxE,QAAQwD,KAAK,CAAC,CAAC,yCAAyC,EAAEwC,QAAQnP,EAAE,CAAC,0BAA0B,CAAC;gBAChG;YACF;YAEAmJ,QAAQC,GAAG,CAAC,CAAC,uBAAuB,EAAEuE,aAAa3N,EAAE,CAAC,gBAAgB,EAAE2N,aAAatP,MAAM,EAAE;YAE7F,iCAAiC;YACjC,MAAMkR,kBAAkB,MAAMpS,YAAE,CAC7BC,UAAU,CAAC,mCACXqF,SAAS,CAAC,2BAA2B,2BAA2B,SAChEpF,KAAK,CAAC,UAAU,KAAKsQ,aAAamB,wBAAwB,EAC1DxR,MAAM,CAAC;gBAAC;aAA+B,EACvCI,gBAAgB;YAEnB,IAAI,CAAC6R,iBAAiB;gBACpBpG,QAAQwD,KAAK,CAAC,CAAC,wDAAwD,EAAEgB,aAAa3N,EAAE,EAAE;gBAC1F;YACF;YAEA,wDAAwD;YACxD,IAAIwP,sBAAsB,MAAMrS,YAAE,CAC/BC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAK8R,QAAQnP,EAAE,EAChD1C,MAAM,CAAC;gBAAC;gBAAM;aAAS,EACvBI,gBAAgB;YAEnB,IAAIoS,gBAA+B;YAEnC,IAAIN,qBAAqB;gBACvB,6DAA6D;gBAC7D,IAAIA,oBAAoBnR,MAAM,KAAK,QAAQ;oBACzC,MAAMlB,YAAE,CACL0E,WAAW,CAAC,gBACZC,GAAG,CAAC;wBACH8I,QAAQpM,OAAO2Q,QAAQY,WAAW,IAAI;wBACtC1R,QAAQ;wBACR+C,YAAY,IAAItD;oBAClB,GACCT,KAAK,CAAC,MAAM,KAAKmS,oBAAoBxP,EAAE,EACvCJ,OAAO;oBAEVuJ,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEoG,oBAAoBxP,EAAE,CAAC,8BAA8B,CAAC;gBACjF,OAAO;oBACLmJ,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEoG,oBAAoBxP,EAAE,CAAC,0BAA0B,CAAC;gBAC7E;gBACA8P,gBAAgBN,oBAAoBxP,EAAE;YACxC,OAAO;gBACL,+BAA+B;gBAC/B,MAAMiB,SAAS,MAAM9D,YAAE,CACpB+D,UAAU,CAAC,gBACXC,MAAM,CAAC;oBACNI,SAASuM,KAAK9N,EAAE;oBAChB2K,yBAAyBwE,QAAQnP,EAAE;oBACnCsJ,qBAAqBiG,gBAAgBjG,mBAAmB;oBACxDsB,QAAQpM,OAAO2Q,QAAQY,WAAW,IAAI;oBACtCtH,UAAU0G,QAAQ1G,QAAQ,CAACuG,WAAW;oBACtC3Q,QAAQ;oBACRwM,aAAa;oBACbC,WAAW6C,aAAa3N,EAAE;oBAC1BO,YAAY,IAAIzC;oBAChBsD,YAAY,IAAItD;gBAClB,GACCJ,gBAAgB;gBAEnByL,QAAQC,GAAG,CAAC,CAAC,oDAAoD,EAAE+F,QAAQnP,EAAE,EAAE;gBAE/E,IAAIiB,UAAUA,OAAOK,QAAQ,EAAE;oBAC7BwO,gBAAgBtR,OAAOyC,OAAOK,QAAQ;oBAEtC,mDAAmD;oBACnDkO,sBAAsB,MAAMrS,YAAE,CAC3BC,UAAU,CAAC,gBACXC,KAAK,CAAC,MAAM,KAAKyS,eACjBxS,MAAM,CAAC;wBAAC;wBAAM;qBAAS,EACvBI,gBAAgB;oBAEnByL,QAAQC,GAAG,CAAC,CAAC,yBAAyB,EAAE0G,eAAe;gBACzD;YACF;YAEA,8CAA8C;YAC9C3G,QAAQC,GAAG,CAAC,CAAC,2BAA2B,EAAEuE,aAAa3N,EAAE,CAAC,EAAE,EAAE2N,aAAatP,MAAM,EAAE;YAEnF,IAAIsP,aAAatP,MAAM,KAAK,UAAU;gBACpC,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;oBACHzD,QAAQ;oBACR+C,YAAY,IAAItD;gBAClB,GACCT,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChCJ,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,gCAAgC,CAAC;gBAE3E,8CAA8C;gBAC9C,MAAMgQ,sBAAsB,MAAM7S,YAAE,CACjCC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChC1C,MAAM,CAAC;oBAAC;iBAAS,EACjBI,gBAAgB;gBAEnByL,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEuE,aAAa3N,EAAE,CAAC,mBAAmB,EAAEgQ,qBAAqB3R,QAAQ;YACxG,OAAO;gBACL8K,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,wDAAwD,CAAC;YACrG;YAEAmJ,QAAQC,GAAG,CAAC,CAAC,uEAAuE,EAAE+F,QAAQnP,EAAE,EAAE;YAElG,mCAAmC;YACnC,0DAA0D;YAC1D,IAAImP,QAAQ9Q,MAAM,KAAK,UAAUyR,eAAe;gBAC9C3G,QAAQC,GAAG,CAAC,CAAC,OAAO,EAAE+F,QAAQnP,EAAE,CAAC,oCAAoC,CAAC;gBAEtE,sDAAsD;gBACtD,MAAM,IAAI,CAACiQ,4BAA4B,CACrCnC,KAAK9N,EAAE,EACP2N,aAAajE,OAAO,EACpBoG,eACAtR,OAAO2Q,QAAQY,WAAW,IAAI,KAC9BZ,QAAQ1G,QAAQ,CAACuG,WAAW;gBAG9B7F,QAAQC,GAAG,CAAC,CAAC,gDAAgD,EAAE+F,QAAQnP,EAAE,EAAE;YAC7E,OAAO;gBACLmJ,QAAQC,GAAG,CAAC,CAAC,OAAO,EAAE+F,QAAQnP,EAAE,CAAC,wEAAwE,CAAC;YAC5G;QAEF,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,0CAA0CA;YACxD,MAAMA;QACR;IACF;IAEI,MAAcuD,qBAAqBf,OAAY,EAAE;QAC/ChG,QAAQC,GAAG,CAAC,sDAAsD+F,QAAQnP,EAAE;QAC5E,IAAI;YACF,MAAM4N,aAAauB,QAAQtB,QAAQ;YACnC,MAAMC,OAAO,MAAM,IAAI,CAACC,yBAAyB,CAACH;YAElD,IAAI,CAACE,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,2CAA2C,EAAEiB,YAAY;gBACxE;YACF;YAEAzE,QAAQC,GAAG,CAAC,CAAC,uCAAuC,EAAE0E,KAAK9N,EAAE,CAAC,eAAe,EAAE4N,WAAW,cAAc,EAAEuB,QAAQnP,EAAE,EAAE;YAEtH,kCAAkC;YAClCmJ,QAAQC,GAAG,CAAC,CAAC,uCAAuC,EAAE+F,QAAQnP,EAAE,CAAC,EAAE,EAAEmP,QAAQxB,YAAY,EAAE;YAC3FxE,QAAQC,GAAG,CAAC+F;YACZ,IAAIxB,eAAe,MAAMxQ,YAAE,CACxBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAK8R,QAAQxB,YAAY,EAC/DrQ,MAAM,CAAC;gBAAC;gBAAM;gBAAW;gBAA4B;aAAS,EAC9DI,gBAAgB;YAGnB,8GAA8G;YAC9G,qDAAqD;YACrD,IAAI,CAACiQ,gBAAgBwB,QAAQxB,YAAY,EAAE;gBACzCxE,QAAQC,GAAG,CAAC,CAAC,yCAAyC,EAAE+F,QAAQnP,EAAE,CAAC,oCAAoC,CAAC;gBAExG,IAAI;oBACF,+BAA+B;oBAC/B,MAAMoP,qBAAqB,MAAM,IAAI,CAAC/D,MAAM,CAACgE,aAAa,CAACC,QAAQ,CAACH,QAAQxB,YAAY;oBACxFxE,QAAQC,GAAG,CAAC,CAAC,6BAA6B,EAAEgG,mBAAmBpP,EAAE,CAAC,UAAU,EAAEoP,mBAAmB/Q,MAAM,EAAE;oBAEzG,uCAAuC;oBACvC,MAAMqR,iBAAiB,MAAM,IAAI,CAAC5C,yBAAyB,CAACsC;oBAE5D,IAAIM,gBAAgB;wBAClB,gCAAgC;wBAChC/B,eAAe,MAAMxQ,YAAE,CACpBC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKmB,OAAOkR,iBACxBpS,MAAM,CAAC;4BAAC;4BAAM;4BAAW;4BAA4B;yBAAS,EAC9DI,gBAAgB;oBACrB,OAAO;wBACL,iDAAiD;wBACjDiQ,eAAe,MAAMxQ,YAAE,CACpBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAK8R,QAAQxB,YAAY,EAC/DrQ,MAAM,CAAC;4BAAC;4BAAM;4BAAW;4BAA4B;yBAAS,EAC9DI,gBAAgB;oBACrB;gBACF,EAAE,OAAOiP,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,oCAAoC,EAAEA,MAAMxK,OAAO,EAAE;gBACtE;YACF;YAEA,IAAI,CAACwL,cAAc;gBACjBxE,QAAQwD,KAAK,CAAC,CAAC,yCAAyC,EAAEwC,QAAQnP,EAAE,CAAC,0BAA0B,CAAC;gBAChG;YACF;YAEAmJ,QAAQC,GAAG,CAAC,CAAC,uBAAuB,EAAEuE,aAAa3N,EAAE,CAAC,gBAAgB,EAAE2N,aAAatP,MAAM,EAAE;YAE7F,+EAA+E;YAC/E,IAAIsP,aAAatP,MAAM,KAAK,UAAU;gBACpC,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;oBACHzD,QAAQ;oBACR+C,YAAY,IAAItD;gBAClB,GACCT,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChCJ,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,gCAAgC,CAAC;gBAE3E,8CAA8C;gBAC9C,MAAMgQ,sBAAsB,MAAM7S,YAAE,CACjCC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChC1C,MAAM,CAAC;oBAAC;iBAAS,EACjBI,gBAAgB;gBAEnByL,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEuE,aAAa3N,EAAE,CAAC,mBAAmB,EAAEgQ,qBAAqB3R,QAAQ;YACxG;YAEA,iCAAiC;YACjC,MAAMkR,kBAAkB,MAAMpS,YAAE,CAC7BC,UAAU,CAAC,mCACXqF,SAAS,CAAC,2BAA2B,2BAA2B,SAChEpF,KAAK,CAAC,UAAU,KAAKsQ,aAAamB,wBAAwB,EAC1DxR,MAAM,CAAC;gBAAC;aAA+B,EACvCI,gBAAgB;YAEnB,IAAI,CAAC6R,iBAAiB;gBACpBpG,QAAQwD,KAAK,CAAC,CAAC,wDAAwD,EAAEgB,aAAa3N,EAAE,EAAE;gBAC1F;YACF;YAEA,wDAAwD;YACxD,IAAIwP,sBAAsB,MAAMrS,YAAE,CAC/BC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAK8R,QAAQnP,EAAE,EAChD1C,MAAM,CAAC;gBAAC;gBAAM;aAAS,EACvBI,gBAAgB;YAEnB,IAAIoS,gBAA+B;YAEnC,IAAIN,qBAAqB;gBACvB,6DAA6D;gBAC7D,IAAIA,oBAAoBnR,MAAM,KAAK,QAAQ;oBACzC,MAAMlB,YAAE,CACL0E,WAAW,CAAC,gBACZC,GAAG,CAAC;wBACH8I,QAAQpM,OAAO2Q,QAAQY,WAAW,IAAI;wBACtC1R,QAAQ;wBACR+C,YAAY,IAAItD;oBAClB,GACCT,KAAK,CAAC,MAAM,KAAKmS,oBAAoBxP,EAAE,EACvCJ,OAAO;oBAEVuJ,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEoG,oBAAoBxP,EAAE,CAAC,8BAA8B,CAAC;gBACjF,OAAO;oBACLmJ,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEoG,oBAAoBxP,EAAE,CAAC,0BAA0B,CAAC;gBAC7E;gBACA8P,gBAAgBN,oBAAoBxP,EAAE;YACxC,OAAO;gBACL,+BAA+B;gBAC/B,MAAMiB,SAAS,MAAM9D,YAAE,CACpB+D,UAAU,CAAC,gBACXC,MAAM,CAAC;oBACNI,SAASuM,KAAK9N,EAAE;oBAChB2K,yBAAyBwE,QAAQnP,EAAE;oBACnCsJ,qBAAqBiG,gBAAgBjG,mBAAmB;oBACxDsB,QAAQpM,OAAO2Q,QAAQY,WAAW,IAAI;oBACtCtH,UAAU0G,QAAQ1G,QAAQ,CAACuG,WAAW;oBACtC3Q,QAAQ;oBACRwM,aAAa;oBACbC,WAAW6C,aAAa3N,EAAE;oBAC1BO,YAAY,IAAIzC;oBAChBsD,YAAY,IAAItD;gBAClB,GACCJ,gBAAgB;gBAEnByL,QAAQC,GAAG,CAAC,CAAC,oDAAoD,EAAE+F,QAAQnP,EAAE,EAAE;gBAE/E,IAAIiB,UAAUA,OAAOK,QAAQ,EAAE;oBAC7BwO,gBAAgBtR,OAAOyC,OAAOK,QAAQ;oBAEtC,mDAAmD;oBACnDkO,sBAAsB,MAAMrS,YAAE,CAC3BC,UAAU,CAAC,gBACXC,KAAK,CAAC,MAAM,KAAKyS,eACjBxS,MAAM,CAAC;wBAAC;wBAAM;qBAAS,EACvBI,gBAAgB;oBAEnByL,QAAQC,GAAG,CAAC,CAAC,yBAAyB,EAAE0G,eAAe;gBACzD;YACF;YAEA,yCAAyC;YACzC,MAAMK,sBAAsB,MAAMhT,YAAE,CACjCC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChC1C,MAAM,CAAC;gBAAC;aAAS,EACjBI,gBAAgB;YAEnB,6EAA6E;YAC7E,IAAIyS,qBAAqB;gBACvBhH,QAAQC,GAAG,CAAC,CAAC,2BAA2B,EAAEuE,aAAa3N,EAAE,CAAC,EAAE,EAAEmQ,oBAAoB9R,MAAM,EAAE;gBAE1F,IAAI8R,oBAAoB9R,MAAM,KAAK,UAAU;oBAC3C,MAAMA,SAAmB;oBACzB,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;wBACHzD;wBACA+C,YAAY,IAAItD;oBAClB,GACCT,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChCJ,OAAO;oBAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,gCAAgC,CAAC;oBAE3E,8CAA8C;oBAC9C,MAAMgQ,sBAAsB,MAAM7S,YAAE,CACjCC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChC1C,MAAM,CAAC;wBAAC;qBAAS,EACjBI,gBAAgB;oBAEnByL,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEuE,aAAa3N,EAAE,CAAC,mBAAmB,EAAEgQ,qBAAqB3R,QAAQ;gBACxG,OAAO;oBACL8K,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,wDAAwD,CAAC;gBACrG;YACF,OAAO;gBACLmJ,QAAQwD,KAAK,CAAC,CAAC,wCAAwC,EAAEgB,aAAa3N,EAAE,CAAC,wBAAwB,CAAC;YACpG;YAEAmJ,QAAQC,GAAG,CAAC,CAAC,uEAAuE,EAAEuE,aAAa3N,EAAE,EAAE;YAEvG,mCAAmC;YACnC,IAAGmP,QAAQiB,IAAI,IAAIN,eAAc;gBAC/B3G,QAAQC,GAAG,CAAC,CAAC,OAAO,EAAE+F,QAAQnP,EAAE,CAAC,oCAAoC,CAAC;gBAEtE,sDAAsD;gBACtD,MAAM,IAAI,CAACiQ,4BAA4B,CACrCnC,KAAK9N,EAAE,EACP2N,aAAajE,OAAO,EACpBoG,eACAtR,OAAO2Q,QAAQY,WAAW,IAAI,KAC9BZ,QAAQ1G,QAAQ,CAACuG,WAAW;gBAG9B7F,QAAQC,GAAG,CAAC,CAAC,gDAAgD,EAAE+F,QAAQnP,EAAE,EAAE;YAC7E,OAAO;gBACLmJ,QAAQC,GAAG,CAAC,CAAC,OAAO,EAAE+F,QAAQnP,EAAE,CAAC,wEAAwE,CAAC;YAC5G;QAKF,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,0CAA0CA;YACxD,MAAMA;QACR;IACF;IAEA,MAAc0D,kBAAkBC,MAAc,EAAE;QAC9CnH,QAAQC,GAAG,CAAC,CAAC,mCAAmC,EAAEkH,QAAQ;QAE1D,IAAI,CAACA,QAAQ;YACXnH,QAAQC,GAAG,CAAC;YACZ,OAAO;gBACLmH,WAAW;gBACXC,kBAAkB;gBAClBC,0BAA0B;YAC5B;QACF;QAEA,gCAAgC;QAChC,MAAM3C,OAAO,MAAM3Q,YAAE,CAACC,UAAU,CAAC,SAC9BC,KAAK,CAAC,MAAM,KAAKiT,QACjBhT,MAAM,CAAC;YAAC;YAAM;SAAQ,EACtBI,gBAAgB;QAEnB,IAAI,CAACoQ,MAAM;YACT3E,QAAQC,GAAG,CAAC,CAAC,eAAe,EAAEkH,OAAO,eAAe,CAAC;YACrD,OAAO;gBACLC,WAAW;gBACXC,kBAAkB;gBAClBC,0BAA0B;YAC5B;QACF;QAEAtH,QAAQC,GAAG,CAAC,CAAC,gCAAgC,EAAE0E,KAAK9N,EAAE,CAAC,QAAQ,EAAE8N,KAAK5N,KAAK,EAAE;QAE7E,gCAAgC;QAChC,MAAMqQ,YAAY,MAAMpT,YAAE,CAACC,UAAU,CAAC,cACnCC,KAAK,CAAC,WAAW,KAAKiT,QACtBhT,MAAM,CAAC;YAAC;YAAM;YAAY;YAAe;YAAW;SAAS,EAC7DI,gBAAgB;QAEnB,IAAI,CAAC6S,WAAW;YACdpH,QAAQC,GAAG,CAAC,CAAC,oCAAoC,EAAEkH,OAAO,oCAAoC,CAAC;YAE/F,kDAAkD;YAClD,IAAI;gBACF,MAAMnT,YAAE,CAAC+D,UAAU,CAAC,cACjBC,MAAM,CAAC;oBACN9C,QAAQ;oBACRkD,SAAS+O;oBACTI,aAAa;oBACbC,QAAQ;oBACRpQ,YAAY,IAAIzC;oBAChBsD,YAAY,IAAItD;gBAClB,GACCJ,gBAAgB;gBAEnByL,QAAQC,GAAG,CAAC,CAAC,uDAAuD,EAAEkH,QAAQ;gBAE9E,OAAO;oBACLC,WAAW;oBACXC,kBAAkB;oBAClBC,0BAA0B;gBAC5B;YACF,EAAE,OAAO9D,OAAO;gBACdxD,QAAQwD,KAAK,CAAC,CAAC,kDAAkD,EAAE2D,OAAO,CAAC,CAAC,EAAE3D;gBAC9E,OAAO;oBACL4D,WAAW;oBACXC,kBAAkB;oBAClBC,0BAA0B;gBAC5B;YACF;QACF;QAEAtH,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEwH,KAAKC,SAAS,CAACN,YAAY;QAE/D,qCAAqC;QACrC,IAAIA,UAAUlS,MAAM,KAAK,UAAU;YACjC8K,QAAQC,GAAG,CAAC,CAAC,SAAS,EAAEkH,OAAO,yBAAyB,EAAEC,UAAUlS,MAAM,CAAC,4BAA4B,CAAC;YAExG,gCAAgC;YAChC,IAAI;gBACF,MAAMlB,YAAE,CAAC0E,WAAW,CAAC,cAClBC,GAAG,CAAC;oBACHzD,QAAQ;oBACR+C,YAAY,IAAItD;gBAClB,GACCT,KAAK,CAAC,MAAM,KAAKkT,UAAUvQ,EAAE,EAC7BJ,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,mBAAmB,EAAEkH,OAAO,yBAAyB,CAAC;YACrE,EAAE,OAAO3D,OAAO;gBACdxD,QAAQwD,KAAK,CAAC,CAAC,qCAAqC,EAAE2D,OAAO,CAAC,CAAC,EAAE3D;YACnE;QACF;QAEA,IAAI6D,mBAAwB;QAC5B,IAAGD,UAAUG,WAAW,EAAE;YACxBF,mBAAmB,MAAMrT,YAAE,CAACC,UAAU,CAAC,cACpCC,KAAK,CAAC,WAAW,KAAKkT,UAAUG,WAAW,EAC3CpT,MAAM,CAAC;gBAAC;gBAAM;gBAAY;gBAAW;aAAS,EAC9CI,gBAAgB;YAEnByL,QAAQC,GAAG,CAAC,CAAC,4BAA4B,EAAEwH,KAAKC,SAAS,CAACL,mBAAmB;YAE7E,4CAA4C;YAC5C,IAAIA,oBAAoBA,iBAAiBnS,MAAM,KAAK,UAAU;gBAC5D8K,QAAQC,GAAG,CAAC,CAAC,gBAAgB,EAAEmH,UAAUG,WAAW,CAAC,yBAAyB,EAAEF,iBAAiBnS,MAAM,CAAC,4BAA4B,CAAC;gBAErI,gCAAgC;gBAChC,IAAI;oBACF,MAAMlB,YAAE,CAAC0E,WAAW,CAAC,cAClBC,GAAG,CAAC;wBACHzD,QAAQ;wBACR+C,YAAY,IAAItD;oBAClB,GACCT,KAAK,CAAC,MAAM,KAAKmT,iBAAiBxQ,EAAE,EACpCJ,OAAO;oBAEVuJ,QAAQC,GAAG,CAAC,CAAC,0BAA0B,EAAEmH,UAAUG,WAAW,CAAC,yBAAyB,CAAC;gBAC3F,EAAE,OAAO/D,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,4CAA4C,EAAE4D,UAAUG,WAAW,CAAC,CAAC,CAAC,EAAE/D;gBACzF;YACF;QACF;QAEA,OAAO;YACL4D,WAAWA,WAAWhE,YAAY;YAClCiE,kBAAkBA,kBAAkBjE,YAAY;YAChDkE,0BAA0BF,UAAUG,WAAW,IAAI;QACrD;IACF;IAEA,MAAcI,sBAAsBpH,OAAe,EAAE;QACnDP,QAAQC,GAAG,CAAC,CAAC,gDAAgD,EAAEM,SAAS;QAExE,IAAI,CAACA,SAAS;YACZP,QAAQC,GAAG,CAAC;YACZ,OAAO;gBACLmH,WAAW;gBACXQ,mBAAmB;gBACnBP,kBAAkB;gBAClBQ,0BAA0B;YAC5B;QACF;QAEA,MAAMC,YAAY,MAAM9T,YAAE,CAACC,UAAU,CAAC,SACnCC,KAAK,CAAC,MAAM,KAAKqM,SACjBpM,MAAM,CAAC;YAAC;YAAQ;YAAuC;SAA+B,EACtFI,gBAAgB;QAEnB,IAAI,CAACuT,WAAW;YACd9H,QAAQC,GAAG,CAAC,CAAC,aAAa,EAAEM,QAAQ,uCAAuC,CAAC;YAC5E,OAAO;gBACL6G,WAAW;gBACXQ,mBAAmB;gBACnBP,kBAAkB;gBAClBQ,0BAA0B,EAAE,qBAAqB;YACnD;QACF;QAEA7H,QAAQC,GAAG,CAAC,CAAC,4BAA4B,EAAEwH,KAAKC,SAAS,CAACI,YAAY;QAEtE,0DAA0D;QAC1D,MAAMC,mBAAmBD,UAAUnJ,4BAA4B,GAAGtJ,OAAOyS,UAAUnJ,4BAA4B,IAAI,IAAI,sBAAsB;QAC7I,MAAMqJ,yBAAyBF,UAAUpJ,mCAAmC,GAAGrJ,OAAOyS,UAAUpJ,mCAAmC,IAAI,GAAG,qBAAqB;QAE/J,kCAAkC;QAClC,MAAMH,QAAQlJ,OAAOyS,UAAUvJ,KAAK,IAAI;QACxC,MAAM0J,iBAAiB1J,QAASwJ,CAAAA,mBAAmB,GAAE;QACrD,MAAMG,uBAAuB3J,QAASyJ,CAAAA,yBAAyB,GAAE;QAEjEhI,QAAQC,GAAG,CAAC,CAAC;oBACC,EAAEgI,eAAe,EAAE,EAAEF,iBAAiB;2BAC/B,EAAEG,qBAAqB,EAAE,EAAEF,uBAAuB,EAAE,CAAC;QAE1E,OAAO;YACHZ,WAAWa;YACXL,mBAAmBG;YACnBV,kBAAkBa;YAClBL,0BAA0BG;QAC9B;IACF;IAEA,MAAc/D,2BAA2B+B,OAAY,EAAE;QACrD,IAAI;YACF,MAAMvB,aAAauB,QAAQtB,QAAQ;YACnC,MAAMC,OAAO,MAAM,IAAI,CAACC,yBAAyB,CAACH;YAElD,IAAI,CAACE,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,2CAA2C,EAAEiB,YAAY;gBACxE;YACF;YAEA,kCAAkC;YAClC,IAAID,eAAe,MAAMxQ,YAAE,CACxBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAK8R,QAAQxB,YAAY,EAC/DrQ,MAAM,CAAC;gBAAC;gBAAM;gBAAW;aAA2B,EACpDI,gBAAgB;YAEnB,8GAA8G;YAC9G,qDAAqD;YACrD,IAAI,CAACiQ,gBAAgBwB,QAAQxB,YAAY,EAAE;gBACzCxE,QAAQC,GAAG,CAAC,CAAC,yCAAyC,EAAE+F,QAAQnP,EAAE,CAAC,oCAAoC,CAAC;gBAExG,IAAI;oBACF,+BAA+B;oBAC/B,MAAMoP,qBAAqB,MAAM,IAAI,CAAC/D,MAAM,CAACgE,aAAa,CAACC,QAAQ,CAACH,QAAQxB,YAAY;oBAExF,uCAAuC;oBACvC,MAAM,IAAI,CAACb,yBAAyB,CAACsC;oBAErC,gCAAgC;oBAChCzB,eAAe,MAAMxQ,YAAE,CACpBC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAK8R,QAAQxB,YAAY,EAC/DrQ,MAAM,CAAC;wBAAC;wBAAM;wBAAW;qBAA2B,EACpDI,gBAAgB;gBACrB,EAAE,OAAOiP,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,oCAAoC,EAAEA,MAAMxK,OAAO,EAAE;gBACtE;YACF;YAEA,IAAI,CAACwL,cAAc;gBACjBxE,QAAQwD,KAAK,CAAC,CAAC,yCAAyC,EAAEwC,QAAQnP,EAAE,CAAC,0BAA0B,CAAC;gBAChG;YACF;YAEA,iCAAiC;YACjC,MAAMuP,kBAAkB,MAAMpS,YAAE,CAC7BC,UAAU,CAAC,mCACXqF,SAAS,CAAC,2BAA2B,2BAA2B,SAChEpF,KAAK,CAAC,UAAU,KAAKsQ,aAAamB,wBAAwB,EAC1DxR,MAAM,CAAC;gBAAC;aAA+B,EACvCI,gBAAgB;YAEnB,IAAI,CAAC6R,iBAAiB;gBACpBpG,QAAQwD,KAAK,CAAC,CAAC,wDAAwD,EAAEgB,aAAa3N,EAAE,EAAE;gBAC1F;YACF;YAEA,wDAAwD;YACxD,MAAMwP,sBAAsB,MAAMrS,YAAE,CACjCC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAK8R,QAAQnP,EAAE,EAChD1C,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI8R,qBAAqB;gBACvB,kCAAkC;gBAClC,MAAMrS,YAAE,CACL0E,WAAW,CAAC,gBACZC,GAAG,CAAC;oBACH8I,QAAQpM,OAAO2Q,QAAQM,UAAU,IAAI;oBACrCpR,QAAQ;oBACR+C,YAAY,IAAItD;gBAClB,GACCT,KAAK,CAAC,MAAM,KAAKmS,oBAAoBxP,EAAE,EACvCJ,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEoG,oBAAoBxP,EAAE,CAAC,gCAAgC,CAAC;YACnF,OAAO;gBACL,qCAAqC;gBACrC,MAAM7C,YAAE,CACL+D,UAAU,CAAC,gBACXC,MAAM,CAAC;oBACNI,SAASuM,KAAK9N,EAAE;oBAChB2K,yBAAyBwE,QAAQnP,EAAE;oBACnCsJ,qBAAqBiG,gBAAgBjG,mBAAmB;oBACxDsB,QAAQpM,OAAO2Q,QAAQM,UAAU,IAAI;oBACrChH,UAAU0G,QAAQ1G,QAAQ,CAACuG,WAAW;oBACtC3Q,QAAQ;oBACRwM,aAAa;oBACbC,WAAW6C,aAAa3N,EAAE;oBAC1BO,YAAY,IAAIzC;oBAChBsD,YAAY,IAAItD;gBAClB,GACC8B,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,8CAA8C,EAAE+F,QAAQnP,EAAE,EAAE;YAC3E;YAEA,gDAAgD;YAChD,MAAM3B,SAAmB;YACzB,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;gBACHzD;gBACA+C,YAAY,IAAItD;YAClB,GACCT,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChCJ,OAAO;YAEVuJ,QAAQC,GAAG,CAAC,CAAC,4CAA4C,EAAE+F,QAAQnP,EAAE,EAAE;QACzE,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,yCAAyCA;YACvD,MAAMA;QACR;IACF;IAEA,6EAA6E;IAC7E,MAAcU,sBAAsBiE,MAAW,EAAE;QAC/C,IAAI;YACF,wDAAwD;YACxD,IAAIA,OAAOnC,OAAO,EAAE;gBAClBhG,QAAQC,GAAG,CAAC,CAAC,SAAS,EAAEkI,OAAOtR,EAAE,CAAC,wEAAwE,CAAC;gBAC3G;YACF;YAEA,MAAM4N,aAAa0D,OAAOzD,QAAQ;YAClC,IAAI,CAACD,YAAY;gBACfzE,QAAQwD,KAAK,CAAC,CAAC,SAAS,EAAE2E,OAAOtR,EAAE,CAAC,8BAA8B,CAAC;gBACnE;YACF;YAEA,MAAM8N,OAAO,MAAM,IAAI,CAACC,yBAAyB,CAACH;YAElD,IAAI,CAACE,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,2CAA2C,EAAEiB,YAAY;gBACxE;YACF;YAEA,0CAA0C;YAC1C,MAAM2B,kBAAkB,MAAMpS,YAAE,CAC7BC,UAAU,CAAC,qBACXC,KAAK,CAAC,QAAQ,KAAK,UACnBC,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI,CAAC6R,iBAAiB;gBACpBpG,QAAQwD,KAAK,CAAC;gBACd;YACF;YAEA,0DAA0D;YAC1D,MAAM6C,sBAAsB,MAAMrS,YAAE,CACjCC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAKiU,OAAOtR,EAAE,EAC/C1C,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI8R,qBAAqB;gBACvBrG,QAAQC,GAAG,CAAC,CAAC,oCAAoC,EAAEkI,OAAOtR,EAAE,EAAE;gBAC9D;YACF;YAEA,wBAAwB;YACxB,MAAM7C,YAAE,CACL+D,UAAU,CAAC,gBACXC,MAAM,CAAC;gBACNI,SAASuM,KAAK9N,EAAE;gBAChB2K,yBAAyB2G,OAAOtR,EAAE;gBAClCsJ,qBAAqBiG,gBAAgBvP,EAAE;gBACvC4K,QAAQpM,OAAO8S,OAAO1G,MAAM,IAAI;gBAChCnC,UAAU6I,OAAO7I,QAAQ,CAACuG,WAAW;gBACrC3Q,QAAQ;gBACRwM,aAAa;gBACbC,WAAW;gBACXvK,YAAY,IAAIzC,KAAKwT,OAAO5C,OAAO,GAAG;gBACtCtN,YAAY,IAAItD;YAClB,GACC8B,OAAO;YAEVuJ,QAAQC,GAAG,CAAC,CAAC,wDAAwD,EAAEkI,OAAOtR,EAAE,EAAE;QACpF,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,4CAA4CA;YAC1D,MAAMA;QACR;IACF;IAEA,8BAA8B;IAC9B,MAAcW,qBAAqBgE,MAAW,EAAE;QAC9C,IAAI;YACF,iCAAiC;YACjC,MAAMC,cAAc,MAAMpU,YAAE,CACzBC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAKiU,OAAOtR,EAAE,EAC/C1C,MAAM,CAAC;gBAAC;gBAAM;gBAAW;gBAAuB;gBAAe;aAAY,EAC3EI,gBAAgB;YAEnB,IAAI,CAAC6T,aAAa;gBAChBpI,QAAQwD,KAAK,CAAC,CAAC,0CAA0C,EAAE2E,OAAOtR,EAAE,EAAE;gBACtE;YACF;YAEA,8CAA8C;YAC9C,MAAMwR,eAAeF,OAAOG,QAAQ;YAEpC,IAAID,cAAc;gBAChB,mDAAmD;gBACnD,MAAMrU,YAAE,CACL0E,WAAW,CAAC,gBACZC,GAAG,CAAC;oBACHzD,QAAQ;oBACR+C,YAAY,IAAItD;gBAClB,GACCT,KAAK,CAAC,MAAM,KAAKkU,YAAYvR,EAAE,EAC/BJ,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,UAAU,EAAEmI,YAAYvR,EAAE,CAAC,yBAAyB,CAAC;gBAElE,yEAAyE;gBACzE,IAAIuR,YAAY1G,WAAW,KAAK,kBAAkB0G,YAAYzG,SAAS,EAAE;oBACvE,MAAMzM,SAAqB;oBAC3B,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;wBACHzD;wBACA8L,UAAU,IAAIrM;wBACdsD,YAAY,IAAItD;oBAClB,GACCT,KAAK,CAAC,MAAM,KAAKkU,YAAYzG,SAAS,EACtClL,OAAO;oBAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEmI,YAAYzG,SAAS,CAAC,0CAA0C,CAAC;gBAC7F;YACF,OAAO;gBACL,mFAAmF;gBACnF,kCAAkC;gBAClC,MAAM4G,eAAelT,OAAO8S,OAAOK,eAAe,IAAI;gBAEtD,MAAMxU,YAAE,CACL+D,UAAU,CAAC,gBACXC,MAAM,CAAC;oBACNI,SAASgQ,YAAYhQ,OAAO;oBAC5BoJ,yBAAyB,GAAG2G,OAAOtR,EAAE,CAAC,OAAO,CAAC;oBAC9CsJ,qBAAqBiI,YAAYjI,mBAAmB;oBACpDsB,QAAQ,CAAC8G;oBACTjJ,UAAU6I,OAAO7I,QAAQ,CAACuG,WAAW;oBACrC3Q,QAAQ;oBACRwM,aAAa0G,YAAY1G,WAAW;oBACpCC,WAAWyG,YAAYzG,SAAS;oBAChCvK,YAAY,IAAIzC;oBAChBsD,YAAY,IAAItD;gBAClB,GACC8B,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,8CAA8C,EAAEmI,YAAYvR,EAAE,EAAE;YAC/E;QACF,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,gCAAgCA;YAC9C,MAAMA;QACR;IACF;IAEA,8DAA8D;IAC9D,MAAca,6BAA6BoE,aAAkB,EAAE;QAC7D,IAAI;YACFzI,QAAQC,GAAG,CAAC,CAAC,cAAc,EAAEwI,cAAc5R,EAAE,CAAC,iBAAiB,CAAC;YAEhE,iEAAiE;YACjE,wEAAwE;YACxE,IAAI4R,cAAczC,OAAO,EAAE;gBACzB,IAAI;oBACF,sDAAsD;oBACtD,MAAMA,UAAU,MAAM,IAAI,CAAC9D,MAAM,CAACwG,QAAQ,CAACvC,QAAQ,CAACsC,cAAczC,OAAO;oBAEzE,6EAA6E;oBAC7E,IAAIA,QAAQxB,YAAY,EAAE;wBACxB,6EAA6E;wBAC7ExE,QAAQC,GAAG,CAAC,CAAC,cAAc,EAAEwI,cAAc5R,EAAE,CAAC,+BAA+B,EAAEmP,QAAQxB,YAAY,EAAE;wBAErG,wCAAwC;wBACxC,MAAMA,eAAe,MAAMxQ,YAAE,CAC1BC,UAAU,CAAC,sBACZ,6EAA6E;yBAC5EC,KAAK,CAAC,gCAAgC,KAAK8R,QAAQxB,YAAY,EAC/DrQ,MAAM,CAAC;4BAAC;4BAAM;yBAAS,EACvBI,gBAAgB;wBAEnB,IAAIiQ,cAAc;4BAChB,sEAAsE;4BACtE,IAAIA,aAAatP,MAAM,KAAK,WAAW;gCACrC,MAAMA,SAAmB;gCACzB,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;oCACHzD;oCACA+C,YAAY,IAAItD;gCAClB,GACCT,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChCJ,OAAO;gCAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,uDAAuD,EAAE4R,cAAc5R,EAAE,EAAE;4BACvH;wBACF;oBACF;gBACF,EAAE,OAAO2M,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,sDAAsD,EAAEA,MAAMxK,OAAO,EAAE;gBACxF;YACF;QACF,EAAE,OAAOwK,OAAO;YACdxD,QAAQwD,KAAK,CAAC,iDAAiDA;YAC/D,MAAMA;QACR;IACF;IAEA,8CAA8C;IAC9C,MAAcc,kCAAkCqE,QAAa,EAAE;QAC7D,IAAI;YACF3I,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAE0I,SAAS9R,EAAE,CAAC,WAAW,CAAC;YAE5D,gFAAgF;YAChF,IAAI8R,SAASnE,YAAY,EAAE;gBACzB,wCAAwC;gBACxC,MAAMA,eAAe,MAAMxQ,YAAE,CAC1BC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAKyU,SAASnE,YAAY,EAChErQ,MAAM,CAAC;oBAAC;oBAAM;iBAAS,EACvBI,gBAAgB;gBAEnB,IAAIiQ,gBAAgBA,aAAatP,MAAM,KAAK,WAAW;oBACrD,+DAA+D;oBAC/D,MAAM+Q,qBAAqB,MAAM,IAAI,CAAC/D,MAAM,CAACgE,aAAa,CAACC,QAAQ,CAACwC,SAASnE,YAAY;oBAEzF,IAAIyB,mBAAmB/Q,MAAM,KAAK,UAAU;wBAC1C,MAAMA,SAAmB;wBACzB,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;4BACHzD;4BACA+C,YAAY,IAAItD;wBAClB,GACCT,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChCJ,OAAO;wBAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,oCAAoC,CAAC;oBACjF;gBACF;YACF;QACF,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,2DAA2DA;YACzE,MAAMA;QACR;IACF;IAEA,kDAAkD;IAClD,MAAcY,+BAA+BwE,OAAY,EAAE;QACzD,IAAI;YACF,0DAA0D;YAC1D,IAAIA,QAAQC,IAAI,KAAK,gBAAgB;gBACnC7I,QAAQC,GAAG,CAAC,CAAC,mBAAmB,EAAE2I,QAAQ/R,EAAE,CAAC,uEAAuE,CAAC;gBAErH,4EAA4E;gBAC5E,IAAI+R,QAAQpE,YAAY,EAAE;oBACxB,IAAI;wBACF,wCAAwC;wBACxC,MAAMA,eAAe,MAAMxQ,YAAE,CAC1BC,UAAU,CAAC,uBACXC,KAAK,CAAC,gCAAgC,KAAK0U,QAAQpE,YAAY,EAC/DrQ,MAAM,CAAC;4BAAC;4BAAM;4BAAU;yBAAU,EAClCI,gBAAgB;wBAEnB,IAAIiQ,cAAc;4BAChB,qEAAqE;4BACrE,IAAIA,aAAatP,MAAM,KAAK,UAAU;gCACpC,MAAMA,SAAmB;gCACzB,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;oCACHzD;oCACA+C,YAAY,IAAItD;gCAClB,GACCT,KAAK,CAAC,MAAM,KAAKsQ,aAAa3N,EAAE,EAChCJ,OAAO;gCAEVuJ,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAEuE,aAAa3N,EAAE,CAAC,mCAAmC,CAAC;4BAChF;4BAEA,wDAAwD;4BACxD,MAAMwP,sBAAsB,MAAMrS,YAAE,CACjCC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAK0U,QAAQ/R,EAAE,EAChD1C,MAAM,CAAC;gCAAC;6BAAK,EACbI,gBAAgB;4BAEnB,IAAI,CAAC8R,qBAAqB;gCACxB,0CAA0C;gCAC1C,MAAMD,kBAAkB,MAAMpS,YAAE,CAC7BC,UAAU,CAAC,qBACXC,KAAK,CAAC,QAAQ,KAAK,UACnBC,MAAM,CAAC;oCAAC;iCAAK,EACbI,gBAAgB;gCAEnB,IAAI6R,iBAAiB;oCACnB,wCAAwC;oCACxC,MAAMpS,YAAE,CACL+D,UAAU,CAAC,gBACXC,MAAM,CAAC;wCACNI,SAASoM,aAAapM,OAAO;wCAC7BoJ,yBAAyBoH,QAAQ/R,EAAE;wCACnCsJ,qBAAqBiG,gBAAgBvP,EAAE;wCACvC4K,QAAQpM,OAAOuT,QAAQE,YAAY,IAAI;wCACvCxJ,UAAUsJ,QAAQtJ,QAAQ,CAACuG,WAAW;wCACtC3Q,QAAQ;wCACRwM,aAAa;wCACbC,WAAW6C,aAAa3N,EAAE;wCAC1BO,YAAY,IAAIzC,KAAKiU,QAAQrD,OAAO,GAAG;wCACvCtN,YAAY,IAAItD;oCAClB,GACC8B,OAAO;oCAEVuJ,QAAQC,GAAG,CAAC,CAAC,yEAAyE,EAAE2I,QAAQ/R,EAAE,EAAE;gCACtG;4BACF;wBACF;oBACF,EAAE,OAAO2M,OAAO;wBACdxD,QAAQwD,KAAK,CAAC,CAAC,sDAAsD,EAAEA,MAAMxK,OAAO,EAAE;oBACxF;gBACF;gBAEA;YACF;YAEA,MAAMyL,aAAamE,QAAQlE,QAAQ;YACnC,IAAI,CAACD,YAAY;gBACfzE,QAAQwD,KAAK,CAAC,CAAC,mBAAmB,EAAEoF,QAAQ/R,EAAE,CAAC,8BAA8B,CAAC;gBAC9E;YACF;YAEA,MAAM8N,OAAO,MAAM,IAAI,CAACC,yBAAyB,CAACH;YAElD,IAAI,CAACE,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,2CAA2C,EAAEiB,YAAY;gBACxE;YACF;YAEA,0CAA0C;YAC1C,MAAM2B,kBAAkB,MAAMpS,YAAE,CAC7BC,UAAU,CAAC,qBACXC,KAAK,CAAC,QAAQ,KAAK,UACnBC,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI,CAAC6R,iBAAiB;gBACpBpG,QAAQwD,KAAK,CAAC;gBACd;YACF;YAEA,wDAAwD;YACxD,MAAM6C,sBAAsB,MAAMrS,YAAE,CACjCC,UAAU,CAAC,gBACXC,KAAK,CAAC,2BAA2B,KAAK0U,QAAQ/R,EAAE,EAChD1C,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI8R,qBAAqB;gBACvBrG,QAAQC,GAAG,CAAC,CAAC,8CAA8C,EAAE2I,QAAQ/R,EAAE,EAAE;gBACzE;YACF;YAEA,wBAAwB;YACxB,MAAM7C,YAAE,CACL+D,UAAU,CAAC,gBACXC,MAAM,CAAC;gBACNI,SAASuM,KAAK9N,EAAE;gBAChB2K,yBAAyBoH,QAAQ/R,EAAE;gBACnCsJ,qBAAqBiG,gBAAgBvP,EAAE;gBACvC4K,QAAQpM,OAAOuT,QAAQE,YAAY,IAAI;gBACvCxJ,UAAUsJ,QAAQtJ,QAAQ,CAACuG,WAAW;gBACtC3Q,QAAQ;gBACRwM,aAAa;gBACbC,WAAW;gBACXvK,YAAY,IAAIzC,KAAKiU,QAAQrD,OAAO,GAAG;gBACvCtN,YAAY,IAAItD;YAClB,GACC8B,OAAO;YAEVuJ,QAAQC,GAAG,CAAC,CAAC,kEAAkE,EAAE2I,QAAQ/R,EAAE,EAAE;QAC/F,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,mDAAmDA;YACjE,MAAMA;QACR;IACF;IAEA,qBAAqB;IACrB,MAAcoB,0BAA0BH,UAAkB,EAAE;QAC1D,MAAME,OAAO,MAAM3Q,YAAE,CAClBC,UAAU,CAAC,SACXC,KAAK,CAAC,YAAY,KAAKuQ,YACvBtQ,MAAM,CAAC;YAAC;YAAM;YAAS;SAAS,EAChCI,gBAAgB;QAEnB,IAAIoQ,MAAM;YACR3E,QAAQC,GAAG,CAAC,CAAC,sCAAsC,EAAEwE,WAAW,KAAK,EAAEE,KAAK9N,EAAE,CAAC,QAAQ,EAAE8N,KAAK5N,KAAK,CAAC,SAAS,EAAE4N,KAAKwC,MAAM,EAAE;QAC9H,OAAO;YACLnH,QAAQC,GAAG,CAAC,CAAC,6CAA6C,EAAEwE,YAAY;QAC1E;QAEA,OAAOE;IACT;IAEA,MAAcI,kCAAkCgE,OAAe,EAAE;QAC/D,OAAO/U,YAAE,CACNC,UAAU,CAAC,4BACXC,KAAK,CAAC,gCAAgC,KAAK6U,SAC3C5U,MAAM,CAAC;YAAC;YAAM;YAAW;SAAW,EACpCI,gBAAgB;IACrB;IAEA,MAAc2Q,mCAAmCJ,qBAA6B,EAAE;QAC9E,OAAO9Q,YAAE,CACNC,UAAU,CAAC,mCACXqF,SAAS,CAAC,cAAc,eAAe,QACvCpF,KAAK,CAAC,UAAU,KAAK4Q,uBACrB3Q,MAAM,CAAC;YAAC;YAAmB;SAAe,EAC1CI,gBAAgB;IACrB;IAEA,6CAA6C;IAC7C,MAAcuS,6BAA6B5O,MAAc,EAAEyH,MAAc,EAAEgH,aAAqB,EAAEqC,aAAqB,EAAE1J,QAAgB,EAAE;QACzI,IAAI;YACFU,QAAQC,GAAG,CAAC,CAAC,mCAAmC,EAAE/H,OAAO,QAAQ,EAAEyH,OAAO,YAAY,EAAEgH,eAAe;YAEvG,uCAAuC;YACvC,MAAMhC,OAAO,MAAM3Q,YAAE,CAClBC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAKgE,QACjB/D,MAAM,CAAC;gBAAC;gBAAM;gBAAS;aAAS,EAChCI,gBAAgB;YAEnB,IAAI,CAACoQ,MAAM;gBACT3E,QAAQwD,KAAK,CAAC,CAAC,QAAQ,EAAEtL,OAAO,eAAe,CAAC;gBAChD;YACF;YAEA8H,QAAQC,GAAG,CAAC,CAAC,uBAAuB,EAAE0E,KAAK9N,EAAE,CAAC,QAAQ,EAAE8N,KAAK5N,KAAK,CAAC,SAAS,EAAE4N,KAAKwC,MAAM,IAAI,gBAAgB;YAE7G,iEAAiE;YACjE,MAAM8B,qBAAqB,MAAMjV,YAAE,CAChCC,UAAU,CAAC,yBACXC,KAAK,CAAC,kBAAkB,KAAKyS,eAC7BxS,MAAM,CAAC;gBAAC;aAAK,EACbI,gBAAgB;YAEnB,IAAI0U,oBAAoB;gBACtBjJ,QAAQC,GAAG,CAAC,CAAC,wCAAwC,EAAE0G,cAAc,wBAAwB,CAAC;gBAE9F,4CAA4C;gBAC5C,MAAM3S,YAAE,CACL+E,UAAU,CAAC,yBACX7E,KAAK,CAAC,kBAAkB,KAAKyS,eAC7BlQ,OAAO;gBAEVuJ,QAAQC,GAAG,CAAC,CAAC,sCAAsC,EAAE0G,cAAc,gBAAgB,CAAC;YACtF;YAEA,0BAA0B;YAC1B,MAAMQ,SAASxC,KAAKwC,MAAM,IAAI;YAC9B,MAAM+B,aAAa,MAAM,IAAI,CAAChC,iBAAiB,CAACC;YAChDnH,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEwH,KAAKC,SAAS,CAACwB,aAAa;YAEhE,mCAAmC;YACnC,MAAMC,aAAa,MAAM,IAAI,CAACxB,qBAAqB,CAAChI;YACpDK,QAAQC,GAAG,CAAC,CAAC,2BAA2B,EAAEN,OAAO,EAAE,EAAE8H,KAAKC,SAAS,CAACyB,aAAa;YAEjF,IAAI,CAACA,YAAY;gBACfnJ,QAAQC,GAAG,CAAC,CAAC,uDAAuD,EAAEN,QAAQ;gBAC9E;YACF;YAEA,6CAA6C;YAC7C,IAAIwH,SAAS,GAAG;gBACd,IAAI;oBACFnH,QAAQC,GAAG,CAAC,CAAC,mCAAmC,EAAEkH,QAAQ;oBAE1D,MAAMrP,SAAS,MAAM9D,YAAE,CACpB+D,UAAU,CAAC,yBACXC,MAAM,CAAC;wBACNoR,WAAW;wBACXC,aAAalC;wBACb/O,SAASF;wBACTqI,SAASZ;wBACT2J,gBAAgB3C;wBAChB4C,oBAAoBlU,OAAO8T,WAAWvB,iBAAiB,IAAI;wBAC3D4B,kBAAkBnU,OAAO8T,WAAW/B,SAAS,IAAI;wBACjDlS,QAAQ;wBACR0N,UAAU6E,KAAKC,SAAS,CAAC;4BACvB+B,oBAAoBT;4BACpB1J,UAAUA;wBACZ;wBACAlI,YAAY,IAAIzC;wBAChBsD,YAAY,IAAItD;oBAClB,GACCJ,gBAAgB;oBAEnByL,QAAQC,GAAG,CAAC,CAAC,kCAAkC,EAAEkH,OAAO,CAAC,CAAC,EAAErP;oBAE5D,yCAAyC;oBACzC,IAAIA,UAAU,cAAcA,QAAQ;wBAClC,MAAM4R,eAAerU,OAAOyC,OAAOK,QAAQ;wBAC3C6H,QAAQC,GAAG,CAAC,CAAC,4BAA4B,EAAEyJ,cAAc;wBAEzD,mDAAmD;wBACnD,MAAMC,aAAa,MAAM3V,YAAE,CACxBC,UAAU,CAAC,yBACXC,KAAK,CAAC,MAAM,KAAKwV,cACjBvV,MAAM,CAAC;4BAAC;4BAAM;yBAAS,EACvBI,gBAAgB;wBAEnB,IAAIoV,YAAY;4BACd3J,QAAQC,GAAG,CAAC,CAAC,SAAS,EAAEyJ,aAAa,0CAA0C,EAAEC,WAAWzU,MAAM,EAAE;wBACtG,OAAO;4BACL8K,QAAQwD,KAAK,CAAC,CAAC,SAAS,EAAEkG,aAAa,+CAA+C,CAAC;wBACzF;oBACF;gBACF,EAAE,OAAOlG,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,yCAAyC,EAAE2D,OAAO,CAAC,CAAC,EAAE3D;gBACvE;YACF;YAEA,sDAAsD;YACtD,IAAI0F,WAAW5B,wBAAwB,EAAE;gBACvC,IAAI;oBACFtH,QAAQC,GAAG,CAAC,CAAC,0CAA0C,EAAEiJ,WAAW5B,wBAAwB,EAAE;oBAE9F,MAAMxP,SAAS,MAAM9D,YAAE,CACpB+D,UAAU,CAAC,yBACXC,MAAM,CAAC;wBACNoR,WAAW;wBACXC,aAAaH,WAAW5B,wBAAwB;wBAChDlP,SAASF;wBACTqI,SAASZ;wBACT2J,gBAAgB3C;wBAChB4C,oBAAoBlU,OAAO8T,WAAWtB,wBAAwB,IAAI;wBAClE2B,kBAAkBnU,OAAO8T,WAAW9B,gBAAgB,IAAI;wBACxDnS,QAAQ;wBACR0N,UAAU6E,KAAKC,SAAS,CAAC;4BACvB+B,oBAAoBT;4BACpB1J,UAAUA;wBACZ;wBACAlI,YAAY,IAAIzC;wBAChBsD,YAAY,IAAItD;oBAClB,GACCJ,gBAAgB;oBAEnByL,QAAQC,GAAG,CAAC,CAAC,yCAAyC,EAAEiJ,WAAW5B,wBAAwB,CAAC,CAAC,CAAC,EAAExP;oBAEhG,yCAAyC;oBACzC,IAAIA,UAAU,cAAcA,QAAQ;wBAClC,MAAM4R,eAAerU,OAAOyC,OAAOK,QAAQ;wBAC3C6H,QAAQC,GAAG,CAAC,CAAC,mCAAmC,EAAEyJ,cAAc;wBAEhE,mDAAmD;wBACnD,MAAMC,aAAa,MAAM3V,YAAE,CACxBC,UAAU,CAAC,yBACXC,KAAK,CAAC,MAAM,KAAKwV,cACjBvV,MAAM,CAAC;4BAAC;4BAAM;yBAAS,EACvBI,gBAAgB;wBAEnB,IAAIoV,YAAY;4BACd3J,QAAQC,GAAG,CAAC,CAAC,gBAAgB,EAAEyJ,aAAa,0CAA0C,EAAEC,WAAWzU,MAAM,EAAE;wBAC7G,OAAO;4BACL8K,QAAQwD,KAAK,CAAC,CAAC,gBAAgB,EAAEkG,aAAa,+CAA+C,CAAC;wBAChG;oBACF;gBACF,EAAE,OAAOlG,OAAO;oBACdxD,QAAQwD,KAAK,CAAC,CAAC,gDAAgD,EAAE0F,WAAW5B,wBAAwB,CAAC,CAAC,CAAC,EAAE9D;gBAC3G;YACF;YAEAxD,QAAQC,GAAG,CAAC,CAAC,sDAAsD,EAAE0G,eAAe;QACtF,EAAE,OAAOnD,OAAO;YACdxD,QAAQwD,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAEA;QAChD;IACF;IAEA,iDAAiD;IACjD,MAAMoG,qBAAqB1R,MAAc,EAAE;QACzC,IAAI;YACF,MAAMgO,gBAAgB,MAAMlS,YAAE,CAC3BC,UAAU,CAAC,6BACXqF,SAAS,CAAC,cAAc,cAAc,QACtCA,SAAS,CAAC,mCAAmC,+BAA+B,UAC5EA,SAAS,CAAC,2BAA2B,2BAA2B,SAChEpF,KAAK,CAAC,cAAc,KAAKgE,QACzBhE,KAAK,CAAC,iBAAiB,MAAM,MAC7BC,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAiC,OAAO,CAAC,iBAAiB,QACzBK,OAAO;YAEV,OAAO;gBACLvB,QAAQ;gBACRC,MAAM+Q,cAAcvP,GAAG,CAAC6N,CAAAA,eAAiB,CAAA;wBACvC3N,IAAI2N,aAAa3N,EAAE;wBACnB3B,QAAQsP,aAAatP,MAAM;wBAC3B8I,UAAUwG,aAAaxG,QAAQ;wBAC/BO,OAAOlJ,OAAOmP,aAAajG,KAAK;wBAChCe,UAAUkF,aAAalF,QAAQ;wBAC/ByB,YAAY,IAAI,CAACpN,cAAc,CAAC6Q,aAAazD,UAAU;wBACvDC,UAAUwD,aAAaxD,QAAQ,GAAG,IAAI,CAACrN,cAAc,CAAC6Q,aAAaxD,QAAQ,IAAI;wBAC/EC,mBAAmBuD,aAAavD,iBAAiB,GAAG,IAAI,CAACtN,cAAc,CAAC6Q,aAAavD,iBAAiB,IAAI;wBAC1GC,sBAAsBsD,aAAatD,oBAAoB;wBACvDC,UAAUqD,aAAarD,QAAQ;wBAC/BC,kBAAkBoD,aAAapD,gBAAgB,GAAG,IAAI,CAACzN,cAAc,CAAC6Q,aAAapD,gBAAgB,IAAI;wBACvGC,gBAAgBmD,aAAanD,cAAc,GAAG,IAAI,CAAC1N,cAAc,CAAC6Q,aAAanD,cAAc,IAAI;wBACjG5C,YAAY+F,aAAa/F,UAAU;wBACnCrH,YAAY,IAAI,CAACzD,cAAc,CAAC6Q,aAAapN,UAAU;wBACvDkK,WAAWkD,aAAalD,SAAS;wBACjCuI,kBAAkBrF,aAAaqF,gBAAgB;wBAC/C/K,kBAAkB0F,aAAazG,qBAAqB;oBACtD,CAAA;YACF;QACF,EAAE,OAAOyF,OAAO;YACdxD,QAAQwD,KAAK,CAAC,yCAAyCA;YACvD,MAAMA;QACR;IACF;IAEA,gDAAgD;IAChD,MAAMsG,oBAAoB5R,MAAc,EAAE;QACxC,IAAI;YACF,MAAM6R,eAAe,MAAM/V,YAAE,CAC1BC,UAAU,CAAC,qBACXqF,SAAS,CAAC,2BAA2B,yBAAyB,SAC9DpF,KAAK,CAAC,aAAa,KAAKgE,QACxBhE,KAAK,CAAC,gBAAgB,MAAM,MAC5BC,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAiC,OAAO,CAAC,gBAAgB,QACxBK,OAAO;YAEV,OAAO;gBACLvB,QAAQ;gBACRC,MAAM4U,aAAapT,GAAG,CAACyR,CAAAA,cAAgB,CAAA;wBACrCvR,IAAIuR,YAAYvR,EAAE;wBAClB2K,yBAAyB4G,YAAY5G,uBAAuB;wBAC5DC,QAAQpM,OAAO+S,YAAY3G,MAAM;wBACjCnC,UAAU8I,YAAY9I,QAAQ;wBAC9BpK,QAAQkT,YAAYlT,MAAM;wBAC1BwM,aAAa0G,YAAY1G,WAAW;wBACpCC,WAAWyG,YAAYzG,SAAS;wBAChCvK,YAAY,IAAI,CAACzD,cAAc,CAACyU,YAAYhR,UAAU;wBACtD0H,kBAAkBsJ,YAAYrK,qBAAqB;oBACrD,CAAA;YACF;QACF,EAAE,OAAOyF,OAAO;YACdxD,QAAQwD,KAAK,CAAC,wCAAwCA;YACtD,MAAMA;QACR;IACF;IAEA,yDAAyD;IACzD,MAAMwG,sBAAsBrD,aAAqB,EAAEzO,MAAc,EAAE;QACjE,IAAI;YACF,qBAAqB;YACrB,MAAMkQ,cAAc,MAAMpU,YAAE,CACzBC,UAAU,CAAC,qBACXqF,SAAS,CAAC,2BAA2B,yBAAyB,SAC9DpF,KAAK,CAAC,QAAQ,KAAKyS,eACnBzS,KAAK,CAAC,aAAa,KAAKgE,QACxBhE,KAAK,CAAC,gBAAgB,MAAM,MAC5BC,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAI,gBAAgB;YAEnB,IAAI,CAAC6T,aAAa;gBAChB,OAAO;oBACLlT,QAAQ;oBACR8D,SAAS;gBACX;YACF;YAEA,wDAAwD;YACxD,IAAIiR,gBAAqB;YACzB,IAAI7B,YAAY1G,WAAW,KAAK,kBAAkB0G,YAAYzG,SAAS,EAAE;gBACvE,gCAAgC;gBAChC,MAAM6C,eAAe,MAAMxQ,YAAE,CAC1BC,UAAU,CAAC,6BACXqF,SAAS,CAAC,cAAc,cAAc,QACtCpF,KAAK,CAAC,SAAS,KAAKkU,YAAYzG,SAAS,EACzCxN,MAAM,CAAC;oBACN;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD,EACAI,gBAAgB;gBAEnB,IAAIiQ,cAAc;oBAChByF,gBAAgB;wBACd/Q,MAAM;wBACNrC,IAAI2N,aAAa3N,EAAE;wBACnB3B,QAAQsP,aAAatP,MAAM;wBAC3BoM,WAAWkD,aAAalD,SAAS;wBACjCuI,kBAAkBrF,aAAaqF,gBAAgB;wBAC/CtL,OAAOlJ,OAAOmP,aAAajG,KAAK;wBAChCe,UAAUkF,aAAalF,QAAQ;wBAC/ByB,YAAY,IAAI,CAACpN,cAAc,CAAC6Q,aAAazD,UAAU;wBACvDC,UAAUwD,aAAaxD,QAAQ,GAAG,IAAI,CAACrN,cAAc,CAAC6Q,aAAaxD,QAAQ,IAAI;oBACjF;gBACF;YACF,OAAO,IAAIoH,YAAY1G,WAAW,KAAK,gBAAgB;gBACrD,0EAA0E;gBAC1EuI,gBAAgB;oBACd/Q,MAAM;oBACNoF,aAAa;gBACf;YACF;YAEA,6CAA6C;YAC7C,IAAI4L,gBAAqB;YACzB,IAAI9B,YAAY5G,uBAAuB,IAAI4G,YAAYrK,qBAAqB,KAAK,UAAU;gBACzF,IAAI;oBACF,4CAA4C;oBAC5C,IAAIqK,YAAY5G,uBAAuB,CAAC2I,UAAU,CAAC,QAAQ;wBACzD,eAAe;wBACf,MAAMnE,UAAU,MAAM,IAAI,CAAC9D,MAAM,CAACwG,QAAQ,CAACvC,QAAQ,CAACiC,YAAY5G,uBAAuB;wBACvF0I,gBAAgB;4BACdE,gBAAgBpE,QAAQqE,MAAM;4BAC9BC,aAAatE,QAAQuE,kBAAkB;4BACvCC,SAASxE,QAAQyE,WAAW;4BAC5B,6EAA6E;4BAC7EC,gBAAgB1E,QAAQ0E,cAAc;4BACtCC,gBAAgB3E,QAAQ2E,cAAc;wBACxC;oBACF,OAAO,IAAIvC,YAAY5G,uBAAuB,CAAC2I,UAAU,CAAC,QAAQ;wBAChE,iBAAiB;wBACjB,MAAMhC,SAAS,MAAM,IAAI,CAACjG,MAAM,CAAC0I,OAAO,CAACzE,QAAQ,CAACiC,YAAY5G,uBAAuB;wBACrF0I,gBAAgB;4BACdW,aAAa1C,OAAO0C,WAAW;4BAC/BC,gBAAgB3C,OAAO4C,sBAAsB,EAAE7R;4BAC/CwR,gBAAgBvC,OAAOuC,cAAc;4BACrCpM,aAAa6J,OAAO7J,WAAW;wBACjC;oBACF;gBACF,EAAE,OAAO0M,aAAa;oBACpBhL,QAAQwD,KAAK,CAAC,qCAAqCwH;gBACnD,8CAA8C;gBAChD;YACF;YAEA,OAAO;gBACL9V,QAAQ;gBACRC,MAAM;oBACJ0B,IAAIuR,YAAYvR,EAAE;oBAClB2K,yBAAyB4G,YAAY5G,uBAAuB;oBAC5DC,QAAQpM,OAAO+S,YAAY3G,MAAM;oBACjCnC,UAAU8I,YAAY9I,QAAQ;oBAC9BpK,QAAQkT,YAAYlT,MAAM;oBAC1BwM,aAAa0G,YAAY1G,WAAW;oBACpCC,WAAWyG,YAAYzG,SAAS;oBAChCvK,YAAY,IAAI,CAACzD,cAAc,CAACyU,YAAYhR,UAAU;oBACtDa,YAAY,IAAI,CAACtE,cAAc,CAACyU,YAAYnQ,UAAU;oBACtD6G,kBAAkBsJ,YAAYrK,qBAAqB;oBACnDkN,gBAAgBhB;oBAChBiB,kBAAkBhB;gBACpB;YACF;QACF,EAAE,OAAO1G,OAAO;YACdxD,QAAQwD,KAAK,CAAC,wCAAwCA;YACtD,MAAMA;QACR;IACF;IAEA,sCAAsC;IACtC,MAAM2H,mBAAmB5E,cAAsB,EAAErO,MAAc,EAAE;QAC/D,IAAI;YACF,yDAAyD;YACzD,MAAMsM,eAAe,MAAMxQ,YAAE,CAC1BC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKqS,gBACjBrS,KAAK,CAAC,WAAW,KAAKgE,QACtBhE,KAAK,CAAC,cAAc,MAAM,MAC1BC,MAAM,CAAC;gBAAC;gBAAgC;aAAS,EACjDI,gBAAgB;YAEnB,IAAI,CAACiQ,cAAc;gBACjB,OAAO;oBACLtP,QAAQ;oBACR8D,SAAS;gBACX;YACF;YAEA,8CAA8C;YAC9C,IAAIwL,aAAatP,MAAM,KAAK,YAAY;gBACtC,OAAO;oBACLA,QAAQ;oBACR8D,SAAS;gBACX;YACF;YAEA,kCAAkC;YAClC,IAAIwL,aAAavG,4BAA4B,EAAE;gBAC7C,IAAI;oBACF,MAAM,IAAI,CAACiE,MAAM,CAACgE,aAAa,CAACkF,MAAM,CAAC5G,aAAavG,4BAA4B,EAAE;wBAChFiD,sBAAsB;oBACxB;gBACF,EAAE,OAAO8J,aAAa;oBACpBhL,QAAQwD,KAAK,CAAC,0CAA0CwH;gBACxD,kEAAkE;gBACpE;YACF;YAEA,2CAA2C;YAC3C,MAAMhX,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;gBACHuI,sBAAsB;gBACtBjJ,YAAY,IAAItD;YAClB,GACCT,KAAK,CAAC,MAAM,KAAKqS,gBACjB9P,OAAO;YAEV,OAAO;gBACLvB,QAAQ;gBACR8D,SAAS;YACX;QACF,EAAE,OAAOwK,OAAO;YACdxD,QAAQwD,KAAK,CAAC,gCAAgCA;YAC9C,MAAMA;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM6H,8BAA8B9E,cAAsB,EAAErO,MAAc,EAAE;QAC1E,IAAI;YACF,yDAAyD;YACzD,MAAMsM,eAAe,MAAMxQ,YAAE,CAC1BC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKqS,gBACjBrS,KAAK,CAAC,WAAW,KAAKgE,QACtBhE,KAAK,CAAC,cAAc,MAAM,MAC1BC,MAAM,CAAC;gBAAC;gBAAgC;aAAS,EACjDI,gBAAgB;YAEnB,IAAI,CAACiQ,cAAc;gBACjB,OAAO;oBACLtP,QAAQ;oBACR8D,SAAS;gBACX;YACF;YAEA,8CAA8C;YAC9C,IAAIwL,aAAatP,MAAM,KAAK,YAAY;gBACtC,OAAO;oBACLA,QAAQ;oBACR8D,SAAS;gBACX;YACF;YAEA,gDAAgD;YAChD,IAAIwL,aAAavG,4BAA4B,EAAE;gBAC7C,IAAI;oBACF,MAAM,IAAI,CAACiE,MAAM,CAACgE,aAAa,CAACoF,MAAM,CAAC9G,aAAavG,4BAA4B;gBAClF,EAAE,OAAO+M,aAAa;oBACpBhL,QAAQwD,KAAK,CAAC,0CAA0CwH;gBACxD,kEAAkE;gBACpE;YACF;YAEA,2CAA2C;YAC3C,MAAM9V,SAAqB;YAC3B,MAAMlB,YAAE,CACL0E,WAAW,CAAC,uBACZC,GAAG,CAAC;gBACHzD;gBACA8L,UAAU,IAAIrM;gBACdsD,YAAY,IAAItD;YAClB,GACCT,KAAK,CAAC,MAAM,KAAKqS,gBACjB9P,OAAO;YAEV,OAAO;gBACLvB,QAAQ;gBACR8D,SAAS;YACX;QACF,EAAE,OAAOwK,OAAO;YACdxD,QAAQwD,KAAK,CAAC,8CAA8CA;YAC5D,MAAMA;QACR;IACF;IAEF,kEAAkE;IAClE,MAAM+H,mCAAmChF,cAAsB,EAAE;QAC/D,IAAI;YACFvG,QAAQC,GAAG,CAAC,CAAC,oDAAoD,EAAEsG,gBAAgB;YAEnF,sBAAsB;YACtB,MAAM/B,eAAe,MAAMxQ,YAAE,CAC1BC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKqS,gBACjBpS,MAAM,CAAC;gBAAC;gBAAM;gBAAW;gBAAW;gBAAS;aAAW,EACxDI,gBAAgB;YAEnB,IAAI,CAACiQ,cAAc;gBACjBxE,QAAQwD,KAAK,CAAC,CAAC,WAAW,EAAE+C,eAAe,eAAe,CAAC;gBAC3D,OAAO;oBACLrR,QAAQ;oBACR8D,SAAS;gBACX;YACF;YAEA,uDAAuD;YACvD,MAAMoP,cAAc,MAAMpU,YAAE,CACzBC,UAAU,CAAC,gBACXC,KAAK,CAAC,aAAa,KAAKqS,gBACxBrS,KAAK,CAAC,eAAe,KAAK,gBAC1BA,KAAK,CAAC,UAAU,KAAK,QACrBkC,OAAO,CAAC,MAAM,QACdjC,MAAM,CAAC;gBAAC;gBAAM;gBAAU;aAAW,EACnCI,gBAAgB;YAEnB,IAAI,CAAC6T,aAAa;gBAChBpI,QAAQwD,KAAK,CAAC,CAAC,+CAA+C,EAAE+C,gBAAgB;gBAEhF,2CAA2C;gBAC3C,MAAMiF,iBAAiB,MAAMxX,YAAE,CAC5B+D,UAAU,CAAC,gBACXC,MAAM,CAAC;oBACNI,SAASoM,aAAapM,OAAO;oBAC7BoJ,yBAAyB,CAAC,OAAO,EAAE7M,KAAK8W,GAAG,IAAI;oBAC/CtL,qBAAqB;oBACrBsB,QAAQpM,OAAOmP,aAAajG,KAAK,IAAI;oBACrCe,UAAUkF,aAAalF,QAAQ,IAAI;oBACnCpK,QAAQ;oBACRwM,aAAa;oBACbC,WAAW6C,aAAa3N,EAAE;oBAC1BO,YAAY,IAAIzC;oBAChBsD,YAAY,IAAItD;gBAClB,GACCJ,gBAAgB;gBAEnB,IAAIiX,kBAAkB,cAAcA,gBAAgB;oBAClD,MAAM7E,gBAAgBtR,OAAOmW,eAAerT,QAAQ;oBACpD6H,QAAQC,GAAG,CAAC,CAAC,8BAA8B,EAAE0G,eAAe;oBAE5D,sBAAsB;oBACtB,MAAM,IAAI,CAACG,4BAA4B,CACrCtC,aAAapM,OAAO,EACpBoM,aAAajE,OAAO,EACpBoG,eACAtR,OAAOmP,aAAajG,KAAK,IAAI,IAC7BiG,aAAalF,QAAQ,IAAI;oBAG3B,OAAO;wBACLpK,QAAQ;wBACR8D,SAAS;wBACT7D,MAAM;4BACJmU,gBAAgB3C;wBAClB;oBACF;gBACF,OAAO;oBACL3G,QAAQwD,KAAK,CAAC,CAAC,0CAA0C,EAAE+C,gBAAgB;oBAC3E,OAAO;wBACLrR,QAAQ;wBACR8D,SAAS;oBACX;gBACF;YACF,OAAO;gBACLgH,QAAQC,GAAG,CAAC,CAAC,sBAAsB,EAAEmI,YAAYvR,EAAE,EAAE;gBAErD,sBAAsB;gBACtB,MAAM,IAAI,CAACiQ,4BAA4B,CACrCtC,aAAapM,OAAO,EACpBoM,aAAajE,OAAO,EACpB6H,YAAYvR,EAAE,EACdxB,OAAO+S,YAAY3G,MAAM,IAAI,IAC7B2G,YAAY9I,QAAQ,IAAI;gBAG1B,OAAO;oBACLpK,QAAQ;oBACR8D,SAAS;oBACT7D,MAAM;wBACJmU,gBAAgBlB,YAAYvR,EAAE;oBAChC;gBACF;YACF;QACF,EAAE,OAAO2M,OAAO;YACdxD,QAAQwD,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;gBACLtO,QAAQ;gBACR8D,SAAS;YACX;QACF;IACF;IAx/GI0S,aAAc;QACZ,aAAa;QACb,IAAI,CAACxJ,MAAM,GAAG,IAAIyJ,cAAM,CAACC,QAAQC,GAAG,CAACC,SAAS,EAAE;QAEhD;IACF;AAo/GN"}