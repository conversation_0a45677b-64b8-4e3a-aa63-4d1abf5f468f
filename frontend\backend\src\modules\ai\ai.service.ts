import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface MacroNutrients {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

interface FoodSuggestion {
  name: string;
  quantity: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  description?: string;
}

interface AIFoodSuggestionsRequest {
  type: 'text' | 'image' | 'audio';
  content: string;
  meal_type?: string;
}

@Injectable()
export class AIService {
  constructor(private configService: ConfigService) {}

  async generateFoodSuggestions(
    userId: string,
    request: AIFoodSuggestionsRequest,
    mealId?: string,
    mealType?: string
  ): Promise<FoodSuggestion[]> {
    try {
      // Para desenvolvimento, retornar sugestões mockadas inteligentes
      if (request.type === 'text' && request.content) {
        return this.generateTextBasedSuggestions(request.content, mealType);
      } else if (request.type === 'image') {
        return this.generateImageBasedSuggestions();
      } else if (request.type === 'audio') {
        return this.generateAudioBasedSuggestions();
      } else {
        return this.generateDefaultSuggestions(mealType);
      }
    } catch (error) {
      console.error('Error generating AI food suggestions:', error);
      return this.generateDefaultSuggestions(mealType);
    }
  }

  private generateTextBasedSuggestions(content: string, mealType?: string): FoodSuggestion[] {
    const lowerContent = content.toLowerCase();
    
    // Detectar preferências do usuário no texto
    if (lowerContent.includes('proteína') || lowerContent.includes('protein')) {
      return this.getHighProteinSuggestions();
    } else if (lowerContent.includes('carboidrato') || lowerContent.includes('carbs')) {
      return this.getHighCarbSuggestions();
    } else if (lowerContent.includes('vegetariano') || lowerContent.includes('vegano')) {
      return this.getVegetarianSuggestions();
    } else if (lowerContent.includes('low carb') || lowerContent.includes('cetogênica')) {
      return this.getLowCarbSuggestions();
    } else {
      return this.generateDefaultSuggestions(mealType);
    }
  }

  private generateImageBasedSuggestions(): FoodSuggestion[] {
    // Simulação de reconhecimento de imagem
    return [
      {
        name: 'Prato identificado na imagem',
        quantity: '1 porção',
        calories: 350,
        protein: 25,
        carbs: 30,
        fat: 15,
        description: 'Baseado na análise da imagem enviada'
      },
      {
        name: 'Alternativa similar',
        quantity: '1 porção',
        calories: 320,
        protein: 22,
        carbs: 28,
        fat: 12,
        description: 'Opção similar com menos calorias'
      }
    ];
  }

  private generateAudioBasedSuggestions(): FoodSuggestion[] {
    // Simulação de reconhecimento de áudio
    return [
      {
        name: 'Alimento mencionado no áudio',
        quantity: '1 porção',
        calories: 280,
        protein: 20,
        carbs: 25,
        fat: 10,
        description: 'Baseado na transcrição do áudio'
      }
    ];
  }

  private getHighProteinSuggestions(): FoodSuggestion[] {
    return [
      {
        name: 'Peito de frango grelhado',
        quantity: '150g',
        calories: 248,
        protein: 46,
        carbs: 0,
        fat: 5,
        description: 'Excelente fonte de proteína magra'
      },
      {
        name: 'Ovo mexido',
        quantity: '2 unidades',
        calories: 155,
        protein: 13,
        carbs: 1,
        fat: 11,
        description: 'Proteína completa e versátil'
      },
      {
        name: 'Iogurte grego natural',
        quantity: '200g',
        calories: 130,
        protein: 20,
        carbs: 9,
        fat: 0,
        description: 'Rico em proteína e probióticos'
      }
    ];
  }

  private getHighCarbSuggestions(): FoodSuggestion[] {
    return [
      {
        name: 'Arroz integral',
        quantity: '100g cozido',
        calories: 111,
        protein: 3,
        carbs: 23,
        fat: 1,
        description: 'Carboidrato complexo e nutritivo'
      },
      {
        name: 'Batata doce assada',
        quantity: '150g',
        calories: 129,
        protein: 2,
        carbs: 30,
        fat: 0,
        description: 'Rica em vitaminas e fibras'
      },
      {
        name: 'Aveia',
        quantity: '50g',
        calories: 190,
        protein: 7,
        carbs: 32,
        fat: 4,
        description: 'Carboidrato de liberação lenta'
      }
    ];
  }

  private getVegetarianSuggestions(): FoodSuggestion[] {
    return [
      {
        name: 'Quinoa cozida',
        quantity: '100g',
        calories: 120,
        protein: 4,
        carbs: 22,
        fat: 2,
        description: 'Proteína vegetal completa'
      },
      {
        name: 'Tofu grelhado',
        quantity: '100g',
        calories: 76,
        protein: 8,
        carbs: 2,
        fat: 5,
        description: 'Fonte de proteína vegetal'
      },
      {
        name: 'Lentilha cozida',
        quantity: '100g',
        calories: 116,
        protein: 9,
        carbs: 20,
        fat: 0,
        description: 'Rica em proteína e fibras'
      }
    ];
  }

  private getLowCarbSuggestions(): FoodSuggestion[] {
    return [
      {
        name: 'Salmão grelhado',
        quantity: '120g',
        calories: 231,
        protein: 25,
        carbs: 0,
        fat: 14,
        description: 'Rico em ômega-3 e proteína'
      },
      {
        name: 'Abacate',
        quantity: '1/2 unidade',
        calories: 160,
        protein: 2,
        carbs: 4,
        fat: 15,
        description: 'Gordura saudável e fibras'
      },
      {
        name: 'Brócolis refogado',
        quantity: '150g',
        calories: 51,
        protein: 4,
        carbs: 6,
        fat: 1,
        description: 'Baixo em carboidratos e nutritivo'
      }
    ];
  }

  private generateDefaultSuggestions(mealType?: string): FoodSuggestion[] {
    const timeOfDay = new Date().getHours();
    
    if (mealType === 'replacement' || timeOfDay < 10) {
      // Café da manhã
      return [
        {
          name: 'Tapioca com queijo',
          quantity: '1 unidade',
          calories: 180,
          protein: 8,
          carbs: 25,
          fat: 6,
          description: 'Opção leve e nutritiva para o café da manhã'
        },
        {
          name: 'Vitamina de banana',
          quantity: '300ml',
          calories: 150,
          protein: 6,
          carbs: 30,
          fat: 2,
          description: 'Rica em potássio e energia'
        }
      ];
    } else if (timeOfDay >= 12 && timeOfDay < 15) {
      // Almoço
      return [
        {
          name: 'Prato balanceado',
          quantity: '1 porção',
          calories: 450,
          protein: 30,
          carbs: 45,
          fat: 15,
          description: 'Arroz, feijão, carne e salada'
        },
        {
          name: 'Salada completa',
          quantity: '1 porção',
          calories: 320,
          protein: 25,
          carbs: 20,
          fat: 18,
          description: 'Mix de folhas, proteína e azeite'
        }
      ];
    } else {
      // Jantar ou lanche
      return [
        {
          name: 'Sanduíche natural',
          quantity: '1 unidade',
          calories: 280,
          protein: 15,
          carbs: 35,
          fat: 8,
          description: 'Pão integral com recheio saudável'
        },
        {
          name: 'Sopa de legumes',
          quantity: '300ml',
          calories: 120,
          protein: 5,
          carbs: 20,
          fat: 3,
          description: 'Leve e nutritiva'
        }
      ];
    }
  }

  async generateMealSuggestion(macros: MacroNutrients, preferences?: string[]): Promise<string> {
    // Implementação futura para integração com OpenAI
    return `Sugestão baseada em ${macros.calories}kcal, ${macros.protein}g proteína`;
  }
}
