import { <PERSON>, <PERSON>, Body, HttpStatus, Res, Bind, UseGuards, Get, Param, Request, HttpException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { CreateUserDto } from './create-user.dto';
import { JwtAuthGuard } from './jwt-auth.guard';
import { AuthGuard } from '@nestjs/passport';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  // login
  @Post('login')
  async login(@Body() user: any): Promise<any> {
      return this.authService.login(user);
  }

  // Rota POST /auth/register
  @Post('register')
  async register(@Body() createUserDto: CreateUserDto): Promise<any> {
      return this.authService.register(createUserDto);
  }

  // Refresh token
  @Post('refresh')
  async refreshToken(@Body() { refresh_token, device_uid }: { refresh_token: string, device_uid: string }): Promise<any> {
      if (!refresh_token) {
          throw new Error('Refresh token is required');
      }

      if (!device_uid) {
          throw new Error('Device UID is required');
      }

      return this.authService.refreshToken(refresh_token, device_uid);
  }

  // Account recovery
  @Post('recover-account')
  async recoverAccount(@Body() { userId }: { userId: number }): Promise<any> {
      if (!userId) {
          throw new Error('User ID is required');
      }

      return this.authService.recoverAccount(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('role/:role')
  getRole(@Param('role') role: string, @Request() req: any) {
    const userId: number = req.user.userId;
      return this.authService.getRole(userId, role);
  }

  @Post('request-password-reset')
  async requestPasswordReset(@Body() body: { email: string }) {
    return this.authService.requestPasswordReset(body.email);
  }

  @Post('reset-password')
  async resetPassword(@Body() body: { token: string; password: string }) {
    return this.authService.resetPassword(body.token, body.password);
  }

  @Post('verify-email')
  async verifyEmail(@Body() body: { token: string }) {
    return this.authService.verifyEmail(body.token);
  }

  @Post('resend-verification')
  async resendVerification(@Body() body: { email: string }) {
    return this.authService.resendVerification(body.email);
  }

  @Get('sessions')
  @UseGuards(JwtAuthGuard)
  async getSessions(@Request() req: any) {
    const userId = req.user.userId;
    return this.authService.getUserSessions(userId);
  }

  @Post('sessions/:id/terminate')
  @UseGuards(JwtAuthGuard)
  async terminateSession(@Param('id') sessionId: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.authService.terminateSession(sessionId, userId);
  }

  @Post('logout-all')
  @UseGuards(JwtAuthGuard)
  async logoutAllSessions(@Request() req: any) {
    const userId = req.user.userId;
    return this.authService.logoutAllSessions(userId);
  }

  // Google OAuth Routes
  @Get('google')
  @UseGuards(AuthGuard('google'))
  async googleAuth(@Request() req: any) {
    // This route initiates the Google OAuth flow
  }

  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Request() req: any, @Res() res: any) {
    try {
      const result = await this.authService.loginWithOAuth(req.user, 'google');

      // Redirect to frontend with tokens
      const redirectUrl = `${process.env.GOOGLE_REDIRECT_URL}?access_token=${result.data.access_token}&refresh_token=${result.data.refresh_token}&device_uid=${result.data.device_uid}`;
      return res.redirect(redirectUrl);
    } catch (error) {
      console.error('Google OAuth error:', error);
      return res.redirect(`${process.env.GOOGLE_REDIRECT_URL}?error=oauth_failed`);
    }
  }

  // Apple OAuth Routes
  @Get('apple')
  @UseGuards(AuthGuard('apple'))
  async appleAuth(@Request() req: any) {
    // This route initiates the Apple OAuth flow
  }

  @Post('apple/callback')
  @UseGuards(AuthGuard('apple'))
  async appleAuthRedirect(@Request() req: any, @Res() res: any) {
    try {
      const result = await this.authService.loginWithOAuth(req.user, 'apple');

      // Redirect to frontend with tokens
      const redirectUrl = `${process.env.APPLE_REDIRECT_URL}?access_token=${result.data.access_token}&refresh_token=${result.data.refresh_token}&device_uid=${result.data.device_uid}`;
      return res.redirect(redirectUrl);
    } catch (error) {
      console.error('Apple OAuth error:', error);
      return res.redirect(`${process.env.APPLE_REDIRECT_URL}?error=oauth_failed`);
    }
  }

  // Frontend-initiated OAuth endpoints
  @Post('oauth/google')
  async googleOAuthToken(@Body() body: { token: string, userInfo: any }) {
    try {
      // Validate Google token and extract user info
      const result = await this.authService.validateGoogleToken(body.token, body.userInfo);
      return result;
    } catch (error) {
      console.error('Google OAuth token validation error:', error);
      throw new HttpException('Invalid Google token', HttpStatus.UNAUTHORIZED);
    }
  }

  @Post('oauth/apple')
  async appleOAuthToken(@Body() body: { token: string, userInfo: any }) {
    try {
      // Validate Apple token and extract user info
      const result = await this.authService.validateAppleToken(body.token, body.userInfo);
      return result;
    } catch (error) {
      console.error('Apple OAuth token validation error:', error);
      throw new HttpException('Invalid Apple token', HttpStatus.UNAUTHORIZED);
    }
  }
}