{"version": 3, "sources": ["../../../src/admin/dto/import-coach-protocol.dto.ts"], "sourcesContent": ["import { IsInt } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class ImportCoachProtocolDto {\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  protocol_id: number;\r\n}"], "names": ["ImportCoachProtocolDto", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAHS;kCACD;;;;;;;;;;AAEd,IAAA,AAAMA,yBAAN,MAAMA;AAIb;;;oCAFcC"}