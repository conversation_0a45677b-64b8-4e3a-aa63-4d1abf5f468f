{"version": 3, "sources": ["../../src/challenges/challenges.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { ChallengesController } from './challenges.controller';\nimport { ChallengesService } from './challenges.service';\n\n@Module({\n  controllers: [ChallengesController],\n  providers: [ChallengesService],\n  exports: [ChallengesService],\n})\nexport class ChallengesModule {}\n"], "names": ["ChallengesModule", "controllers", "ChallengesController", "providers", "ChallengesService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;sCACc;mCACH;;;;;;;AAO3B,IAAA,AAAMA,mBAAN,MAAMA;AAAkB;;;QAJ7BC,aAAa;YAACC,0CAAoB;SAAC;QACnCC,WAAW;YAACC,oCAAiB;SAAC;QAC9BC,SAAS;YAACD,oCAAiB;SAAC"}