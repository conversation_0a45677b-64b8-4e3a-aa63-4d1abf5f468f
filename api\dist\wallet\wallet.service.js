"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "WalletService", {
    enumerable: true,
    get: function() {
        return WalletService;
    }
});
const _common = require("@nestjs/common");
const _database = require("../database");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
// Custos em tokens para diferentes features de IA
const TOKEN_COSTS = {
    'ai_protocol_generation': 10,
    'ai_meal_suggestion': 2,
    'ai_workout_creation': 8,
    'ai_analysis': 5,
    'ai_chat_message': 1
};
let WalletService = class WalletService {
    async getUserWallet(userId) {
        try {
            // Buscar pontos do usuário (SnapCoins)
            const pointsResult = await _database.db.execute(`SELECT
          total_points as totalPoints,
          monthly_points as monthlyPoints,
          yearly_points as yearlyPoints,
          updated_at as lastUpdated
        FROM user_points
        WHERE user_id = ?`, [
                userId
            ]);
            // Verificar se pointsResult é um array e tem dados
            const pointsData = Array.isArray(pointsResult) && pointsResult.length > 0 ? pointsResult[0] : null;
            const points = pointsData && Array.isArray(pointsData) && pointsData.length > 0 ? pointsData[0] : {
                totalPoints: 0,
                monthlyPoints: 0,
                yearlyPoints: 0,
                lastUpdated: null
            };
            // Calcular pontos gastos
            const spentResult = await _database.db.execute(`SELECT COALESCE(SUM(points), 0) as totalSpent
        FROM points_transactions
        WHERE user_id = ? AND type = 'spent'`, [
                userId
            ]);
            const spentData = Array.isArray(spentResult) && spentResult.length > 0 ? spentResult[0] : null;
            const totalSpent = spentData && Array.isArray(spentData) && spentData.length > 0 ? spentData[0]?.totalSpent || 0 : 0;
            const totalEarned = points.totalPoints + totalSpent;
            // Buscar assinatura ativa do usuário para determinar o plano e tokens
            const subscriptionResult = await _database.db.execute(`SELECT
          us.snaptokens,
          p.name as plan_name,
          p.snaptokens as plan_snaptokens
        FROM users_subscriptions us
        INNER JOIN plans p ON us.plan_id = p.id
        WHERE us.user_id = ? AND us.status = 'active' AND us.deleted_at IS NULL
        ORDER BY us.created_at DESC
        LIMIT 1`, [
                userId
            ]);
            const subscriptionData = Array.isArray(subscriptionResult) && subscriptionResult.length > 0 ? subscriptionResult[0] : null;
            const subscription = subscriptionData && Array.isArray(subscriptionData) && subscriptionData.length > 0 ? subscriptionData[0] : null;
            // Se não tem assinatura ativa, usar valores padrão (plano gratuito)
            const planName = subscription?.plan_name || 'Free';
            const planTokenLimit = subscription?.snaptokens || subscription?.plan_snaptokens || 0;
            // Buscar uso de tokens do mês atual
            const currentMonth = new Date().getMonth() + 1;
            const currentYear = new Date().getFullYear();
            const tokensUsedResult = await _database.db.execute(`SELECT COALESCE(SUM(tokens_used), 0) as tokensUsed
        FROM token_usage
        WHERE user_id = ? AND MONTH(created_at) = ? AND YEAR(created_at) = ?`, [
                userId,
                currentMonth,
                currentYear
            ]);
            const tokensData = Array.isArray(tokensUsedResult) && tokensUsedResult.length > 0 ? tokensUsedResult[0] : null;
            const tokensUsed = tokensData && Array.isArray(tokensData) && tokensData.length > 0 ? tokensData[0]?.tokensUsed || 0 : 0;
            const tokensRemaining = planTokenLimit === -1 ? -1 : Math.max(0, planTokenLimit - tokensUsed);
            // Data do próximo reset (primeiro dia do próximo mês)
            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1, 1);
            nextMonth.setHours(0, 0, 0, 0);
            const wallet = {
                snapCoins: {
                    total: points.totalPoints,
                    earned: totalEarned,
                    spent: totalSpent,
                    monthlyEarned: points.monthlyPoints,
                    yearlyEarned: points.yearlyPoints
                },
                snapTokens: {
                    total: planTokenLimit,
                    used: tokensUsed,
                    remaining: tokensRemaining,
                    monthlyLimit: planTokenLimit,
                    planType: planName,
                    resetDate: nextMonth.toISOString()
                }
            };
            return {
                status: 'success',
                data: wallet
            };
        } catch (error) {
            console.error('Error fetching user wallet:', error);
            throw new _common.HttpException('Failed to fetch wallet data', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async earnSnapCoins(userId, amount, reason, category) {
        try {
            await _database.db.execute('START TRANSACTION');
            // Atualizar pontos do usuário
            await _database.db.execute(`INSERT INTO user_points (user_id, total_points, monthly_points, yearly_points, updated_at)
         VALUES (?, ?, ?, ?, NOW())
         ON DUPLICATE KEY UPDATE
         total_points = total_points + ?,
         monthly_points = monthly_points + ?,
         yearly_points = yearly_points + ?,
         updated_at = NOW()`, [
                userId,
                amount,
                amount,
                amount,
                amount,
                amount,
                amount
            ]);
            // Registrar transação
            await _database.db.execute(`INSERT INTO points_transactions (user_id, points, type, reason, category, created_at)
         VALUES (?, ?, 'earned', ?, ?, NOW())`, [
                userId,
                amount,
                reason,
                category || 'general'
            ]);
            await _database.db.execute('COMMIT');
            return {
                status: 'success',
                message: `Earned ${amount} SnapCoins for ${reason}`,
                data: {
                    amount,
                    reason,
                    category: category || 'general',
                    type: 'earned'
                }
            };
        } catch (error) {
            await _database.db.execute('ROLLBACK');
            console.error('Error earning SnapCoins:', error);
            throw new _common.HttpException('Failed to earn SnapCoins', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async spendSnapCoins(userId, amount, reason, category) {
        try {
            // Verificar se o usuário tem pontos suficientes
            const [pointsResult] = await _database.db.execute('SELECT total_points FROM user_points WHERE user_id = ?', [
                userId
            ]);
            const currentPoints = pointsResult[0]?.total_points || 0;
            if (currentPoints < amount) {
                throw new _common.HttpException('Insufficient SnapCoins', _common.HttpStatus.BAD_REQUEST);
            }
            await _database.db.execute('START TRANSACTION');
            // Atualizar pontos do usuário
            await _database.db.execute(`UPDATE user_points 
         SET total_points = total_points - ?, updated_at = NOW()
         WHERE user_id = ?`, [
                amount,
                userId
            ]);
            // Registrar transação
            await _database.db.execute(`INSERT INTO points_transactions (user_id, points, type, reason, category, created_at)
         VALUES (?, ?, 'spent', ?, ?, NOW())`, [
                userId,
                amount,
                reason,
                category || 'general'
            ]);
            await _database.db.execute('COMMIT');
            return {
                status: 'success',
                message: `Spent ${amount} SnapCoins on ${reason}`,
                data: {
                    amount,
                    reason,
                    category: category || 'general',
                    type: 'spent'
                }
            };
        } catch (error) {
            await _database.db.execute('ROLLBACK');
            console.error('Error spending SnapCoins:', error);
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Failed to spend SnapCoins', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async useSnapTokens(userId, amount, feature) {
        try {
            // Verificar se o usuário pode usar a feature
            const canUse = await this.canUseFeature(userId, feature);
            if (!canUse.data.canUse) {
                throw new _common.HttpException(canUse.data.reason || 'Cannot use this feature', _common.HttpStatus.BAD_REQUEST);
            }
            // Registrar uso de tokens (exceto para plano SnapMonster que é ilimitado)
            const [userResult] = await _database.db.execute('SELECT plan_type FROM users WHERE id = ?', [
                userId
            ]);
            const userPlan = userResult[0]?.plan_type || 'snapbasic';
            if (userPlan !== 'snapmonster') {
                await _database.db.execute(`INSERT INTO token_usage (user_id, tokens_used, feature, created_at)
           VALUES (?, ?, ?, NOW())`, [
                    userId,
                    amount,
                    feature
                ]);
            }
            return {
                status: 'success',
                message: `Used ${amount} SnapTokens for ${feature}`,
                data: {
                    amount,
                    feature,
                    type: 'used'
                }
            };
        } catch (error) {
            console.error('Error using SnapTokens:', error);
            if (error instanceof _common.HttpException) {
                throw error;
            }
            throw new _common.HttpException('Failed to use SnapTokens', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async canUseFeature(userId, feature) {
        try {
            const cost = TOKEN_COSTS[feature] || 1;
            // Buscar assinatura ativa do usuário para determinar o plano e tokens
            const subscriptionResult = await _database.db.execute(`SELECT
          us.snaptokens,
          p.name as plan_name,
          p.snaptokens as plan_snaptokens
        FROM users_subscriptions us
        INNER JOIN plans p ON us.plan_id = p.id
        WHERE us.user_id = ? AND us.status = 'active' AND us.deleted_at IS NULL
        ORDER BY us.created_at DESC
        LIMIT 1`, [
                userId
            ]);
            const subscriptionData = Array.isArray(subscriptionResult) && subscriptionResult.length > 0 ? subscriptionResult[0] : null;
            const subscription = subscriptionData && Array.isArray(subscriptionData) && subscriptionData.length > 0 ? subscriptionData[0] : null;
            // Se não tem assinatura ativa, usar valores padrão (plano gratuito)
            const planName = subscription?.plan_name || 'Free';
            const planTokenLimit = subscription?.snaptokens || subscription?.plan_snaptokens || 0;
            // Verificar se tem tokens ilimitados
            if (planTokenLimit === -1) {
                return {
                    status: 'success',
                    data: {
                        canUse: true,
                        reason: 'Unlimited tokens',
                        cost,
                        planType: planName
                    }
                };
            }
            // Verificar uso de tokens do mês atual
            const currentMonth = new Date().getMonth() + 1;
            const currentYear = new Date().getFullYear();
            const tokensUsedResult = await _database.db.execute(`SELECT COALESCE(SUM(tokens_used), 0) as tokensUsed
        FROM token_usage
        WHERE user_id = ? AND MONTH(created_at) = ? AND YEAR(created_at) = ?`, [
                userId,
                currentMonth,
                currentYear
            ]);
            const tokensData = Array.isArray(tokensUsedResult) && tokensUsedResult.length > 0 ? tokensUsedResult[0] : null;
            const tokensUsed = tokensData && Array.isArray(tokensData) && tokensData.length > 0 ? tokensData[0]?.tokensUsed || 0 : 0;
            const tokensRemaining = Math.max(0, planTokenLimit - tokensUsed);
            const canUse = tokensRemaining >= cost;
            return {
                status: 'success',
                data: {
                    canUse,
                    reason: canUse ? 'Sufficient tokens available' : 'Insufficient tokens',
                    cost,
                    tokensRemaining,
                    monthlyLimit: planTokenLimit,
                    planType: planName
                }
            };
        } catch (error) {
            console.error('Error checking feature availability:', error);
            throw new _common.HttpException('Failed to check feature availability', _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
WalletService = _ts_decorate([
    (0, _common.Injectable)()
], WalletService);

//# sourceMappingURL=wallet.service.js.map