"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    convertFromUTC: function() {
        return convertFromUTC;
    },
    convertToUTC: function() {
        return convertToUTC;
    }
});
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
dayjs.extend(utc);
dayjs.extend(timezone);
function convertToUTC(date, tz) {
    return dayjs.tz(date, tz).utc().format('YYYY-MM-DD HH:mm:ss');
}
function convertFromUTC(date, tz) {
    return dayjs.utc(date).tz(tz).format('YYYY-MM-DD HH:mm:ss');
}

//# sourceMappingURL=date.util.js.map