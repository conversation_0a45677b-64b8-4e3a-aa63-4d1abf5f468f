"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AppleStrategy", {
    enumerable: true,
    get: function() {
        return AppleStrategy;
    }
});
const _common = require("@nestjs/common");
const _passport = require("@nestjs/passport");
const _passportapple = require("@nicokaiser/passport-apple");
const _dotenv = require("dotenv");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
(0, _dotenv.config)();
let AppleStrategy = class AppleStrategy extends (0, _passport.PassportStrategy)(_passportapple.Strategy, 'apple') {
    async validate(accessToken, refreshToken, idToken, profile) {
        try {
            // Apple provides user data differently than Google
            const { sub, email, email_verified } = idToken;
            // Extract user data from Apple profile
            const user = {
                appleId: sub,
                email: email,
                emailVerified: email_verified,
                name: profile?.name ? `${profile.name.firstName || ''} ${profile.name.lastName || ''}`.trim() : null,
                firstName: profile?.name?.firstName,
                lastName: profile?.name?.lastName,
                accessToken,
                refreshToken
            };
            // Validate that we have required data
            if (!user.email) {
                throw new Error('Email not provided by Apple');
            }
            return user;
        } catch (error) {
            throw error;
        }
    }
    constructor(){
        // Check if Apple OAuth is properly configured
        if (!process.env.APPLE_CLIENT_ID || !process.env.APPLE_TEAM_ID || !process.env.APPLE_KEY_ID || !process.env.APPLE_PRIVATE_KEY) {
            console.warn('Apple OAuth not properly configured. Some environment variables are missing.');
            // Use dummy values to prevent strategy initialization errors
            super({
                clientID: 'dummy',
                teamID: 'dummy',
                keyID: 'dummy',
                key: '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\n-----END PRIVATE KEY-----',
                callbackURL: process.env.APPLE_CALLBACK_URL || 'http://localhost:3000/auth/apple/callback',
                scope: [
                    'email',
                    'name'
                ]
            });
            return;
        }
        super({
            clientID: process.env.APPLE_CLIENT_ID,
            teamID: process.env.APPLE_TEAM_ID,
            keyID: process.env.APPLE_KEY_ID,
            key: process.env.APPLE_PRIVATE_KEY,
            callbackURL: process.env.APPLE_CALLBACK_URL,
            scope: [
                'email',
                'name'
            ]
        });
    }
};
AppleStrategy = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [])
], AppleStrategy);

//# sourceMappingURL=apple.strategy.js.map