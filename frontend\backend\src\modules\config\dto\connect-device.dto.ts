import { IsString, IsN<PERSON>ber, IsObject, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ConnectDeviceDto {
  @ApiProperty()
  @IsString()
  provider: string;

  @ApiProperty()
  @IsString()
  providerId: string;

  @ApiProperty()
  @IsString()
  accessToken: string;

  @ApiProperty()
  @IsString()
  refreshToken: string;

  @ApiProperty()
  @IsNumber()
  expiresIn: number;

  @ApiProperty({ required: false })
  @IsObject()
  @IsOptional()
  settings?: Record<string, any>;
}