// src/admin/dto/create-user.dto.ts
import { IsString, IsEmail, IsNotEmpty, IsInt, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsInt()
  @Type(() => Number) // Converte role_id para número
  role_id: number;

  @IsString()
  @IsOptional()
  photo?: string;
}