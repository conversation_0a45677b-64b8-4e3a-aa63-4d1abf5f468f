{"version": 3, "sources": ["../src/dayjs.ts"], "sourcesContent": ["import dayjs from 'dayjs';\r\nimport utc from 'dayjs/plugin/utc';\r\nimport isTomorrow from 'dayjs/plugin/isToday';\r\nimport isToday from 'dayjs/plugin/isTomorrow';\r\nimport relativeTime from 'dayjs/plugin/relativeTime';\r\n\r\ndayjs.extend(utc);\r\ndayjs.extend(isToday);\r\ndayjs.extend(isTomorrow);\r\ndayjs.extend(relativeTime);\r\n\r\nexport {\r\n  dayjs, \r\n  utc, isToday, isTomorrow, relativeTime, // important to not get compiling errors when using these plugins\r\n};"], "names": ["dayjs", "isToday", "isTomorrow", "relativeTime", "utc", "extend"], "mappings": ";;;;;;;;;;;IAYEA,KAAK;eAALA,cAAK;;IACAC,OAAO;eAAPA,mBAAO;;IAAEC,UAAU;eAAVA,gBAAU;;IAAEC,YAAY;eAAZA,qBAAY;;IAAtCC,GAAG;eAAHA,YAAG;;;8DAba;4DACF;gEACO;mEACH;qEACK;;;;;;AAEzBJ,cAAK,CAACK,MAAM,CAACD,YAAG;AAChBJ,cAAK,CAACK,MAAM,CAACJ,mBAAO;AACpBD,cAAK,CAACK,MAAM,CAACH,gBAAU;AACvBF,cAAK,CAACK,MAAM,CAACF,qBAAY"}