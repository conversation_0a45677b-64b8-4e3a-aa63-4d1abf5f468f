{"version": 3, "sources": ["../src/database.ts"], "sourcesContent": ["import { Kysely, Generated, ColumnType, MysqlDialect, sql } from 'kysely';\r\nimport { createPool } from 'mysql2'\r\nimport * as dotenv from 'dotenv';\r\n\r\n// Extend Kysely interface to include execute method\r\ndeclare module 'kysely' {\r\n  interface Kysely<DB> {\r\n    execute(sql: string, params?: any[]): Promise<any>;\r\n  }\r\n}\r\n\r\ndotenv.config();\r\n\r\ninterface SelectOptions {\r\n  id: Generated<number>;\r\n  area_key?: string;\r\n  value_option?: string | null;\r\n  sort_order?: number;\r\n  user_id?: string | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface User {\r\n  id: Generated<number>;\r\n  name: string | null;\r\n  email: string | null;\r\n  password: string | null;\r\n  username?: string | null;\r\n  phone?: string | null;\r\n  photo?: string | null;\r\n  date_of_birth?: Date | null;\r\n  height?: number | null;\r\n  weight?: number | null;\r\n  bodyfat?: number | null;\r\n  goal_id?: number | null;\r\n  activity_level_id?: number | null;\r\n  medical_conditions?: string | null;\r\n  allergies?: string | null;\r\n  aff_id?: number | null;\r\n  invite?: string | null;\r\n  stripeId?: string | null;\r\n  email_verification_token?: string | null;\r\n  email_verified_at?: Date | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface Role {\r\n  id: Generated<number>;\r\n  name: string;\r\n  description: string | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface UserRole {\r\n  user_id: number;\r\n  role_id: number;\r\n}\r\n\r\ninterface AuthProvider {\r\n    id: Generated<number>; // Auto-increment primary key\r\n    name: string; // Nome do provedor (ex.: Google, Facebook)\r\n    created_at?: Date;\r\n    updated_at?: Date;\r\n  }\r\n\r\n  // Interface para a tabela `user_auths`\r\n  interface UserAuth {\r\n    id: Generated<number>; // Auto-increment primary key\r\n    user_id: number; // Foreign key para `users(id)`\r\n    provider_id: number; // Foreign key para `auth_providers(id)`\r\n    provider_uid: string; // Identificador único do provedor\r\n    device_uid: string | null; // Identificador do dispositivo\r\n    refresh_token: string | null; // Token de atualização\r\n    expire_date: Date | null; // Data de expiração\r\n    created_at?: Date; // Data de criação\r\n    updated_at?: Date; // Data de atualização\r\n  }\r\n\r\n  interface Food {\r\n    id: Generated<number>;\r\n    name: string;\r\n    category_id: number;\r\n    quantity: number;\r\n    unit: string;\r\n    calories: number;\r\n    protein: number;\r\n    carbs: number;\r\n    fat: number;\r\n    fiber: number;\r\n    user_id?: string | null;\r\n    created_at?: Date;\r\n    updated_at?: Date;\r\n    deleted_at?: Date | null;\r\n  }\r\n\r\n\r\n\r\ninterface Exercises {\r\n  id: Generated<number>;\r\n  name: string;\r\n  muscle_group_id: number;\r\n  equipment_id: number;\r\n  media_url: string | null;\r\n  user_id?: string | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface ExercisesItems {\r\n  id: Generated<number>;\r\n  exercise_id: number;\r\n  key_item: string;\r\n  value_item: string;\r\n}\r\n\r\ninterface Clients {\r\n  id: Generated<number>;\r\n  role_id: number;\r\n  client_id: number;\r\n  user_id: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface CoachProtocolsTemplates {\r\n  id: Generated<number>;\r\n  status?: number;\r\n  name: string;\r\n  type_id: number;\r\n  split: string;\r\n  frequency: number;\r\n  objective: string;\r\n  notes?: string;\r\n  user_id: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface CoachProtocolsTemplatesWorkouts {\r\n  id: Generated<number>;\r\n  protocol_id: number;\r\n  exercise_id: number;\r\n  split_group: number;\r\n  sets: number;\r\n  reps: number;\r\n  rpe: number;\r\n  rest_seconds: number;\r\n  notes?: string;\r\n}\r\n\r\ninterface CoachProtocols {\r\n  id: Generated<number>;\r\n  status?: number;\r\n  client_id: number;\r\n  user_id?: number;\r\n  name: string;\r\n  type_id: number;\r\n  split: string;\r\n  frequency: number;\r\n  objective: string;\r\n  general_notes?: string;\r\n  started_at: Date;\r\n  ended_at?: Date | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface CoachProtocolsWorkouts {\r\n  id: Generated<number>;\r\n  protocol_id: number;\r\n  name: string;\r\n  general_notes?: string;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface CoachProtocolsWorkoutsExercises {\r\n  id: Generated<number>;\r\n  workout_id: number;\r\n  exercise_id?: number | null;\r\n  name?: string | null;\r\n  sets: number;\r\n  reps: number;\r\n  rpe?: number | null;\r\n  rest_seconds?: number | null;\r\n  notes?: string | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\n\r\ninterface NutritionistProtocolsTemplates {\r\n  id: Generated<number>;\r\n  status?: number;\r\n  name: string;\r\n  type_id: number;\r\n  objective: string;\r\n  goal_calories: number;\r\n  goal_protein: number;\r\n  goal_carbs: number;\r\n  goal_fat: number;\r\n  goal_water: number;\r\n  general_notes?: string;\r\n  user_id: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface NutritionistProtocolsTemplatesMeals {\r\n  id: Generated<number>;\r\n  protocol_id: number;\r\n  name: string;\r\n  meal_time: string;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface NutritionistProtocolsTemplatesMealsFoods {\r\n  id: Generated<number>;\r\n  meal_id: number;\r\n  food_id?: number;\r\n  name: string;\r\n  quantity: number;\r\n  unit: string;\r\n  calories: number;\r\n  protein: number;\r\n  carbs: number;\r\n  fat: number;\r\n  fiber: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface NutritionistProtocolsTemplatessupplements {\r\n  id: Generated<number>;\r\n  protocol_id: number;\r\n  name: string;\r\n  dosage: string;\r\n  supplement_time: string;\r\n  notes?: string;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface NutritionistProtocols {\r\n  id: Generated<number>;\r\n  status?: number;\r\n  client_id: number;\r\n  user_id?: number; // Nullable, pois pode ser `ON DELETE SET NULL`\r\n  name: string;\r\n  type_id: number;\r\n  objective?: string;\r\n  initial_weight: number;\r\n  goal_calories: number;\r\n  goal_protein: number;\r\n  goal_carbs: number;\r\n  goal_fat: number;\r\n  goal_water: number;\r\n  general_notes?: string | null;\r\n  ref_id?: number | null;\r\n  started_at: Date;\r\n  ended_at?: Date | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface NutritionistProtocolsMeals {\r\n  id: Generated<number>;\r\n  protocol_id: number;\r\n  name: string;\r\n  day_of_week: string;// 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';\r\n  meal_time: string;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface NutritionistProtocolsMealsFoods {\r\n  id: Generated<number>;\r\n  meal_id: number;\r\n  food_id?: number | null;\r\n  name: string;\r\n  quantity: number;\r\n  unit: string;\r\n  calories: number;\r\n  protein: number;\r\n  carbs: number;\r\n  fat: number;\r\n  fiber: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface NutritionistProtocolssupplements {\r\n  id: Generated<number>;\r\n  protocol_id: number;\r\n  name: string;\r\n  dosage: string;\r\n  supplement_time: string;\r\n  notes?: string;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface DailyWater {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  consumed: number;\r\n  daily_at?: Date;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface DailyWorkoutsActivities {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  activity_id: number;\r\n  activity_time: Date | string | null;\r\n  calories: number;\r\n  daily_at?: Date;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface WorkoutsActivities {\r\n  id: Generated<number>;\r\n  name: string;\r\n  calories: number;\r\n  sort_order: number;\r\n}\r\n\r\n/*\r\ninterface DailyNutritionistProtocol {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  protocol_id: number;\r\n  meal_id: number;\r\n  meal_time: Date;\r\n  calories: number;\r\n  carbs: number;\r\n  protein: number;\r\n  fat: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n*/\r\n\r\ninterface DailyCoachProtocol {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  protocol_id: number;\r\n  protocol_workout_id: number;\r\n  met: number;\r\n  workout_time: Date;\r\n  total_calories: number;\r\n  total_weight?: number;\r\n  daily_at?: Date;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface DailyCoachProtocolSeries {\r\n  id: Generated<number>;\r\n  daily_id: number;\r\n  protocol_exercise_id: number;\r\n  calories: number;\r\n  weight?: number;\r\n  reps?: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface DailyMeals {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  protocol_id?: number | null;\r\n  meal_id?: number | null;\r\n  name: string;\r\n  calories: number;\r\n  protein: number;\r\n  carbs: number;\r\n  fat: number;\r\n  fiber: number;\r\n  daily_at?: Date;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface DailyMealsFoods {\r\n  id: Generated<number>;\r\n  meal_id: number;\r\n  food_id?: number;\r\n  name: string;\r\n  quantity: number;\r\n  unit: string;\r\n  calories: number;\r\n  protein: number;\r\n  carbs: number;\r\n  fat: number;\r\n  fiber: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface dailyMealsGoal {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  goal_date: string;\r\n  meals: number;\r\n  meals_completed: number;\r\n  goal_met: boolean;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n};\r\n\r\ninterface evaluations {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  weight: number;\r\n  bf: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface evaluationsPhotos {\r\n  id: Generated<number>;\r\n  evaluation_id: number;\r\n  media_type: 'image' | 'video';\r\n  media_position: 'front' | 'back' | 'side';\r\n  media_url: string;\r\n}\r\n\r\ninterface evaluationsMeasurements {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  shoulders: number;\r\n  chest: number;\r\n  waist: number;\r\n  abdomen: number;\r\n  hips: number;\r\n  biceps_right: number;\r\n  biceps_left: number;\r\n  forearm_right: number;\r\n  forearm_left: number;\r\n  thigh_right: number;\r\n  thigh_left: number;\r\n  calf_right: number;\r\n  calf_left: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\n// Plans\r\ninterface PaymentProvidersTable {\r\n  id: Generated<number>;\r\n  name: string;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface PlansTable {\r\n  id: Generated<number>;\r\n  name: string;\r\n  description?: string | null;\r\n  price: number;\r\n  currency: string;\r\n  frequency: 'daily' | 'weekly' | 'monthly' | 'annually';\r\n  interval_value: number;\r\n  is_active?: number | null;\r\n  role_id: number;\r\n  user_id: number;\r\n  snaptokens?: number | null;\r\n  allows_trial?: boolean | null;\r\n  trial_period_days?: number | null;\r\n  affiliate_master_commission_percent?: number | null;\r\n  affiliate_commission_percent?: number | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface PlansPaymentsProvidersTable {\r\n  id: Generated<number>;\r\n  plan_id: number; // ID do plano\r\n  platform: 'web' | 'android' | 'ios'; // Plataforma, com valor padrão 'web'\r\n  payment_provider_id: number; // ID do provedor de pagamento\r\n\r\n  price?: number | null; // Preço customizado\r\n  currency?: string | null; // Moeda\r\n\r\n  snaptokens?: number | null; // Snaptokens customizados\r\n\r\n  payment_provider_external_id?: string | null; // Identificadores externos\r\n\r\n  created_at: Date;\r\n  updated_at: Date;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\n// Afiliados\r\ninterface Affiliates {\r\n  id: Generated<number>;\r\n  status: string;\r\n  user_id: number;\r\n  ref_user_id: number;\r\n  invite: string;\r\n  is_master?: number | null;\r\n  stripeId?: string | null;\r\n  accepted_at?: Date | null;\r\n  created_at?: Date | null;\r\n  updated_at?: Date | null;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\ninterface AffiliateCommissions {\r\n  id: Generated<number>;\r\n  aff_level: number;\r\n  aff_user_id: number;\r\n  user_id: number;\r\n  plan_id: number;\r\n  transaction_id: number;\r\n  commission_percent: number;\r\n  commission_value: number;\r\n  status: string;\r\n  metadata: any;\r\n  created_at: Date;\r\n  updated_at: Date;\r\n  deleted_at: Date | null;\r\n}\r\n\r\ninterface AffiliateLinks {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  name: string;\r\n  invite: string;\r\n  link_type: 'signup_user' | 'signup_affiliate';\r\n  created_at: Date;\r\n  updated_at: Date;\r\n  deleted_at: Date | null;\r\n}\r\n\r\ninterface AffiliateLinksVisits {\r\n  id: Generated<number>;\r\n  link_id: number;\r\n  created_at: Date;\r\n}\r\n\r\n// Assinaturas dos usuários\r\ninterface UsersSubscriptions {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  plan_id: number;\r\n  plan_payment_provider_id: number;\r\n\r\n  payment_provider_external_id?: string | null;\r\n\r\n  platform?: 'web' | 'android' | 'ios';\r\n\r\n  status?: 'pending' | 'active' | 'canceled' | 'paused' | 'expired';\r\n\r\n  price?: number | null;\r\n  currency?: string | null;\r\n\r\n  start_date: Date;\r\n  end_date?: Date | null;\r\n  next_billing_date?: Date | null;\r\n  cancel_at_period_end?: boolean | null;\r\n\r\n  // Trial\r\n  is_trial?: boolean | null;\r\n  trial_start_date?: Date | null;\r\n  trial_end_date?: Date | null;\r\n\r\n  snaptokens?: number | null;\r\n\r\n  created_at?: Date | null;\r\n  updated_at?: Date | null;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\n// Transações registradas\r\ninterface Transactions {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  provider_transaction_id?: string | null;\r\n  payment_provider_id: number;\r\n\r\n  amount: number;\r\n  currency?: string | null;\r\n\r\n  status?: 'pending' | 'paid' | 'failed' | 'refunded';\r\n\r\n  source_type: 'subscription' | 'invoice_item';\r\n  source_id: number;\r\n\r\n  created_at?: Date | null;\r\n  updated_at?: Date | null;\r\n  deleted_at?: Date | null;\r\n}\r\n\r\n// Tabelas de autenticação adicionais\r\ninterface PasswordResetTokens {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  token: string;\r\n  expires_at: Date;\r\n  used_at?: Date | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface RefreshTokens {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  device_uid: string;\r\n  token: string;\r\n  expires_at: Date;\r\n  last_used_at?: Date | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\n// Tabelas de gamificação\r\ninterface Challenges {\r\n  id: Generated<number>;\r\n  title: string;\r\n  description: string;\r\n  type: 'daily' | 'weekly' | 'monthly' | 'custom';\r\n  target_value: number;\r\n  target_unit: string;\r\n  points_reward: number;\r\n  start_date: Date;\r\n  end_date: Date;\r\n  is_active: boolean;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface ChallengeParticipations {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  challenge_id: number;\r\n  current_progress: number;\r\n  status: 'active' | 'completed' | 'failed';\r\n  completed_at?: Date | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface UserPoints {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  points: number;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface PointsTransactions {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  points: number;\r\n  type: 'earned' | 'spent';\r\n  description: string;\r\n  reference_type?: string | null;\r\n  reference_id?: number | null;\r\n  created_at?: Date;\r\n}\r\n\r\ninterface Rewards {\r\n  id: Generated<number>;\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  points_cost: number;\r\n  stock_quantity?: number | null;\r\n  max_redemptions_per_user?: number | null;\r\n  is_active: boolean;\r\n  image_url?: string | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface UserRewards {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  reward_id: number;\r\n  redeemed_at: Date;\r\n  status: 'pending' | 'delivered' | 'cancelled';\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface TokenUsage {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  tokens_used: number;\r\n  feature: string;\r\n  created_at?: Date;\r\n}\r\n\r\n// Tabelas sociais\r\ninterface Friendships {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  friend_id: number;\r\n  status: 'pending' | 'accepted' | 'blocked';\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface FriendRequests {\r\n  id: Generated<number>;\r\n  sender_id: number;\r\n  receiver_id: number;\r\n  status: 'pending' | 'accepted' | 'rejected';\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\n// Notificações\r\ninterface Notifications {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  title: string;\r\n  message: string;\r\n  type: string;\r\n  is_read: boolean;\r\n  data?: any;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface NotificationSettings {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  push_enabled: boolean;\r\n  email_enabled: boolean;\r\n  sms_enabled: boolean;\r\n  challenge_notifications: boolean;\r\n  friend_notifications: boolean;\r\n  reward_notifications: boolean;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\n// Wearables\r\ninterface WearableDevices {\r\n  id: Generated<number>;\r\n  user_id: number;\r\n  device_type: string;\r\n  device_name: string;\r\n  device_id: string;\r\n  is_active: boolean;\r\n  last_sync_at?: Date | null;\r\n  created_at?: Date;\r\n  updated_at?: Date;\r\n}\r\n\r\ninterface WearableData {\r\n  id: Generated<number>;\r\n  device_id: number;\r\n  user_id: number;\r\n  data_type: string;\r\n  value: number;\r\n  unit: string;\r\n  recorded_at: Date;\r\n  synced_at?: Date;\r\n  created_at?: Date;\r\n}\r\n\r\ninterface Database {\r\n  select_options: SelectOptions;\r\n  users: User;\r\n  roles: Role;\r\n  users_roles: UserRole;\r\n  auth_providers: AuthProvider;\r\n  user_auths: UserAuth;\r\n  foods: Food;\r\n  exercises: Exercises;\r\n  exercises_items: ExercisesItems;\r\n  clients: Clients;\r\n  coach_protocols: CoachProtocols;\r\n  coach_protocols_workouts: CoachProtocolsWorkouts;\r\n  coach_protocols_workouts_exercises: CoachProtocolsWorkoutsExercises;\r\n  coach_protocols_templates: CoachProtocolsTemplates;\r\n  coach_protocols_templates_workouts: CoachProtocolsTemplatesWorkouts;\r\n  nutritionist_protocols: NutritionistProtocols;\r\n  nutritionist_protocols_meals: NutritionistProtocolsMeals;\r\n  nutritionist_protocols_meals_foods: NutritionistProtocolsMealsFoods;\r\n  nutritionist_protocols_supplements: NutritionistProtocolssupplements;\r\n  nutritionist_protocols_templates: NutritionistProtocolsTemplates;\r\n  nutritionist_protocols_templates_meals: NutritionistProtocolsTemplatesMeals;\r\n  nutritionist_protocols_templates_meals_foods: NutritionistProtocolsTemplatesMealsFoods;\r\n  nutritionist_protocols_templates_supplements: NutritionistProtocolsTemplatessupplements;\r\n  workouts_activities: WorkoutsActivities;\r\n  daily_workouts_activities: DailyWorkoutsActivities;\r\n  daily_coach_protocol: DailyCoachProtocol;\r\n  daily_coach_protocol_series: DailyCoachProtocolSeries;\r\n  // daily_nutritionist_protocol: DailyNutritionistProtocol;\r\n  daily_meals: DailyMeals;\r\n  daily_meals_foods: DailyMealsFoods;\r\n  daily_meals_goal: dailyMealsGoal;\r\n  daily_water: DailyWater;\r\n  evaluations: evaluations;\r\n  evaluations_photos: evaluationsPhotos;\r\n  evaluations_measurements: evaluationsMeasurements;\r\n  // Plans\r\n  payment_providers: PaymentProvidersTable;\r\n  plans: PlansTable;\r\n  plans_payments_providers: PlansPaymentsProvidersTable;\r\n  users_subscriptions: UsersSubscriptions;\r\n  transactions: Transactions;\r\n  // Afiliados\r\n  affiliates: Affiliates;\r\n  affiliate_commissions: AffiliateCommissions;\r\n  affiliate_links: AffiliateLinks;\r\n  affiliate_links_visits: AffiliateLinksVisits;\r\n  // Autenticação adicional\r\n  password_reset_tokens: PasswordResetTokens;\r\n  refresh_tokens: RefreshTokens;\r\n  // Gamificação\r\n  challenges: Challenges;\r\n  challenge_participations: ChallengeParticipations;\r\n  user_points: UserPoints;\r\n  points_transactions: PointsTransactions;\r\n  rewards: Rewards;\r\n  user_rewards: UserRewards;\r\n  token_usage: TokenUsage;\r\n  // Social\r\n  friendships: Friendships;\r\n  friend_requests: FriendRequests;\r\n  // Notificações\r\n  notifications: Notifications;\r\n  notification_settings: NotificationSettings;\r\n  // Wearables\r\n  wearable_devices: WearableDevices;\r\n  wearable_data: WearableData;\r\n}\r\n\r\nexport const db = new Kysely<Database>({\r\n  dialect: new MysqlDialect({\r\n    pool: createPool({\r\n      host: process.env.DB_HOST,\r\n      user: process.env.DB_USERNAME,\r\n      password: process.env.DB_PASSWORD,\r\n      database: process.env.DB_DATABASE,\r\n      timezone: '+00:00',\r\n    }),\r\n  }),\r\n});\r\n\r\n// Helper function to execute raw SQL with parameters\r\nexport async function executeRawSql(sqlQuery: string, params: any[] = []): Promise<any> {\r\n  // For Kysely, we need to use sql template literals\r\n  // This is a simplified approach - in production you'd want more sophisticated parameter binding\r\n  let processedSql = sqlQuery;\r\n\r\n  // Replace ? placeholders with actual values (be careful with SQL injection)\r\n  params.forEach((param) => {\r\n    if (typeof param === 'string') {\r\n      processedSql = processedSql.replace('?', `'${param.replace(/'/g, \"''\")}'`);\r\n    } else if (param === null || param === undefined) {\r\n      processedSql = processedSql.replace('?', 'NULL');\r\n    } else if (typeof param === 'number') {\r\n      // Numbers should not be quoted\r\n      processedSql = processedSql.replace('?', String(param));\r\n    } else {\r\n      // For other types, convert to string without quotes\r\n      processedSql = processedSql.replace('?', String(param));\r\n    }\r\n  });\r\n\r\n  // Execute the raw SQL\r\n  const result = await sql.raw(processedSql).execute(db);\r\n  return result.rows;\r\n}\r\n\r\n// Add a temporary compatibility layer for db.execute\r\n(db as any).execute = executeRawSql;\r\n\r\n// Export Database type for use in other files\r\nexport type { Database };"], "names": ["db", "executeRawSql", "dotenv", "config", "<PERSON><PERSON><PERSON>", "dialect", "MysqlDialect", "pool", "createPool", "host", "process", "env", "DB_HOST", "user", "DB_USERNAME", "password", "DB_PASSWORD", "database", "DB_DATABASE", "timezone", "sqlQuery", "params", "processedSql", "for<PERSON>ach", "param", "replace", "undefined", "String", "result", "sql", "raw", "execute", "rows"], "mappings": ";;;;;;;;;;;IAg2BaA,EAAE;eAAFA;;IAaSC,aAAa;eAAbA;;;wBA72B2C;wBACtC;gEACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxBC,QAAOC,MAAM;AAq1BN,MAAMH,KAAK,IAAII,cAAM,CAAW;IACrCC,SAAS,IAAIC,oBAAY,CAAC;QACxBC,MAAMC,IAAAA,kBAAU,EAAC;YACfC,MAAMC,QAAQC,GAAG,CAACC,OAAO;YACzBC,MAAMH,QAAQC,GAAG,CAACG,WAAW;YAC7BC,UAAUL,QAAQC,GAAG,CAACK,WAAW;YACjCC,UAAUP,QAAQC,GAAG,CAACO,WAAW;YACjCC,UAAU;QACZ;IACF;AACF;AAGO,eAAelB,cAAcmB,QAAgB,EAAEC,SAAgB,EAAE;IACtE,mDAAmD;IACnD,gGAAgG;IAChG,IAAIC,eAAeF;IAEnB,4EAA4E;IAC5EC,OAAOE,OAAO,CAAC,CAACC;QACd,IAAI,OAAOA,UAAU,UAAU;YAC7BF,eAAeA,aAAaG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAED,MAAMC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;QAC3E,OAAO,IAAID,UAAU,QAAQA,UAAUE,WAAW;YAChDJ,eAAeA,aAAaG,OAAO,CAAC,KAAK;QAC3C,OAAO,IAAI,OAAOD,UAAU,UAAU;YACpC,+BAA+B;YAC/BF,eAAeA,aAAaG,OAAO,CAAC,KAAKE,OAAOH;QAClD,OAAO;YACL,oDAAoD;YACpDF,eAAeA,aAAaG,OAAO,CAAC,KAAKE,OAAOH;QAClD;IACF;IAEA,sBAAsB;IACtB,MAAMI,SAAS,MAAMC,WAAG,CAACC,GAAG,CAACR,cAAcS,OAAO,CAAC/B;IACnD,OAAO4B,OAAOI,IAAI;AACpB;AAEA,qDAAqD;AACpDhC,GAAW+B,OAAO,GAAG9B"}