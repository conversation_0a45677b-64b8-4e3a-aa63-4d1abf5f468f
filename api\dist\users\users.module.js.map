{"version": 3, "sources": ["../../src/users/users.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { UsersService } from './users.service';\r\n\r\n@Module({\r\n    'providers': [UsersService]\r\n})\r\nexport class UsersModule {}\r\n"], "names": ["UsersModule", "UsersService"], "mappings": ";;;;+BAMaA;;;eAAAA;;;wBANU;8BACM;;;;;;;AAKtB,IAAA,AAAMA,cAAN,MAAMA;AAAa;;;QAFtB,aAAa;YAACC,0BAAY;SAAC"}