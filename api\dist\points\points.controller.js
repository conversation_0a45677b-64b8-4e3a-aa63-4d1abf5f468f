"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "PointsController", {
    enumerable: true,
    get: function() {
        return PointsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _pointsservice = require("./points.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let PointsController = class PointsController {
    async getPointsBalance(req) {
        const userId = req.user.userId;
        return this.pointsService.getPointsBalance(userId);
    }
    async getPointsHistory(req, query) {
        const userId = req.user.userId;
        return this.pointsService.getPointsHistory(userId, query);
    }
    async earnPoints(body, req) {
        const userId = req.user.userId;
        return this.pointsService.earnPoints(userId, body.points, body.reason);
    }
    async spendPoints(body, req) {
        const userId = req.user.userId;
        return this.pointsService.spendPoints(userId, body.points, body.reason);
    }
    async getLeaderboard(query) {
        return this.pointsService.getLeaderboard(query);
    }
    constructor(pointsService){
        this.pointsService = pointsService;
    }
};
_ts_decorate([
    (0, _common.Get)('balance'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], PointsController.prototype, "getPointsBalance", null);
_ts_decorate([
    (0, _common.Get)('history'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], PointsController.prototype, "getPointsHistory", null);
_ts_decorate([
    (0, _common.Post)('earn'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], PointsController.prototype, "earnPoints", null);
_ts_decorate([
    (0, _common.Post)('spend'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], PointsController.prototype, "spendPoints", null);
_ts_decorate([
    (0, _common.Get)('leaderboard'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], PointsController.prototype, "getLeaderboard", null);
PointsController = _ts_decorate([
    (0, _common.Controller)('points'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _pointsservice.PointsService === "undefined" ? Object : _pointsservice.PointsService
    ])
], PointsController);

//# sourceMappingURL=points.controller.js.map