const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testEndpoints() {
  const baseUrl = 'http://localhost:3000';
  
  // Test endpoints without authentication first
  const endpoints = [
    '/friends',
    '/friends/requests',
    '/profile/friends',
    '/notifications'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing ${endpoint}...`);
      const response = await fetch(`${baseUrl}${endpoint}`);
      console.log(`Status: ${response.status}`);
      
      if (response.status === 200) {
        const data = await response.json();
        console.log('Response:', JSON.stringify(data, null, 2));
      } else {
        const text = await response.text();
        console.log('Error response:', text);
      }
      console.log('---');
    } catch (error) {
      console.error(`Error testing ${endpoint}:`, error.message);
      console.log('---');
    }
  }
}

testEndpoints();
