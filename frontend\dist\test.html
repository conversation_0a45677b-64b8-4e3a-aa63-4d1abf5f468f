<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Test</title>
  <style>
    body {
      background-color: black;
      color: white;
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    
    .container {
      max-width: 500px;
      width: 90%;
      background-color: #1E1E1E;
      padding: 20px;
      border-radius: 10px;
      text-align: center;
    }
    
    h1 {
      font-size: 2rem;
      margin-bottom: 20px;
    }
    
    button {
      background-color: #B9FF43;
      color: black;
      border: none;
      padding: 10px 20px;
      border-radius: 20px;
      font-weight: bold;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Static HTML Test</h1>
    <p>This is a simple static HTML page to test if the server is working correctly.</p>
    <button onclick="alert('Button clicked!')">Test Button</button>
  </div>
</body>
</html>
