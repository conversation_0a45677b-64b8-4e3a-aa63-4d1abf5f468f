# OAuth Callback URLs - Controle Total via .env

## ✅ Implementação Completa

Agora você tem controle total sobre todas as URLs OAuth através do arquivo `.env`!

### 🔧 Variáveis de Ambiente Adicionadas

```env
# OAuth Callback URLs - Where Google/Apple redirect to API
GOOGLE_CALLBACK_URL=https://api.mysnapfit.com.br/auth/google/callback
APPLE_CALLBACK_URL=https://api.mysnapfit.com.br/auth/apple/callback

# OAuth Redirect URLs - Where API redirects to Frontend
GOOGLE_REDIRECT_URL=https://app.mysnapfit.com.br/auth/callback
APPLE_REDIRECT_URL=https://app.mysnapfit.com.br/auth/callback
```

### 📍 Onde são Usadas no Código

#### 1. **Google Strategy** (`src/auth/google.strategy.ts`):
```typescript
super({
  clientID: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  callbackURL: process.env.GOOGLE_CALLBACK_URL, // ← Nova variável
  scope: ['email', 'profile'],
});
```

#### 2. **Apple Strategy** (`src/auth/apple.strategy.ts`):
```typescript
super({
  clientID: process.env.APPLE_CLIENT_ID,
  teamID: process.env.APPLE_TEAM_ID,
  keyID: process.env.APPLE_KEY_ID,
  key: process.env.APPLE_PRIVATE_KEY,
  callbackURL: process.env.APPLE_CALLBACK_URL, // ← Nova variável
  scope: ['email', 'name'],
});
```

#### 3. **AuthController** (já estava usando as redirect URLs):
```typescript
// Google callback
const redirectUrl = `${process.env.GOOGLE_REDIRECT_URL}?access_token=...`;

// Apple callback  
const redirectUrl = `${process.env.APPLE_REDIRECT_URL}?access_token=...`;
```

### 🔄 Fluxo OAuth Completo

#### Google OAuth:
1. **Frontend**: `https://app.mysnapfit.com.br/auth/oauth/google`
2. **API Initiation**: `https://api.mysnapfit.com.br/auth/google`
3. **Google OAuth**: Usuário autoriza
4. **Google Callback**: `https://api.mysnapfit.com.br/auth/google/callback` ← `GOOGLE_CALLBACK_URL`
5. **Frontend Redirect**: `https://app.mysnapfit.com.br/auth/callback` ← `GOOGLE_REDIRECT_URL`

#### Apple OAuth:
1. **Frontend**: `https://app.mysnapfit.com.br/auth/oauth/apple`
2. **API Initiation**: `https://api.mysnapfit.com.br/auth/apple`
3. **Apple OAuth**: Usuário autoriza
4. **Apple Callback**: `https://api.mysnapfit.com.br/auth/apple/callback` ← `APPLE_CALLBACK_URL`
5. **Frontend Redirect**: `https://app.mysnapfit.com.br/auth/callback` ← `APPLE_REDIRECT_URL`

### 🎯 Vantagens da Nova Configuração

1. **Controle Total**: Todas as URLs são configuráveis via .env
2. **Flexibilidade**: Pode usar URLs diferentes para dev/prod facilmente
3. **Clareza**: Separação clara entre callback (API) e redirect (Frontend)
4. **Debugging**: Mais fácil identificar problemas de configuração
5. **Manutenção**: Mudanças de URL sem tocar no código

### 📋 Configurações nos Consoles OAuth

#### Google Cloud Console:
- **Authorized redirect URIs**: `https://api.mysnapfit.com.br/auth/google/callback`

#### Apple Developer Console:
- **Return URLs**: `https://api.mysnapfit.com.br/auth/apple/callback`

### 🔍 Para Verificar Configuração:

```bash
node check-oauth-urls.js
```

### 🚀 Para Desenvolvimento Local:

Descomente as linhas de desenvolvimento no .env:
```env
# For development
GOOGLE_CALLBACK_URL=http://localhost:3000/auth/google/callback
APPLE_CALLBACK_URL=http://localhost:3000/auth/apple/callback
GOOGLE_REDIRECT_URL=http://localhost:5173/auth/callback
APPLE_REDIRECT_URL=http://localhost:5173/auth/callback
```

Agora você tem controle total sobre todas as URLs OAuth! 🎉
