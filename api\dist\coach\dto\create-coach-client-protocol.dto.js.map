{"version": 3, "sources": ["../../../src/coach/dto/create-coach-client-protocol.dto.ts"], "sourcesContent": ["import { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional, IsNumber, IsObject } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class CreateCoachClientProtocolDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  type_id: number;\r\n\r\n  @IsString()\r\n  split: string;\r\n\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  frequency: number;\r\n\r\n  @IsString()\r\n  objective: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  notes?: string;\r\n\r\n  @ArrayNotEmpty()\r\n  @IsArray()\r\n  workouts: {\r\n    exercises: {\r\n    exercise_id: number,\r\n    split_group: number,\r\n    sets: number,\r\n    reps: number,\r\n    rpe: number,\r\n    rest_seconds: number,\r\n    notes?: string\r\n    }[],\r\n  }[];\r\n}"], "names": ["CreateCoachClientProtocolDto", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAHuF;kCAC/E;;;;;;;;;;AAEd,IAAA,AAAMA,+BAAN,MAAMA;AAoCb;;;;;;;;oCA9BcC;;;;;;;;;oCAOAA"}