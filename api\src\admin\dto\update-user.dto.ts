// src/admin/dto/update-user.dto.ts
import { IsString, IsEmail, IsNotEmpty, IsInt, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateUserDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsOptional()
  password?: string; // Senha é opcional

  @IsInt()
  @Type(() => Number) // Converte role_id para número
  role_id: number;

  @IsString()
  @IsOptional()
  photo?: string;
}