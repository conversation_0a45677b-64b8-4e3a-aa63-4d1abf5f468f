import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Body, 
  Param, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FriendsService } from './friends.service';

@Controller('friends')
@UseGuards(JwtAuthGuard)
export class FriendsController {
  constructor(private readonly friendsService: FriendsService) {}

  @Get('search')
  async searchUsers(@Query('q') query: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.friendsService.searchUsers(query, userId);
  }

  @Get()
  async getFriends(@Request() req: any) {
    const userId = req.user.userId;
    return this.friendsService.getFriends(userId);
  }

  @Get('requests')
  async getFriendRequests(@Request() req: any) {
    const userId = req.user.userId;
    return this.friendsService.getFriendRequests(userId);
  }

  @Post('request')
  async sendFriendRequest(@Body() body: { userId: string }, @Request() req: any) {
    const fromUserId = req.user.userId;
    return this.friendsService.sendFriendRequest(fromUserId, Number(body.userId));
  }

  @Post('request/:id/accept')
  async acceptFriendRequest(@Param('id') requestId: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.friendsService.acceptFriendRequest(Number(requestId), userId);
  }

  @Post('request/:id/reject')
  async rejectFriendRequest(@Param('id') requestId: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.friendsService.rejectFriendRequest(Number(requestId), userId);
  }

  @Delete(':id')
  async removeFriend(@Param('id') friendId: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.friendsService.removeFriend(userId, Number(friendId));
  }

  @Get('ranking')
  async getFriendsRanking(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.friendsService.getFriendsRanking(userId, query);
  }
}
