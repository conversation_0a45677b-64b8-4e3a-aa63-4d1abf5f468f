import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';

class FoodItemDto {
    @IsNumber()
    @IsOptional()
    @Type(() => Number)
    food_id?: number;

    @IsString()
    @IsNotEmpty()
    name: string;
  
    @IsNumber()
    @Type(() => Number)
    quantity: number;

    @IsString()
    unit: string;

    @IsNumber()
    @Type(() => Number)
    calories: number;

    @IsNumber()
    @Type(() => Number)
    protein: number;

    @IsNumber()
    @Type(() => Number)
    carbs: number;

    @IsNumber()
    @Type(() => Number)
    fat: number;

    @IsNumber()
    @Type(() => Number)
    fiber: number;
  }
  

class MealDto {
    @IsString()
    name: string;
  
    @IsString()
    day_of_week: string;
  
    @IsString()
    meal_time: string;
  
    @IsArray()
    @ValidateNested()
    @Type(() => FoodItemDto)  // Crucial: Transforms plain objects to FoodItemDto instances
    foods: FoodItemDto[];
  }

  class SupplementDto {
    @IsString()
    name: string;
  
    @IsString()
    dosage: string;
  
    @IsString()
    supplement_time: string;
  
    @IsOptional()
    @IsString()
    notes?: string;
  }

export class CreateProtocolDietDto {
    @IsString()
    @IsNotEmpty()
    name: string;

    @IsNumber()
    @Type(() => Number)
    type_id: number;

    @IsOptional()
    @IsString()
    objective?: string;

    @IsOptional()
    @IsString()
    notes?: string;

    @IsObject()
    nutritional_goals: {
        calories: number;
        protein: number;
        carbs: number;
        fat: number;
        water: number;
    }

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => MealDto)
    meals: MealDto[];

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => SupplementDto)
    supplements?: SupplementDto[];

    @IsOptional()
    @IsString()
    general_notes?: string;
}