{"version": 3, "sources": ["../../src/sync/sync.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { SyncController } from './sync.controller';\nimport { SyncService } from './sync.service';\n\n@Module({\n  controllers: [SyncController],\n  providers: [SyncService],\n  exports: [SyncService],\n})\nexport class SyncModule {}\n"], "names": ["SyncModule", "controllers", "SyncController", "providers", "SyncService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;gCACQ;6BACH;;;;;;;AAOrB,IAAA,AAAMA,aAAN,MAAMA;AAAY;;;QAJvBC,aAAa;YAACC,8BAAc;SAAC;QAC7BC,WAAW;YAACC,wBAAW;SAAC;QACxBC,SAAS;YAACD,wBAAW;SAAC"}