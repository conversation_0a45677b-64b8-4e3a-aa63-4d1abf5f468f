"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "FoodsController", {
    enumerable: true,
    get: function() {
        return FoodsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _foodsservice = require("./foods.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let FoodsController = class FoodsController {
    async searchFoods(query) {
        return this.foodsService.searchFoods(query);
    }
    async searchNutritionix(query) {
        return this.foodsService.searchNutritionix(query.q);
    }
    async searchEdamam(query) {
        return this.foodsService.searchEdamam(query.q);
    }
    async searchOpenFoodFacts(query) {
        return this.foodsService.searchOpenFoodFacts(query.q);
    }
    async createCustomFood(foodData, req) {
        const userId = req.user.userId;
        return this.foodsService.createCustomFood(foodData, userId);
    }
    async getCustomFoods(req) {
        const userId = req.user.userId;
        return this.foodsService.getCustomFoods(userId);
    }
    async importFoodDatabase(importData) {
        return this.foodsService.importFoodDatabase(importData);
    }
    async getFoodCategories() {
        return this.foodsService.getFoodCategories();
    }
    async getPopularFoods(query) {
        return this.foodsService.getPopularFoods(query);
    }
    async getRecentFoods(req, query) {
        const userId = req.user.userId;
        return this.foodsService.getRecentFoods(userId, query);
    }
    constructor(foodsService){
        this.foodsService = foodsService;
    }
};
_ts_decorate([
    (0, _common.Get)('search'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "searchFoods", null);
_ts_decorate([
    (0, _common.Get)('search/nutritionix'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "searchNutritionix", null);
_ts_decorate([
    (0, _common.Get)('search/edamam'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "searchEdamam", null);
_ts_decorate([
    (0, _common.Get)('search/openfoodfacts'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "searchOpenFoodFacts", null);
_ts_decorate([
    (0, _common.Post)('custom'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "createCustomFood", null);
_ts_decorate([
    (0, _common.Get)('custom'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "getCustomFoods", null);
_ts_decorate([
    (0, _common.Post)('import'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "importFoodDatabase", null);
_ts_decorate([
    (0, _common.Get)('categories'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "getFoodCategories", null);
_ts_decorate([
    (0, _common.Get)('popular'),
    _ts_param(0, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "getPopularFoods", null);
_ts_decorate([
    (0, _common.Get)('recent'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FoodsController.prototype, "getRecentFoods", null);
FoodsController = _ts_decorate([
    (0, _common.Controller)('foods'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _foodsservice.FoodsService === "undefined" ? Object : _foodsservice.FoodsService
    ])
], FoodsController);

//# sourceMappingURL=foods.controller.js.map