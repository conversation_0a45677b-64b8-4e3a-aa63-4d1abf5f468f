# OAuth Setup Guide - Quick Fix

## Issue Fixed
The Apple OAuth strategy was failing with "AppleStrategy requires a key option" error. This has been resolved by:

1. **Fixed Apple Strategy Configuration**: Changed `privateKeyString` to `key` parameter
2. **Added Error Handling**: Both Google and Apple strategies now handle missing credentials gracefully
3. **Improved Environment Variables**: Added proper format for Apple private key

## Environment Variables Setup

### Current .env Configuration
Your `.env` file should have these OAuth variables:

```env
# OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
APPLE_CLIENT_ID=your_apple_client_id_here
APPLE_TEAM_ID=your_apple_team_id_here
APPLE_KEY_ID=your_apple_key_id_here
APPLE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----
your_apple_private_key_content_here
-----E<PERSON> PRIVATE KEY-----
```

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/auth/google/callback` (development)
   - `https://your-domain.com/auth/google/callback` (production)
6. Copy Client ID and Client Secret to `.env`

### Apple OAuth Setup
1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Create an App ID with Sign In with Apple capability
3. Create a Services ID for web authentication
4. Generate a private key for Sign In with Apple
5. Download the private key file (.p8)
6. Copy the private key content (including BEGIN/END lines) to `.env`
7. Add redirect URLs:
   - `http://localhost:3000/auth/apple/callback` (development)
   - `https://your-domain.com/auth/apple/callback` (production)

## Testing OAuth Configuration

### 1. Start the Server
```bash
npm run start:dev
```

### 2. Check for Errors
If OAuth credentials are missing, you'll see warnings but the server will still start:
```
Google OAuth not properly configured. Some environment variables are missing.
Apple OAuth not properly configured. Some environment variables are missing.
```

### 3. Test OAuth Endpoints
```bash
node test-oauth-endpoints.js
```

### 4. Test in Browser
1. Navigate to `http://localhost:5173/login`
2. Click "Continue with Google" or "Continue with Apple"
3. Should redirect to OAuth provider (if credentials are configured)
4. If not configured, will show appropriate error messages

## Error Handling

The OAuth strategies now handle missing credentials gracefully:

- **Missing Google Credentials**: Strategy initializes with dummy values, OAuth buttons will show errors
- **Missing Apple Credentials**: Strategy initializes with dummy values, OAuth buttons will show errors
- **Server Startup**: No longer crashes due to missing OAuth credentials

## Production Deployment

Before deploying to production:

1. **Configure Real OAuth Credentials**: Replace all placeholder values with actual OAuth app credentials
2. **Update Redirect URLs**: Ensure redirect URLs match your production domain
3. **Use HTTPS**: OAuth providers require HTTPS in production
4. **Test OAuth Flow**: Verify complete OAuth flow works with real credentials

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI"**: Check that redirect URLs in OAuth app settings match exactly
2. **"Invalid client ID"**: Verify client ID is correct and app is enabled
3. **"Key format error"**: Ensure Apple private key includes BEGIN/END lines and proper formatting
4. **CORS errors**: Configure CORS settings for OAuth callback URLs

### Debug Steps

1. Check server logs for OAuth configuration warnings
2. Verify all environment variables are set correctly
3. Test OAuth endpoints with test script
4. Check OAuth provider app settings
5. Verify redirect URLs match exactly

## Next Steps

1. **Configure Real Credentials**: Replace placeholder values with actual OAuth app credentials
2. **Test OAuth Flow**: Verify complete OAuth authentication works
3. **Test User Registration**: Confirm new users are created automatically
4. **Test Account Linking**: Verify existing users can link OAuth accounts
5. **Deploy to Production**: Update production environment variables
