{"version": 3, "sources": ["../../../src/users/dto/update-protocol-workout.dto.ts"], "sourcesContent": ["import { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional, IsNumber } from 'class-validator';\nimport { Type } from 'class-transformer';\n\nclass ExerciseUpdateDto {\n  @IsOptional()\n  exercise_id?: number | string;\n\n  @IsOptional()\n  @IsString()\n  name?: string;\n\n  @IsNumber()\n  @Type(() => Number)\n  sets: number;\n\n  @IsNumber()\n  @Type(() => Number)\n  reps: number;\n\n  @IsOptional()\n  @IsNumber()\n  @Type(() => Number)\n  rpe?: number;\n\n  @IsOptional()\n  @IsNumber()\n  @Type(() => Number)\n  rest_seconds?: number;\n\n  @IsOptional()\n  @IsString()\n  notes?: string;\n}\n\nclass WorkoutUpdateDto {\n  @IsString()\n  name: string;\n\n  @IsArray()\n  @ArrayNotEmpty()\n  exercises: ExerciseUpdateDto[];\n}\n\nexport class UpdateProtocolWorkoutDto {\n  @IsString()\n  @IsNotEmpty()\n  name: string;\n\n  @IsOptional()\n  @IsNumber()\n  @Type(() => Number)\n  type?: number;\n\n  @IsString()\n  split: string;\n\n  @IsNumber()\n  @Type(() => Number)\n  frequency: number;\n\n  @IsString()\n  objective: string;\n\n  @IsOptional()\n  @IsString()\n  notes?: string;\n\n  @IsOptional()\n  @IsArray()\n  workouts?: WorkoutUpdateDto[];\n}\n"], "names": ["UpdateProtocolWorkoutDto", "ExerciseUpdateDto", "Number", "WorkoutUpdateDto"], "mappings": ";;;;+BA2CaA;;;eAAAA;;;gCA3C6E;kCACrE;;;;;;;;;;AAErB,IAAA,AAAMC,oBAAN,MAAMA;AA6BN;;;;;;;;;;;;oCApBcC;;;;;oCAIAA;;;;;;oCAKAA;;;;;;oCAKAA;;;;;;;;AAQd,IAAA,AAAMC,mBAAN,MAAMA;AAON;;;;;;;;;;AAEO,IAAA,AAAMH,2BAAN,MAAMA;AA2Bb;;;;;;;;;oCApBcE;;;;;;;;;oCAOAA"}