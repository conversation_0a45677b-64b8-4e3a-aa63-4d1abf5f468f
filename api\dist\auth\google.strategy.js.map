{"version": 3, "sources": ["../../src/auth/google.strategy.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { PassportStrategy } from '@nestjs/passport';\nimport { Strategy, StrategyOptions } from 'passport-google-oauth20';\nimport { config } from 'dotenv';\n\nconfig();\n\n@Injectable()\nexport class GoogleStrategy extends PassportStrategy(Strategy, 'google') {\n  constructor() {\n    // Check if Google OAuth is properly configured\n    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {\n      console.warn('Google OAuth not properly configured. Some environment variables are missing.');\n      // Use dummy values to prevent strategy initialization errors\n      super({\n        clientID: 'dummy',\n        clientSecret: 'dummy',\n        callbackURL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback',\n        scope: ['email', 'profile'],\n      } as StrategyOptions);\n      return;\n    }\n\n    super({\n      clientID: process.env.GOOGLE_CLIENT_ID,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n      callbackURL: process.env.GOOGLE_CALLBACK_URL,\n      scope: ['email', 'profile'],\n    } as StrategyOptions);\n  }\n\n  async validate(\n    accessToken: string,\n    refreshToken: string,\n    profile: any,\n  ): Promise<any> {\n    try {\n      const { id, name, emails, photos } = profile;\n\n      // Extract user data from Google profile\n      const user = {\n        googleId: id,\n        email: emails[0]?.value,\n        firstName: name?.givenName,\n        lastName: name?.familyName,\n        name: name?.givenName + ' ' + name?.familyName,\n        picture: photos[0]?.value,\n        accessToken,\n        refreshToken,\n      };\n\n      // Validate that we have required data\n      if (!user.email) {\n        throw new Error('Email not provided by Google');\n      }\n\n      return user;\n    } catch (error) {\n      throw error;\n    }\n  }\n}\n"], "names": ["GoogleStrategy", "config", "PassportStrategy", "Strategy", "validate", "accessToken", "refreshToken", "profile", "id", "name", "emails", "photos", "user", "googleId", "email", "value", "firstName", "<PERSON><PERSON><PERSON>", "lastName", "<PERSON><PERSON>ame", "picture", "Error", "error", "constructor", "process", "env", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "console", "warn", "clientID", "clientSecret", "callbackURL", "GOOGLE_CALLBACK_URL", "scope"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARc;0BACM;uCACS;wBACnB;;;;;;;;;;AAEvBC,IAAAA,cAAM;AAGC,IAAA,AAAMD,iBAAN,MAAMA,uBAAuBE,IAAAA,0BAAgB,EAACC,+BAAQ,EAAE;IAuB7D,MAAMC,SACJC,WAAmB,EACnBC,YAAoB,EACpBC,OAAY,EACE;QACd,IAAI;YACF,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE,GAAGJ;YAErC,wCAAwC;YACxC,MAAMK,OAAO;gBACXC,UAAUL;gBACVM,OAAOJ,MAAM,CAAC,EAAE,EAAEK;gBAClBC,WAAWP,MAAMQ;gBACjBC,UAAUT,MAAMU;gBAChBV,MAAMA,MAAMQ,YAAY,MAAMR,MAAMU;gBACpCC,SAAST,MAAM,CAAC,EAAE,EAAEI;gBACpBV;gBACAC;YACF;YAEA,sCAAsC;YACtC,IAAI,CAACM,KAAKE,KAAK,EAAE;gBACf,MAAM,IAAIO,MAAM;YAClB;YAEA,OAAOT;QACT,EAAE,OAAOU,OAAO;YACd,MAAMA;QACR;IACF;IAnDAC,aAAc;QACZ,+CAA+C;QAC/C,IAAI,CAACC,QAAQC,GAAG,CAACC,gBAAgB,IAAI,CAACF,QAAQC,GAAG,CAACE,oBAAoB,EAAE;YACtEC,QAAQC,IAAI,CAAC;YACb,6DAA6D;YAC7D,KAAK,CAAC;gBACJC,UAAU;gBACVC,cAAc;gBACdC,aAAaR,QAAQC,GAAG,CAACQ,mBAAmB,IAAI;gBAChDC,OAAO;oBAAC;oBAAS;iBAAU;YAC7B;YACA;QACF;QAEA,KAAK,CAAC;YACJJ,UAAUN,QAAQC,GAAG,CAACC,gBAAgB;YACtCK,cAAcP,QAAQC,GAAG,CAACE,oBAAoB;YAC9CK,aAAaR,QAAQC,GAAG,CAACQ,mBAAmB;YAC5CC,OAAO;gBAAC;gBAAS;aAAU;QAC7B;IACF;AAgCF"}