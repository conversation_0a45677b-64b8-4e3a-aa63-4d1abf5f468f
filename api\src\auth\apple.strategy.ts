import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from '@nicokaiser/passport-apple';
import { config } from 'dotenv';

config();

@Injectable()
export class AppleStrategy extends PassportStrategy(Strategy, 'apple') {
  constructor() {
    // Check if Apple OAuth is properly configured
    if (!process.env.APPLE_CLIENT_ID || !process.env.APPLE_TEAM_ID || !process.env.APPLE_KEY_ID || !process.env.APPLE_PRIVATE_KEY) {
      console.warn('Apple OAuth not properly configured. Some environment variables are missing.');
      // Use dummy values to prevent strategy initialization errors
      super({
        clientID: 'dummy',
        teamID: 'dummy',
        keyID: 'dummy',
        key: '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\n-----END PRIVATE KEY-----',
        callbackURL: process.env.APPLE_CALLBACK_URL || 'http://localhost:3000/auth/apple/callback',
        scope: ['email', 'name'],
      });
      return;
    }

    super({
      clientID: process.env.APPLE_CLIENT_ID,
      teamID: process.env.APPLE_TEAM_ID,
      keyID: process.env.APPLE_KEY_ID,
      key: process.env.APPLE_PRIVATE_KEY,
      callbackURL: process.env.APPLE_CALLBACK_URL,
      scope: ['email', 'name'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    idToken: any,
    profile: any,
  ): Promise<any> {
    try {
      // Apple provides user data differently than Google
      const { sub, email, email_verified } = idToken;

      // Extract user data from Apple profile
      const user = {
        appleId: sub,
        email: email,
        emailVerified: email_verified,
        name: profile?.name ? `${profile.name.firstName || ''} ${profile.name.lastName || ''}`.trim() : null,
        firstName: profile?.name?.firstName,
        lastName: profile?.name?.lastName,
        accessToken,
        refreshToken,
      };

      // Validate that we have required data
      if (!user.email) {
        throw new Error('Email not provided by Apple');
      }

      return user;
    } catch (error) {
      throw error;
    }
  }
}
