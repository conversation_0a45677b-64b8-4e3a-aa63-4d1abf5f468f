import { Controller, Get, Post, Body, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { WalletService } from './wallet.service';

@Controller('wallet')
@UseGuards(JwtAuthGuard)
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @Get()
  async getUserWallet(@Request() req: any) {
    const userId = req.user.userId;
    return this.walletService.getUserWallet(userId);
  }

  @Post('earn-coins')
  async earnSnapCoins(
    @Body() body: { amount: number; reason: string; category?: string },
    @Request() req: any
  ) {
    const userId = req.user.userId;
    return this.walletService.earnSnapCoins(userId, body.amount, body.reason, body.category);
  }

  @Post('spend-coins')
  async spendSnapCoins(
    @Body() body: { amount: number; reason: string; category?: string },
    @Request() req: any
  ) {
    const userId = req.user.userId;
    return this.walletService.spendSnapCoins(userId, body.amount, body.reason, body.category);
  }

  @Post('use-tokens')
  async useSnapTokens(
    @Body() body: { amount: number; feature: string },
    @Request() req: any
  ) {
    const userId = req.user.userId;
    return this.walletService.useSnapTokens(userId, body.amount, body.feature);
  }

  @Get('can-use-feature/:feature')
  async canUseFeature(
    @Request() req: any,
    @Body() body: { feature: string }
  ) {
    const userId = req.user.userId;
    return this.walletService.canUseFeature(userId, body.feature);
  }
}
