import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import * as dotenv from 'dotenv';
import { AuthModule } from './auth/auth.module';
import { DashboardController } from './dashboard/dashboard.controller';
import { DashboardService } from './dashboard/dashboard.service';
import { DashboardModule } from './dashboard/dashboard.module';
import { UsersController } from './users/users.controller';
import { AdminController } from './admin/admin.controller';
import { AdminModule } from './admin/admin.module';
import { AdminService } from './admin/admin.service';
import { CoachController } from './coach/coach.controller';
import { NutritionistController } from './nutritionist/nutritionist.controller';
import { CoachModule } from './coach/coach.module';
import { NutritionistModule } from './nutritionist/nutritionist.module';
import { CoachService } from './coach/coach.service';
import { NutritionistService } from './nutritionist/nutritionist.service';
import { UsersService } from './users/users.service';
import { UsersModule } from './users/users.module';
import { join } from 'path';
import { ServeStaticModule } from '@nestjs/serve-static';
import { AffiliatesModule } from './admin/affiliates.module';
import { AffiliatesController } from './admin/affiliates.controller';
import { AffiliatesService } from './admin/affiliates.service';
import { ClientsModule } from './clients/clients.module';
import { FriendsModule } from './friends/friends.module';
import { NotificationsModule } from './notifications/notifications.module';
import { FoodsModule } from './foods/foods.module';
import { ChallengesModule } from './challenges/challenges.module';
import { PointsModule } from './points/points.module';
import { RewardsModule } from './rewards/rewards.module';
import { CompatibilityModule } from './compatibility/compatibility.module';
import { WearablesModule } from './wearables/wearables.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { SyncModule } from './sync/sync.module';
import { SettingsModule } from './settings/settings.module';
import { WalletModule } from './wallet/wallet.module';

dotenv.config();

/*
TypeOrmModule.forRoot({
    type: 'mysql',
    host: process.env.DB_HOST,
    // port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
   ...
*/

@Module({
  imports: [
    /*
  ServeStaticModule.forRoot(
    {
      rootPath: join(__dirname, '..', 'storage'),  // Caminho para o diretório de arquivos estáticos
      serveRoot: '/storage/',                        // Prefixo da URL para acessar os arquivos
    }),
    ServeStaticModule.forRoot(
    {
      rootPath: join(__dirname, '..', '__temp'),  // Caminho para o diretório de arquivos estáticos
      serveRoot: '/__temp/',                        // Prefixo da URL para acessar os arquivos
    }), */
  AuthModule,
  DashboardModule,
  AdminModule,
  CoachModule,
  NutritionistModule,
  UsersModule,
  AffiliatesModule,
  ClientsModule,
  FriendsModule,
  NotificationsModule,
  FoodsModule,
  ChallengesModule,
  PointsModule,
  RewardsModule,
  CompatibilityModule,
  WearablesModule,
  AnalyticsModule,
  SyncModule,
  SettingsModule,
  WalletModule,
],
  controllers: [AppController, DashboardController, UsersController, AdminController, CoachController, NutritionistController, AffiliatesController],
  providers: [AppService, DashboardService, AdminService, CoachService, NutritionistService, UsersService, AffiliatesService],
})
export class AppModule {}
