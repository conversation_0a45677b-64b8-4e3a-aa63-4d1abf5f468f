"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _core = require("@nestjs/core");
const _appmodule = require("./app.module");
const _common = require("@nestjs/common");
const _httpexceptionfilter = require("./http-exception/http-exception.filter");
const _dotenv = /*#__PURE__*/ _interop_require_wildcard(require("dotenv"));
const _path = require("path");
const _bodyparser = /*#__PURE__*/ _interop_require_wildcard(require("body-parser"));
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
_dotenv.config();
async function bootstrap() {
    const app = await _core.NestFactory.create(_appmodule.AppModule);
    // app.use(express.static(join(__dirname, 'storage')));
    // app.use(express.static(join(__dirname, '__temp')));
    // Aumentar o limite para ~50MB
    app.use(_bodyparser.json({
        limit: '50mb'
    }));
    app.use(_bodyparser.urlencoded({
        extended: true,
        limit: '50mb'
    }));
    // Configurar CORS
    app.enableCors({
        // origin: 'http://localhost:3000',
        origin: [
            'http://localhost:5173',
            'http://localhost:5174',
            'http://localhost:5177',
            'http://localhost:5178',
            'http://localhost:5179',
            'https://api.mysnapfit.com.br',
            'https://api2.mysnapfit.com.br',
            'https://app.mysnapfit.com.br'
        ],
        // origin: ['*'],
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true
    });
    app.useGlobalPipes(new _common.ValidationPipe({
        transform: true,
        whitelist: true
    }));
    app.useGlobalFilters(new _httpexceptionfilter.HttpExceptionFilter());
    // Serve arquivos estáticos media/...
    app.useStaticAssets((0, _path.join)(__dirname, '..', 'media'), {
        prefix: '/media/'
    });
    /*
  // Serve arquivos estáticos storage/...
  app.useStaticAssets(join(__dirname, '..', 'storage'), {
    prefix: '/storage/',
  });

  // Serve aquivos estáticos __temp/...
  app.useStaticAssets(join(__dirname, '..', '__temp'), {
    prefix: '/__temp/',
  });
  */ await app.listen(process.env.PORT ?? 3000);
}
bootstrap();

//# sourceMappingURL=main.js.map