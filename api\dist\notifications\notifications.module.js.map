{"version": 3, "sources": ["../../src/notifications/notifications.module.ts"], "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\nimport { NotificationsController } from './notifications.controller';\nimport { NotificationsService } from './notifications.service';\n\n@Module({\n  controllers: [NotificationsController],\n  providers: [NotificationsService],\n  exports: [NotificationsService],\n})\nexport class NotificationsModule {}\n"], "names": ["NotificationsModule", "controllers", "NotificationsController", "providers", "NotificationsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;yCACiB;sCACH;;;;;;;AAO9B,IAAA,AAAMA,sBAAN,MAAMA;AAAqB;;;QAJhCC,aAAa;YAACC,gDAAuB;SAAC;QACtCC,WAAW;YAACC,0CAAoB;SAAC;QACjCC,SAAS;YAACD,0CAAoB;SAAC"}