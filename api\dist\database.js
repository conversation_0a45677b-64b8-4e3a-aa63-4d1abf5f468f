"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    db: function() {
        return db;
    },
    executeRawSql: function() {
        return executeRawSql;
    }
});
const _kysely = require("kysely");
const _mysql2 = require("mysql2");
const _dotenv = /*#__PURE__*/ _interop_require_wildcard(require("dotenv"));
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
_dotenv.config();
const db = new _kysely.Kysely({
    dialect: new _kysely.MysqlDialect({
        pool: (0, _mysql2.createPool)({
            host: process.env.DB_HOST,
            user: process.env.DB_USERNAME,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_DATABASE,
            timezone: '+00:00'
        })
    })
});
async function executeRawSql(sqlQuery, params = []) {
    // For Kysely, we need to use sql template literals
    // This is a simplified approach - in production you'd want more sophisticated parameter binding
    let processedSql = sqlQuery;
    // Replace ? placeholders with actual values (be careful with SQL injection)
    params.forEach((param)=>{
        if (typeof param === 'string') {
            processedSql = processedSql.replace('?', `'${param.replace(/'/g, "''")}'`);
        } else if (param === null || param === undefined) {
            processedSql = processedSql.replace('?', 'NULL');
        } else if (typeof param === 'number') {
            // Numbers should not be quoted
            processedSql = processedSql.replace('?', String(param));
        } else {
            // For other types, convert to string without quotes
            processedSql = processedSql.replace('?', String(param));
        }
    });
    // Execute the raw SQL
    const result = await _kysely.sql.raw(processedSql).execute(db);
    return result.rows;
}
// Add a temporary compatibility layer for db.execute
db.execute = executeRawSql;

//# sourceMappingURL=database.js.map