{"version": 3, "sources": ["../../src/points/points.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Body, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { PointsService } from './points.service';\n\n@Controller('points')\n@UseGuards(JwtAuthGuard)\nexport class PointsController {\n  constructor(private readonly pointsService: PointsService) {}\n\n  @Get('balance')\n  async getPointsBalance(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.pointsService.getPointsBalance(userId);\n  }\n\n  @Get('history')\n  async getPointsHistory(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.pointsService.getPointsHistory(userId, query);\n  }\n\n  @Post('earn')\n  async earnPoints(@Body() body: { points: number; reason: string }, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.pointsService.earnPoints(userId, body.points, body.reason);\n  }\n\n  @Post('spend')\n  async spendPoints(@Body() body: { points: number; reason: string }, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.pointsService.spendPoints(userId, body.points, body.reason);\n  }\n\n  @Get('leaderboard')\n  async getLeaderboard(@Query() query: any) {\n    return this.pointsService.getLeaderboard(query);\n  }\n}\n"], "names": ["PointsController", "getPointsBalance", "req", "userId", "user", "pointsService", "getPointsHistory", "query", "earnPoints", "body", "points", "reason", "spendPoints", "getLeaderboard", "constructor"], "mappings": ";;;;+BAcaA;;;eAAAA;;;wBANN;8BACsB;+BACC;;;;;;;;;;;;;;;AAIvB,IAAA,AAAMA,mBAAN,MAAMA;IAGX,MACMC,iBAAiB,AAAWC,GAAQ,EAAE;QAC1C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACJ,gBAAgB,CAACE;IAC7C;IAEA,MACMG,iBAAiB,AAAWJ,GAAQ,EAAE,AAASK,KAAU,EAAE;QAC/D,MAAMJ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACC,gBAAgB,CAACH,QAAQI;IACrD;IAEA,MACMC,WAAW,AAAQC,IAAwC,EAAE,AAAWP,GAAQ,EAAE;QACtF,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACG,UAAU,CAACL,QAAQM,KAAKC,MAAM,EAAED,KAAKE,MAAM;IACvE;IAEA,MACMC,YAAY,AAAQH,IAAwC,EAAE,AAAWP,GAAQ,EAAE;QACvF,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACO,WAAW,CAACT,QAAQM,KAAKC,MAAM,EAAED,KAAKE,MAAM;IACxE;IAEA,MACME,eAAe,AAASN,KAAU,EAAE;QACxC,OAAO,IAAI,CAACF,aAAa,CAACQ,cAAc,CAACN;IAC3C;IA7BAO,YAAY,AAAiBT,aAA4B,CAAE;aAA9BA,gBAAAA;IAA+B;AA8B9D"}