{"version": 3, "sources": ["../../src/points/points.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db } from '../database';\n\n@Injectable()\nexport class PointsService {\n\n  async getPointsBalance(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          total_points as totalPoints,\n          monthly_points as monthlyPoints,\n          yearly_points as yearlyPoints,\n          updated_at as lastUpdated\n        FROM user_points \n        WHERE user_id = ?\n      `;\n\n      const [result] = await db.execute(sql, [userId]);\n\n      const balance = result[0] || {\n        totalPoints: 0,\n        monthlyPoints: 0,\n        yearlyPoints: 0,\n        lastUpdated: null\n      };\n\n      // Get user rank\n      const [rankResult] = await db.execute(\n        `SELECT COUNT(*) + 1 as rank\n         FROM user_points \n         WHERE total_points > (\n           SELECT COALESCE(total_points, 0) \n           FROM user_points \n           WHERE user_id = ?\n         )`,\n        [userId]\n      );\n\n      balance.rank = rankResult[0]?.rank || 1;\n\n      return {\n        status: 'success',\n        data: balance\n      };\n    } catch (error) {\n      console.error('Error getting points balance:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get points balance'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getPointsHistory(userId: number, query: any) {\n    try {\n      const { page = 1, limit = 20, type, startDate, endDate } = query;\n      const offset = (page - 1) * limit;\n\n      let sql = `\n        SELECT \n          id,\n          points,\n          type,\n          reason,\n          created_at as createdAt\n        FROM points_transactions \n        WHERE user_id = ?\n      `;\n\n      const params = [userId];\n\n      if (type) {\n        sql += ` AND type = ?`;\n        params.push(type);\n      }\n\n      if (startDate) {\n        sql += ` AND created_at >= ?`;\n        params.push(startDate);\n      }\n\n      if (endDate) {\n        sql += ` AND created_at <= ?`;\n        params.push(endDate);\n      }\n\n      sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;\n      params.push(limit, offset);\n\n      const [transactions] = await db.execute(sql, params);\n\n      // Get total count\n      let countSql = `SELECT COUNT(*) as total FROM points_transactions WHERE user_id = ?`;\n      const countParams = [userId];\n\n      if (type) {\n        countSql += ` AND type = ?`;\n        countParams.push(type);\n      }\n\n      if (startDate) {\n        countSql += ` AND created_at >= ?`;\n        countParams.push(startDate);\n      }\n\n      if (endDate) {\n        countSql += ` AND created_at <= ?`;\n        countParams.push(endDate);\n      }\n\n      const [countResult] = await db.execute(countSql, countParams);\n      const total = countResult[0]?.total || 0;\n\n      return {\n        status: 'success',\n        data: {\n          transactions,\n          pagination: {\n            page: Number(page),\n            limit: Number(limit),\n            total,\n            totalPages: Math.ceil(total / limit)\n          }\n        }\n      };\n    } catch (error) {\n      console.error('Error getting points history:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get points history'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async earnPoints(userId: number, points: number, reason: string) {\n    try {\n      if (points <= 0) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Points must be positive'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      // Start transaction\n      await db.execute('START TRANSACTION');\n\n      try {\n        // Update user points\n        await db.execute(\n          `INSERT INTO user_points (user_id, total_points, monthly_points, yearly_points, updated_at)\n           VALUES (?, ?, ?, ?, NOW())\n           ON DUPLICATE KEY UPDATE\n           total_points = total_points + ?,\n           monthly_points = monthly_points + ?,\n           yearly_points = yearly_points + ?,\n           updated_at = NOW()`,\n          [userId, points, points, points, points, points, points]\n        );\n\n        // Log transaction\n        await db.execute(\n          `INSERT INTO points_transactions (user_id, points, type, reason, created_at)\n           VALUES (?, ?, 'earned', ?, NOW())`,\n          [userId, points, reason]\n        );\n\n        await db.execute('COMMIT');\n\n        return {\n          status: 'success',\n          message: `Earned ${points} points for ${reason}`,\n          data: {\n            points,\n            reason,\n            type: 'earned'\n          }\n        };\n      } catch (error) {\n        await db.execute('ROLLBACK');\n        throw error;\n      }\n    } catch (error) {\n      console.error('Error earning points:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to earn points'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async spendPoints(userId: number, points: number, reason: string) {\n    try {\n      if (points <= 0) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Points must be positive'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      // Check if user has enough points\n      const [balance] = await db.execute(\n        'SELECT total_points FROM user_points WHERE user_id = ?',\n        [userId]\n      );\n\n      const currentPoints = balance[0]?.total_points || 0;\n\n      if (currentPoints < points) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Insufficient points'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      // Start transaction\n      await db.execute('START TRANSACTION');\n\n      try {\n        // Update user points\n        await db.execute(\n          `UPDATE user_points \n           SET total_points = total_points - ?, updated_at = NOW()\n           WHERE user_id = ?`,\n          [points, userId]\n        );\n\n        // Log transaction\n        await db.execute(\n          `INSERT INTO points_transactions (user_id, points, type, reason, created_at)\n           VALUES (?, ?, 'spent', ?, NOW())`,\n          [userId, points, reason]\n        );\n\n        await db.execute('COMMIT');\n\n        return {\n          status: 'success',\n          message: `Spent ${points} points on ${reason}`,\n          data: {\n            points,\n            reason,\n            type: 'spent',\n            remainingPoints: currentPoints - points\n          }\n        };\n      } catch (error) {\n        await db.execute('ROLLBACK');\n        throw error;\n      }\n    } catch (error) {\n      console.error('Error spending points:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to spend points'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getLeaderboard(query: any) {\n    try {\n      const { period = 'all', limit = 50 } = query;\n      \n      let pointsColumn = 'total_points';\n      if (period === 'monthly') pointsColumn = 'monthly_points';\n      if (period === 'yearly') pointsColumn = 'yearly_points';\n\n      const sql = `\n        SELECT \n          u.id,\n          u.name,\n          u.username,\n          u.photo,\n          COALESCE(up.${pointsColumn}, 0) as points,\n          u.last_active as lastActive\n        FROM users u\n        LEFT JOIN user_points up ON u.id = up.user_id\n        WHERE u.role = 'client'\n        ORDER BY up.${pointsColumn} DESC, u.name ASC\n        LIMIT ?\n      `;\n\n      const [results] = await db.execute(sql, [limit]);\n\n      // Add ranking\n      const leaderboard = results.map((user, index) => ({\n        ...user,\n        rank: index + 1\n      }));\n\n      return {\n        status: 'success',\n        data: {\n          period,\n          leaderboard\n        }\n      };\n    } catch (error) {\n      console.error('Error getting leaderboard:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get leaderboard'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  // Helper method for automatic point earning\n  async awardPointsForActivity(userId: number, activity: string, value?: number) {\n    try {\n      const pointsRules = {\n        'workout_completed': 50,\n        'meal_logged': 10,\n        'water_goal_reached': 25,\n        'daily_goal_completed': 100,\n        'weekly_goal_completed': 500,\n        'friend_added': 20,\n        'challenge_joined': 30,\n        'challenge_completed': 200,\n        'profile_completed': 100,\n        'first_workout': 150,\n        'streak_7_days': 300,\n        'streak_30_days': 1000\n      };\n\n      const points = value || pointsRules[activity] || 0;\n\n      if (points > 0) {\n        await this.earnPoints(userId, points, activity.replace('_', ' '));\n        \n        // Create notification\n        await this.createPointsNotification(userId, points, activity);\n      }\n\n      return points;\n    } catch (error) {\n      console.error('Error awarding points for activity:', error);\n      return 0;\n    }\n  }\n\n  private async createPointsNotification(userId: number, points: number, activity: string) {\n    try {\n      const activityNames = {\n        'workout_completed': 'treino concluído',\n        'meal_logged': 'refeição registrada',\n        'water_goal_reached': 'meta de água atingida',\n        'daily_goal_completed': 'meta diária concluída',\n        'weekly_goal_completed': 'meta semanal concluída',\n        'friend_added': 'amigo adicionado',\n        'challenge_joined': 'desafio iniciado',\n        'challenge_completed': 'desafio concluído',\n        'profile_completed': 'perfil completado',\n        'first_workout': 'primeiro treino',\n        'streak_7_days': 'sequência de 7 dias',\n        'streak_30_days': 'sequência de 30 dias'\n      };\n\n      const activityName = activityNames[activity] || activity;\n\n      await db.execute(\n        `INSERT INTO notifications (user_id, type, title, message, data, created_at)\n         VALUES (?, 'points_earned', 'SnapCoins Ganhos!', ?, ?, NOW())`,\n        [\n          userId,\n          `Você ganhou ${points} SnapCoins por ${activityName}!`,\n          JSON.stringify({ points, activity, type: 'points_earned' })\n        ]\n      );\n    } catch (error) {\n      console.error('Error creating points notification:', error);\n    }\n  }\n\n  // Method to reset monthly/yearly points (to be called by cron job)\n  async resetPeriodicPoints(period: 'monthly' | 'yearly') {\n    try {\n      const column = period === 'monthly' ? 'monthly_points' : 'yearly_points';\n      \n      await db.execute(\n        `UPDATE user_points SET ${column} = 0, updated_at = NOW()`,\n        []\n      );\n\n      console.log(`Reset ${period} points for all users`);\n      return true;\n    } catch (error) {\n      console.error(`Error resetting ${period} points:`, error);\n      return false;\n    }\n  }\n}\n"], "names": ["PointsService", "getPointsBalance", "userId", "sql", "result", "db", "execute", "balance", "totalPoints", "monthlyPoints", "yearlyPoints", "lastUpdated", "rankResult", "rank", "status", "data", "error", "console", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "getPointsHistory", "query", "page", "limit", "type", "startDate", "endDate", "offset", "params", "push", "transactions", "countSql", "countParams", "count<PERSON><PERSON><PERSON>", "total", "pagination", "Number", "totalPages", "Math", "ceil", "earnPoints", "points", "reason", "BAD_REQUEST", "spendPoints", "currentPoints", "total_points", "remainingPoints", "getLeaderboard", "period", "pointsColumn", "results", "leaderboard", "map", "user", "index", "awardPointsForActivity", "activity", "value", "pointsRules", "replace", "createPointsNotification", "activityNames", "activityName", "JSON", "stringify", "resetPeriodicPoints", "column", "log"], "mappings": ";;;;+BAIaA;;;eAAAA;;;wBAJyC;0BACnC;;;;;;;AAGZ,IAAA,AAAMA,gBAAN,MAAMA;IAEX,MAAMC,iBAAiBC,MAAc,EAAE;QACrC,IAAI;YACF,MAAMC,MAAM,CAAC;;;;;;;;MAQb,CAAC;YAED,MAAM,CAACC,OAAO,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACH,KAAK;gBAACD;aAAO;YAE/C,MAAMK,UAAUH,MAAM,CAAC,EAAE,IAAI;gBAC3BI,aAAa;gBACbC,eAAe;gBACfC,cAAc;gBACdC,aAAa;YACf;YAEA,gBAAgB;YAChB,MAAM,CAACC,WAAW,GAAG,MAAMP,YAAE,CAACC,OAAO,CACnC,CAAC;;;;;;UAMC,CAAC,EACH;gBAACJ;aAAO;YAGVK,QAAQM,IAAI,GAAGD,UAAU,CAAC,EAAE,EAAEC,QAAQ;YAEtC,OAAO;gBACLC,QAAQ;gBACRC,MAAMR;YACR;QACF,EAAE,OAAOS,OAAO;YACdC,QAAQD,KAAK,CAAC,iCAAiCA;YAC/C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMC,iBAAiBpB,MAAc,EAAEqB,KAAU,EAAE;QACjD,IAAI;YACF,MAAM,EAAEC,OAAO,CAAC,EAAEC,QAAQ,EAAE,EAAEC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAGL;YAC3D,MAAMM,SAAS,AAACL,CAAAA,OAAO,CAAA,IAAKC;YAE5B,IAAItB,MAAM,CAAC;;;;;;;;;MASX,CAAC;YAED,MAAM2B,SAAS;gBAAC5B;aAAO;YAEvB,IAAIwB,MAAM;gBACRvB,OAAO,CAAC,aAAa,CAAC;gBACtB2B,OAAOC,IAAI,CAACL;YACd;YAEA,IAAIC,WAAW;gBACbxB,OAAO,CAAC,oBAAoB,CAAC;gBAC7B2B,OAAOC,IAAI,CAACJ;YACd;YAEA,IAAIC,SAAS;gBACXzB,OAAO,CAAC,oBAAoB,CAAC;gBAC7B2B,OAAOC,IAAI,CAACH;YACd;YAEAzB,OAAO,CAAC,0CAA0C,CAAC;YACnD2B,OAAOC,IAAI,CAACN,OAAOI;YAEnB,MAAM,CAACG,aAAa,GAAG,MAAM3B,YAAE,CAACC,OAAO,CAACH,KAAK2B;YAE7C,kBAAkB;YAClB,IAAIG,WAAW,CAAC,mEAAmE,CAAC;YACpF,MAAMC,cAAc;gBAAChC;aAAO;YAE5B,IAAIwB,MAAM;gBACRO,YAAY,CAAC,aAAa,CAAC;gBAC3BC,YAAYH,IAAI,CAACL;YACnB;YAEA,IAAIC,WAAW;gBACbM,YAAY,CAAC,oBAAoB,CAAC;gBAClCC,YAAYH,IAAI,CAACJ;YACnB;YAEA,IAAIC,SAAS;gBACXK,YAAY,CAAC,oBAAoB,CAAC;gBAClCC,YAAYH,IAAI,CAACH;YACnB;YAEA,MAAM,CAACO,YAAY,GAAG,MAAM9B,YAAE,CAACC,OAAO,CAAC2B,UAAUC;YACjD,MAAME,QAAQD,WAAW,CAAC,EAAE,EAAEC,SAAS;YAEvC,OAAO;gBACLtB,QAAQ;gBACRC,MAAM;oBACJiB;oBACAK,YAAY;wBACVb,MAAMc,OAAOd;wBACbC,OAAOa,OAAOb;wBACdW;wBACAG,YAAYC,KAAKC,IAAI,CAACL,QAAQX;oBAChC;gBACF;YACF;QACF,EAAE,OAAOT,OAAO;YACdC,QAAQD,KAAK,CAAC,iCAAiCA;YAC/C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMqB,WAAWxC,MAAc,EAAEyC,MAAc,EAAEC,MAAc,EAAE;QAC/D,IAAI;YACF,IAAID,UAAU,GAAG;gBACf,MAAM,IAAIzB,qBAAa,CAAC;oBACtBJ,QAAQ;oBACRK,SAAS;gBACX,GAAGC,kBAAU,CAACyB,WAAW;YAC3B;YAEA,oBAAoB;YACpB,MAAMxC,YAAE,CAACC,OAAO,CAAC;YAEjB,IAAI;gBACF,qBAAqB;gBACrB,MAAMD,YAAE,CAACC,OAAO,CACd,CAAC;;;;;;6BAMkB,CAAC,EACpB;oBAACJ;oBAAQyC;oBAAQA;oBAAQA;oBAAQA;oBAAQA;oBAAQA;iBAAO;gBAG1D,kBAAkB;gBAClB,MAAMtC,YAAE,CAACC,OAAO,CACd,CAAC;4CACiC,CAAC,EACnC;oBAACJ;oBAAQyC;oBAAQC;iBAAO;gBAG1B,MAAMvC,YAAE,CAACC,OAAO,CAAC;gBAEjB,OAAO;oBACLQ,QAAQ;oBACRK,SAAS,CAAC,OAAO,EAAEwB,OAAO,YAAY,EAAEC,QAAQ;oBAChD7B,MAAM;wBACJ4B;wBACAC;wBACAlB,MAAM;oBACR;gBACF;YACF,EAAE,OAAOV,OAAO;gBACd,MAAMX,YAAE,CAACC,OAAO,CAAC;gBACjB,MAAMU;YACR;QACF,EAAE,OAAOA,OAAO;YACdC,QAAQD,KAAK,CAAC,yBAAyBA;YACvC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMyB,YAAY5C,MAAc,EAAEyC,MAAc,EAAEC,MAAc,EAAE;QAChE,IAAI;YACF,IAAID,UAAU,GAAG;gBACf,MAAM,IAAIzB,qBAAa,CAAC;oBACtBJ,QAAQ;oBACRK,SAAS;gBACX,GAAGC,kBAAU,CAACyB,WAAW;YAC3B;YAEA,kCAAkC;YAClC,MAAM,CAACtC,QAAQ,GAAG,MAAMF,YAAE,CAACC,OAAO,CAChC,0DACA;gBAACJ;aAAO;YAGV,MAAM6C,gBAAgBxC,OAAO,CAAC,EAAE,EAAEyC,gBAAgB;YAElD,IAAID,gBAAgBJ,QAAQ;gBAC1B,MAAM,IAAIzB,qBAAa,CAAC;oBACtBJ,QAAQ;oBACRK,SAAS;gBACX,GAAGC,kBAAU,CAACyB,WAAW;YAC3B;YAEA,oBAAoB;YACpB,MAAMxC,YAAE,CAACC,OAAO,CAAC;YAEjB,IAAI;gBACF,qBAAqB;gBACrB,MAAMD,YAAE,CAACC,OAAO,CACd,CAAC;;4BAEiB,CAAC,EACnB;oBAACqC;oBAAQzC;iBAAO;gBAGlB,kBAAkB;gBAClB,MAAMG,YAAE,CAACC,OAAO,CACd,CAAC;2CACgC,CAAC,EAClC;oBAACJ;oBAAQyC;oBAAQC;iBAAO;gBAG1B,MAAMvC,YAAE,CAACC,OAAO,CAAC;gBAEjB,OAAO;oBACLQ,QAAQ;oBACRK,SAAS,CAAC,MAAM,EAAEwB,OAAO,WAAW,EAAEC,QAAQ;oBAC9C7B,MAAM;wBACJ4B;wBACAC;wBACAlB,MAAM;wBACNuB,iBAAiBF,gBAAgBJ;oBACnC;gBACF;YACF,EAAE,OAAO3B,OAAO;gBACd,MAAMX,YAAE,CAACC,OAAO,CAAC;gBACjB,MAAMU;YACR;QACF,EAAE,OAAOA,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM6B,eAAe3B,KAAU,EAAE;QAC/B,IAAI;YACF,MAAM,EAAE4B,SAAS,KAAK,EAAE1B,QAAQ,EAAE,EAAE,GAAGF;YAEvC,IAAI6B,eAAe;YACnB,IAAID,WAAW,WAAWC,eAAe;YACzC,IAAID,WAAW,UAAUC,eAAe;YAExC,MAAMjD,MAAM,CAAC;;;;;;sBAMG,EAAEiD,aAAa;;;;;oBAKjB,EAAEA,aAAa;;MAE7B,CAAC;YAED,MAAM,CAACC,QAAQ,GAAG,MAAMhD,YAAE,CAACC,OAAO,CAACH,KAAK;gBAACsB;aAAM;YAE/C,cAAc;YACd,MAAM6B,cAAcD,QAAQE,GAAG,CAAC,CAACC,MAAMC,QAAW,CAAA;oBAChD,GAAGD,IAAI;oBACP3C,MAAM4C,QAAQ;gBAChB,CAAA;YAEA,OAAO;gBACL3C,QAAQ;gBACRC,MAAM;oBACJoC;oBACAG;gBACF;YACF;QACF,EAAE,OAAOtC,OAAO;YACdC,QAAQD,KAAK,CAAC,8BAA8BA;YAC5C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,4CAA4C;IAC5C,MAAMqC,uBAAuBxD,MAAc,EAAEyD,QAAgB,EAAEC,KAAc,EAAE;QAC7E,IAAI;YACF,MAAMC,cAAc;gBAClB,qBAAqB;gBACrB,eAAe;gBACf,sBAAsB;gBACtB,wBAAwB;gBACxB,yBAAyB;gBACzB,gBAAgB;gBAChB,oBAAoB;gBACpB,uBAAuB;gBACvB,qBAAqB;gBACrB,iBAAiB;gBACjB,iBAAiB;gBACjB,kBAAkB;YACpB;YAEA,MAAMlB,SAASiB,SAASC,WAAW,CAACF,SAAS,IAAI;YAEjD,IAAIhB,SAAS,GAAG;gBACd,MAAM,IAAI,CAACD,UAAU,CAACxC,QAAQyC,QAAQgB,SAASG,OAAO,CAAC,KAAK;gBAE5D,sBAAsB;gBACtB,MAAM,IAAI,CAACC,wBAAwB,CAAC7D,QAAQyC,QAAQgB;YACtD;YAEA,OAAOhB;QACT,EAAE,OAAO3B,OAAO;YACdC,QAAQD,KAAK,CAAC,uCAAuCA;YACrD,OAAO;QACT;IACF;IAEA,MAAc+C,yBAAyB7D,MAAc,EAAEyC,MAAc,EAAEgB,QAAgB,EAAE;QACvF,IAAI;YACF,MAAMK,gBAAgB;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,sBAAsB;gBACtB,wBAAwB;gBACxB,yBAAyB;gBACzB,gBAAgB;gBAChB,oBAAoB;gBACpB,uBAAuB;gBACvB,qBAAqB;gBACrB,iBAAiB;gBACjB,iBAAiB;gBACjB,kBAAkB;YACpB;YAEA,MAAMC,eAAeD,aAAa,CAACL,SAAS,IAAIA;YAEhD,MAAMtD,YAAE,CAACC,OAAO,CACd,CAAC;sEAC6D,CAAC,EAC/D;gBACEJ;gBACA,CAAC,YAAY,EAAEyC,OAAO,eAAe,EAAEsB,aAAa,CAAC,CAAC;gBACtDC,KAAKC,SAAS,CAAC;oBAAExB;oBAAQgB;oBAAUjC,MAAM;gBAAgB;aAC1D;QAEL,EAAE,OAAOV,OAAO;YACdC,QAAQD,KAAK,CAAC,uCAAuCA;QACvD;IACF;IAEA,mEAAmE;IACnE,MAAMoD,oBAAoBjB,MAA4B,EAAE;QACtD,IAAI;YACF,MAAMkB,SAASlB,WAAW,YAAY,mBAAmB;YAEzD,MAAM9C,YAAE,CAACC,OAAO,CACd,CAAC,uBAAuB,EAAE+D,OAAO,wBAAwB,CAAC,EAC1D,EAAE;YAGJpD,QAAQqD,GAAG,CAAC,CAAC,MAAM,EAAEnB,OAAO,qBAAqB,CAAC;YAClD,OAAO;QACT,EAAE,OAAOnC,OAAO;YACdC,QAAQD,KAAK,CAAC,CAAC,gBAAgB,EAAEmC,OAAO,QAAQ,CAAC,EAAEnC;YACnD,OAAO;QACT;IACF;AACF"}