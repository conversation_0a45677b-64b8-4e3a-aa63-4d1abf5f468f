{"version": 3, "sources": ["../../../src/clients/dto/create-client.dto.ts"], "sourcesContent": ["import { IsString, <PERSON>E<PERSON>, <PERSON><PERSON><PERSON>al, IsN<PERSON>ber, IsEnum } from 'class-validator';\n\nexport class CreateClientDto {\n  @IsString()\n  name: string;\n\n  @IsEmail()\n  email: string;\n\n  @IsString()\n  password: string;\n\n  @IsOptional()\n  @IsString()\n  phone?: string;\n\n  @IsOptional()\n  @IsNumber()\n  height?: number;\n\n  @IsOptional()\n  @IsNumber()\n  weight?: number;\n\n  @IsOptional()\n  @IsEnum(['sedentary', 'lightly_active', 'moderately_active', 'very_active', 'extra_active'])\n  activityLevel?: string;\n\n  @IsOptional()\n  @IsString()\n  photo?: string;\n\n  @IsOptional()\n  @IsString()\n  goals?: string;\n\n  @IsOptional()\n  @IsString()\n  medicalConditions?: string;\n\n  @IsOptional()\n  @IsString()\n  allergies?: string;\n\n  @IsOptional()\n  @IsString()\n  notes?: string;\n}\n"], "names": ["CreateClientDto"], "mappings": ";;;;+BAEaA;;;eAAAA;;;gCAFmD;;;;;;;;;;AAEzD,IAAA,AAAMA,kBAAN,MAAMA;AA6Cb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAtBW;QAAa;QAAkB;QAAqB;QAAe"}