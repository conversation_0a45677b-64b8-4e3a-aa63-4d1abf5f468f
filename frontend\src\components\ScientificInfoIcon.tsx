import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Info, ExternalLink, X, HelpCircle } from 'lucide-react';

interface ScientificInfoIconProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'modal' | 'navigate';
  title?: string;
  description?: string;
}

export function ScientificInfoIcon({ 
  className = '',
  size = 'md',
  variant = 'navigate',
  title = 'Informação Científica',
  description = 'Esta recomendação é baseada em diretrizes científicas oficiais e pesquisas revisadas por pares.'
}: ScientificInfoIconProps) {
  const [showModal, setShowModal] = useState(false);
  const navigate = useNavigate();

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  const getButtonSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-6 h-6';
      case 'lg':
        return 'w-8 h-8';
      default:
        return 'w-7 h-7';
    }
  };

  const handleClick = () => {
    if (variant === 'modal') {
      setShowModal(true);
    } else {
      navigate('/dashboard/scientific-sources');
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
  };

  const handleViewSources = () => {
    setShowModal(false);
    navigate('/dashboard/scientific-sources');
  };

  return (
    <>
      <button
        onClick={handleClick}
        className={`
          ${getButtonSizeClasses()}
          bg-blue-500/10 border border-blue-500/20 rounded-full 
          flex items-center justify-center
          hover:bg-blue-500/20 hover:border-blue-500/40
          transition-all duration-200 group
          ${className}
        `}
        title="Ver informações científicas"
      >
        <Info className={`
          ${getSizeClasses()} 
          text-blue-400 group-hover:text-blue-300 
          transition-colors duration-200
        `} />
      </button>

      {/* Modal */}
      {showModal && variant === 'modal' && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-[5000]">
          <div className="bg-snapfit-gray rounded-xl shadow-xl max-w-md w-full border border-snapfit-green/20">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-snapfit-green/20">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30">
                  <Info className="w-5 h-5 text-blue-400" />
                </div>
                <h3 className="text-lg font-semibold text-white">{title}</h3>
              </div>
              <button
                onClick={handleModalClose}
                className="p-1 hover:bg-snapfit-dark-gray rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4">
              <p className="text-gray-300 text-sm leading-relaxed mb-4">
                {description}
              </p>
              
              <div className="bg-snapfit-dark-gray rounded-lg p-3 border border-snapfit-green/10">
                <h4 className="text-white font-medium text-sm mb-2">
                  Fontes Científicas Utilizadas:
                </h4>
                <ul className="text-gray-400 text-xs space-y-1">
                  <li>• Dietary Guidelines for Americans (USDA)</li>
                  <li>• WHO Healthy Diet Guidelines</li>
                  <li>• Guia Alimentar Brasileiro (Ministério da Saúde)</li>
                  <li>• Pesquisas revisadas por pares (PubMed)</li>
                </ul>
              </div>
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-snapfit-green/20">
              <button
                onClick={handleViewSources}
                className="w-full flex items-center justify-center gap-2 bg-snapfit-green text-black py-2.5 px-4 rounded-lg font-medium hover:bg-snapfit-green/90 transition-colors"
              >
                <ExternalLink className="w-4 h-4" />
                Ver todas as fontes científicas
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// Componente específico para uso em títulos de seções
export function SectionScientificInfo({ 
  className = '',
  title,
  description 
}: { 
  className?: string;
  title?: string;
  description?: string;
}) {
  return (
    <ScientificInfoIcon 
      size="sm"
      variant="modal"
      title={title}
      description={description}
      className={className}
    />
  );
}

// Componente específico para uso em cards de cálculo
export function CalculationScientificInfo({ className = '' }: { className?: string }) {
  return (
    <ScientificInfoIcon
      size="md"
      variant="navigate"
      title="Base Científica dos Cálculos"
      description="Os cálculos nutricionais utilizados são baseados em fórmulas validadas cientificamente e diretrizes oficiais de saúde."
      className={className}
    />
  );
}

// Componente badge discreto para estatísticas
export function StatScientificBadge({ className = '' }: { className?: string }) {
  const navigate = useNavigate();

  return (
    <button
      onClick={() => navigate('/dashboard/scientific-sources')}
      className={`
        inline-flex items-center gap-1 px-2 py-1
        bg-blue-500/10 border border-blue-500/20 rounded-full
        text-xs text-blue-400 hover:text-blue-300
        hover:bg-blue-500/20 hover:border-blue-500/40
        transition-all duration-200
        ${className}
      `}
      title="Ver base científica"
    >
      <HelpCircle className="w-3 h-3" />
      <span>Científico</span>
    </button>
  );
}

// Componente tooltip hover para análises
export function AnalysisScientificTooltip({
  children,
  title = "Base Científica",
  description = "Esta análise é baseada em diretrizes científicas oficiais.",
  className = ''
}: {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
}) {
  const [showTooltip, setShowTooltip] = useState(false);
  const navigate = useNavigate();

  return (
    <div className={`relative inline-block ${className}`}>
      <div
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className="cursor-help"
      >
        {children}
      </div>

      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50">
          <div className="bg-snapfit-gray border border-snapfit-green/20 rounded-lg p-3 shadow-xl max-w-xs">
            <div className="flex items-center gap-2 mb-2">
              <Info className="w-4 h-4 text-blue-400" />
              <h4 className="text-sm font-medium text-white">{title}</h4>
            </div>
            <p className="text-xs text-gray-300 mb-3">{description}</p>
            <button
              onClick={() => navigate('/dashboard/scientific-sources')}
              className="flex items-center gap-1 text-xs text-snapfit-green hover:text-snapfit-green/80 transition-colors"
            >
              <ExternalLink className="w-3 h-3" />
              Ver fontes completas
            </button>
          </div>
          {/* Tooltip arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-snapfit-gray"></div>
        </div>
      )}
    </div>
  );
}

// Componente inline discreto para textos
export function InlineScientificReference({ className = '' }: { className?: string }) {
  const navigate = useNavigate();

  return (
    <button
      onClick={() => navigate('/dashboard/scientific-sources')}
      className={`
        inline-flex items-center gap-1 text-xs text-gray-500
        hover:text-blue-400 transition-colors
        ${className}
      `}
      title="Ver base científica"
    >
      <span>*</span>
      <span className="underline decoration-dotted">baseado em diretrizes oficiais</span>
    </button>
  );
}

// Componente específico para cálculos de exercício
export function WorkoutScientificInfo({
  className = '',
  context = "cálculos de exercício"
}: {
  className?: string;
  context?: string;
}) {
  return (
    <ScientificInfoIcon
      size="md"
      variant="modal"
      title="Base Científica dos Cálculos de Exercício"
      description={`Os ${context} são baseados nas diretrizes do ACSM (American College of Sports Medicine), NSCA (National Strength and Conditioning Association) e protocolos validados cientificamente para prescrição de exercícios.`}
      className={className}
    />
  );
}

// Componente para análises de performance
export function PerformanceScientificInfo({ className = '' }: { className?: string }) {
  return (
    <ScientificInfoIcon
      size="sm"
      variant="modal"
      title="Metodologia de Análise de Performance"
      description="As análises de performance seguem protocolos científicos estabelecidos pelo ACSM e NSCA para avaliação de força, potência e resistência cardiovascular."
      className={className}
    />
  );
}

// Componente para prescrição de treino
export function ExercisePrescriptionInfo({ className = '' }: { className?: string }) {
  return (
    <ScientificInfoIcon
      size="md"
      variant="navigate"
      title="Prescrição de Exercícios Baseada em Evidências"
      description="As recomendações de exercícios seguem as diretrizes do ACSM para prescrição de atividade física, incluindo frequência, intensidade, tempo e tipo de exercício (princípio FITT)."
      className={className}
    />
  );
}
