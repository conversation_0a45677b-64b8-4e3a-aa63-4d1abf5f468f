{"version": 3, "sources": ["../../src/coach/coach.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { GetCoachClientsQueryDto } from '../admin/dto/get-coach-clients-query.dto';\r\nimport { db } from '../database';\r\nimport * as dayjs from 'dayjs';\r\nimport { ImportCoachProtocolDto } from 'src/admin/dto/import-coach-protocol.dto';\r\nimport { CreateCoachProtocolDto } from 'src/admin/dto/create-coach-protocol.dto';\r\nimport { CreateCoachClientProtocolDto } from './dto/create-coach-client-protocol.dto';\r\n\r\n@Injectable()\r\nexport class CoachService {\r\n    formatDatetime(datetime: any): string {\r\n        return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');\r\n    }\r\n\r\n    calculateAge(birthDate: Date): number | null {\r\n        if (!birthDate) {\r\n          return null;\r\n        }\r\n\r\n        const today = new Date();\r\n        const birthDateObj = new Date(birthDate);\r\n    \r\n        let age = today.getFullYear() - birthDateObj.getFullYear();\r\n        const monthDifference = today.getMonth() - birthDateObj.getMonth();\r\n    \r\n        if (\r\n          monthDifference < 0 ||\r\n          (monthDifference === 0 && today.getDate() < birthDateObj.getDate())\r\n        ) {\r\n          age--;\r\n        }\r\n    \r\n        return age;\r\n      }\r\n\r\n    async getClients(query: GetCoachClientsQueryDto, userId: number) {\r\n        const { q } = query;\r\n        let { page = 1, limit = 100 } = query;\r\n        const offset = (page - 1) * limit;\r\n            \r\n        let queryBuilder = db\r\n            .selectFrom('users')\r\n            .innerJoin('users_roles', 'users.id', 'users_roles.user_id')\r\n            .innerJoin('clients', 'users.id', 'clients.client_id')\r\n            .leftJoin('coach_protocols', 'coach_protocols.user_id', 'users.id')\r\n            .select([\r\n            'users.id',\r\n            'users.name',\r\n            'users.email',\r\n            'users.photo',\r\n            'users.created_at',\r\n            'coach_protocols.name as protocol'\r\n            // db.fn<string>('group_concat', ['roles.name']).as('roles'),\r\n            ])\r\n            .where('clients.role_id', '=', 2)\r\n            .where('clients.user_id', '=', userId)\r\n            .groupBy('users.id')\r\n            .orderBy('users.created_at', 'desc');\r\n    \r\n        if (q) {\r\n            queryBuilder = queryBuilder.where((eb) =>\r\n            eb.or([\r\n                eb('users.name', 'like', `%${q}%`),\r\n                eb('users.email', 'like', `%${q}%`),\r\n            ])\r\n            );\r\n        }\r\n    \r\n        const [data, total] = await Promise.all([\r\n            queryBuilder.limit(limit).offset(offset).execute(),\r\n            db.selectFrom('users').select(db.fn.countAll().as('total')).executeTakeFirst(),\r\n        ]);\r\n    \r\n        return {\r\n            status: 'success',\r\n            data: data.map((row) => ({\r\n            id: row.id,\r\n            name: row.name,\r\n            email: row.email,\r\n            photo: row.photo,\r\n            protocol: row.protocol,\r\n            plan: '',\r\n            plan_status: '',\r\n            attendance: 99,\r\n            date: this.formatDatetime(row.created_at),            \r\n            })),\r\n            pagination: {\r\n            page,\r\n            limit,\r\n            total: Number(total?.total),\r\n            },\r\n        };\r\n    }\r\n\r\n    async getClient(id: number, userId: number) {\r\n        const client = await db\r\n            .selectFrom('users')\r\n            .innerJoin('clients', 'users.id', 'clients.client_id')\r\n            .select([\r\n                'clients.client_id as id',\r\n                'users.name',\r\n                'users.email',\r\n                'users.photo',\r\n                'users.height',\r\n                'users.weight',\r\n                'clients.created_at as client_date',\r\n                'users.date_of_birth'\r\n            ])\r\n            .where('users.id', '=', id)\r\n            .where('clients.client_id', '=', id)\r\n            .where('clients.user_id', '=', userId)\r\n            .where('clients.role_id', '=', 2)\r\n            .groupBy('users.id')\r\n            .executeTakeFirst();\r\n\r\n            if (!client) {\r\n                return {\r\n                    status: 'error',\r\n                    message: 'Client not found',\r\n                };\r\n            }\r\n\r\n            const attendance = {\r\n                week: 999,\r\n                month: 999,\r\n                sequence: 999,\r\n                record: 999\r\n            };\r\n\r\n            /*\r\n            const lastProtocol = await db\r\n                .selectFrom('coach_protocols as p')\r\n                .where('p.user_id', '=', userId)\r\n                .where('p.client_id', '=', id)\r\n                .where('p.ended_at', 'is', null)\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select([\r\n                    'p.id',\r\n                    'p.name',\r\n                    's.value_option as type',\r\n                    'p.split',\r\n                    'p.frequency',\r\n                    'p.notes',\r\n                    'p.started_at',\r\n                    'p.objective',\r\n                ])\r\n                .orderBy('p.id', 'desc')\r\n                .limit(1)\r\n                .executeTakeFirst();\r\n\r\n            let protocol: any = null;\r\n\r\n            if(lastProtocol) {\r\n            protocol = {\r\n                id: lastProtocol.id,\r\n                name: lastProtocol.name,\r\n                type: lastProtocol.type,\r\n                split: lastProtocol.split,\r\n                frequency: lastProtocol.frequency,\r\n                objective: lastProtocol.objective,\r\n                notes: lastProtocol.notes,\r\n                started_at: this.formatDatetime(lastProtocol.started_at),\r\n            }\r\n            }\r\n\r\n            const clientData = {\r\n                id: client.id,\r\n                name: client.name,\r\n                email: client.email,\r\n                photo: client.photo,                \r\n                height: client.height,\r\n                weight: client.weight,\r\n                age: client.date_of_birth ? this.calculateAge(client.date_of_birth) : null,\r\n                client_date: this.formatDatetime(client.client_date),\r\n                // last_protocol_date: this.formatDatetime(client.last_protocol_date),\r\n                attendance,\r\n                protocol\r\n            };\r\n            \r\n        return {\r\n            status: 'success',\r\n            data: clientData,\r\n        };\r\n        */\r\n    }\r\n\r\n    async getClientProtocols(id: number, userId: number) {\r\n        /*\r\n        const protocols = await db\r\n            .selectFrom('coach_protocols as p')\r\n            .where('p.user_id', '=', userId)\r\n            .where('p.client_id', '=', id)\r\n            .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n            .select([\r\n                'p.id',\r\n                'p.name',\r\n                's.value_option as type',\r\n                'p.split',\r\n                'p.frequency',\r\n                'p.objective',\r\n                'p.notes',\r\n                'p.started_at',\r\n                'p.ended_at',\r\n                'p.created_at',\r\n                'p.updated_at',\r\n            ])\r\n            .orderBy('p.created_at', 'desc')\r\n            .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: protocols,\r\n        };\r\n        */\r\n    }\r\n\r\n    async createClientProtocol(id: number, createCoachClientProtocolDto: CreateCoachClientProtocolDto, userId: number) {\r\n        const { name, type_id, split, frequency, objective, notes, workouts } = createCoachClientProtocolDto;\r\n\r\n        const new_protocol = await db\r\n            .insertInto('coach_protocols')\r\n            .values({\r\n                client_id: id,\r\n                user_id: userId,\r\n                name: name,\r\n                type_id: type_id,\r\n                split: split,\r\n                frequency: frequency,\r\n                objective: objective,\r\n                // general_notes: general_notes,\r\n                started_at: new Date(),\r\n                ended_at: null,\r\n                created_at: new Date(),\r\n                updated_at: new Date(),\r\n            })\r\n            .executeTakeFirst();\r\n\r\n        const new_protocol_id = Number(new_protocol.insertId);\r\n        \r\n        for (const [index, workout] of workouts.entries()) {\r\n            for (const exercise of workout.exercises) {\r\n                /*\r\n                await db.insertInto('coach_protocols_workouts')\r\n                    .values({\r\n                        protocol_id: new_protocol_id,\r\n                        exercise_id: exercise.exercise_id,\r\n                        split_group: index + 1,\r\n                        sets: exercise.sets,\r\n                        reps: exercise.reps,\r\n                        rpe: exercise.rpe,\r\n                        rest_seconds: exercise.rest_seconds,\r\n                        notes: exercise.notes,\r\n                    })\r\n                    .execute();\r\n                    */\r\n            }\r\n        }\r\n\r\n        return {\r\n            status: 'success',\r\n            data: [],\r\n        };\r\n    }\r\n\r\n    async importProtocol(id: number, importCoachProtocolDto: ImportCoachProtocolDto, userId: number) {\r\n        const { protocol_id } = importCoachProtocolDto;\r\n\r\n        const protocol_template = await db\r\n            .selectFrom('coach_protocols_templates')\r\n            .where('id', '=', protocol_id)\r\n            .where('user_id', '=', userId)\r\n            .select(['id', 'name', 'type_id', 'split', 'frequency', 'objective', 'notes'])\r\n            .executeTakeFirst();\r\n\r\n        if (!protocol_template) {\r\n            return {\r\n                status: 'error',\r\n                message: 'Protocol not found',\r\n            };\r\n        }\r\n\r\n        const protocol_template_workouts = await db\r\n            .selectFrom('coach_protocols_templates_workouts')\r\n            .where('protocol_id', '=', protocol_id)\r\n            .select(['id', 'exercise_id', 'split_group', 'sets', 'reps', 'rpe', 'rest_seconds', 'notes'])\r\n            .execute();\r\n\r\n        const new_protocol = await db\r\n            .insertInto('coach_protocols')\r\n            .values({\r\n                client_id: id,\r\n                user_id: userId,\r\n                name: protocol_template.name,\r\n                type_id: protocol_template.type_id,\r\n                split: protocol_template.split,\r\n                frequency: protocol_template.frequency,\r\n                objective: protocol_template.objective,\r\n                general_notes: protocol_template.notes,\r\n                started_at: new Date(),\r\n                ended_at: null,\r\n                created_at: new Date(),\r\n                updated_at: new Date(),\r\n            })\r\n            .executeTakeFirst();\r\n\r\n            const new_protocol_id = Number(new_protocol.insertId);\r\n\r\n            /*\r\n            await db\r\n                .insertInto('coach_protocols_workouts')\r\n                .values(protocol_template_workouts.map((workout) => ({\r\n                    protocol_id: new_protocol_id,\r\n                    exercise_id: workout.exercise_id,\r\n                    split_group: workout.split_group,\r\n                    sets: workout.sets,\r\n                    reps: workout.reps,\r\n                    rpe: workout.rpe,\r\n                    rest_seconds: workout.rest_seconds,\r\n                    notes: workout.notes,\r\n                })))\r\n                .execute();\r\n                */\r\n\r\n        return {\r\n            status: 'success',\r\n            data: [],\r\n        };\r\n    }\r\n\r\n    async getProtocols(userId: number) {\r\n        const protocols = await db\r\n        .selectFrom('coach_protocols_templates as p')\r\n        .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n        .where('p.user_id', '=', userId)\r\n        .select(['p.id', 'p.name', 's.value_option as type', 'p.split', 'p.frequency', 'p.objective', 'p.notes', 'p.created_at', 'p.updated_at'])\r\n        .orderBy('p.id', 'desc')\r\n        .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: protocols,\r\n        };\r\n    }\r\n\r\n    async getProtocol(protocolId: number, userId: number) {\r\n        // Buscar o protocolo específico\r\n        const protocol = await db\r\n            .selectFrom('coach_protocols_templates as p')\r\n            .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n            .where('p.user_id', '=', userId)\r\n            .where('p.id', '=', protocolId)\r\n            .select([\r\n                'p.id',\r\n                'p.name',\r\n                's.value_option as type',\r\n                'p.split',\r\n                'p.frequency',\r\n                'p.objective',\r\n                'p.notes',\r\n                'p.created_at',\r\n                'p.updated_at'\r\n            ])\r\n            .executeTakeFirst();\r\n\r\n        if (!protocol) {\r\n            return {\r\n                status: 'error',\r\n                message: 'Protocol not found',\r\n            };\r\n        }\r\n\r\n        // Para templates, buscar exercícios diretamente da tabela de templates\r\n        const templateExercises = await db\r\n            .selectFrom('coach_protocols_templates_workouts as tw')\r\n            .leftJoin('exercises as e', 'e.id', 'tw.exercise_id')\r\n            .where('tw.protocol_id', '=', protocolId)\r\n            .selectAll()\r\n            .execute();\r\n\r\n        // Agrupar exercícios por split_group para formar os workouts\r\n        const workoutGroups: { [key: number]: any[] } = {};\r\n        templateExercises.forEach(exercise => {\r\n            const splitGroup = exercise.split_group;\r\n            if (!workoutGroups[splitGroup]) {\r\n                workoutGroups[splitGroup] = [];\r\n            }\r\n            workoutGroups[splitGroup].push({\r\n                id: exercise.id,\r\n                exerciseId: exercise.exercise_id,\r\n                name: exercise.name,\r\n                muscle_group: exercise.muscle_group_id,\r\n                equipment: exercise.equipment_id,\r\n                media_url: exercise.media_url,\r\n                sets: exercise.sets,\r\n                reps: exercise.reps,\r\n                rpe: exercise.rpe,\r\n                restTime: exercise.rest_seconds,\r\n                notes: exercise.notes,\r\n                orderIndex: workoutGroups[splitGroup].length + 1\r\n            });\r\n        });\r\n\r\n        // Converter grupos em workouts\r\n        const workoutsWithExercises = Object.keys(workoutGroups).map(splitGroup => {\r\n            const splitLetter = String.fromCharCode(64 + parseInt(splitGroup)); // A, B, C, etc.\r\n            return {\r\n                id: splitGroup,\r\n                name: `Treino ${splitLetter}`,\r\n                exercises: workoutGroups[parseInt(splitGroup)]\r\n            };\r\n        });\r\n\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                ...protocol,\r\n                workouts: workoutsWithExercises\r\n            },\r\n        };\r\n    }\r\n\r\n    async createProtocol(createCoachProtocolDto: CreateCoachProtocolDto, userId: number) {\r\n        const { name, type_id, split, frequency, objective, notes, workouts } = createCoachProtocolDto;\r\n\r\n        const new_protocol = await db\r\n            .insertInto('coach_protocols_templates')\r\n            .values({\r\n                name: name,\r\n                type_id: type_id,\r\n                split: split,\r\n                frequency: frequency,\r\n                objective: objective,\r\n                notes: notes,\r\n                user_id: userId\r\n            })\r\n            .executeTakeFirst();\r\n\r\n            const new_protocol_id = Number(new_protocol.insertId);\r\n\r\n            for (const [index, workout] of workouts.entries()) {\r\n                await Promise.all(\r\n                    workout.exercises.map(async (exercise) => {\r\n                        await db\r\n                            .insertInto('coach_protocols_templates_workouts')\r\n                            .values({\r\n                                protocol_id: new_protocol_id,\r\n                                exercise_id: exercise.exercise_id,\r\n                                split_group: index + 1, \r\n                                sets: exercise.sets,\r\n                                reps: exercise.reps,\r\n                                rpe: exercise.rpe,\r\n                                rest_seconds: exercise.rest_seconds,\r\n                                notes: exercise?.notes,\r\n                            })\r\n                            .execute();\r\n                    })\r\n                );\r\n            }\r\n            \r\n\r\n        return {\r\n            status: 'success',\r\n            data: [],\r\n        };\r\n    }\r\n\r\n    async updateProtocol(protocolId: number, updateData: any, userId: number) {\r\n        // Verificar se o protocolo existe e pertence ao usuário\r\n        const existingProtocol = await db\r\n            .selectFrom('coach_protocols')\r\n            .select(['id', 'client_id', 'ended_at', 'started_at'])\r\n            .where('id', '=', protocolId)\r\n            .where('client_id', '=', userId)\r\n            .executeTakeFirst();\r\n\r\n        if (!existingProtocol) {\r\n            return {\r\n                status: 'error',\r\n                message: 'Protocolo não encontrado ou você não tem permissão para editá-lo.',\r\n            };\r\n        }\r\n\r\n        // Verificar se o protocolo ainda está ativo (não foi finalizado)\r\n        if (existingProtocol.ended_at) {\r\n            return {\r\n                status: 'error',\r\n                message: 'Não é possível editar um protocolo que já foi finalizado.',\r\n            };\r\n        }\r\n\r\n        try {\r\n            // Atualizar dados básicos do protocolo\r\n            await db\r\n                .updateTable('coach_protocols')\r\n                .set({\r\n                    name: updateData.name,\r\n                    type_id: updateData.type,\r\n                    split: updateData.split,\r\n                    frequency: updateData.frequency,\r\n                    objective: updateData.objective,\r\n                    general_notes: updateData.notes,\r\n                    updated_at: new Date(),\r\n                })\r\n                .where('id', '=', protocolId)\r\n                .execute();\r\n\r\n            // Se há workouts para atualizar\r\n            if (updateData.workouts && Array.isArray(updateData.workouts)) {\r\n                // Remover workouts e exercícios existentes\r\n                const existingWorkouts = await db\r\n                    .selectFrom('coach_protocols_workouts')\r\n                    .select(['id'])\r\n                    .where('protocol_id', '=', protocolId)\r\n                    .execute();\r\n\r\n                for (const workout of existingWorkouts) {\r\n                    await db.deleteFrom('coach_protocols_workouts_exercises').where('workout_id', '=', workout.id).execute();\r\n                }\r\n                await db.deleteFrom('coach_protocols_workouts').where('protocol_id', '=', protocolId).execute();\r\n\r\n                // Adicionar novos workouts e exercícios\r\n                const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\r\n                for (const [index, workout] of updateData.workouts.entries()) {\r\n                    const workoutName = workout.name || `Treino ${letters[index]}`;\r\n\r\n                    const newWorkout = await db\r\n                        .insertInto('coach_protocols_workouts')\r\n                        .values({\r\n                            protocol_id: protocolId,\r\n                            name: workoutName,\r\n                            created_at: new Date(),\r\n                            updated_at: new Date(),\r\n                        })\r\n                        .executeTakeFirst();\r\n\r\n                    const workoutId = Number(newWorkout.insertId);\r\n\r\n                    if (workout.exercises && Array.isArray(workout.exercises)) {\r\n                        for (const exercise of workout.exercises) {\r\n                            // Verificar se é um exercício personalizado (ID começa com 'custom_')\r\n                            const isCustomExercise = typeof exercise.exercise_id === 'string' &&\r\n                                exercise.exercise_id.toString().startsWith('custom_');\r\n\r\n                            await db\r\n                                .insertInto('coach_protocols_workouts_exercises')\r\n                                .values({\r\n                                    workout_id: workoutId,\r\n                                    // Para exercícios personalizados, definir exercise_id como null\r\n                                    exercise_id: isCustomExercise ? null : (exercise.exercise?.id || exercise.exercise_id),\r\n                                    name: exercise.name || exercise.exercise_name || exercise.exercise?.name,\r\n                                    sets: exercise.sets,\r\n                                    reps: exercise.reps,\r\n                                    rpe: exercise.rpe,\r\n                                    rest_seconds: exercise.restTime || exercise.rest_seconds,\r\n                                    notes: exercise.notes,\r\n                                })\r\n                                .execute();\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            return {\r\n                status: 'success',\r\n                message: 'Protocolo atualizado com sucesso',\r\n                data: { id: protocolId }\r\n            };\r\n        } catch (error) {\r\n            console.error('Erro ao atualizar protocolo:', error);\r\n            return {\r\n                status: 'error',\r\n                message: 'Erro interno do servidor ao atualizar protocolo',\r\n            };\r\n        }\r\n    }\r\n\r\n    async deleteProtocol(id: number, userId: number) {\r\n        await db\r\n            .deleteFrom('coach_protocols_templates')\r\n            .where('id', '=', id)\r\n            .execute();\r\n\r\n        await db\r\n            .deleteFrom('coach_protocols_templates_workouts')\r\n            .where('protocol_id', '=', id)\r\n            .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: [],\r\n        };\r\n    }\r\n\r\n}\r\n"], "names": ["CoachService", "formatDatetime", "datetime", "dayjs", "format", "calculateAge", "birthDate", "today", "Date", "birthDate<PERSON><PERSON><PERSON>", "age", "getFullYear", "monthDifference", "getMonth", "getDate", "getClients", "query", "userId", "q", "page", "limit", "offset", "queryBuilder", "db", "selectFrom", "innerJoin", "leftJoin", "select", "where", "groupBy", "orderBy", "eb", "or", "data", "total", "Promise", "all", "execute", "fn", "countAll", "as", "executeTakeFirst", "status", "map", "row", "id", "name", "email", "photo", "protocol", "plan", "plan_status", "attendance", "date", "created_at", "pagination", "Number", "getClient", "client", "message", "week", "month", "sequence", "record", "getClientProtocols", "createClientProtocol", "createCoachClientProtocolDto", "type_id", "split", "frequency", "objective", "notes", "workouts", "new_protocol", "insertInto", "values", "client_id", "user_id", "started_at", "ended_at", "updated_at", "new_protocol_id", "insertId", "index", "workout", "entries", "exercise", "exercises", "importProtocol", "importCoachProtocolDto", "protocol_id", "protocol_template", "protocol_template_workouts", "general_notes", "getProtocols", "protocols", "getProtocol", "protocolId", "templateExercises", "selectAll", "workoutGroups", "for<PERSON>ach", "splitGroup", "split_group", "push", "exerciseId", "exercise_id", "muscle_group", "muscle_group_id", "equipment", "equipment_id", "media_url", "sets", "reps", "rpe", "restTime", "rest_seconds", "orderIndex", "length", "workoutsWithExercises", "Object", "keys", "splitLetter", "String", "fromCharCode", "parseInt", "createProtocol", "createCoachProtocolDto", "updateProtocol", "updateData", "existingProtocol", "updateTable", "set", "type", "Array", "isArray", "existingWorkouts", "deleteFrom", "letters", "workoutName", "newWorkout", "workoutId", "isCustomExercise", "toString", "startsWith", "workout_id", "exercise_name", "error", "console", "deleteProtocol"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATc;0BAER;+DACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB,IAAA,AAAMA,eAAN,MAAMA;IACTC,eAAeC,QAAa,EAAU;QAClC,OAAOC,OAAMD,UAAUE,MAAM,CAAC;IAClC;IAEAC,aAAaC,SAAe,EAAiB;QACzC,IAAI,CAACA,WAAW;YACd,OAAO;QACT;QAEA,MAAMC,QAAQ,IAAIC;QAClB,MAAMC,eAAe,IAAID,KAAKF;QAE9B,IAAII,MAAMH,MAAMI,WAAW,KAAKF,aAAaE,WAAW;QACxD,MAAMC,kBAAkBL,MAAMM,QAAQ,KAAKJ,aAAaI,QAAQ;QAEhE,IACED,kBAAkB,KACjBA,oBAAoB,KAAKL,MAAMO,OAAO,KAAKL,aAAaK,OAAO,IAChE;YACAJ;QACF;QAEA,OAAOA;IACT;IAEF,MAAMK,WAAWC,KAA8B,EAAEC,MAAc,EAAE;QAC7D,MAAM,EAAEC,CAAC,EAAE,GAAGF;QACd,IAAI,EAAEG,OAAO,CAAC,EAAEC,QAAQ,GAAG,EAAE,GAAGJ;QAChC,MAAMK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,IAAIE,eAAeC,YAAE,CAChBC,UAAU,CAAC,SACXC,SAAS,CAAC,eAAe,YAAY,uBACrCA,SAAS,CAAC,WAAW,YAAY,qBACjCC,QAAQ,CAAC,mBAAmB,2BAA2B,YACvDC,MAAM,CAAC;YACR;YACA;YACA;YACA;YACA;YACA;SAEC,EACAC,KAAK,CAAC,mBAAmB,KAAK,GAC9BA,KAAK,CAAC,mBAAmB,KAAKX,QAC9BY,OAAO,CAAC,YACRC,OAAO,CAAC,oBAAoB;QAEjC,IAAIZ,GAAG;YACHI,eAAeA,aAAaM,KAAK,CAAC,CAACG,KACnCA,GAAGC,EAAE,CAAC;oBACFD,GAAG,cAAc,QAAQ,CAAC,CAAC,EAAEb,EAAE,CAAC,CAAC;oBACjCa,GAAG,eAAe,QAAQ,CAAC,CAAC,EAAEb,EAAE,CAAC,CAAC;iBACrC;QAEL;QAEA,MAAM,CAACe,MAAMC,MAAM,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACpCd,aAAaF,KAAK,CAACA,OAAOC,MAAM,CAACA,QAAQgB,OAAO;YAChDd,YAAE,CAACC,UAAU,CAAC,SAASG,MAAM,CAACJ,YAAE,CAACe,EAAE,CAACC,QAAQ,GAAGC,EAAE,CAAC,UAAUC,gBAAgB;SAC/E;QAED,OAAO;YACHC,QAAQ;YACRT,MAAMA,KAAKU,GAAG,CAAC,CAACC,MAAS,CAAA;oBACzBC,IAAID,IAAIC,EAAE;oBACVC,MAAMF,IAAIE,IAAI;oBACdC,OAAOH,IAAIG,KAAK;oBAChBC,OAAOJ,IAAII,KAAK;oBAChBC,UAAUL,IAAIK,QAAQ;oBACtBC,MAAM;oBACNC,aAAa;oBACbC,YAAY;oBACZC,MAAM,IAAI,CAACpD,cAAc,CAAC2C,IAAIU,UAAU;gBACxC,CAAA;YACAC,YAAY;gBACZpC;gBACAC;gBACAc,OAAOsB,OAAOtB,OAAOA;YACrB;QACJ;IACJ;IAEA,MAAMuB,UAAUZ,EAAU,EAAE5B,MAAc,EAAE;QACxC,MAAMyC,SAAS,MAAMnC,YAAE,CAClBC,UAAU,CAAC,SACXC,SAAS,CAAC,WAAW,YAAY,qBACjCE,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAC,KAAK,CAAC,YAAY,KAAKiB,IACvBjB,KAAK,CAAC,qBAAqB,KAAKiB,IAChCjB,KAAK,CAAC,mBAAmB,KAAKX,QAC9BW,KAAK,CAAC,mBAAmB,KAAK,GAC9BC,OAAO,CAAC,YACRY,gBAAgB;QAEjB,IAAI,CAACiB,QAAQ;YACT,OAAO;gBACHhB,QAAQ;gBACRiB,SAAS;YACb;QACJ;QAEA,MAAMP,aAAa;YACfQ,MAAM;YACNC,OAAO;YACPC,UAAU;YACVC,QAAQ;QACZ;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAsDJ,GACJ;IAEA,MAAMC,mBAAmBnB,EAAU,EAAE5B,MAAc,EAAE;IACjD;;;;;;;;;;;;;;;;;;;;;;;;;;QA0BA,GACJ;IAEA,MAAMgD,qBAAqBpB,EAAU,EAAEqB,4BAA0D,EAAEjD,MAAc,EAAE;QAC/G,MAAM,EAAE6B,IAAI,EAAEqB,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GAAGN;QAExE,MAAMO,eAAe,MAAMlD,YAAE,CACxBmD,UAAU,CAAC,mBACXC,MAAM,CAAC;YACJC,WAAW/B;YACXgC,SAAS5D;YACT6B,MAAMA;YACNqB,SAASA;YACTC,OAAOA;YACPC,WAAWA;YACXC,WAAWA;YACX,gCAAgC;YAChCQ,YAAY,IAAItE;YAChBuE,UAAU;YACVzB,YAAY,IAAI9C;YAChBwE,YAAY,IAAIxE;QACpB,GACCiC,gBAAgB;QAErB,MAAMwC,kBAAkBzB,OAAOiB,aAAaS,QAAQ;QAEpD,KAAK,MAAM,CAACC,OAAOC,QAAQ,IAAIZ,SAASa,OAAO,GAAI;YAC/C,KAAK,MAAMC,YAAYF,QAAQG,SAAS,CAAE;YACtC;;;;;;;;;;;;;oBAaI,GACR;QACJ;QAEA,OAAO;YACH7C,QAAQ;YACRT,MAAM,EAAE;QACZ;IACJ;IAEA,MAAMuD,eAAe3C,EAAU,EAAE4C,sBAA8C,EAAExE,MAAc,EAAE;QAC7F,MAAM,EAAEyE,WAAW,EAAE,GAAGD;QAExB,MAAME,oBAAoB,MAAMpE,YAAE,CAC7BC,UAAU,CAAC,6BACXI,KAAK,CAAC,MAAM,KAAK8D,aACjB9D,KAAK,CAAC,WAAW,KAAKX,QACtBU,MAAM,CAAC;YAAC;YAAM;YAAQ;YAAW;YAAS;YAAa;YAAa;SAAQ,EAC5Ec,gBAAgB;QAErB,IAAI,CAACkD,mBAAmB;YACpB,OAAO;gBACHjD,QAAQ;gBACRiB,SAAS;YACb;QACJ;QAEA,MAAMiC,6BAA6B,MAAMrE,YAAE,CACtCC,UAAU,CAAC,sCACXI,KAAK,CAAC,eAAe,KAAK8D,aAC1B/D,MAAM,CAAC;YAAC;YAAM;YAAe;YAAe;YAAQ;YAAQ;YAAO;YAAgB;SAAQ,EAC3FU,OAAO;QAEZ,MAAMoC,eAAe,MAAMlD,YAAE,CACxBmD,UAAU,CAAC,mBACXC,MAAM,CAAC;YACJC,WAAW/B;YACXgC,SAAS5D;YACT6B,MAAM6C,kBAAkB7C,IAAI;YAC5BqB,SAASwB,kBAAkBxB,OAAO;YAClCC,OAAOuB,kBAAkBvB,KAAK;YAC9BC,WAAWsB,kBAAkBtB,SAAS;YACtCC,WAAWqB,kBAAkBrB,SAAS;YACtCuB,eAAeF,kBAAkBpB,KAAK;YACtCO,YAAY,IAAItE;YAChBuE,UAAU;YACVzB,YAAY,IAAI9C;YAChBwE,YAAY,IAAIxE;QACpB,GACCiC,gBAAgB;QAEjB,MAAMwC,kBAAkBzB,OAAOiB,aAAaS,QAAQ;QAEpD;;;;;;;;;;;;;;gBAcI,GAER,OAAO;YACHxC,QAAQ;YACRT,MAAM,EAAE;QACZ;IACJ;IAEA,MAAM6D,aAAa7E,MAAc,EAAE;QAC/B,MAAM8E,YAAY,MAAMxE,YAAE,CACzBC,UAAU,CAAC,kCACXE,QAAQ,CAAC,uBAAuB,QAAQ,aACxCE,KAAK,CAAC,aAAa,KAAKX,QACxBU,MAAM,CAAC;YAAC;YAAQ;YAAU;YAA0B;YAAW;YAAe;YAAe;YAAW;YAAgB;SAAe,EACvIG,OAAO,CAAC,QAAQ,QAChBO,OAAO;QAER,OAAO;YACHK,QAAQ;YACRT,MAAM8D;QACV;IACJ;IAEA,MAAMC,YAAYC,UAAkB,EAAEhF,MAAc,EAAE;QAClD,gCAAgC;QAChC,MAAMgC,WAAW,MAAM1B,YAAE,CACpBC,UAAU,CAAC,kCACXE,QAAQ,CAAC,uBAAuB,QAAQ,aACxCE,KAAK,CAAC,aAAa,KAAKX,QACxBW,KAAK,CAAC,QAAQ,KAAKqE,YACnBtE,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAc,gBAAgB;QAErB,IAAI,CAACQ,UAAU;YACX,OAAO;gBACHP,QAAQ;gBACRiB,SAAS;YACb;QACJ;QAEA,uEAAuE;QACvE,MAAMuC,oBAAoB,MAAM3E,YAAE,CAC7BC,UAAU,CAAC,4CACXE,QAAQ,CAAC,kBAAkB,QAAQ,kBACnCE,KAAK,CAAC,kBAAkB,KAAKqE,YAC7BE,SAAS,GACT9D,OAAO;QAEZ,6DAA6D;QAC7D,MAAM+D,gBAA0C,CAAC;QACjDF,kBAAkBG,OAAO,CAACf,CAAAA;YACtB,MAAMgB,aAAahB,SAASiB,WAAW;YACvC,IAAI,CAACH,aAAa,CAACE,WAAW,EAAE;gBAC5BF,aAAa,CAACE,WAAW,GAAG,EAAE;YAClC;YACAF,aAAa,CAACE,WAAW,CAACE,IAAI,CAAC;gBAC3B3D,IAAIyC,SAASzC,EAAE;gBACf4D,YAAYnB,SAASoB,WAAW;gBAChC5D,MAAMwC,SAASxC,IAAI;gBACnB6D,cAAcrB,SAASsB,eAAe;gBACtCC,WAAWvB,SAASwB,YAAY;gBAChCC,WAAWzB,SAASyB,SAAS;gBAC7BC,MAAM1B,SAAS0B,IAAI;gBACnBC,MAAM3B,SAAS2B,IAAI;gBACnBC,KAAK5B,SAAS4B,GAAG;gBACjBC,UAAU7B,SAAS8B,YAAY;gBAC/B7C,OAAOe,SAASf,KAAK;gBACrB8C,YAAYjB,aAAa,CAACE,WAAW,CAACgB,MAAM,GAAG;YACnD;QACJ;QAEA,+BAA+B;QAC/B,MAAMC,wBAAwBC,OAAOC,IAAI,CAACrB,eAAezD,GAAG,CAAC2D,CAAAA;YACzD,MAAMoB,cAAcC,OAAOC,YAAY,CAAC,KAAKC,SAASvB,cAAc,gBAAgB;YACpF,OAAO;gBACHzD,IAAIyD;gBACJxD,MAAM,CAAC,OAAO,EAAE4E,aAAa;gBAC7BnC,WAAWa,aAAa,CAACyB,SAASvB,YAAY;YAClD;QACJ;QAEA,OAAO;YACH5D,QAAQ;YACRT,MAAM;gBACF,GAAGgB,QAAQ;gBACXuB,UAAU+C;YACd;QACJ;IACJ;IAEA,MAAMO,eAAeC,sBAA8C,EAAE9G,MAAc,EAAE;QACjF,MAAM,EAAE6B,IAAI,EAAEqB,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GAAGuD;QAExE,MAAMtD,eAAe,MAAMlD,YAAE,CACxBmD,UAAU,CAAC,6BACXC,MAAM,CAAC;YACJ7B,MAAMA;YACNqB,SAASA;YACTC,OAAOA;YACPC,WAAWA;YACXC,WAAWA;YACXC,OAAOA;YACPM,SAAS5D;QACb,GACCwB,gBAAgB;QAEjB,MAAMwC,kBAAkBzB,OAAOiB,aAAaS,QAAQ;QAEpD,KAAK,MAAM,CAACC,OAAOC,QAAQ,IAAIZ,SAASa,OAAO,GAAI;YAC/C,MAAMlD,QAAQC,GAAG,CACbgD,QAAQG,SAAS,CAAC5C,GAAG,CAAC,OAAO2C;gBACzB,MAAM/D,YAAE,CACHmD,UAAU,CAAC,sCACXC,MAAM,CAAC;oBACJe,aAAaT;oBACbyB,aAAapB,SAASoB,WAAW;oBACjCH,aAAapB,QAAQ;oBACrB6B,MAAM1B,SAAS0B,IAAI;oBACnBC,MAAM3B,SAAS2B,IAAI;oBACnBC,KAAK5B,SAAS4B,GAAG;oBACjBE,cAAc9B,SAAS8B,YAAY;oBACnC7C,OAAOe,UAAUf;gBACrB,GACClC,OAAO;YAChB;QAER;QAGJ,OAAO;YACHK,QAAQ;YACRT,MAAM,EAAE;QACZ;IACJ;IAEA,MAAM+F,eAAe/B,UAAkB,EAAEgC,UAAe,EAAEhH,MAAc,EAAE;QACtE,wDAAwD;QACxD,MAAMiH,mBAAmB,MAAM3G,YAAE,CAC5BC,UAAU,CAAC,mBACXG,MAAM,CAAC;YAAC;YAAM;YAAa;YAAY;SAAa,EACpDC,KAAK,CAAC,MAAM,KAAKqE,YACjBrE,KAAK,CAAC,aAAa,KAAKX,QACxBwB,gBAAgB;QAErB,IAAI,CAACyF,kBAAkB;YACnB,OAAO;gBACHxF,QAAQ;gBACRiB,SAAS;YACb;QACJ;QAEA,iEAAiE;QACjE,IAAIuE,iBAAiBnD,QAAQ,EAAE;YAC3B,OAAO;gBACHrC,QAAQ;gBACRiB,SAAS;YACb;QACJ;QAEA,IAAI;YACA,uCAAuC;YACvC,MAAMpC,YAAE,CACH4G,WAAW,CAAC,mBACZC,GAAG,CAAC;gBACDtF,MAAMmF,WAAWnF,IAAI;gBACrBqB,SAAS8D,WAAWI,IAAI;gBACxBjE,OAAO6D,WAAW7D,KAAK;gBACvBC,WAAW4D,WAAW5D,SAAS;gBAC/BC,WAAW2D,WAAW3D,SAAS;gBAC/BuB,eAAeoC,WAAW1D,KAAK;gBAC/BS,YAAY,IAAIxE;YACpB,GACCoB,KAAK,CAAC,MAAM,KAAKqE,YACjB5D,OAAO;YAEZ,gCAAgC;YAChC,IAAI4F,WAAWzD,QAAQ,IAAI8D,MAAMC,OAAO,CAACN,WAAWzD,QAAQ,GAAG;gBAC3D,2CAA2C;gBAC3C,MAAMgE,mBAAmB,MAAMjH,YAAE,CAC5BC,UAAU,CAAC,4BACXG,MAAM,CAAC;oBAAC;iBAAK,EACbC,KAAK,CAAC,eAAe,KAAKqE,YAC1B5D,OAAO;gBAEZ,KAAK,MAAM+C,WAAWoD,iBAAkB;oBACpC,MAAMjH,YAAE,CAACkH,UAAU,CAAC,sCAAsC7G,KAAK,CAAC,cAAc,KAAKwD,QAAQvC,EAAE,EAAER,OAAO;gBAC1G;gBACA,MAAMd,YAAE,CAACkH,UAAU,CAAC,4BAA4B7G,KAAK,CAAC,eAAe,KAAKqE,YAAY5D,OAAO;gBAE7F,wCAAwC;gBACxC,MAAMqG,UAAU;gBAChB,KAAK,MAAM,CAACvD,OAAOC,QAAQ,IAAI6C,WAAWzD,QAAQ,CAACa,OAAO,GAAI;oBAC1D,MAAMsD,cAAcvD,QAAQtC,IAAI,IAAI,CAAC,OAAO,EAAE4F,OAAO,CAACvD,MAAM,EAAE;oBAE9D,MAAMyD,aAAa,MAAMrH,YAAE,CACtBmD,UAAU,CAAC,4BACXC,MAAM,CAAC;wBACJe,aAAaO;wBACbnD,MAAM6F;wBACNrF,YAAY,IAAI9C;wBAChBwE,YAAY,IAAIxE;oBACpB,GACCiC,gBAAgB;oBAErB,MAAMoG,YAAYrF,OAAOoF,WAAW1D,QAAQ;oBAE5C,IAAIE,QAAQG,SAAS,IAAI+C,MAAMC,OAAO,CAACnD,QAAQG,SAAS,GAAG;wBACvD,KAAK,MAAMD,YAAYF,QAAQG,SAAS,CAAE;4BACtC,sEAAsE;4BACtE,MAAMuD,mBAAmB,OAAOxD,SAASoB,WAAW,KAAK,YACrDpB,SAASoB,WAAW,CAACqC,QAAQ,GAAGC,UAAU,CAAC;4BAE/C,MAAMzH,YAAE,CACHmD,UAAU,CAAC,sCACXC,MAAM,CAAC;gCACJsE,YAAYJ;gCACZ,gEAAgE;gCAChEnC,aAAaoC,mBAAmB,OAAQxD,SAASA,QAAQ,EAAEzC,MAAMyC,SAASoB,WAAW;gCACrF5D,MAAMwC,SAASxC,IAAI,IAAIwC,SAAS4D,aAAa,IAAI5D,SAASA,QAAQ,EAAExC;gCACpEkE,MAAM1B,SAAS0B,IAAI;gCACnBC,MAAM3B,SAAS2B,IAAI;gCACnBC,KAAK5B,SAAS4B,GAAG;gCACjBE,cAAc9B,SAAS6B,QAAQ,IAAI7B,SAAS8B,YAAY;gCACxD7C,OAAOe,SAASf,KAAK;4BACzB,GACClC,OAAO;wBAChB;oBACJ;gBACJ;YACJ;YAEA,OAAO;gBACHK,QAAQ;gBACRiB,SAAS;gBACT1B,MAAM;oBAAEY,IAAIoD;gBAAW;YAC3B;QACJ,EAAE,OAAOkD,OAAO;YACZC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;gBACHzG,QAAQ;gBACRiB,SAAS;YACb;QACJ;IACJ;IAEA,MAAM0F,eAAexG,EAAU,EAAE5B,MAAc,EAAE;QAC7C,MAAMM,YAAE,CACHkH,UAAU,CAAC,6BACX7G,KAAK,CAAC,MAAM,KAAKiB,IACjBR,OAAO;QAEZ,MAAMd,YAAE,CACHkH,UAAU,CAAC,sCACX7G,KAAK,CAAC,eAAe,KAAKiB,IAC1BR,OAAO;QAEZ,OAAO;YACHK,QAAQ;YACRT,MAAM,EAAE;QACZ;IACJ;AAEJ"}