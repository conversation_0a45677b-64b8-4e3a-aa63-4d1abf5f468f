<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Meals API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .auth-status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            font-weight: bold;
        }
        .authenticated {
            background-color: #d4edda;
            color: #155724;
        }
        .not-authenticated {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Meals API - SnapFit</h1>
        
        <div id="auth-status" class="auth-status not-authenticated">
            Status: Não autenticado
        </div>

        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" placeholder="Digite seu email">
        </div>

        <div class="form-group">
            <label for="password">Senha:</label>
            <input type="password" id="password" value="Teste123" placeholder="Digite sua senha">
        </div>

        <button onclick="login()">Login</button>
        
        <hr style="margin: 30px 0;">
        
        <h3>Testes de API</h3>
        <button onclick="createTestProtocol()">0. Criar Protocolo de Teste</button>
        <button onclick="testActiveProtocol()">1. Testar Protocolo Ativo</button>
        <button onclick="testMealsToday()">2. Testar Refeições de Hoje</button>
        <button onclick="testMealsSpecificDate()">3. Testar Refeições Data Específica</button>
        <button onclick="testMealsMonday()">4. Testar Refeições Segunda-feira</button>
        <button onclick="runAllTests()">Executar Todos os Testes</button>

        <div id="result" class="result info" style="display: none;"></div>
    </div>

    <script>
        let authToken = localStorage.getItem('accessToken') || '';
        
        function updateAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            if (authToken) {
                statusDiv.textContent = 'Status: Autenticado ✓';
                statusDiv.className = 'auth-status authenticated';
            } else {
                statusDiv.textContent = 'Status: Não autenticado ✗';
                statusDiv.className = 'auth-status not-authenticated';
            }
        }
        
        function showResult(content, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = content;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                showResult('Fazendo login...', 'info');
                
                const response = await fetch('http://localhost:3000/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.data?.access_token) {
                    authToken = data.data.access_token;
                    localStorage.setItem('accessToken', authToken);
                    localStorage.setItem('refreshToken', data.data.refresh_token);
                    localStorage.setItem('deviceUid', data.data.device_uid);
                    updateAuthStatus();
                    showResult('Login realizado com sucesso!\n\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    throw new Error(data.message || 'Login failed');
                }
            } catch (error) {
                showResult('Erro no login: ' + error.message, 'error');
            }
        }
        
        async function testActiveProtocol() {
            if (!authToken) {
                showResult('Erro: Faça login primeiro', 'error');
                return;
            }
            
            try {
                showResult('Testando protocolo ativo...', 'info');
                
                const response = await fetch('http://localhost:3000/users/protocols/diet/active', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('Protocolo ativo:\n\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult('Erro ao buscar protocolo:\n\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Erro na requisição: ' + error.message, 'error');
            }
        }
        
        async function testMealsToday() {
            if (!authToken) {
                showResult('Erro: Faça login primeiro', 'error');
                return;
            }

            try {
                const today = new Date().toISOString().split('T')[0];
                const dateParam = `${today} 00:00:00`;
                const dayOfWeek = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

                showResult(`Testando refeições de hoje (${dateParam})...\nDia da semana: ${dayOfWeek}`, 'info');

                const response = await fetch(`http://localhost:3000/users/protocols/diet/meals/active?date=${encodeURIComponent(dateParam)}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    const result = `Refeições de hoje:\n\nData: ${dateParam}\nDia da semana: ${dayOfWeek}\nHas Protocol: ${data.data?.has_protocol}\nMeals Count: ${data.data?.meals?.length || 0}\n\n${JSON.stringify(data, null, 2)}`;
                    showResult(result, 'success');
                } else {
                    showResult('Erro ao buscar refeições:\n\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Erro na requisição: ' + error.message, 'error');
            }
        }
        
        async function testMealsSpecificDate() {
            if (!authToken) {
                showResult('Erro: Faça login primeiro', 'error');
                return;
            }
            
            try {
                const dateParam = '2024-01-15 00:00:00';
                
                showResult(`Testando refeições de data específica (${dateParam})...`, 'info');
                
                const response = await fetch(`http://localhost:3000/users/protocols/diet/meals/active?date=${encodeURIComponent(dateParam)}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('Refeições da data específica:\n\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult('Erro ao buscar refeições:\n\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Erro na requisição: ' + error.message, 'error');
            }
        }
        
        async function createTestProtocol() {
            if (!authToken) {
                showResult('Erro: Faça login primeiro', 'error');
                return;
            }

            try {
                showResult('Criando protocolo de teste...', 'info');

                // Get current day of week
                const currentDayOfWeek = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

                const testProtocolData = {
                    name: "Protocolo de Emagrecimento Demo",
                    type_id: 1, // Assuming 1 = weight loss
                    objective: "Perda de peso saudável mantendo massa muscular",
                    nutritional_goals: {
                        calories: 1800,
                        protein: 140,
                        carbs: 150,
                        fat: 60,
                        water: 2500
                    },
                    meals: [
                        // Monday meals
                        {
                            name: "Café da Manhã",
                            day_of_week: "monday",
                            meal_time: "07:00",
                            foods: [
                                {
                                    name: "Aveia",
                                    quantity: 50,
                                    unit: "g",
                                    calories: 190,
                                    protein: 6.9,
                                    carbs: 32.8,
                                    fat: 3.4,
                                    fiber: 5.0
                                }
                            ]
                        },
                        {
                            name: "Almoço",
                            day_of_week: "monday",
                            meal_time: "12:30",
                            foods: [
                                {
                                    name: "Peito de Frango",
                                    quantity: 150,
                                    unit: "g",
                                    calories: 248,
                                    protein: 46.2,
                                    carbs: 0,
                                    fat: 5.4,
                                    fiber: 0
                                }
                            ]
                        },
                        // Current day meals (if different from Monday)
                        {
                            name: "Café da Manhã",
                            day_of_week: currentDayOfWeek,
                            meal_time: "07:00",
                            foods: [
                                {
                                    name: "Aveia",
                                    quantity: 50,
                                    unit: "g",
                                    calories: 190,
                                    protein: 6.9,
                                    carbs: 32.8,
                                    fat: 3.4,
                                    fiber: 5.0
                                }
                            ]
                        },
                        {
                            name: "Almoço",
                            day_of_week: currentDayOfWeek,
                            meal_time: "12:30",
                            foods: [
                                {
                                    name: "Peito de Frango",
                                    quantity: 150,
                                    unit: "g",
                                    calories: 248,
                                    protein: 46.2,
                                    carbs: 0,
                                    fat: 5.4,
                                    fiber: 0
                                }
                            ]
                        },
                        {
                            name: "Jantar",
                            day_of_week: currentDayOfWeek,
                            meal_time: "19:00",
                            foods: [
                                {
                                    name: "Salmão",
                                    quantity: 120,
                                    unit: "g",
                                    calories: 208,
                                    protein: 25.4,
                                    carbs: 0,
                                    fat: 12.4,
                                    fiber: 0
                                }
                            ]
                        }
                    ],
                    supplements: [
                        {
                            name: "Whey Protein",
                            dosage: "30g",
                            supplement_time: "Pós-treino",
                            notes: "Misturar com 300ml de água"
                        }
                    ],
                    general_notes: "Protocolo de demonstração focado em emagrecimento saudável."
                };

                const response = await fetch('http://localhost:3000/users/protocols/diet', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testProtocolData)
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('Protocolo criado com sucesso!\n\n' + JSON.stringify(data, null, 2), 'success');
                } else {
                    showResult('Erro ao criar protocolo:\n\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Erro na requisição: ' + error.message, 'error');
            }
        }

        async function testMealsMonday() {
            if (!authToken) {
                showResult('Erro: Faça login primeiro', 'error');
                return;
            }

            try {
                // Get next Monday
                const today = new Date();
                const daysUntilMonday = (1 + 7 - today.getDay()) % 7;
                const nextMonday = new Date(today);
                nextMonday.setDate(today.getDate() + daysUntilMonday);

                const mondayDate = nextMonday.toISOString().split('T')[0];
                const dateParam = `${mondayDate} 00:00:00`;

                showResult(`Testando refeições de segunda-feira (${dateParam})...`, 'info');

                const response = await fetch(`http://localhost:3000/users/protocols/diet/meals/active?date=${encodeURIComponent(dateParam)}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    const result = `Refeições de segunda-feira:\n\nData: ${dateParam}\nHas Protocol: ${data.data?.has_protocol}\nMeals Count: ${data.data?.meals?.length || 0}\n\n${JSON.stringify(data, null, 2)}`;
                    showResult(result, 'success');
                } else {
                    showResult('Erro ao buscar refeições:\n\n' + JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                showResult('Erro na requisição: ' + error.message, 'error');
            }
        }

        async function runAllTests() {
            await testActiveProtocol();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testMealsToday();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testMealsSpecificDate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testMealsMonday();
        }
        
        // Initialize
        updateAuthStatus();
    </script>
</body>
</html>
