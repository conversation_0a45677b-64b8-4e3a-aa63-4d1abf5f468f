import React from 'react';
import { Check<PERSON>ir<PERSON>, Clock, Archive, Dumbbell } from 'lucide-react';

interface WorkoutProtocolBadgeProps {
  protocolName: string;
  protocolStatus: 'active' | 'completed' | 'archived';
  isCurrentProtocol: boolean;
  showProtocolName?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function WorkoutProtocolBadge({
  protocolName,
  protocolStatus,
  isCurrentProtocol,
  showProtocolName = true,
  size = 'md',
  className = ''
}: WorkoutProtocolBadgeProps) {
  
  const getStatusConfig = () => {
    if (isCurrentProtocol) {
      return {
        color: 'bg-snapfit-green/20 text-snapfit-green border-snapfit-green/30',
        icon: <Dumbbell className="w-3 h-3" />,
        label: 'Protocolo Atual',
        textColor: 'text-snapfit-green'
      };
    }
    
    switch (protocolStatus) {
      case 'completed':
        return {
          color: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
          icon: <CheckCircle className="w-3 h-3" />,
          label: 'Protocolo Finalizado',
          textColor: 'text-blue-400'
        };
      case 'archived':
        return {
          color: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
          icon: <Archive className="w-3 h-3" />,
          label: 'Protocolo Arquivado',
          textColor: 'text-gray-400'
        };
      default:
        return {
          color: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
          icon: <Clock className="w-3 h-3" />,
          label: 'Protocolo Anterior',
          textColor: 'text-orange-400'
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'lg':
        return 'px-4 py-2 text-sm';
      default:
        return 'px-3 py-1.5 text-xs';
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`inline-flex items-center gap-1.5 rounded-full border ${config.color} ${getSizeClasses()} ${className}`}>
      {config.icon}
      <span className="font-medium">
        {showProtocolName ? protocolName : config.label}
      </span>
    </div>
  );
}

// Componente para tooltip com informações detalhadas
interface WorkoutProtocolTooltipProps {
  protocolName: string;
  protocolStatus: 'active' | 'completed' | 'archived';
  protocolObjective?: string;
  protocolSplit?: string;
  protocolFrequency?: number;
  startedAt?: string;
  endedAt?: string;
  children: React.ReactNode;
}

export function WorkoutProtocolTooltip({
  protocolName,
  protocolStatus,
  protocolObjective,
  protocolSplit,
  protocolFrequency,
  startedAt,
  endedAt,
  children
}: WorkoutProtocolTooltipProps) {
  const [showTooltip, setShowTooltip] = React.useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {children}
      
      {showTooltip && (
        <div className="absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 p-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg shadow-lg">
          <div className="text-white text-sm space-y-2">
            <div className="font-semibold text-snapfit-green">{protocolName}</div>
            
            {protocolObjective && (
              <div>
                <span className="text-gray-400">Objetivo:</span>
                <div className="text-xs text-gray-300">{protocolObjective}</div>
              </div>
            )}
            
            <div className="flex gap-4 text-xs">
              {protocolSplit && (
                <div>
                  <span className="text-gray-400">Divisão:</span>
                  <div className="text-white">{protocolSplit}</div>
                </div>
              )}
              
              {protocolFrequency && (
                <div>
                  <span className="text-gray-400">Frequência:</span>
                  <div className="text-white">{protocolFrequency}x/semana</div>
                </div>
              )}
            </div>
            
            <div className="text-xs border-t border-gray-600 pt-2">
              <div className="flex justify-between">
                <span className="text-gray-400">Início:</span>
                <span className="text-white">{formatDate(startedAt)}</span>
              </div>
              {endedAt && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Fim:</span>
                  <span className="text-white">{formatDate(endedAt)}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* Tooltip arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-snapfit-dark-gray"></div>
        </div>
      )}
    </div>
  );
}
