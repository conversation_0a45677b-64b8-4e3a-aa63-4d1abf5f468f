"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "NotificationsController", {
    enumerable: true,
    get: function() {
        return NotificationsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _notificationsservice = require("./notifications.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let NotificationsController = class NotificationsController {
    async getNotifications(req, query) {
        const userId = req.user.userId;
        return this.notificationsService.getNotifications(userId, query);
    }
    async getUnreadCount(req) {
        const userId = req.user.userId;
        return this.notificationsService.getUnreadCount(userId);
    }
    async markAsRead(body, req) {
        const userId = req.user.userId;
        return this.notificationsService.markAsRead(body.notificationIds, userId);
    }
    async markAllAsRead(req) {
        const userId = req.user.userId;
        return this.notificationsService.markAllAsRead(userId);
    }
    async getNotificationSettings(req) {
        const userId = req.user.userId;
        return this.notificationsService.getNotificationSettings(userId);
    }
    async updateNotificationSettings(settings, req) {
        const userId = req.user.userId;
        return this.notificationsService.updateNotificationSettings(userId, settings);
    }
    async subscribeToPushNotifications(subscription, req) {
        const userId = req.user.userId;
        return this.notificationsService.subscribeToPushNotifications(userId, subscription);
    }
    async unsubscribeFromPushNotifications(body, req) {
        const userId = req.user.userId;
        return this.notificationsService.unsubscribeFromPushNotifications(userId, body.endpoint);
    }
    async sendTestNotification(req) {
        const userId = req.user.userId;
        return this.notificationsService.sendTestNotification(userId);
    }
    constructor(notificationsService){
        this.notificationsService = notificationsService;
    }
};
_ts_decorate([
    (0, _common.Get)(),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "getNotifications", null);
_ts_decorate([
    (0, _common.Get)('unread-count'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "getUnreadCount", null);
_ts_decorate([
    (0, _common.Post)('mark-read'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "markAsRead", null);
_ts_decorate([
    (0, _common.Post)('mark-all-read'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "markAllAsRead", null);
_ts_decorate([
    (0, _common.Get)('settings'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "getNotificationSettings", null);
_ts_decorate([
    (0, _common.Put)('settings'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "updateNotificationSettings", null);
_ts_decorate([
    (0, _common.Post)('push/subscribe'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "subscribeToPushNotifications", null);
_ts_decorate([
    (0, _common.Post)('push/unsubscribe'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "unsubscribeFromPushNotifications", null);
_ts_decorate([
    (0, _common.Post)('test'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], NotificationsController.prototype, "sendTestNotification", null);
NotificationsController = _ts_decorate([
    (0, _common.Controller)('notifications'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _notificationsservice.NotificationsService === "undefined" ? Object : _notificationsservice.NotificationsService
    ])
], NotificationsController);

//# sourceMappingURL=notifications.controller.js.map