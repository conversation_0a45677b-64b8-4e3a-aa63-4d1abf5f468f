import { Body, Controller, Delete, Get, Param, Post, Put, Query, Request, UseGuards } from '@nestjs/common';
import { GetCoachClientsQueryDto } from 'src/admin/dto/get-coach-clients-query.dto';
import { CoachService } from './coach.service';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { ImportCoachProtocolDto } from 'src/admin/dto/import-coach-protocol.dto';
import { CreateCoachProtocolDto } from 'src/admin/dto/create-coach-protocol.dto';
import { CreateCoachClientProtocolDto } from './dto/create-coach-client-protocol.dto';

@Controller('coach')
export class CoachController {
    constructor(private readonly coachService: CoachService) {}

    @Get('stats')
    getStats() {
        return {
            status: 'success',
            data: {
                total_clients: 99,
                active_protocols: 99,
                completion_rate: 99,
            },
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('clients')
    getClients(@Query() query: GetCoachClientsQueryDto, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.coachService.getClients(query, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('clients/:id')
    getClient(@Param('id') id: number, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.coachService.getClient(id, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('clients/:id/protocols')
    getClientProtocols(@Param('id') id: number, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.coachService.getClientProtocols(id, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('clients/:id/protocols/import')
    importProtocol(@Param('id') id: number, @Body() importCoachProtocolDto: ImportCoachProtocolDto, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.coachService.importProtocol(id, importCoachProtocolDto, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('clients/:id/protocols')
    createClientProtocol(@Param('id') id: number, @Body() createCoachClientProtocolDto: CreateCoachClientProtocolDto, @Request() req: any) {
        const userId: number = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.coachService.createClientProtocol(id, createCoachClientProtocolDto, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('protocols')
    getProtocols(@Request() req: any) {
        const userId: number = req.user?.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.coachService.getProtocols(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('protocols/:id')
    getProtocol(@Param('id') id: number, @Request() req: any) {
        const userId: number = req.user?.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.coachService.getProtocol(id, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('protocols')
    createProtocol(@Body() createCoachProtocolDto: CreateCoachProtocolDto, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.coachService.createProtocol(createCoachProtocolDto, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Put('protocols/:id')
    updateProtocol(@Param('id') id: number, @Body() updateData: any, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.coachService.updateProtocol(id, updateData, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Delete('protocols/:id')
    deleteProtocol(@Param('id') id: number, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.coachService.deleteProtocol(id, userId);
    }
}
