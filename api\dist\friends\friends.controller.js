"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "FriendsController", {
    enumerable: true,
    get: function() {
        return FriendsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _friendsservice = require("./friends.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let FriendsController = class FriendsController {
    async searchUsers(query, req) {
        const userId = req.user.userId;
        return this.friendsService.searchUsers(query, userId);
    }
    async getFriends(req) {
        const userId = req.user.userId;
        return this.friendsService.getFriends(userId);
    }
    async getFriendRequests(req) {
        const userId = req.user.userId;
        return this.friendsService.getFriendRequests(userId);
    }
    async sendFriendRequest(body, req) {
        const fromUserId = req.user.userId;
        return this.friendsService.sendFriendRequest(fromUserId, Number(body.userId));
    }
    async acceptFriendRequest(requestId, req) {
        const userId = req.user.userId;
        return this.friendsService.acceptFriendRequest(Number(requestId), userId);
    }
    async rejectFriendRequest(requestId, req) {
        const userId = req.user.userId;
        return this.friendsService.rejectFriendRequest(Number(requestId), userId);
    }
    async removeFriend(friendId, req) {
        const userId = req.user.userId;
        return this.friendsService.removeFriend(userId, Number(friendId));
    }
    async getFriendsRanking(req, query) {
        const userId = req.user.userId;
        return this.friendsService.getFriendsRanking(userId, query);
    }
    constructor(friendsService){
        this.friendsService = friendsService;
    }
};
_ts_decorate([
    (0, _common.Get)('search'),
    _ts_param(0, (0, _common.Query)('q')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "searchUsers", null);
_ts_decorate([
    (0, _common.Get)(),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "getFriends", null);
_ts_decorate([
    (0, _common.Get)('requests'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "getFriendRequests", null);
_ts_decorate([
    (0, _common.Post)('request'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "sendFriendRequest", null);
_ts_decorate([
    (0, _common.Post)('request/:id/accept'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "acceptFriendRequest", null);
_ts_decorate([
    (0, _common.Post)('request/:id/reject'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "rejectFriendRequest", null);
_ts_decorate([
    (0, _common.Delete)(':id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "removeFriend", null);
_ts_decorate([
    (0, _common.Get)('ranking'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], FriendsController.prototype, "getFriendsRanking", null);
FriendsController = _ts_decorate([
    (0, _common.Controller)('friends'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _friendsservice.FriendsService === "undefined" ? Object : _friendsservice.FriendsService
    ])
], FriendsController);

//# sourceMappingURL=friends.controller.js.map