{"version": 3, "sources": ["../../src/wearables/wearables.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Delete, \n  Body, \n  Param, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { WearablesService } from './wearables.service';\n\n@Controller('wearables')\n@UseGuards(JwtAuthGuard)\nexport class WearablesController {\n  constructor(private readonly wearablesService: WearablesService) {}\n\n  @Get('devices')\n  async getConnectedDevices(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.getConnectedDevices(userId);\n  }\n\n  @Get('supported')\n  async getSupportedDevices() {\n    return this.wearablesService.getSupportedDevices();\n  }\n\n  @Post('connect')\n  async connectDevice(@Body() deviceData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.connectDevice(userId, deviceData);\n  }\n\n  @Delete(':id')\n  async disconnectDevice(@Param('id') deviceId: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.disconnectDevice(userId, Number(deviceId));\n  }\n\n  @Get('data')\n  async getSyncedData(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.getSyncedData(userId, query);\n  }\n\n  @Post('sync')\n  async syncData(@Request() req: any, @Body() syncOptions?: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.syncData(userId, syncOptions);\n  }\n\n  @Get('data/steps')\n  async getStepsData(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.getStepsData(userId, query);\n  }\n\n  @Get('data/heart-rate')\n  async getHeartRateData(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.getHeartRateData(userId, query);\n  }\n\n  @Get('data/sleep')\n  async getSleepData(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.getSleepData(userId, query);\n  }\n\n  @Get('data/calories')\n  async getCaloriesData(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.getCaloriesData(userId, query);\n  }\n\n  @Post('data/manual')\n  async addManualData(@Body() data: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.addManualData(userId, data);\n  }\n\n  @Get('sync-status')\n  async getSyncStatus(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.getSyncStatus(userId);\n  }\n\n  @Post('oauth/fitbit')\n  async connectFitbit(@Body() authData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.connectFitbit(userId, authData);\n  }\n\n  @Post('oauth/garmin')\n  async connectGarmin(@Body() authData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.connectGarmin(userId, authData);\n  }\n\n  @Post('oauth/apple-health')\n  async connectAppleHealth(@Body() authData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.connectAppleHealth(userId, authData);\n  }\n\n  @Post('oauth/google-fit')\n  async connectGoogleFit(@Body() authData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.wearablesService.connectGoogleFit(userId, authData);\n  }\n}\n"], "names": ["WearablesController", "getConnectedDevices", "req", "userId", "user", "wearablesService", "getSupportedDevices", "connectDevice", "deviceData", "disconnectDevice", "deviceId", "Number", "getSyncedData", "query", "syncData", "syncOptions", "getStepsData", "getHeartRateData", "getSleepData", "getCaloriesData", "addManualData", "data", "getSyncStatus", "connectFitbit", "authData", "connectGarmin", "connectAppleHealth", "connectGoogleFit", "constructor"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;wBANN;8BACsB;kCACI;;;;;;;;;;;;;;;AAI1B,IAAA,AAAMA,sBAAN,MAAMA;IAGX,MACMC,oBAAoB,AAAWC,GAAQ,EAAE;QAC7C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACJ,mBAAmB,CAACE;IACnD;IAEA,MACMG,sBAAsB;QAC1B,OAAO,IAAI,CAACD,gBAAgB,CAACC,mBAAmB;IAClD;IAEA,MACMC,cAAc,AAAQC,UAAe,EAAE,AAAWN,GAAQ,EAAE;QAChE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACE,aAAa,CAACJ,QAAQK;IACrD;IAEA,MACMC,iBAAiB,AAAaC,QAAgB,EAAE,AAAWR,GAAQ,EAAE;QACzE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACI,gBAAgB,CAACN,QAAQQ,OAAOD;IAC/D;IAEA,MACME,cAAc,AAAWV,GAAQ,EAAE,AAASW,KAAU,EAAE;QAC5D,MAAMV,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACO,aAAa,CAACT,QAAQU;IACrD;IAEA,MACMC,SAAS,AAAWZ,GAAQ,EAAE,AAAQa,WAAiB,EAAE;QAC7D,MAAMZ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACS,QAAQ,CAACX,QAAQY;IAChD;IAEA,MACMC,aAAa,AAAWd,GAAQ,EAAE,AAASW,KAAU,EAAE;QAC3D,MAAMV,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACW,YAAY,CAACb,QAAQU;IACpD;IAEA,MACMI,iBAAiB,AAAWf,GAAQ,EAAE,AAASW,KAAU,EAAE;QAC/D,MAAMV,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACY,gBAAgB,CAACd,QAAQU;IACxD;IAEA,MACMK,aAAa,AAAWhB,GAAQ,EAAE,AAASW,KAAU,EAAE;QAC3D,MAAMV,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACa,YAAY,CAACf,QAAQU;IACpD;IAEA,MACMM,gBAAgB,AAAWjB,GAAQ,EAAE,AAASW,KAAU,EAAE;QAC9D,MAAMV,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACc,eAAe,CAAChB,QAAQU;IACvD;IAEA,MACMO,cAAc,AAAQC,IAAS,EAAE,AAAWnB,GAAQ,EAAE;QAC1D,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACe,aAAa,CAACjB,QAAQkB;IACrD;IAEA,MACMC,cAAc,AAAWpB,GAAQ,EAAE;QACvC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACiB,aAAa,CAACnB;IAC7C;IAEA,MACMoB,cAAc,AAAQC,QAAa,EAAE,AAAWtB,GAAQ,EAAE;QAC9D,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACkB,aAAa,CAACpB,QAAQqB;IACrD;IAEA,MACMC,cAAc,AAAQD,QAAa,EAAE,AAAWtB,GAAQ,EAAE;QAC9D,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACoB,aAAa,CAACtB,QAAQqB;IACrD;IAEA,MACME,mBAAmB,AAAQF,QAAa,EAAE,AAAWtB,GAAQ,EAAE;QACnE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACqB,kBAAkB,CAACvB,QAAQqB;IAC1D;IAEA,MACMG,iBAAiB,AAAQH,QAAa,EAAE,AAAWtB,GAAQ,EAAE;QACjE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACsB,gBAAgB,CAACxB,QAAQqB;IACxD;IA/FAI,YAAY,AAAiBvB,gBAAkC,CAAE;aAApCA,mBAAAA;IAAqC;AAgGpE"}