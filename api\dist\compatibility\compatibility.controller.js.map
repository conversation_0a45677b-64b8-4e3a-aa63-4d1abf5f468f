{"version": 3, "sources": ["../../src/compatibility/compatibility.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Put, \n  Delete, \n  Body, \n  Param, \n  Query, \n  Request, \n  UseGuards,\n  Redirect\n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\n\n/**\n * Controller para manter compatibilidade com o frontend\n * Redireciona rotas antigas para as novas estruturas\n */\n@Controller()\nexport class CompatibilityController {\n\n  // Redirect /user/data to /users/me\n  @Get('user/data')\n  @UseGuards(JwtAuthGuard)\n  @Redirect('/users/me', 301)\n  getUserData() {\n    // This will redirect to the new endpoint\n  }\n\n  // Redirect /subscriptions to /users/subscriptions\n  @Get('subscriptions')\n  @UseGuards(JwtAuthGuard)\n  @Redirect('/users/subscriptions', 301)\n  getSubscriptions() {\n    // This will redirect to the new endpoint\n  }\n\n  @Post('subscriptions/:id/cancel')\n  @UseGuards(JwtAuthGuard)\n  @Redirect('/users/subscriptions/:id/cancel', 301)\n  cancelSubscription() {\n    // This will redirect to the new endpoint\n  }\n\n  // Redirect /protocols/coach to /coach/protocols\n  @Get('protocols/coach')\n  @UseGuards(JwtAuthGuard)\n  @Redirect('/coach/protocols', 301)\n  getCoachProtocols() {\n    // This will redirect to the new endpoint\n  }\n\n  // Redirect /protocols/nutritionist to /nutritionist/protocols\n  @Get('protocols/nutritionist')\n  @UseGuards(JwtAuthGuard)\n  @Redirect('/nutritionist/protocols', 301)\n  getNutritionistProtocols() {\n    // This will redirect to the new endpoint\n  }\n\n  // Profile friends endpoint (redirect to friends)\n  @Get('profile/friends')\n  @UseGuards(JwtAuthGuard)\n  @Redirect('/friends', 301)\n  getProfileFriends() {\n    // This will redirect to the new endpoint\n  }\n}\n"], "names": ["CompatibilityController", "getUserData", "getSubscriptions", "cancelSubscription", "getCoachProtocols", "getNutritionistProtocols", "getProfileFriends"], "mappings": ";;;;+BAoBaA;;;eAAAA;;;wBARN;8BACsB;;;;;;;;;;AAOtB,IAAA,AAAMA,0BAAN,MAAMA;IAEX,mCAAmC;IAInCC,cAAc;IACZ,yCAAyC;IAC3C;IAEA,kDAAkD;IAIlDC,mBAAmB;IACjB,yCAAyC;IAC3C;IAKAC,qBAAqB;IACnB,yCAAyC;IAC3C;IAEA,gDAAgD;IAIhDC,oBAAoB;IAClB,yCAAyC;IAC3C;IAEA,8DAA8D;IAI9DC,2BAA2B;IACzB,yCAAyC;IAC3C;IAEA,iDAAiD;IAIjDC,oBAAoB;IAClB,yCAAyC;IAC3C;AACF"}