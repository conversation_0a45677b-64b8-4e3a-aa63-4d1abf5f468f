import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { db } from '../database';

@Injectable()
export class ClientsService {
  
  async getClients(professionalId: number, query: any) {
    try {
      const { search, page = 1, limit = 10, status } = query;
      const offset = (page - 1) * limit;

      let sql = `
        SELECT 
          u.id,
          u.name,
          u.email,
          u.phone,
          u.photo,
          u.created_at as createdAt,
          u.updated_at as updatedAt,
          up.height,
          up.weight,
          up.activity_level as activityLevel,
          up.goals,
          up.medical_conditions as medicalConditions,
          up.allergies,
          pc.created_at as relationshipCreatedAt,
          pc.status as relationshipStatus,
          pc.notes
        FROM users u
        INNER JOIN professional_clients pc ON u.id = pc.client_id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE pc.professional_id = ?
      `;

      const params: any[] = [professionalId];

      if (search) {
        sql += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
        params.push(`%${search}%`, `%${search}%`);
      }

      if (status) {
        sql += ` AND pc.status = ?`;
        params.push(status);
      }

      sql += ` ORDER BY u.name ASC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const clients = await db.execute(sql, params);

      // Get total count for pagination
      let countSql = `
        SELECT COUNT(*) as total
        FROM users u
        INNER JOIN professional_clients pc ON u.id = pc.client_id
        WHERE pc.professional_id = ?
      `;
      const countParams: any[] = [professionalId];

      if (search) {
        countSql += ` AND (u.name LIKE ? OR u.email LIKE ?)`;
        countParams.push(`%${search}%`, `%${search}%`);
      }

      if (status) {
        countSql += ` AND pc.status = ?`;
        countParams.push(status);
      }

      const [countResult] = await db.execute(countSql, countParams);
      const total = countResult[0]?.total || 0;

      return {
        status: 'success',
        data: {
          clients: clients[0],
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      };
    } catch (error) {
      console.error('Error getting clients:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get clients'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getClient(clientId: number, professionalId: number) {
    try {
      const sql = `
        SELECT 
          u.id,
          u.name,
          u.email,
          u.phone,
          u.photo,
          u.created_at as createdAt,
          u.updated_at as updatedAt,
          up.height,
          up.weight,
          up.activity_level as activityLevel,
          up.goals,
          up.medical_conditions as medicalConditions,
          up.allergies,
          pc.created_at as relationshipCreatedAt,
          pc.status as relationshipStatus,
          pc.notes
        FROM users u
        INNER JOIN professional_clients pc ON u.id = pc.client_id
        LEFT JOIN user_profiles up ON u.id = up.user_id
        WHERE u.id = ? AND pc.professional_id = ?
      `;

      const [result] = await db.execute(sql, [clientId, professionalId]);
      
      if (!result[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      return {
        status: 'success',
        data: result[0]
      };
    } catch (error) {
      console.error('Error getting client:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to get client'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async createClient(createClientDto: CreateClientDto, professionalId: number) {
    try {
      // Check if email already exists
      const [existingUser] = await db.execute(
        'SELECT id FROM users WHERE email = ?',
        [createClientDto.email]
      );

      if (existingUser[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Email already exists'
        }, HttpStatus.CONFLICT);
      }

      // Create user
      const [userResult] = await db.execute(
        `INSERT INTO users (name, email, password, phone, photo, role, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, 'client', NOW(), NOW())`,
        [
          createClientDto.name,
          createClientDto.email,
          createClientDto.password, // Should be hashed in production
          createClientDto.phone || null,
          createClientDto.photo || null
        ]
      );

      const userId = userResult.insertId;

      // Create user profile
      if (createClientDto.height || createClientDto.weight || createClientDto.activityLevel) {
        await db.execute(
          `INSERT INTO user_profiles (user_id, height, weight, activity_level, goals, medical_conditions, allergies, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
          [
            userId,
            createClientDto.height || null,
            createClientDto.weight || null,
            createClientDto.activityLevel || null,
            createClientDto.goals || null,
            createClientDto.medicalConditions || null,
            createClientDto.allergies || null
          ]
        );
      }

      // Create professional-client relationship
      await db.execute(
        `INSERT INTO professional_clients (professional_id, client_id, status, notes, created_at, updated_at)
         VALUES (?, ?, 'active', ?, NOW(), NOW())`,
        [professionalId, userId, createClientDto.notes || null]
      );

      return {
        status: 'success',
        message: 'Client created successfully',
        data: {
          id: userId,
          ...createClientDto
        }
      };
    } catch (error) {
      console.error('Error creating client:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to create client'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateClient(clientId: number, updateClientDto: UpdateClientDto, professionalId: number) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // Update user basic info
      const userFields = ['name', 'email', 'phone', 'photo'];
      const userUpdates: string[] = [];
      const userParams: any[] = [];

      userFields.forEach(field => {
        if (updateClientDto[field] !== undefined) {
          userUpdates.push(`${field} = ?`);
          userParams.push(updateClientDto[field]);
        }
      });

      if (userUpdates.length > 0) {
        userParams.push(clientId);
        await db.execute(
          `UPDATE users SET ${userUpdates.join(', ')}, updated_at = NOW() WHERE id = ?`,
          userParams
        );
      }

      // Update user profile
      const profileFields = ['height', 'weight', 'activityLevel', 'goals', 'medicalConditions', 'allergies'];
      const profileUpdates: string[] = [];
      const profileParams: any[] = [];

      profileFields.forEach(field => {
        if (updateClientDto[field] !== undefined) {
          const dbField = field === 'activityLevel' ? 'activity_level' : 
                         field === 'medicalConditions' ? 'medical_conditions' : field;
          profileUpdates.push(`${dbField} = ?`);
          profileParams.push(updateClientDto[field]);
        }
      });

      if (profileUpdates.length > 0) {
        profileParams.push(clientId);
        
        // Check if profile exists
        const [profileExists] = await db.execute(
          'SELECT id FROM user_profiles WHERE user_id = ?',
          [clientId]
        );

        if (profileExists[0]) {
          await db.execute(
            `UPDATE user_profiles SET ${profileUpdates.join(', ')}, updated_at = NOW() WHERE user_id = ?`,
            profileParams
          );
        } else {
          // Create profile if it doesn't exist
          const profileColumns = profileFields
            .filter(field => updateClientDto[field] !== undefined)
            .map(field => field === 'activityLevel' ? 'activity_level' : 
                         field === 'medicalConditions' ? 'medical_conditions' : field);
          
          const profileValues = profileFields
            .filter(field => updateClientDto[field] !== undefined)
            .map(field => updateClientDto[field]);

          await db.execute(
            `INSERT INTO user_profiles (user_id, ${profileColumns.join(', ')}, created_at, updated_at)
             VALUES (?, ${profileColumns.map(() => '?').join(', ')}, NOW(), NOW())`,
            [clientId, ...profileValues]
          );
        }
      }

      // Update notes in professional_clients relationship
      if (updateClientDto.notes !== undefined) {
        await db.execute(
          'UPDATE professional_clients SET notes = ?, updated_at = NOW() WHERE professional_id = ? AND client_id = ?',
          [updateClientDto.notes, professionalId, clientId]
        );
      }

      return {
        status: 'success',
        message: 'Client updated successfully'
      };
    } catch (error) {
      console.error('Error updating client:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to update client'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async deleteClient(clientId: number, professionalId: number) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // Instead of deleting, we'll mark the relationship as inactive
      await db.execute(
        'UPDATE professional_clients SET status = "inactive", updated_at = NOW() WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      return {
        status: 'success',
        message: 'Client relationship deactivated successfully'
      };
    } catch (error) {
      console.error('Error deleting client:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to delete client'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getClientProtocols(clientId: number, professionalId: number) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // Get workout protocols
      const [workoutProtocols] = await db.execute(
        `SELECT id, name, description, status, created_at as createdAt, updated_at as updatedAt
         FROM user_protocols_workout 
         WHERE user_id = ? AND created_by = ?
         ORDER BY created_at DESC`,
        [clientId, professionalId]
      );

      // Get diet protocols
      const [dietProtocols] = await db.execute(
        `SELECT id, name, description, status, created_at as createdAt, updated_at as updatedAt
         FROM user_protocols_diet 
         WHERE user_id = ? AND created_by = ?
         ORDER BY created_at DESC`,
        [clientId, professionalId]
      );

      return {
        status: 'success',
        data: {
          workoutProtocols: workoutProtocols || [],
          dietProtocols: dietProtocols || []
        }
      };
    } catch (error) {
      console.error('Error getting client protocols:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to get client protocols'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getClientAssessments(clientId: number, professionalId: number) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      const [assessments] = await db.execute(
        `SELECT id, weight, bf as bodyFat, front_image as frontImage, back_image as backImage, 
                side_image as sideImage, created_at as createdAt
         FROM user_progress_evaluations 
         WHERE user_id = ?
         ORDER BY created_at DESC`,
        [clientId]
      );

      return {
        status: 'success',
        data: assessments || []
      };
    } catch (error) {
      console.error('Error getting client assessments:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to get client assessments'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async createClientProtocol(clientId: number, protocolData: any, professionalId: number) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // This would delegate to the appropriate protocol creation service
      // For now, return a success response
      return {
        status: 'success',
        message: 'Protocol created successfully',
        data: {
          id: Date.now(),
          ...protocolData,
          clientId,
          createdBy: professionalId
        }
      };
    } catch (error) {
      console.error('Error creating client protocol:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to create client protocol'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getClientProgress(clientId: number, professionalId: number, query: any) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // This would return comprehensive progress data
      // For now, return mock structure
      return {
        status: 'success',
        data: {
          weight: [],
          bodyFat: [],
          measurements: [],
          workouts: [],
          nutrition: []
        }
      };
    } catch (error) {
      console.error('Error getting client progress:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to get client progress'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getClientNutritionData(clientId: number, professionalId: number, query: any) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // This would return nutrition tracking data
      return {
        status: 'success',
        data: {
          dailyIntake: [],
          macros: [],
          adherence: []
        }
      };
    } catch (error) {
      console.error('Error getting client nutrition data:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to get client nutrition data'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getClientWorkoutData(clientId: number, professionalId: number, query: any) {
    try {
      // Verify access to client
      const [accessCheck] = await db.execute(
        'SELECT id FROM professional_clients WHERE professional_id = ? AND client_id = ?',
        [professionalId, clientId]
      );

      if (!accessCheck[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Client not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // This would return workout tracking data
      return {
        status: 'success',
        data: {
          workouts: [],
          volume: [],
          strength: []
        }
      };
    } catch (error) {
      console.error('Error getting client workout data:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to get client workout data'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
