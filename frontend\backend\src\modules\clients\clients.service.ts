import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole } from '../users/entities/user.entity';
import { Protocol } from '../protocols/entities/protocol.entity';
import { Assessment } from '../progress/entities/assessment.entity';
import { CreateClientDto } from './dto/create-client.dto';
import { UpdateClientDto } from './dto/update-client.dto';
import { ProtocolType } from '../protocols/enums/protocol-type.enum';
import * as bcrypt from 'bcrypt';

@Injectable()
export class ClientsService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Protocol)
    private readonly protocolRepository: Repository<Protocol>,
    @InjectRepository(Assessment)
    private readonly assessmentRepository: Repository<Assessment>
  ) {}

  async findAll(nutritionistId: string, search?: string) {
    const query = this.userRepository
      .createQueryBuilder('user')
      .where('user.nutritionistId = :nutritionistId', { nutritionistId });

    if (search) {
      query.andWhere('(user.name ILIKE :search OR user.email ILIKE :search)', {
        search: `%${search}%`
      });
    }

    query
      .leftJoinAndSelect('user.currentDietProtocol', 'currentDietProtocol')
      .orderBy('user.name', 'ASC');

    return query.getMany();
  }

  async findOne(nutritionistId: string, id: string) {
    const client = await this.userRepository.findOne({
      where: {
        id,
        nutritionist: { id: nutritionistId }
      },
      relations: ['currentDietProtocol']
    });

    if (!client) {
      throw new NotFoundException('Client not found');
    }

    return client;
  }

  async create(nutritionistId: string, createClientDto: CreateClientDto) {
    const hashedPassword = await bcrypt.hash(createClientDto.password, 10);

    const client = this.userRepository.create({
      ...createClientDto,
      password: hashedPassword,
      role: UserRole.USER,
      nutritionist: { id: nutritionistId }
    });

    return this.userRepository.save(client);
  }

  async update(nutritionistId: string, id: string, updateClientDto: UpdateClientDto) {
    const client = await this.findOne(nutritionistId, id);

    if (updateClientDto.password) {
      updateClientDto.password = await bcrypt.hash(updateClientDto.password, 10);
    }

    Object.assign(client, updateClientDto);
    return this.userRepository.save(client);
  }

  async remove(nutritionistId: string, id: string) {
    const client = await this.findOne(nutritionistId, id);
    await this.userRepository.remove(client);
  }

  async getProtocols(nutritionistId: string, clientId: string) {
    const client = await this.findOne(nutritionistId, clientId);

    return this.protocolRepository.find({
      where: {
        user: { id: client.id },
        createdBy: { id: nutritionistId },
        type: ProtocolType.DIET
      },
      order: {
        startDate: 'DESC'
      }
    });
  }

  async getAssessments(nutritionistId: string, clientId: string) {
    const client = await this.findOne(nutritionistId, clientId);

    return this.assessmentRepository.find({
      where: {
        user: { id: client.id }
      },
      order: {
        date: 'DESC'
      }
    });
  }
}