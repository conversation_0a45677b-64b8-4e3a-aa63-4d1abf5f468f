{"version": 3, "sources": ["../../src/auth/auth.module.ts"], "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\r\nimport { AuthController } from './auth.controller';\r\nimport { AuthService } from './auth.service';\r\nimport { JwtStrategy } from './jwt.strategy';\r\nimport { GoogleStrategy } from './google.strategy';\r\nimport { AppleStrategy } from './apple.strategy';\r\nimport { JwtModule } from '@nestjs/jwt';\r\nimport * as dotenv from 'dotenv';\r\n\r\ndotenv.config();\r\n\r\n@Module({\r\n  imports: [\r\n  JwtModule.register({\r\n    secret: process.env.JWT_SECRET,\r\n    signOptions: { expiresIn: process.env.JWT_EXPIRATION_TIME },\r\n  }),],\r\n  controllers: [AuthController],\r\n  providers: [AuthService, JwtStrategy, GoogleStrategy, AppleStrategy],\r\n})\r\nexport class AuthModule {}"], "names": ["AuthModule", "dotenv", "config", "imports", "JwtModule", "register", "secret", "process", "env", "JWT_SECRET", "signOptions", "expiresIn", "JWT_EXPIRATION_TIME", "controllers", "AuthController", "providers", "AuthService", "JwtStrategy", "GoogleStrategy", "AppleStrategy"], "mappings": ";;;;+BAoBaA;;;eAAAA;;;wBApBU;gCACQ;6BACH;6BACA;gCACG;+BACD;qBACJ;gEACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExBC,QAAOC,MAAM;AAWN,IAAA,AAAMF,aAAN,MAAMA;AAAY;;;QARvBG,SAAS;YACTC,cAAS,CAACC,QAAQ,CAAC;gBACjBC,QAAQC,QAAQC,GAAG,CAACC,UAAU;gBAC9BC,aAAa;oBAAEC,WAAWJ,QAAQC,GAAG,CAACI,mBAAmB;gBAAC;YAC5D;SAAI;QACJC,aAAa;YAACC,8BAAc;SAAC;QAC7BC,WAAW;YAACC,wBAAW;YAAEC,wBAAW;YAAEC,8BAAc;YAAEC,4BAAa;SAAC"}