{"version": 3, "sources": ["../../src/friends/friends.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Delete, \n  Body, \n  Param, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { FriendsService } from './friends.service';\n\n@Controller('friends')\n@UseGuards(JwtAuthGuard)\nexport class FriendsController {\n  constructor(private readonly friendsService: FriendsService) {}\n\n  @Get('search')\n  async searchUsers(@Query('q') query: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.friendsService.searchUsers(query, userId);\n  }\n\n  @Get()\n  async getFriends(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.friendsService.getFriends(userId);\n  }\n\n  @Get('requests')\n  async getFriendRequests(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.friendsService.getFriendRequests(userId);\n  }\n\n  @Post('request')\n  async sendFriendRequest(@Body() body: { userId: string }, @Request() req: any) {\n    const fromUserId = req.user.userId;\n    return this.friendsService.sendFriendRequest(fromUserId, Number(body.userId));\n  }\n\n  @Post('request/:id/accept')\n  async acceptFriendRequest(@Param('id') requestId: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.friendsService.acceptFriendRequest(Number(requestId), userId);\n  }\n\n  @Post('request/:id/reject')\n  async rejectFriendRequest(@Param('id') requestId: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.friendsService.rejectFriendRequest(Number(requestId), userId);\n  }\n\n  @Delete(':id')\n  async removeFriend(@Param('id') friendId: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.friendsService.removeFriend(userId, Number(friendId));\n  }\n\n  @Get('ranking')\n  async getFriendsRanking(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.friendsService.getFriendsRanking(userId, query);\n  }\n}\n"], "names": ["FriendsController", "searchUsers", "query", "req", "userId", "user", "friendsService", "getFriends", "getFriendRequests", "sendFriendRequest", "body", "fromUserId", "Number", "acceptFriendRequest", "requestId", "rejectFriendRequest", "removeFriend", "friendId", "getFriendsRanking", "constructor"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;wBANN;8BACsB;gCACE;;;;;;;;;;;;;;;AAIxB,IAAA,AAAMA,oBAAN,MAAMA;IAGX,MACMC,YAAY,AAAYC,KAAa,EAAE,AAAWC,GAAQ,EAAE;QAChE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACL,WAAW,CAACC,OAAOE;IAChD;IAEA,MACMG,WAAW,AAAWJ,GAAQ,EAAE;QACpC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACC,UAAU,CAACH;IACxC;IAEA,MACMI,kBAAkB,AAAWL,GAAQ,EAAE;QAC3C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACE,iBAAiB,CAACJ;IAC/C;IAEA,MACMK,kBAAkB,AAAQC,IAAwB,EAAE,AAAWP,GAAQ,EAAE;QAC7E,MAAMQ,aAAaR,IAAIE,IAAI,CAACD,MAAM;QAClC,OAAO,IAAI,CAACE,cAAc,CAACG,iBAAiB,CAACE,YAAYC,OAAOF,KAAKN,MAAM;IAC7E;IAEA,MACMS,oBAAoB,AAAaC,SAAiB,EAAE,AAAWX,GAAQ,EAAE;QAC7E,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACO,mBAAmB,CAACD,OAAOE,YAAYV;IACpE;IAEA,MACMW,oBAAoB,AAAaD,SAAiB,EAAE,AAAWX,GAAQ,EAAE;QAC7E,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACS,mBAAmB,CAACH,OAAOE,YAAYV;IACpE;IAEA,MACMY,aAAa,AAAaC,QAAgB,EAAE,AAAWd,GAAQ,EAAE;QACrE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACU,YAAY,CAACZ,QAAQQ,OAAOK;IACzD;IAEA,MACMC,kBAAkB,AAAWf,GAAQ,EAAE,AAASD,KAAU,EAAE;QAChE,MAAME,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACY,iBAAiB,CAACd,QAAQF;IACvD;IAhDAiB,YAAY,AAAiBb,cAA8B,CAAE;aAAhCA,iBAAAA;IAAiC;AAiDhE"}