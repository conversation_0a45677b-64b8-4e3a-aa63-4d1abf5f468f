{"version": 3, "sources": ["../../src/challenges/challenges.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db, executeRawSql } from '../database';\n\n@Injectable()\nexport class ChallengesService {\n\n  async getChallenges(userId: number, query: any) {\n    try {\n      const { status = 'active', category, page = 1, limit = 20 } = query;\n      const offset = (page - 1) * limit;\n\n      let sql = `\n        SELECT \n          c.id,\n          c.title,\n          c.description,\n          c.category,\n          c.type,\n          c.target_value as targetValue,\n          c.target_unit as targetUnit,\n          c.duration_days as durationDays,\n          c.start_date as startDate,\n          c.end_date as endDate,\n          c.status,\n          c.reward_points as rewardPoints,\n          c.reward_description as rewardDescription,\n          c.image_url as imageUrl,\n          c.created_at as createdAt,\n          COUNT(cp.user_id) as participantCount,\n          CASE WHEN cp_user.user_id IS NOT NULL THEN true ELSE false END as isParticipating\n        FROM challenges c\n        LEFT JOIN challenge_participants cp ON c.id = cp.challenge_id\n        LEFT JOIN challenge_participants cp_user ON c.id = cp_user.challenge_id AND cp_user.user_id = ?\n        WHERE c.status = ?\n      `;\n\n      const params = [userId, status];\n\n      if (category) {\n        sql += ` AND c.category = ?`;\n        params.push(category);\n      }\n\n      sql += ` GROUP BY c.id ORDER BY c.created_at DESC LIMIT ? OFFSET ?`;\n      params.push(limit, offset);\n\n      const challenges = await executeRawSql(sql, params);\n\n      // Get total count\n      let countSql = `SELECT COUNT(*) as total FROM challenges WHERE status = ?`;\n      const countParams = [status];\n\n      if (category) {\n        countSql += ` AND category = ?`;\n        countParams.push(category);\n      }\n\n      const countResult = await executeRawSql(countSql, countParams);\n      const total = countResult[0]?.total || 0;\n\n      return {\n        status: 'success',\n        data: {\n          challenges,\n          pagination: {\n            page: Number(page),\n            limit: Number(limit),\n            total,\n            totalPages: Math.ceil(total / limit)\n          }\n        }\n      };\n    } catch (error) {\n      console.error('Error getting challenges:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get challenges'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getMyChallenges(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          c.id,\n          c.title,\n          c.description,\n          c.category,\n          c.type,\n          c.target_value as targetValue,\n          c.target_unit as targetUnit,\n          c.duration_days as durationDays,\n          c.start_date as startDate,\n          c.end_date as endDate,\n          c.status,\n          c.reward_points as rewardPoints,\n          c.reward_description as rewardDescription,\n          c.image_url as imageUrl,\n          cp.joined_at as joinedAt,\n          cp.current_progress as currentProgress,\n          cp.completed_at as completedAt,\n          cp.status as participationStatus\n        FROM challenges c\n        INNER JOIN challenge_participants cp ON c.id = cp.challenge_id\n        WHERE cp.user_id = ?\n        ORDER BY cp.joined_at DESC\n      `;\n\n      const challenges = await executeRawSql(sql, [userId]);\n\n      return {\n        status: 'success',\n        data: challenges\n      };\n    } catch (error) {\n      console.error('Error getting my challenges:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get my challenges'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getChallenge(challengeId: number, userId: number) {\n    try {\n      const sql = `\n        SELECT \n          c.id,\n          c.title,\n          c.description,\n          c.category,\n          c.type,\n          c.target_value as targetValue,\n          c.target_unit as targetUnit,\n          c.duration_days as durationDays,\n          c.start_date as startDate,\n          c.end_date as endDate,\n          c.status,\n          c.reward_points as rewardPoints,\n          c.reward_description as rewardDescription,\n          c.image_url as imageUrl,\n          c.rules,\n          c.created_at as createdAt,\n          COUNT(cp.user_id) as participantCount,\n          CASE WHEN cp_user.user_id IS NOT NULL THEN true ELSE false END as isParticipating,\n          cp_user.current_progress as myProgress,\n          cp_user.joined_at as myJoinedAt,\n          cp_user.status as myStatus\n        FROM challenges c\n        LEFT JOIN challenge_participants cp ON c.id = cp.challenge_id\n        LEFT JOIN challenge_participants cp_user ON c.id = cp_user.challenge_id AND cp_user.user_id = ?\n        WHERE c.id = ?\n        GROUP BY c.id\n      `;\n\n      const result = await executeRawSql(sql, [userId, challengeId]);\n\n      if (!result[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Challenge not found'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      return {\n        status: 'success',\n        data: result[0]\n      };\n    } catch (error) {\n      console.error('Error getting challenge:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get challenge'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async createChallenge(challengeData: any, userId: number) {\n    try {\n      const {\n        title,\n        description,\n        category,\n        type,\n        targetValue,\n        targetUnit,\n        durationDays,\n        rewardPoints,\n        rewardDescription,\n        imageUrl,\n        rules\n      } = challengeData;\n\n      const startDate = new Date();\n      const endDate = new Date(startDate.getTime() + (durationDays * 24 * 60 * 60 * 1000));\n\n      const [result] = await db.execute(\n        `INSERT INTO challenges (\n          title, description, category, type, target_value, target_unit,\n          duration_days, start_date, end_date, status, reward_points,\n          reward_description, image_url, rules, created_by, created_at, updated_at\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, ?, ?, NOW(), NOW())`,\n        [\n          title, description, category, type, targetValue, targetUnit,\n          durationDays, startDate.toISOString(), endDate.toISOString(),\n          rewardPoints, rewardDescription, imageUrl, rules, userId\n        ]\n      );\n\n      // Auto-join the creator\n      await db.execute(\n        `INSERT INTO challenge_participants (challenge_id, user_id, joined_at, status)\n         VALUES (?, ?, NOW(), 'active')`,\n        [result.insertId, userId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Challenge created successfully',\n        data: {\n          id: result.insertId,\n          ...challengeData\n        }\n      };\n    } catch (error) {\n      console.error('Error creating challenge:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to create challenge'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async joinChallenge(challengeId: number, userId: number) {\n    try {\n      // Check if challenge exists and is active\n      const [challenge] = await db.execute(\n        'SELECT id, status, end_date FROM challenges WHERE id = ?',\n        [challengeId]\n      );\n\n      if (!challenge[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Challenge not found'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      if (challenge[0].status !== 'active') {\n        throw new HttpException({\n          status: 'error',\n          message: 'Challenge is not active'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      if (new Date(challenge[0].end_date) < new Date()) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Challenge has ended'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      // Check if already participating\n      const [existing] = await db.execute(\n        'SELECT id FROM challenge_participants WHERE challenge_id = ? AND user_id = ?',\n        [challengeId, userId]\n      );\n\n      if (existing[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Already participating in this challenge'\n        }, HttpStatus.CONFLICT);\n      }\n\n      // Join challenge\n      await db.execute(\n        `INSERT INTO challenge_participants (challenge_id, user_id, joined_at, status, current_progress)\n         VALUES (?, ?, NOW(), 'active', 0)`,\n        [challengeId, userId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Successfully joined challenge'\n      };\n    } catch (error) {\n      console.error('Error joining challenge:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to join challenge'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async leaveChallenge(challengeId: number, userId: number) {\n    try {\n      const result = await db.execute(\n        'DELETE FROM challenge_participants WHERE challenge_id = ? AND user_id = ?',\n        [challengeId, userId]\n      );\n\n      if (result.affectedRows === 0) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Not participating in this challenge'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      return {\n        status: 'success',\n        message: 'Successfully left challenge'\n      };\n    } catch (error) {\n      console.error('Error leaving challenge:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to leave challenge'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getChallengeLeaderboard(challengeId: number, userId: number) {\n    try {\n      const sql = `\n        SELECT \n          u.id,\n          u.name,\n          u.username,\n          u.photo,\n          cp.current_progress as progress,\n          cp.completed_at as completedAt,\n          cp.joined_at as joinedAt,\n          CASE WHEN u.id = ? THEN true ELSE false END as isCurrentUser\n        FROM challenge_participants cp\n        INNER JOIN users u ON cp.user_id = u.id\n        WHERE cp.challenge_id = ?\n        ORDER BY cp.current_progress DESC, cp.completed_at ASC, cp.joined_at ASC\n      `;\n\n      const [results] = await db.execute(sql, [userId, challengeId]);\n\n      // Add ranking\n      const leaderboard = results.map((participant, index) => ({\n        ...participant,\n        rank: index + 1\n      }));\n\n      return {\n        status: 'success',\n        data: leaderboard\n      };\n    } catch (error) {\n      console.error('Error getting challenge leaderboard:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get challenge leaderboard'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async updateChallengeProgress(challengeId: number, userId: number, progressData: any) {\n    try {\n      const { progress, completed = false } = progressData;\n\n      // Check if participating\n      const [participation] = await db.execute(\n        'SELECT id, current_progress FROM challenge_participants WHERE challenge_id = ? AND user_id = ?',\n        [challengeId, userId]\n      );\n\n      if (!participation[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Not participating in this challenge'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // Update progress\n      const updateData: any = {\n        current_progress: progress,\n        updated_at: new Date().toISOString()\n      };\n\n      if (completed) {\n        updateData.completed_at = new Date().toISOString();\n        updateData.status = 'completed';\n      }\n\n      await db.execute(\n        `UPDATE challenge_participants \n         SET current_progress = ?, completed_at = ?, status = ?, updated_at = NOW()\n         WHERE challenge_id = ? AND user_id = ?`,\n        [\n          progress,\n          completed ? new Date().toISOString() : null,\n          completed ? 'completed' : 'active',\n          challengeId,\n          userId\n        ]\n      );\n\n      // Award points if completed\n      if (completed) {\n        const [challenge] = await db.execute(\n          'SELECT reward_points FROM challenges WHERE id = ?',\n          [challengeId]\n        );\n\n        if (challenge[0]?.reward_points > 0) {\n          await this.awardPoints(userId, challenge[0].reward_points, 'challenge_completion');\n        }\n      }\n\n      return {\n        status: 'success',\n        message: completed ? 'Challenge completed!' : 'Progress updated successfully'\n      };\n    } catch (error) {\n      console.error('Error updating challenge progress:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to update challenge progress'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  private async awardPoints(userId: number, points: number, reason: string) {\n    try {\n      // Update user points\n      await db.execute(\n        `INSERT INTO user_points (user_id, total_points, monthly_points, yearly_points, updated_at)\n         VALUES (?, ?, ?, ?, NOW())\n         ON DUPLICATE KEY UPDATE\n         total_points = total_points + ?,\n         monthly_points = monthly_points + ?,\n         yearly_points = yearly_points + ?,\n         updated_at = NOW()`,\n        [userId, points, points, points, points, points, points]\n      );\n\n      // Log points transaction\n      await db.execute(\n        `INSERT INTO points_transactions (user_id, points, type, reason, created_at)\n         VALUES (?, ?, 'earned', ?, NOW())`,\n        [userId, points, reason]\n      );\n\n      return true;\n    } catch (error) {\n      console.error('Error awarding points:', error);\n      return false;\n    }\n  }\n}\n"], "names": ["ChallengesService", "getChallenges", "userId", "query", "status", "category", "page", "limit", "offset", "sql", "params", "push", "challenges", "executeRawSql", "countSql", "countParams", "count<PERSON><PERSON><PERSON>", "total", "data", "pagination", "Number", "totalPages", "Math", "ceil", "error", "console", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "getMyChallenges", "getChallenge", "challengeId", "result", "NOT_FOUND", "createChallenge", "challengeData", "title", "description", "type", "targetValue", "targetUnit", "durationDays", "rewardPoints", "rewardDescription", "imageUrl", "rules", "startDate", "Date", "endDate", "getTime", "db", "execute", "toISOString", "insertId", "id", "joinChallenge", "challenge", "BAD_REQUEST", "end_date", "existing", "CONFLICT", "leaveChallenge", "affectedRows", "getChallengeLeaderboard", "results", "leaderboard", "map", "participant", "index", "rank", "updateChallengeProgress", "progressData", "progress", "completed", "participation", "updateData", "current_progress", "updated_at", "completed_at", "reward_points", "awardPoints", "points", "reason"], "mappings": ";;;;+BAIaA;;;eAAAA;;;wBAJyC;0BACpB;;;;;;;AAG3B,IAAA,AAAMA,oBAAN,MAAMA;IAEX,MAAMC,cAAcC,MAAc,EAAEC,KAAU,EAAE;QAC9C,IAAI;YACF,MAAM,EAAEC,SAAS,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAEC,QAAQ,EAAE,EAAE,GAAGJ;YAC9D,MAAMK,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5B,IAAIE,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;MAuBX,CAAC;YAED,MAAMC,SAAS;gBAACR;gBAAQE;aAAO;YAE/B,IAAIC,UAAU;gBACZI,OAAO,CAAC,mBAAmB,CAAC;gBAC5BC,OAAOC,IAAI,CAACN;YACd;YAEAI,OAAO,CAAC,0DAA0D,CAAC;YACnEC,OAAOC,IAAI,CAACJ,OAAOC;YAEnB,MAAMI,aAAa,MAAMC,IAAAA,uBAAa,EAACJ,KAAKC;YAE5C,kBAAkB;YAClB,IAAII,WAAW,CAAC,yDAAyD,CAAC;YAC1E,MAAMC,cAAc;gBAACX;aAAO;YAE5B,IAAIC,UAAU;gBACZS,YAAY,CAAC,iBAAiB,CAAC;gBAC/BC,YAAYJ,IAAI,CAACN;YACnB;YAEA,MAAMW,cAAc,MAAMH,IAAAA,uBAAa,EAACC,UAAUC;YAClD,MAAME,QAAQD,WAAW,CAAC,EAAE,EAAEC,SAAS;YAEvC,OAAO;gBACLb,QAAQ;gBACRc,MAAM;oBACJN;oBACAO,YAAY;wBACVb,MAAMc,OAAOd;wBACbC,OAAOa,OAAOb;wBACdU;wBACAI,YAAYC,KAAKC,IAAI,CAACN,QAAQV;oBAChC;gBACF;YACF;QACF,EAAE,OAAOiB,OAAO;YACdC,QAAQD,KAAK,CAAC,6BAA6BA;YAC3C,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMC,gBAAgB5B,MAAc,EAAE;QACpC,IAAI;YACF,MAAMO,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;MAwBb,CAAC;YAED,MAAMG,aAAa,MAAMC,IAAAA,uBAAa,EAACJ,KAAK;gBAACP;aAAO;YAEpD,OAAO;gBACLE,QAAQ;gBACRc,MAAMN;YACR;QACF,EAAE,OAAOY,OAAO;YACdC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAME,aAAaC,WAAmB,EAAE9B,MAAc,EAAE;QACtD,IAAI;YACF,MAAMO,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA4Bb,CAAC;YAED,MAAMwB,SAAS,MAAMpB,IAAAA,uBAAa,EAACJ,KAAK;gBAACP;gBAAQ8B;aAAY;YAE7D,IAAI,CAACC,MAAM,CAAC,EAAE,EAAE;gBACd,MAAM,IAAIP,qBAAa,CAAC;oBACtBtB,QAAQ;oBACRuB,SAAS;gBACX,GAAGC,kBAAU,CAACM,SAAS;YACzB;YAEA,OAAO;gBACL9B,QAAQ;gBACRc,MAAMe,MAAM,CAAC,EAAE;YACjB;QACF,EAAE,OAAOT,OAAO;YACdC,QAAQD,KAAK,CAAC,4BAA4BA;YAC1C,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMM,gBAAgBC,aAAkB,EAAElC,MAAc,EAAE;QACxD,IAAI;YACF,MAAM,EACJmC,KAAK,EACLC,WAAW,EACXjC,QAAQ,EACRkC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,YAAY,EACZC,YAAY,EACZC,iBAAiB,EACjBC,QAAQ,EACRC,KAAK,EACN,GAAGV;YAEJ,MAAMW,YAAY,IAAIC;YACtB,MAAMC,UAAU,IAAID,KAAKD,UAAUG,OAAO,KAAMR,eAAe,KAAK,KAAK,KAAK;YAE9E,MAAM,CAACT,OAAO,GAAG,MAAMkB,YAAE,CAACC,OAAO,CAC/B,CAAC;;;;mFAI0E,CAAC,EAC5E;gBACEf;gBAAOC;gBAAajC;gBAAUkC;gBAAMC;gBAAaC;gBACjDC;gBAAcK,UAAUM,WAAW;gBAAIJ,QAAQI,WAAW;gBAC1DV;gBAAcC;gBAAmBC;gBAAUC;gBAAO5C;aACnD;YAGH,wBAAwB;YACxB,MAAMiD,YAAE,CAACC,OAAO,CACd,CAAC;uCAC8B,CAAC,EAChC;gBAACnB,OAAOqB,QAAQ;gBAAEpD;aAAO;YAG3B,OAAO;gBACLE,QAAQ;gBACRuB,SAAS;gBACTT,MAAM;oBACJqC,IAAItB,OAAOqB,QAAQ;oBACnB,GAAGlB,aAAa;gBAClB;YACF;QACF,EAAE,OAAOZ,OAAO;YACdC,QAAQD,KAAK,CAAC,6BAA6BA;YAC3C,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM2B,cAAcxB,WAAmB,EAAE9B,MAAc,EAAE;QACvD,IAAI;YACF,0CAA0C;YAC1C,MAAM,CAACuD,UAAU,GAAG,MAAMN,YAAE,CAACC,OAAO,CAClC,4DACA;gBAACpB;aAAY;YAGf,IAAI,CAACyB,SAAS,CAAC,EAAE,EAAE;gBACjB,MAAM,IAAI/B,qBAAa,CAAC;oBACtBtB,QAAQ;oBACRuB,SAAS;gBACX,GAAGC,kBAAU,CAACM,SAAS;YACzB;YAEA,IAAIuB,SAAS,CAAC,EAAE,CAACrD,MAAM,KAAK,UAAU;gBACpC,MAAM,IAAIsB,qBAAa,CAAC;oBACtBtB,QAAQ;oBACRuB,SAAS;gBACX,GAAGC,kBAAU,CAAC8B,WAAW;YAC3B;YAEA,IAAI,IAAIV,KAAKS,SAAS,CAAC,EAAE,CAACE,QAAQ,IAAI,IAAIX,QAAQ;gBAChD,MAAM,IAAItB,qBAAa,CAAC;oBACtBtB,QAAQ;oBACRuB,SAAS;gBACX,GAAGC,kBAAU,CAAC8B,WAAW;YAC3B;YAEA,iCAAiC;YACjC,MAAM,CAACE,SAAS,GAAG,MAAMT,YAAE,CAACC,OAAO,CACjC,gFACA;gBAACpB;gBAAa9B;aAAO;YAGvB,IAAI0D,QAAQ,CAAC,EAAE,EAAE;gBACf,MAAM,IAAIlC,qBAAa,CAAC;oBACtBtB,QAAQ;oBACRuB,SAAS;gBACX,GAAGC,kBAAU,CAACiC,QAAQ;YACxB;YAEA,iBAAiB;YACjB,MAAMV,YAAE,CAACC,OAAO,CACd,CAAC;0CACiC,CAAC,EACnC;gBAACpB;gBAAa9B;aAAO;YAGvB,OAAO;gBACLE,QAAQ;gBACRuB,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,4BAA4BA;YAC1C,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMiC,eAAe9B,WAAmB,EAAE9B,MAAc,EAAE;QACxD,IAAI;YACF,MAAM+B,SAAS,MAAMkB,YAAE,CAACC,OAAO,CAC7B,6EACA;gBAACpB;gBAAa9B;aAAO;YAGvB,IAAI+B,OAAO8B,YAAY,KAAK,GAAG;gBAC7B,MAAM,IAAIrC,qBAAa,CAAC;oBACtBtB,QAAQ;oBACRuB,SAAS;gBACX,GAAGC,kBAAU,CAACM,SAAS;YACzB;YAEA,OAAO;gBACL9B,QAAQ;gBACRuB,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,4BAA4BA;YAC1C,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMmC,wBAAwBhC,WAAmB,EAAE9B,MAAc,EAAE;QACjE,IAAI;YACF,MAAMO,MAAM,CAAC;;;;;;;;;;;;;;MAcb,CAAC;YAED,MAAM,CAACwD,QAAQ,GAAG,MAAMd,YAAE,CAACC,OAAO,CAAC3C,KAAK;gBAACP;gBAAQ8B;aAAY;YAE7D,cAAc;YACd,MAAMkC,cAAcD,QAAQE,GAAG,CAAC,CAACC,aAAaC,QAAW,CAAA;oBACvD,GAAGD,WAAW;oBACdE,MAAMD,QAAQ;gBAChB,CAAA;YAEA,OAAO;gBACLjE,QAAQ;gBACRc,MAAMgD;YACR;QACF,EAAE,OAAO1C,OAAO;YACdC,QAAQD,KAAK,CAAC,wCAAwCA;YACtD,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM0C,wBAAwBvC,WAAmB,EAAE9B,MAAc,EAAEsE,YAAiB,EAAE;QACpF,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAEC,YAAY,KAAK,EAAE,GAAGF;YAExC,yBAAyB;YACzB,MAAM,CAACG,cAAc,GAAG,MAAMxB,YAAE,CAACC,OAAO,CACtC,kGACA;gBAACpB;gBAAa9B;aAAO;YAGvB,IAAI,CAACyE,aAAa,CAAC,EAAE,EAAE;gBACrB,MAAM,IAAIjD,qBAAa,CAAC;oBACtBtB,QAAQ;oBACRuB,SAAS;gBACX,GAAGC,kBAAU,CAACM,SAAS;YACzB;YAEA,kBAAkB;YAClB,MAAM0C,aAAkB;gBACtBC,kBAAkBJ;gBAClBK,YAAY,IAAI9B,OAAOK,WAAW;YACpC;YAEA,IAAIqB,WAAW;gBACbE,WAAWG,YAAY,GAAG,IAAI/B,OAAOK,WAAW;gBAChDuB,WAAWxE,MAAM,GAAG;YACtB;YAEA,MAAM+C,YAAE,CAACC,OAAO,CACd,CAAC;;+CAEsC,CAAC,EACxC;gBACEqB;gBACAC,YAAY,IAAI1B,OAAOK,WAAW,KAAK;gBACvCqB,YAAY,cAAc;gBAC1B1C;gBACA9B;aACD;YAGH,4BAA4B;YAC5B,IAAIwE,WAAW;gBACb,MAAM,CAACjB,UAAU,GAAG,MAAMN,YAAE,CAACC,OAAO,CAClC,qDACA;oBAACpB;iBAAY;gBAGf,IAAIyB,SAAS,CAAC,EAAE,EAAEuB,gBAAgB,GAAG;oBACnC,MAAM,IAAI,CAACC,WAAW,CAAC/E,QAAQuD,SAAS,CAAC,EAAE,CAACuB,aAAa,EAAE;gBAC7D;YACF;YAEA,OAAO;gBACL5E,QAAQ;gBACRuB,SAAS+C,YAAY,yBAAyB;YAChD;QACF,EAAE,OAAOlD,OAAO;YACdC,QAAQD,KAAK,CAAC,sCAAsCA;YACpD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBtB,QAAQ;gBACRuB,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAcoD,YAAY/E,MAAc,EAAEgF,MAAc,EAAEC,MAAc,EAAE;QACxE,IAAI;YACF,qBAAqB;YACrB,MAAMhC,YAAE,CAACC,OAAO,CACd,CAAC;;;;;;2BAMkB,CAAC,EACpB;gBAAClD;gBAAQgF;gBAAQA;gBAAQA;gBAAQA;gBAAQA;gBAAQA;aAAO;YAG1D,yBAAyB;YACzB,MAAM/B,YAAE,CAACC,OAAO,CACd,CAAC;0CACiC,CAAC,EACnC;gBAAClD;gBAAQgF;gBAAQC;aAAO;YAG1B,OAAO;QACT,EAAE,OAAO3D,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,OAAO;QACT;IACF;AACF"}