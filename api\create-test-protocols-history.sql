-- ============================================================================
-- SCRIPT PARA CRIAR PROTOCOLOS DE TESTE PARA HISTÓRICO
-- ============================================================================

-- Verificar se o usuário demo existe (ID 72)
SELECT id, name, email FROM users WHERE id = 72;

-- <PERSON>car o type_id para treinos de hipertrofia
SET @type_id = (SELECT id FROM coach_protocol_types WHERE name = 'Hipertrofia' LIMIT 1);

-- Se não existir, criar o tipo
INSERT IGNORE INTO coach_protocol_types (name, description, created_at, updated_at) 
VALUES ('Hipertrofia', 'Protocolo focado em ganho de massa muscular', NOW(), NOW());

-- Pegar o ID do tipo criado ou existente
SET @type_id = (SELECT id FROM coach_protocol_types WHERE name = 'Hipertrofia' LIMIT 1);

-- ============================================================================
-- PROTOCOLO 1: ATIVO (SEM ended_at)
-- ============================================================================

INSERT INTO coach_protocols (
    client_id,
    user_id,
    name,
    type_id,
    split,
    frequency,
    objective,
    general_notes,
    started_at,
    ended_at,
    created_at,
    updated_at
) VALUES (
    72, -- client_id (demo user)
    NULL, -- user_id (coach - can be null for demo)
    'Protocolo Hipertrofia Atual',
    @type_id,
    'ABCDE',
    5,
    'Ganho de massa muscular e força - protocolo atual',
    'Protocolo ativo focado em hipertrofia com divisão ABCDE. Exercícios compostos e isolados.',
    '2024-12-01', -- started_at (recent date)
    NULL, -- ended_at (null = active)
    NOW(),
    NOW()
);

-- ============================================================================
-- PROTOCOLO 2: FINALIZADO RECENTEMENTE
-- ============================================================================

INSERT INTO coach_protocols (
    client_id,
    user_id,
    name,
    type_id,
    split,
    frequency,
    objective,
    general_notes,
    started_at,
    ended_at,
    created_at,
    updated_at
) VALUES (
    72, -- client_id (demo user)
    NULL, -- user_id (coach - can be null for demo)
    'Protocolo Força Finalizado',
    @type_id,
    'ABC',
    3,
    'Ganho de força e potência',
    'Protocolo finalizado focado em força com divisão ABC. Exercícios compostos pesados.',
    '2024-10-01', -- started_at
    '2024-11-30', -- ended_at (finished)
    '2024-10-01 10:00:00',
    '2024-11-30 18:00:00'
);

-- ============================================================================
-- PROTOCOLO 3: FINALIZADO HÁ MAIS TEMPO
-- ============================================================================

INSERT INTO coach_protocols (
    client_id,
    user_id,
    name,
    type_id,
    split,
    frequency,
    objective,
    general_notes,
    started_at,
    ended_at,
    created_at,
    updated_at
) VALUES (
    72, -- client_id (demo user)
    NULL, -- user_id (coach - can be null for demo)
    'Protocolo Resistência Anterior',
    @type_id,
    'AB',
    4,
    'Melhoria da resistência cardiovascular e muscular',
    'Protocolo anterior focado em resistência com divisão AB. Circuitos e supersets.',
    '2024-08-01', -- started_at
    '2024-09-30', -- ended_at (finished)
    '2024-08-01 09:00:00',
    '2024-09-30 17:30:00'
);

-- ============================================================================
-- PROTOCOLO 4: FINALIZADO ANTIGO
-- ============================================================================

INSERT INTO coach_protocols (
    client_id,
    user_id,
    name,
    type_id,
    split,
    frequency,
    objective,
    general_notes,
    started_at,
    ended_at,
    created_at,
    updated_at
) VALUES (
    72, -- client_id (demo user)
    NULL, -- user_id (coach - can be null for demo)
    'Protocolo Iniciante Completo',
    @type_id,
    'ABC',
    3,
    'Adaptação inicial ao treinamento de força',
    'Primeiro protocolo para iniciantes. Foco em aprendizado de movimentos e adaptação.',
    '2024-06-01', -- started_at
    '2024-07-31', -- ended_at (finished)
    '2024-06-01 08:00:00',
    '2024-07-31 19:00:00'
);

-- ============================================================================
-- VERIFICAR OS PROTOCOLOS CRIADOS
-- ============================================================================

SELECT 
    id,
    name,
    objective,
    started_at,
    ended_at,
    CASE 
        WHEN ended_at IS NULL THEN 'active'
        ELSE 'finished'
    END as status,
    DATEDIFF(COALESCE(ended_at, NOW()), started_at) as duration_days
FROM coach_protocols 
WHERE client_id = 72 
ORDER BY created_at DESC;

-- ============================================================================
-- ESTATÍSTICAS
-- ============================================================================

SELECT 
    COUNT(*) as total_protocols,
    SUM(CASE WHEN ended_at IS NULL THEN 1 ELSE 0 END) as active_protocols,
    SUM(CASE WHEN ended_at IS NOT NULL THEN 1 ELSE 0 END) as finished_protocols
FROM coach_protocols 
WHERE client_id = 72;
