import { HttpException, HttpStatus, Injectable, Res } from '@nestjs/common';
import * as bcrypt from 'bcryptjs';
import { CreateUserDto } from './create-user.dto';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../database';
import * as dotenv from 'dotenv';
import dayjs from 'dayjs';

dotenv.config();

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
  ) {}  

  formatDatetime(datetime: string): string {
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
  }

  async login(user: {email: string, password: string}): Promise<any> {
    if (!user?.email || !user?.password) {
      throw new HttpException({message: ['Email e senha são obrigatórios'], status: HttpStatus.BAD_REQUEST},
        HttpStatus.BAD_REQUEST);
    }

    const checkUser: any = await db.selectFrom('users')
    .selectAll()
    .where('email', '=', user.email)
    .executeTakeFirst();

    if (!checkUser) {
      throw new HttpException({message: ['Usuário não encontrado'], status: HttpStatus.NOT_FOUND},
        HttpStatus.NOT_FOUND);
    }

    // check password
    const isPasswordValid = await bcrypt.compare(user.password, checkUser.password);
    if (!isPasswordValid) {
      throw new HttpException({message: ['Senha inválida'], status: HttpStatus.UNAUTHORIZED},
        HttpStatus.UNAUTHORIZED);
    }

    // Check if account is soft deleted
    if (checkUser.deleted_at) {
      const deletedDate = new Date(checkUser.deleted_at);
      const now = new Date();
      const daysDifference = Math.floor((now.getTime() - deletedDate.getTime()) / (1000 * 60 * 60 * 24));

      // If deleted more than 30 days ago, treat as permanently deleted
      if (daysDifference > 30) {
        throw new HttpException({
          message: ['Esta conta foi permanentemente excluída.'],
          status: HttpStatus.FORBIDDEN
        }, HttpStatus.FORBIDDEN);
      }

      // Account is within recovery period - return special response
      return {
        status: "account_recovery_required",
        data: {
          message: `Sua conta foi marcada para exclusão há ${daysDifference} dias. Você tem ${30 - daysDifference} dias restantes para recuperá-la.`,
          userId: checkUser.id,
          daysRemaining: 30 - daysDifference,
          deletedAt: checkUser.deleted_at
        }
      };
    }

    const payload = { userId: checkUser.id };
    const access_token = this.jwtService.sign(payload, {
      expiresIn: process.env.JWT_EXPIRATION_TIME,
    });
    const refresh_token = uuidv4();
    const provider_id = 1;
    const device_uid = uuidv4();

    // save refresh token in database
    const userAuthData: any = {
      user_id: checkUser.id,
      provider_uid: user.email,
      provider_id: provider_id,
      device_uid,
      refresh_token,
      expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
    }

    await db.insertInto('user_auths')
    .values(userAuthData)
    .executeTakeFirstOrThrow();

    return {
      status: "success",
      data: {
        access_token,
        refresh_token,
        device_uid
      }
    };
  }

  async recoverAccount(userId: number): Promise<any> {
    // Check if user exists and is deleted
    const checkUser: any = await db.selectFrom('users')
    .select(['id', 'deleted_at', 'email'])
    .where('id', '=', userId)
    .executeTakeFirst();

    if (!checkUser) {
      throw new HttpException({message: ['Usuário não encontrado'], status: HttpStatus.NOT_FOUND},
        HttpStatus.NOT_FOUND);
    }

    if (!checkUser.deleted_at) {
      throw new HttpException({message: ['Esta conta não está marcada para exclusão'], status: HttpStatus.BAD_REQUEST},
        HttpStatus.BAD_REQUEST);
    }

    // Check if still within recovery period
    const deletedDate = new Date(checkUser.deleted_at);
    const now = new Date();
    const daysDifference = Math.floor((now.getTime() - deletedDate.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDifference > 30) {
      throw new HttpException({
        message: ['O período de recuperação de 30 dias expirou. Esta conta foi permanentemente excluída.'],
        status: HttpStatus.FORBIDDEN
      }, HttpStatus.FORBIDDEN);
    }

    // Clear deleted_at to recover the account
    await db
      .updateTable('users')
      .set({
        deleted_at: null,
        updated_at: new Date(),
      })
      .where('id', '=', userId)
      .execute();

    // Generate new tokens for the recovered account
    const payload = { userId: checkUser.id };
    const access_token = this.jwtService.sign(payload, {
      expiresIn: process.env.JWT_EXPIRATION_TIME,
    });
    const refresh_token = uuidv4();
    const provider_id = 1;
    const device_uid = uuidv4();

    // save refresh token in database
    const userAuthData: any = {
      user_id: checkUser.id,
      provider_uid: checkUser.email,
      provider_id: provider_id,
      device_uid,
      refresh_token,
      expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
    }

    await db.insertInto('user_auths')
    .values(userAuthData)
    .executeTakeFirstOrThrow();

    return {
      status: "success",
      data: {
        access_token,
        refresh_token,
        device_uid,
        message: 'Conta recuperada com sucesso!'
      }
    };
  }

  // Método para registrar o usuário
  async register(createUserDto: CreateUserDto): Promise<any> {
    const { name, email, password, invite } = createUserDto;
    let aff_id = null;
    
    const existingUser: any = await db.selectFrom('users')
    .where('email', '=', email)
    .selectAll()
    .executeTakeFirst();

    if (existingUser) {
      throw new HttpException({message: ['O email já está cadastrado'], status: HttpStatus.CONFLICT},
        HttpStatus.CONFLICT);
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    if(invite) {
      // get invite and user_id
      const inviteData: any = await db.selectFrom('affiliate_links')
      .where('invite', '=', invite.toLowerCase().trim())
      .select(['user_id'])
      .executeTakeFirst();

      if (inviteData) {
        aff_id = inviteData.user_id;
      }

      if (!inviteData) {
        throw new HttpException({message: ['Código promocional inválido'], status: HttpStatus.BAD_REQUEST},
          HttpStatus.BAD_REQUEST);
      }
    }

    const userData: any = {
      name: name.trim(),
      email: email.trim(),
      password: hashedPassword,
      invite: invite ? invite.toLowerCase().trim() : null,
      aff_id
    }

    const user: any = await db.insertInto('users')
    .values(userData)
    .executeTakeFirstOrThrow();

    const userId = parseInt(user.insertId) || user.insertId;

    // Salvar o usuário no banco
    // const savedUser = await this.userRepository.save(user);

    const payload = { userId: userId };

    const access_token = this.jwtService.sign(payload, { expiresIn: process.env.JWT_EXPIRATION_TIME });

    const deviceUid = uuidv4();

    const refresh_token = uuidv4();

    const provider_id = 1; // Local


    // insert on user_auths
    /*
    const userAuth = this.userAuthRepository.create({
      user: savedUser,
      provider: { id: provider_id },
      providerUid: email,
      deviceUid,
      refreshToken: refresh_token,
      expireDate: new Date(Date.now() + 60 * 60 * 1000), // 1 hora
    });
    */
   const userAuthData: any = {
    user_id: userId,
    provider_id,
    provider_uid: email,
    device_uid: deviceUid,
    refresh_token,
    expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
   }

   const userAuth = await db.insertInto('user_auths')
   .values(userAuthData)
   .executeTakeFirstOrThrow();


    // const savedUserAuth = await this.userAuthRepository.save(userAuth);

    return {
        "status": "success",
        "data": {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "device_uid": deviceUid
        }
    };
  }

  // Refresh token
  async refreshToken(refresh_token: string, device_uid: string) {
    try {
     const userAuth = await db.selectFrom('user_auths')
     .select(['user_id', 'provider_id', 'provider_uid', 'device_uid'])
     .where('refresh_token', '=', refresh_token)
     .where('device_uid', '=', device_uid)
     .executeTakeFirst();

      if (!userAuth) {
        throw new HttpException({message: ['Refresh token inválido'], status: HttpStatus.UNAUTHORIZED},
          HttpStatus.UNAUTHORIZED);
      }

      const userId = userAuth.user_id;
      
      await db.deleteFrom('user_auths')
      .where('user_id', '=', userId)
      .where('device_uid', '=', device_uid)
      .executeTakeFirstOrThrow();
      
      // generate new refresh token
      const new_refresh_token = uuidv4();
      
      // add new refresh token to db
      const userAuthData: any = {
        user_id: userId,
        provider_id: userAuth.provider_id,
        provider_uid: userAuth.provider_uid,
        device_uid,
        refresh_token: new_refresh_token,
        expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
      }
      
      await db.insertInto('user_auths')
      .values(userAuthData)
      .executeTakeFirstOrThrow();

      // generate new access token
      const payload = { userId: userId };
      const new_access_token = this.jwtService.sign(payload, {
        expiresIn: process.env.JWT_EXPIRATION_TIME,
      });
      
      return {
        "status": "success",
        "data": {
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,
            "device_uid": device_uid
        }
      };
      
    } catch (error) {
      throw new HttpException({message: ['Refresh token inválido'], status: HttpStatus.UNAUTHORIZED},
        HttpStatus.UNAUTHORIZED);
    }
    
  }

  async getRole(userId: number, role: string) {
    const rolesIds = {
      'admin': 1,
      'coach': 2,
      'nutritionist': 3,
      'user': 4
    };

    const userRole = await db
    .selectFrom('users_roles')
    .select(['role_id'])
    .where('user_id', '=', userId)
    .where('role_id', '=', rolesIds[role])
    .executeTakeFirst();

    if (!userRole) {
      if (role === 'admin') {
        return {
          status: 'error',
          role: null,
        };
      }

      // add role
      const newRole = await db
        .insertInto('users_roles')
        .values({
          user_id: userId,
          role_id: rolesIds[role]
        })
        .execute();
    }

    return {
      status: 'success',
      role: role
    };
  }

  async requestPasswordReset(email: string): Promise<any> {
    try {
      const user = await db.selectFrom('users')
        .selectAll()
        .where('email', '=', email)
        .executeTakeFirst();

      if (!user) {
        // Don't reveal if email exists or not for security
        return {
          status: 'success',
          message: 'Se o email existir em nossa base, você receberá instruções para redefinir sua senha.'
        };
      }

      // Generate reset token
      const resetToken = uuidv4();
      const expiresAt = new Date(Date.now() + 3600000); // 1 hour

      // Store reset token
      await db.insertInto('password_reset_tokens')
        .values({
          user_id: user.id,
          token: resetToken,
          expires_at: expiresAt,
          created_at: new Date()
        })
        .execute();

      // Here you would send email with reset link
      // await this.emailService.sendPasswordResetEmail(email, resetToken);

      return {
        status: 'success',
        message: 'Se o email existir em nossa base, você receberá instruções para redefinir sua senha.',
        // For development only - remove in production
        resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined
      };
    } catch (error) {
      console.error('Error requesting password reset:', error);
      throw new HttpException({
        message: ['Erro interno do servidor'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async resetPassword(token: string, newPassword: string): Promise<any> {
    try {
      // Find valid reset token
      const resetToken = await db.selectFrom('password_reset_tokens')
        .selectAll()
        .where('token', '=', token)
        .where('expires_at', '>', new Date())
        .where('used_at', 'is', null)
        .executeTakeFirst();

      if (!resetToken) {
        throw new HttpException({
          message: ['Token inválido ou expirado'],
          status: HttpStatus.BAD_REQUEST
        }, HttpStatus.BAD_REQUEST);
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update user password
      await db.updateTable('users')
        .set({
          password: hashedPassword,
          updated_at: new Date()
        })
        .where('id', '=', resetToken.user_id)
        .execute();

      // Mark token as used
      await db.updateTable('password_reset_tokens')
        .set({ used_at: new Date() })
        .where('id', '=', resetToken.id)
        .execute();

      // Invalidate all refresh tokens for security
      await db.deleteFrom('refresh_tokens')
        .where('user_id', '=', resetToken.user_id)
        .execute();

      return {
        status: 'success',
        message: 'Senha redefinida com sucesso'
      };
    } catch (error) {
      console.error('Error resetting password:', error);
      if (error instanceof HttpException) throw error;

      throw new HttpException({
        message: ['Erro interno do servidor'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async verifyEmail(token: string): Promise<any> {
    try {
      // Find user by verification token
      const user = await db.selectFrom('users')
        .selectAll()
        .where('email_verification_token', '=', token)
        .where('email_verified_at', 'is', null)
        .executeTakeFirst();

      if (!user) {
        throw new HttpException({
          message: ['Token de verificação inválido'],
          status: HttpStatus.BAD_REQUEST
        }, HttpStatus.BAD_REQUEST);
      }

      // Mark email as verified
      await db.updateTable('users')
        .set({
          email_verified_at: new Date(),
          email_verification_token: null,
          updated_at: new Date()
        })
        .where('id', '=', user.id)
        .execute();

      return {
        status: 'success',
        message: 'Email verificado com sucesso'
      };
    } catch (error) {
      console.error('Error verifying email:', error);
      if (error instanceof HttpException) throw error;

      throw new HttpException({
        message: ['Erro interno do servidor'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async resendVerification(email: string): Promise<any> {
    try {
      const user = await db.selectFrom('users')
        .selectAll()
        .where('email', '=', email)
        .where('email_verified_at', 'is', null)
        .executeTakeFirst();

      if (!user) {
        return {
          status: 'success',
          message: 'Se o email existir e não estiver verificado, um novo link será enviado.'
        };
      }

      // Generate new verification token
      const verificationToken = uuidv4();

      await db.updateTable('users')
        .set({
          email_verification_token: verificationToken,
          updated_at: new Date()
        })
        .where('id', '=', user.id)
        .execute();

      // Here you would send verification email
      // await this.emailService.sendVerificationEmail(email, verificationToken);

      return {
        status: 'success',
        message: 'Se o email existir e não estiver verificado, um novo link será enviado.',
        // For development only - remove in production
        verificationToken: process.env.NODE_ENV === 'development' ? verificationToken : undefined
      };
    } catch (error) {
      console.error('Error resending verification:', error);
      throw new HttpException({
        message: ['Erro interno do servidor'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getUserSessions(userId: number): Promise<any> {
    try {
      const sessions = await db.selectFrom('refresh_tokens')
        .select(['id', 'device_uid', 'created_at', 'last_used_at', 'expires_at'])
        .where('user_id', '=', userId)
        .where('expires_at', '>', new Date())
        .orderBy('last_used_at', 'desc')
        .execute();

      return {
        status: 'success',
        data: sessions.map(session => ({
          id: session.id,
          deviceId: session.device_uid,
          createdAt: session.created_at,
          lastUsedAt: session.last_used_at,
          expiresAt: session.expires_at
        }))
      };
    } catch (error) {
      console.error('Error getting user sessions:', error);
      throw new HttpException({
        message: ['Erro interno do servidor'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async terminateSession(sessionId: string, userId: number): Promise<any> {
    try {
      const result = await db.deleteFrom('refresh_tokens')
        .where('id', '=', Number(sessionId))
        .where('user_id', '=', userId)
        .execute();

      if (result.length === 0) {
        throw new HttpException({
          message: ['Sessão não encontrada'],
          status: HttpStatus.NOT_FOUND
        }, HttpStatus.NOT_FOUND);
      }

      return {
        status: 'success',
        message: 'Sessão encerrada com sucesso'
      };
    } catch (error) {
      console.error('Error terminating session:', error);
      if (error instanceof HttpException) throw error;

      throw new HttpException({
        message: ['Erro interno do servidor'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async logoutAllSessions(userId: number): Promise<any> {
    try {
      await db.deleteFrom('refresh_tokens')
        .where('user_id', '=', userId)
        .execute();

      return {
        status: 'success',
        message: 'Todas as sessões foram encerradas'
      };
    } catch (error) {
      console.error('Error logging out all sessions:', error);
      throw new HttpException({
        message: ['Erro interno do servidor'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // OAuth Methods
  async validateOAuthUser(oauthUser: any, provider: 'google' | 'apple'): Promise<any> {
    try {
      const { email, name, googleId, appleId } = oauthUser;
      const providerId = provider === 'google' ? 2 : 3;
      const providerUid = provider === 'google' ? googleId : appleId;

      if (!email) {
        throw new HttpException({
          message: ['Email não fornecido pelo provedor OAuth'],
          status: HttpStatus.BAD_REQUEST
        }, HttpStatus.BAD_REQUEST);
      }

      // Check if user exists by email
      let user: any = await db.selectFrom('users')
        .selectAll()
        .where('email', '=', email)
        .executeTakeFirst();

      if (!user) {
        // Create new user for OAuth
        const userData: any = {
          name: name || email.split('@')[0],
          email: email,
          password: null, // OAuth users don't have passwords
          photo: oauthUser.picture || null, // Store profile picture if available
        };

        const newUser: any = await db.insertInto('users')
          .values(userData)
          .executeTakeFirstOrThrow();

        const userId = parseInt(newUser.insertId) || newUser.insertId;
        user = { id: userId, ...userData };
      }

      // Check if OAuth provider is already linked
      const existingAuth = await db.selectFrom('user_auths')
        .selectAll()
        .where('user_id', '=', user.id)
        .where('provider_id', '=', providerId)
        .executeTakeFirst();

      if (!existingAuth) {
        // Link OAuth provider to user
        const deviceUid = uuidv4();
        const refreshToken = uuidv4();

        const userAuthData: any = {
          user_id: user.id,
          provider_id: providerId,
          provider_uid: providerUid,
          device_uid: deviceUid,
          refresh_token: refreshToken,
          expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
        };

        await db.insertInto('user_auths')
          .values(userAuthData)
          .executeTakeFirstOrThrow();
      }

      return user;
    } catch (error) {
      console.error('Error validating OAuth user:', error);
      throw new HttpException({
        message: ['Erro ao validar usuário OAuth'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async loginWithOAuth(user: any, provider: 'google' | 'apple'): Promise<any> {
    try {
      const validatedUser = await this.validateOAuthUser(user, provider);
      const providerId = provider === 'google' ? 2 : 3;

      // Generate JWT token
      const payload = { userId: validatedUser.id };
      const access_token = this.jwtService.sign(payload, {
        expiresIn: process.env.JWT_EXPIRATION_TIME,
      });

      const refresh_token = uuidv4();
      const device_uid = uuidv4();

      // Update or create user_auths entry
      await db.deleteFrom('user_auths')
        .where('user_id', '=', validatedUser.id)
        .where('provider_id', '=', providerId)
        .execute();

      const userAuthData: any = {
        user_id: validatedUser.id,
        provider_id: providerId,
        provider_uid: provider === 'google' ? user.googleId : user.appleId,
        device_uid,
        refresh_token,
        expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
      };

      await db.insertInto('user_auths')
        .values(userAuthData)
        .executeTakeFirstOrThrow();

      return {
        status: "success",
        data: {
          access_token,
          refresh_token,
          device_uid
        }
      };
    } catch (error) {
      console.error('Error in OAuth login:', error);
      throw new HttpException({
        message: ['Erro no login OAuth'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // Frontend-initiated OAuth token validation methods
  async validateGoogleToken(token: string, userInfo: any): Promise<any> {
    try {
      // In a real implementation, you would validate the token with Google's API
      // For now, we'll trust the frontend-provided user info
      // TODO: Add actual Google token validation

      if (!userInfo.email) {
        throw new HttpException({
          message: ['Email não fornecido pelo Google'],
          status: HttpStatus.BAD_REQUEST
        }, HttpStatus.BAD_REQUEST);
      }

      const oauthUser = {
        googleId: userInfo.sub || userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        firstName: userInfo.given_name,
        lastName: userInfo.family_name,
      };

      return await this.loginWithOAuth(oauthUser, 'google');
    } catch (error) {
      console.error('Error validating Google token:', error);
      throw new HttpException({
        message: ['Erro ao validar token do Google'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async validateAppleToken(token: string, userInfo: any): Promise<any> {
    try {
      // In a real implementation, you would validate the token with Apple's API
      // For now, we'll trust the frontend-provided user info
      // TODO: Add actual Apple token validation

      if (!userInfo.email) {
        throw new HttpException({
          message: ['Email não fornecido pela Apple'],
          status: HttpStatus.BAD_REQUEST
        }, HttpStatus.BAD_REQUEST);
      }

      const oauthUser = {
        appleId: userInfo.sub || userInfo.id,
        email: userInfo.email,
        name: userInfo.name || `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim(),
        firstName: userInfo.firstName,
        lastName: userInfo.lastName,
      };

      return await this.loginWithOAuth(oauthUser, 'apple');
    } catch (error) {
      console.error('Error validating Apple token:', error);
      throw new HttpException({
        message: ['Erro ao validar token da Apple'],
        status: HttpStatus.INTERNAL_SERVER_ERROR
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

}