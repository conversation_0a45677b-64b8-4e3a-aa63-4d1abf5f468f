// src/admin/dto/create-exercise.dto.ts
import { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateExerciseDto {
  @IsString()
  @IsNotEmpty()
  name: string; // Nome do exercício

  @IsInt()
  @Type(() => Number)
  muscle_group_id: number; // ID do grupo muscular

  @IsInt()
  @Type(() => Number)
  equipment_id: number; // ID do equipamento

  @IsString()
  @IsOptional()
  media_url?: string; // URL da mídia (opcional)

  @IsArray()
  @IsOptional()
  // @ArrayNotEmpty()
  target_muscles?: string[]; // Músculos-alvo

  @IsArray()
  @IsOptional()
  // @ArrayNotEmpty()
  synergistic_muscles?: string[]; // Músculos sinergistas

  @IsArray()
  @IsOptional()
  // @ArrayNotEmpty()
  instructions?: string[]; // Instruções

  @IsArray()
  @IsOptional()
  tips?: string[]; // Dicas (opcional)
}