{"version": 3, "sources": ["../../src/wallet/wallet.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db } from '../database';\n\n// Custos em tokens para diferentes features de IA\nconst TOKEN_COSTS = {\n  'ai_protocol_generation': 10,\n  'ai_meal_suggestion': 2,\n  'ai_workout_creation': 8,\n  'ai_analysis': 5,\n  'ai_chat_message': 1\n} as const;\n\n@Injectable()\nexport class WalletService {\n\n  async getUserWallet(userId: number) {\n    try {\n      // Buscar pontos do usuário (SnapCoins)\n      const pointsResult = await db.execute(\n        `SELECT\n          total_points as totalPoints,\n          monthly_points as monthlyPoints,\n          yearly_points as yearlyPoints,\n          updated_at as lastUpdated\n        FROM user_points\n        WHERE user_id = ?`,\n        [userId]\n      );\n\n      // Verificar se pointsResult é um array e tem dados\n      const pointsData = Array.isArray(pointsResult) && pointsResult.length > 0 ? pointsResult[0] : null;\n      const points = (pointsData && Array.isArray(pointsData) && pointsData.length > 0) ? pointsData[0] : {\n        totalPoints: 0,\n        monthlyPoints: 0,\n        yearlyPoints: 0,\n        lastUpdated: null\n      };\n\n      // Calcular pontos gastos\n      const spentResult = await db.execute(\n        `SELECT COALESCE(SUM(points), 0) as totalSpent\n        FROM points_transactions\n        WHERE user_id = ? AND type = 'spent'`,\n        [userId]\n      );\n\n      const spentData = Array.isArray(spentResult) && spentResult.length > 0 ? spentResult[0] : null;\n      const totalSpent = (spentData && Array.isArray(spentData) && spentData.length > 0) ? spentData[0]?.totalSpent || 0 : 0;\n      const totalEarned = points.totalPoints + totalSpent;\n\n      // Buscar assinatura ativa do usuário para determinar o plano e tokens\n      const subscriptionResult = await db.execute(\n        `SELECT\n          us.snaptokens,\n          p.name as plan_name,\n          p.snaptokens as plan_snaptokens\n        FROM users_subscriptions us\n        INNER JOIN plans p ON us.plan_id = p.id\n        WHERE us.user_id = ? AND us.status = 'active' AND us.deleted_at IS NULL\n        ORDER BY us.created_at DESC\n        LIMIT 1`,\n        [userId]\n      );\n\n      const subscriptionData = Array.isArray(subscriptionResult) && subscriptionResult.length > 0 ? subscriptionResult[0] : null;\n      const subscription = (subscriptionData && Array.isArray(subscriptionData) && subscriptionData.length > 0) ? subscriptionData[0] : null;\n\n      // Se não tem assinatura ativa, usar valores padrão (plano gratuito)\n      const planName = subscription?.plan_name || 'Free';\n      const planTokenLimit = subscription?.snaptokens || subscription?.plan_snaptokens || 0;\n\n      // Buscar uso de tokens do mês atual\n      const currentMonth = new Date().getMonth() + 1;\n      const currentYear = new Date().getFullYear();\n\n      const tokensUsedResult = await db.execute(\n        `SELECT COALESCE(SUM(tokens_used), 0) as tokensUsed\n        FROM token_usage\n        WHERE user_id = ? AND MONTH(created_at) = ? AND YEAR(created_at) = ?`,\n        [userId, currentMonth, currentYear]\n      );\n\n      const tokensData = Array.isArray(tokensUsedResult) && tokensUsedResult.length > 0 ? tokensUsedResult[0] : null;\n      const tokensUsed = (tokensData && Array.isArray(tokensData) && tokensData.length > 0) ? tokensData[0]?.tokensUsed || 0 : 0;\n      const tokensRemaining = planTokenLimit === -1 ? -1 : Math.max(0, planTokenLimit - tokensUsed);\n\n      // Data do próximo reset (primeiro dia do próximo mês)\n      const nextMonth = new Date();\n      nextMonth.setMonth(nextMonth.getMonth() + 1, 1);\n      nextMonth.setHours(0, 0, 0, 0);\n\n      const wallet = {\n        snapCoins: {\n          total: points.totalPoints,\n          earned: totalEarned,\n          spent: totalSpent,\n          monthlyEarned: points.monthlyPoints,\n          yearlyEarned: points.yearlyPoints\n        },\n        snapTokens: {\n          total: planTokenLimit,\n          used: tokensUsed,\n          remaining: tokensRemaining,\n          monthlyLimit: planTokenLimit,\n          planType: planName,\n          resetDate: nextMonth.toISOString()\n        }\n      };\n\n      return {\n        status: 'success',\n        data: wallet\n      };\n\n    } catch (error) {\n      console.error('Error fetching user wallet:', error);\n      throw new HttpException(\n        'Failed to fetch wallet data',\n        HttpStatus.INTERNAL_SERVER_ERROR\n      );\n    }\n  }\n\n  async earnSnapCoins(userId: number, amount: number, reason: string, category?: string) {\n    try {\n      await db.execute('START TRANSACTION');\n\n      // Atualizar pontos do usuário\n      await db.execute(\n        `INSERT INTO user_points (user_id, total_points, monthly_points, yearly_points, updated_at)\n         VALUES (?, ?, ?, ?, NOW())\n         ON DUPLICATE KEY UPDATE\n         total_points = total_points + ?,\n         monthly_points = monthly_points + ?,\n         yearly_points = yearly_points + ?,\n         updated_at = NOW()`,\n        [userId, amount, amount, amount, amount, amount, amount]\n      );\n\n      // Registrar transação\n      await db.execute(\n        `INSERT INTO points_transactions (user_id, points, type, reason, category, created_at)\n         VALUES (?, ?, 'earned', ?, ?, NOW())`,\n        [userId, amount, reason, category || 'general']\n      );\n\n      await db.execute('COMMIT');\n\n      return {\n        status: 'success',\n        message: `Earned ${amount} SnapCoins for ${reason}`,\n        data: {\n          amount,\n          reason,\n          category: category || 'general',\n          type: 'earned'\n        }\n      };\n\n    } catch (error) {\n      await db.execute('ROLLBACK');\n      console.error('Error earning SnapCoins:', error);\n      throw new HttpException(\n        'Failed to earn SnapCoins',\n        HttpStatus.INTERNAL_SERVER_ERROR\n      );\n    }\n  }\n\n  async spendSnapCoins(userId: number, amount: number, reason: string, category?: string) {\n    try {\n      // Verificar se o usuário tem pontos suficientes\n      const [pointsResult] = await db.execute(\n        'SELECT total_points FROM user_points WHERE user_id = ?',\n        [userId]\n      );\n\n      const currentPoints = pointsResult[0]?.total_points || 0;\n      \n      if (currentPoints < amount) {\n        throw new HttpException(\n          'Insufficient SnapCoins',\n          HttpStatus.BAD_REQUEST\n        );\n      }\n\n      await db.execute('START TRANSACTION');\n\n      // Atualizar pontos do usuário\n      await db.execute(\n        `UPDATE user_points \n         SET total_points = total_points - ?, updated_at = NOW()\n         WHERE user_id = ?`,\n        [amount, userId]\n      );\n\n      // Registrar transação\n      await db.execute(\n        `INSERT INTO points_transactions (user_id, points, type, reason, category, created_at)\n         VALUES (?, ?, 'spent', ?, ?, NOW())`,\n        [userId, amount, reason, category || 'general']\n      );\n\n      await db.execute('COMMIT');\n\n      return {\n        status: 'success',\n        message: `Spent ${amount} SnapCoins on ${reason}`,\n        data: {\n          amount,\n          reason,\n          category: category || 'general',\n          type: 'spent'\n        }\n      };\n\n    } catch (error) {\n      await db.execute('ROLLBACK');\n      console.error('Error spending SnapCoins:', error);\n      \n      if (error instanceof HttpException) {\n        throw error;\n      }\n      \n      throw new HttpException(\n        'Failed to spend SnapCoins',\n        HttpStatus.INTERNAL_SERVER_ERROR\n      );\n    }\n  }\n\n  async useSnapTokens(userId: number, amount: number, feature: string) {\n    try {\n      // Verificar se o usuário pode usar a feature\n      const canUse = await this.canUseFeature(userId, feature);\n      \n      if (!canUse.data.canUse) {\n        throw new HttpException(\n          canUse.data.reason || 'Cannot use this feature',\n          HttpStatus.BAD_REQUEST\n        );\n      }\n\n      // Registrar uso de tokens (exceto para plano SnapMonster que é ilimitado)\n      const [userResult] = await db.execute(\n        'SELECT plan_type FROM users WHERE id = ?',\n        [userId]\n      );\n\n      const userPlan = userResult[0]?.plan_type || 'snapbasic';\n      \n      if (userPlan !== 'snapmonster') {\n        await db.execute(\n          `INSERT INTO token_usage (user_id, tokens_used, feature, created_at)\n           VALUES (?, ?, ?, NOW())`,\n          [userId, amount, feature]\n        );\n      }\n\n      return {\n        status: 'success',\n        message: `Used ${amount} SnapTokens for ${feature}`,\n        data: {\n          amount,\n          feature,\n          type: 'used'\n        }\n      };\n\n    } catch (error) {\n      console.error('Error using SnapTokens:', error);\n      \n      if (error instanceof HttpException) {\n        throw error;\n      }\n      \n      throw new HttpException(\n        'Failed to use SnapTokens',\n        HttpStatus.INTERNAL_SERVER_ERROR\n      );\n    }\n  }\n\n  async canUseFeature(userId: number, feature: string) {\n    try {\n      const cost = TOKEN_COSTS[feature as keyof typeof TOKEN_COSTS] || 1;\n\n      // Buscar assinatura ativa do usuário para determinar o plano e tokens\n      const subscriptionResult = await db.execute(\n        `SELECT\n          us.snaptokens,\n          p.name as plan_name,\n          p.snaptokens as plan_snaptokens\n        FROM users_subscriptions us\n        INNER JOIN plans p ON us.plan_id = p.id\n        WHERE us.user_id = ? AND us.status = 'active' AND us.deleted_at IS NULL\n        ORDER BY us.created_at DESC\n        LIMIT 1`,\n        [userId]\n      );\n\n      const subscriptionData = Array.isArray(subscriptionResult) && subscriptionResult.length > 0 ? subscriptionResult[0] : null;\n      const subscription = (subscriptionData && Array.isArray(subscriptionData) && subscriptionData.length > 0) ? subscriptionData[0] : null;\n\n      // Se não tem assinatura ativa, usar valores padrão (plano gratuito)\n      const planName = subscription?.plan_name || 'Free';\n      const planTokenLimit = subscription?.snaptokens || subscription?.plan_snaptokens || 0;\n\n      // Verificar se tem tokens ilimitados\n      if (planTokenLimit === -1) {\n        return {\n          status: 'success',\n          data: {\n            canUse: true,\n            reason: 'Unlimited tokens',\n            cost,\n            planType: planName\n          }\n        };\n      }\n\n      // Verificar uso de tokens do mês atual\n      const currentMonth = new Date().getMonth() + 1;\n      const currentYear = new Date().getFullYear();\n\n      const tokensUsedResult = await db.execute(\n        `SELECT COALESCE(SUM(tokens_used), 0) as tokensUsed\n        FROM token_usage\n        WHERE user_id = ? AND MONTH(created_at) = ? AND YEAR(created_at) = ?`,\n        [userId, currentMonth, currentYear]\n      );\n\n      const tokensData = Array.isArray(tokensUsedResult) && tokensUsedResult.length > 0 ? tokensUsedResult[0] : null;\n      const tokensUsed = (tokensData && Array.isArray(tokensData) && tokensData.length > 0) ? tokensData[0]?.tokensUsed || 0 : 0;\n      const tokensRemaining = Math.max(0, planTokenLimit - tokensUsed);\n\n      const canUse = tokensRemaining >= cost;\n\n      return {\n        status: 'success',\n        data: {\n          canUse,\n          reason: canUse ? 'Sufficient tokens available' : 'Insufficient tokens',\n          cost,\n          tokensRemaining,\n          monthlyLimit: planTokenLimit,\n          planType: planName\n        }\n      };\n\n    } catch (error) {\n      console.error('Error checking feature availability:', error);\n      throw new HttpException(\n        'Failed to check feature availability',\n        HttpStatus.INTERNAL_SERVER_ERROR\n      );\n    }\n  }\n}\n"], "names": ["WalletService", "TOKEN_COSTS", "getUserWallet", "userId", "pointsResult", "db", "execute", "pointsData", "Array", "isArray", "length", "points", "totalPoints", "monthlyPoints", "yearlyPoints", "lastUpdated", "spentResult", "spentData", "totalSpent", "totalEarned", "subscriptionResult", "subscriptionData", "subscription", "planName", "plan_name", "planTokenLimit", "<PERSON><PERSON><PERSON>", "plan_snaptokens", "currentMonth", "Date", "getMonth", "currentYear", "getFullYear", "tokensUsedResult", "tokensData", "tokensUsed", "tokensRemaining", "Math", "max", "nextMonth", "setMonth", "setHours", "wallet", "snapCoins", "total", "earned", "spent", "monthlyEarned", "yearlyEarned", "snapTokens", "used", "remaining", "monthlyLimit", "planType", "resetDate", "toISOString", "status", "data", "error", "console", "HttpException", "HttpStatus", "INTERNAL_SERVER_ERROR", "earnSnapCoins", "amount", "reason", "category", "message", "type", "spendSnapCoins", "currentPoints", "total_points", "BAD_REQUEST", "useSnapTokens", "feature", "canUse", "canUseFeature", "userResult", "userPlan", "plan_type", "cost"], "mappings": ";;;;+BAaaA;;;eAAAA;;;wBAbyC;0BACnC;;;;;;;AAEnB,kDAAkD;AAClD,MAAMC,cAAc;IAClB,0BAA0B;IAC1B,sBAAsB;IACtB,uBAAuB;IACvB,eAAe;IACf,mBAAmB;AACrB;AAGO,IAAA,AAAMD,gBAAN,MAAMA;IAEX,MAAME,cAAcC,MAAc,EAAE;QAClC,IAAI;YACF,uCAAuC;YACvC,MAAMC,eAAe,MAAMC,YAAE,CAACC,OAAO,CACnC,CAAC;;;;;;yBAMgB,CAAC,EAClB;gBAACH;aAAO;YAGV,mDAAmD;YACnD,MAAMI,aAAaC,MAAMC,OAAO,CAACL,iBAAiBA,aAAaM,MAAM,GAAG,IAAIN,YAAY,CAAC,EAAE,GAAG;YAC9F,MAAMO,SAAS,AAACJ,cAAcC,MAAMC,OAAO,CAACF,eAAeA,WAAWG,MAAM,GAAG,IAAKH,UAAU,CAAC,EAAE,GAAG;gBAClGK,aAAa;gBACbC,eAAe;gBACfC,cAAc;gBACdC,aAAa;YACf;YAEA,yBAAyB;YACzB,MAAMC,cAAc,MAAMX,YAAE,CAACC,OAAO,CAClC,CAAC;;4CAEmC,CAAC,EACrC;gBAACH;aAAO;YAGV,MAAMc,YAAYT,MAAMC,OAAO,CAACO,gBAAgBA,YAAYN,MAAM,GAAG,IAAIM,WAAW,CAAC,EAAE,GAAG;YAC1F,MAAME,aAAa,AAACD,aAAaT,MAAMC,OAAO,CAACQ,cAAcA,UAAUP,MAAM,GAAG,IAAKO,SAAS,CAAC,EAAE,EAAEC,cAAc,IAAI;YACrH,MAAMC,cAAcR,OAAOC,WAAW,GAAGM;YAEzC,sEAAsE;YACtE,MAAME,qBAAqB,MAAMf,YAAE,CAACC,OAAO,CACzC,CAAC;;;;;;;;eAQM,CAAC,EACR;gBAACH;aAAO;YAGV,MAAMkB,mBAAmBb,MAAMC,OAAO,CAACW,uBAAuBA,mBAAmBV,MAAM,GAAG,IAAIU,kBAAkB,CAAC,EAAE,GAAG;YACtH,MAAME,eAAe,AAACD,oBAAoBb,MAAMC,OAAO,CAACY,qBAAqBA,iBAAiBX,MAAM,GAAG,IAAKW,gBAAgB,CAAC,EAAE,GAAG;YAElI,oEAAoE;YACpE,MAAME,WAAWD,cAAcE,aAAa;YAC5C,MAAMC,iBAAiBH,cAAcI,cAAcJ,cAAcK,mBAAmB;YAEpF,oCAAoC;YACpC,MAAMC,eAAe,IAAIC,OAAOC,QAAQ,KAAK;YAC7C,MAAMC,cAAc,IAAIF,OAAOG,WAAW;YAE1C,MAAMC,mBAAmB,MAAM5B,YAAE,CAACC,OAAO,CACvC,CAAC;;4EAEmE,CAAC,EACrE;gBAACH;gBAAQyB;gBAAcG;aAAY;YAGrC,MAAMG,aAAa1B,MAAMC,OAAO,CAACwB,qBAAqBA,iBAAiBvB,MAAM,GAAG,IAAIuB,gBAAgB,CAAC,EAAE,GAAG;YAC1G,MAAME,aAAa,AAACD,cAAc1B,MAAMC,OAAO,CAACyB,eAAeA,WAAWxB,MAAM,GAAG,IAAKwB,UAAU,CAAC,EAAE,EAAEC,cAAc,IAAI;YACzH,MAAMC,kBAAkBX,mBAAmB,CAAC,IAAI,CAAC,IAAIY,KAAKC,GAAG,CAAC,GAAGb,iBAAiBU;YAElF,sDAAsD;YACtD,MAAMI,YAAY,IAAIV;YACtBU,UAAUC,QAAQ,CAACD,UAAUT,QAAQ,KAAK,GAAG;YAC7CS,UAAUE,QAAQ,CAAC,GAAG,GAAG,GAAG;YAE5B,MAAMC,SAAS;gBACbC,WAAW;oBACTC,OAAOjC,OAAOC,WAAW;oBACzBiC,QAAQ1B;oBACR2B,OAAO5B;oBACP6B,eAAepC,OAAOE,aAAa;oBACnCmC,cAAcrC,OAAOG,YAAY;gBACnC;gBACAmC,YAAY;oBACVL,OAAOnB;oBACPyB,MAAMf;oBACNgB,WAAWf;oBACXgB,cAAc3B;oBACd4B,UAAU9B;oBACV+B,WAAWf,UAAUgB,WAAW;gBAClC;YACF;YAEA,OAAO;gBACLC,QAAQ;gBACRC,MAAMf;YACR;QAEF,EAAE,OAAOgB,OAAO;YACdC,QAAQD,KAAK,CAAC,+BAA+BA;YAC7C,MAAM,IAAIE,qBAAa,CACrB,+BACAC,kBAAU,CAACC,qBAAqB;QAEpC;IACF;IAEA,MAAMC,cAAc5D,MAAc,EAAE6D,MAAc,EAAEC,MAAc,EAAEC,QAAiB,EAAE;QACrF,IAAI;YACF,MAAM7D,YAAE,CAACC,OAAO,CAAC;YAEjB,8BAA8B;YAC9B,MAAMD,YAAE,CAACC,OAAO,CACd,CAAC;;;;;;2BAMkB,CAAC,EACpB;gBAACH;gBAAQ6D;gBAAQA;gBAAQA;gBAAQA;gBAAQA;gBAAQA;aAAO;YAG1D,sBAAsB;YACtB,MAAM3D,YAAE,CAACC,OAAO,CACd,CAAC;6CACoC,CAAC,EACtC;gBAACH;gBAAQ6D;gBAAQC;gBAAQC,YAAY;aAAU;YAGjD,MAAM7D,YAAE,CAACC,OAAO,CAAC;YAEjB,OAAO;gBACLkD,QAAQ;gBACRW,SAAS,CAAC,OAAO,EAAEH,OAAO,eAAe,EAAEC,QAAQ;gBACnDR,MAAM;oBACJO;oBACAC;oBACAC,UAAUA,YAAY;oBACtBE,MAAM;gBACR;YACF;QAEF,EAAE,OAAOV,OAAO;YACd,MAAMrD,YAAE,CAACC,OAAO,CAAC;YACjBqD,QAAQD,KAAK,CAAC,4BAA4BA;YAC1C,MAAM,IAAIE,qBAAa,CACrB,4BACAC,kBAAU,CAACC,qBAAqB;QAEpC;IACF;IAEA,MAAMO,eAAelE,MAAc,EAAE6D,MAAc,EAAEC,MAAc,EAAEC,QAAiB,EAAE;QACtF,IAAI;YACF,gDAAgD;YAChD,MAAM,CAAC9D,aAAa,GAAG,MAAMC,YAAE,CAACC,OAAO,CACrC,0DACA;gBAACH;aAAO;YAGV,MAAMmE,gBAAgBlE,YAAY,CAAC,EAAE,EAAEmE,gBAAgB;YAEvD,IAAID,gBAAgBN,QAAQ;gBAC1B,MAAM,IAAIJ,qBAAa,CACrB,0BACAC,kBAAU,CAACW,WAAW;YAE1B;YAEA,MAAMnE,YAAE,CAACC,OAAO,CAAC;YAEjB,8BAA8B;YAC9B,MAAMD,YAAE,CAACC,OAAO,CACd,CAAC;;0BAEiB,CAAC,EACnB;gBAAC0D;gBAAQ7D;aAAO;YAGlB,sBAAsB;YACtB,MAAME,YAAE,CAACC,OAAO,CACd,CAAC;4CACmC,CAAC,EACrC;gBAACH;gBAAQ6D;gBAAQC;gBAAQC,YAAY;aAAU;YAGjD,MAAM7D,YAAE,CAACC,OAAO,CAAC;YAEjB,OAAO;gBACLkD,QAAQ;gBACRW,SAAS,CAAC,MAAM,EAAEH,OAAO,cAAc,EAAEC,QAAQ;gBACjDR,MAAM;oBACJO;oBACAC;oBACAC,UAAUA,YAAY;oBACtBE,MAAM;gBACR;YACF;QAEF,EAAE,OAAOV,OAAO;YACd,MAAMrD,YAAE,CAACC,OAAO,CAAC;YACjBqD,QAAQD,KAAK,CAAC,6BAA6BA;YAE3C,IAAIA,iBAAiBE,qBAAa,EAAE;gBAClC,MAAMF;YACR;YAEA,MAAM,IAAIE,qBAAa,CACrB,6BACAC,kBAAU,CAACC,qBAAqB;QAEpC;IACF;IAEA,MAAMW,cAActE,MAAc,EAAE6D,MAAc,EAAEU,OAAe,EAAE;QACnE,IAAI;YACF,6CAA6C;YAC7C,MAAMC,SAAS,MAAM,IAAI,CAACC,aAAa,CAACzE,QAAQuE;YAEhD,IAAI,CAACC,OAAOlB,IAAI,CAACkB,MAAM,EAAE;gBACvB,MAAM,IAAIf,qBAAa,CACrBe,OAAOlB,IAAI,CAACQ,MAAM,IAAI,2BACtBJ,kBAAU,CAACW,WAAW;YAE1B;YAEA,0EAA0E;YAC1E,MAAM,CAACK,WAAW,GAAG,MAAMxE,YAAE,CAACC,OAAO,CACnC,4CACA;gBAACH;aAAO;YAGV,MAAM2E,WAAWD,UAAU,CAAC,EAAE,EAAEE,aAAa;YAE7C,IAAID,aAAa,eAAe;gBAC9B,MAAMzE,YAAE,CAACC,OAAO,CACd,CAAC;kCACuB,CAAC,EACzB;oBAACH;oBAAQ6D;oBAAQU;iBAAQ;YAE7B;YAEA,OAAO;gBACLlB,QAAQ;gBACRW,SAAS,CAAC,KAAK,EAAEH,OAAO,gBAAgB,EAAEU,SAAS;gBACnDjB,MAAM;oBACJO;oBACAU;oBACAN,MAAM;gBACR;YACF;QAEF,EAAE,OAAOV,OAAO;YACdC,QAAQD,KAAK,CAAC,2BAA2BA;YAEzC,IAAIA,iBAAiBE,qBAAa,EAAE;gBAClC,MAAMF;YACR;YAEA,MAAM,IAAIE,qBAAa,CACrB,4BACAC,kBAAU,CAACC,qBAAqB;QAEpC;IACF;IAEA,MAAMc,cAAczE,MAAc,EAAEuE,OAAe,EAAE;QACnD,IAAI;YACF,MAAMM,OAAO/E,WAAW,CAACyE,QAAoC,IAAI;YAEjE,sEAAsE;YACtE,MAAMtD,qBAAqB,MAAMf,YAAE,CAACC,OAAO,CACzC,CAAC;;;;;;;;eAQM,CAAC,EACR;gBAACH;aAAO;YAGV,MAAMkB,mBAAmBb,MAAMC,OAAO,CAACW,uBAAuBA,mBAAmBV,MAAM,GAAG,IAAIU,kBAAkB,CAAC,EAAE,GAAG;YACtH,MAAME,eAAe,AAACD,oBAAoBb,MAAMC,OAAO,CAACY,qBAAqBA,iBAAiBX,MAAM,GAAG,IAAKW,gBAAgB,CAAC,EAAE,GAAG;YAElI,oEAAoE;YACpE,MAAME,WAAWD,cAAcE,aAAa;YAC5C,MAAMC,iBAAiBH,cAAcI,cAAcJ,cAAcK,mBAAmB;YAEpF,qCAAqC;YACrC,IAAIF,mBAAmB,CAAC,GAAG;gBACzB,OAAO;oBACL+B,QAAQ;oBACRC,MAAM;wBACJkB,QAAQ;wBACRV,QAAQ;wBACRe;wBACA3B,UAAU9B;oBACZ;gBACF;YACF;YAEA,uCAAuC;YACvC,MAAMK,eAAe,IAAIC,OAAOC,QAAQ,KAAK;YAC7C,MAAMC,cAAc,IAAIF,OAAOG,WAAW;YAE1C,MAAMC,mBAAmB,MAAM5B,YAAE,CAACC,OAAO,CACvC,CAAC;;4EAEmE,CAAC,EACrE;gBAACH;gBAAQyB;gBAAcG;aAAY;YAGrC,MAAMG,aAAa1B,MAAMC,OAAO,CAACwB,qBAAqBA,iBAAiBvB,MAAM,GAAG,IAAIuB,gBAAgB,CAAC,EAAE,GAAG;YAC1G,MAAME,aAAa,AAACD,cAAc1B,MAAMC,OAAO,CAACyB,eAAeA,WAAWxB,MAAM,GAAG,IAAKwB,UAAU,CAAC,EAAE,EAAEC,cAAc,IAAI;YACzH,MAAMC,kBAAkBC,KAAKC,GAAG,CAAC,GAAGb,iBAAiBU;YAErD,MAAMwC,SAASvC,mBAAmB4C;YAElC,OAAO;gBACLxB,QAAQ;gBACRC,MAAM;oBACJkB;oBACAV,QAAQU,SAAS,gCAAgC;oBACjDK;oBACA5C;oBACAgB,cAAc3B;oBACd4B,UAAU9B;gBACZ;YACF;QAEF,EAAE,OAAOmC,OAAO;YACdC,QAAQD,KAAK,CAAC,wCAAwCA;YACtD,MAAM,IAAIE,qBAAa,CACrB,wCACAC,kBAAU,CAACC,qBAAqB;QAEpC;IACF;AACF"}