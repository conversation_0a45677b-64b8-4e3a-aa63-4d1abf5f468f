import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBirthDateAndWeightToUsers1709001234570 implements MigrationInterface {
  name = 'AddBirthDateAndWeightToUsers1709001234570';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE users 
      ADD COLUMN birth_date DATE NULL,
      ADD COLUMN weight DECIMAL(5,2) NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE users 
      DROP COLUMN birth_date,
      DROP COLUMN weight
    `);
  }
}
