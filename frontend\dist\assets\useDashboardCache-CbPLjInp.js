const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-D0i1bWZj.js","assets/index-BDFmtPZQ.css"])))=>i.map(i=>d[i]);
import{u as l,q as o,a9 as s}from"./index-D0i1bWZj.js";const a={all:["dashboard"],overview:()=>[...a.all,"overview"],analytics:e=>[...a.all,"analytics",e],progress:()=>[...a.all,"progress"],weight:e=>[...a.progress(),"weight",e],strength:e=>[...a.progress(),"strength",e],assessments:()=>[...a.progress(),"assessments"],nutrition:()=>[...a.all,"nutrition"],nutritionalSummary:e=>[...a.nutrition(),"summary",e],nutritionAnalytics:e=>[...a.nutrition(),"analytics",e],caloricBalance:e=>[...a.nutrition(),"caloric-balance",e],meals:e=>[...a.nutrition(),"meals",e],workout:()=>[...a.all,"workout"],workoutAnalytics:e=>[...a.workout(),"analytics",e],workoutData:()=>[...a.workout(),"data"],hydration:()=>[...a.all,"hydration"],dailyHydration:e=>[...a.hydration(),"daily",e],ai:()=>[...a.all,"ai"],aiSuggestions:e=>[...a.ai(),"suggestions",e]};function P(){const e=l();return{queryKeys:a,invalidateAll:()=>{console.log("🔄 Invalidating all dashboard caches"),e.invalidateQueries({queryKey:a.all})},invalidateProgress:()=>{console.log("🔄 Invalidating progress caches"),e.invalidateQueries({queryKey:a.progress()})},invalidateNutrition:t=>{console.log("🔄 Invalidating nutrition caches",t?`for date: ${t}`:""),t?(e.invalidateQueries({queryKey:a.nutritionalSummary(t)}),e.invalidateQueries({queryKey:a.meals(t)})):e.invalidateQueries({queryKey:a.nutrition()})},invalidateWorkout:()=>{console.log("🔄 Invalidating workout caches"),e.invalidateQueries({queryKey:a.workout()})},invalidateHydration:t=>{console.log("🔄 Invalidating hydration caches",t?`for date: ${t}`:""),t?e.invalidateQueries({queryKey:a.dailyHydration(t)}):e.invalidateQueries({queryKey:a.hydration()})},invalidateAI:t=>{console.log("🔄 Invalidating AI caches",t?`for date: ${t}`:""),t?e.invalidateQueries({queryKey:a.aiSuggestions(t)}):e.invalidateQueries({queryKey:a.ai()})},invalidateAnalytics:t=>{console.log("🔄 Invalidating analytics caches",t?`for period: ${t}`:""),t?(e.invalidateQueries({queryKey:a.analytics(t)}),e.invalidateQueries({queryKey:a.nutritionAnalytics(t)}),e.invalidateQueries({queryKey:a.workoutAnalytics(t)}),e.invalidateQueries({queryKey:a.caloricBalance(t)})):e.invalidateQueries({queryKey:a.all})},prefetchDashboardData:async(t="7d")=>{const r=new Date().toISOString().split("T")[0];console.log("🚀 Prefetching dashboard data..."),await Promise.allSettled([e.prefetchQuery({queryKey:a.assessments(),queryFn:async()=>(await s(()=>import("./index-D0i1bWZj.js").then(i=>i.cf),__vite__mapDeps([0,1])).then(i=>i.apiService.get("users/assessments"))).data||null,staleTime:1e3*60*10}),e.prefetchQuery({queryKey:a.nutritionalSummary(r),queryFn:async()=>(await s(()=>import("./index-D0i1bWZj.js").then(i=>i.cf),__vite__mapDeps([0,1])).then(i=>i.apiService.get("dashboard/nutritional-summary",{searchParams:{date:r}}))).data||null,staleTime:1e3*60*5}),e.prefetchQuery({queryKey:a.analytics(t),queryFn:async()=>(await s(()=>import("./index-D0i1bWZj.js").then(i=>i.cf),__vite__mapDeps([0,1])).then(i=>i.apiService.get("dashboard/progress/analytics",{searchParams:{period:t}}))).data||null,...o.historical})])},prefetchPeriodData:async t=>{console.log(`🚀 Prefetching data for period: ${t}`),await Promise.allSettled([e.prefetchQuery({queryKey:a.weight(t),queryFn:async()=>(await s(()=>import("./index-D0i1bWZj.js").then(n=>n.cf),__vite__mapDeps([0,1])).then(n=>n.apiService.get("dashboard/progress/weight",{searchParams:{period:t}}))).data||null,...o.daily}),e.prefetchQuery({queryKey:a.analytics(t),queryFn:async()=>(await s(()=>import("./index-D0i1bWZj.js").then(n=>n.cf),__vite__mapDeps([0,1])).then(n=>n.apiService.get("dashboard/progress/analytics",{searchParams:{period:t}}))).data||null,...o.historical})])},cleanupStaleCache:()=>{console.log("🧹 Cleaning up stale dashboard cache");const t=Date.now()-1e3*60*60;e.getQueryCache().getAll().forEach(r=>{r.state.dataUpdatedAt<t&&r.queryKey[0]==="dashboard"&&e.removeQueries({queryKey:r.queryKey})})},setCacheData:(t,r)=>{console.log("💾 Setting cache data for:",t),e.setQueryData(t,r)},getCacheData:t=>e.getQueryData(t),onMealAdded:t=>{console.log("🍽️ Meal added, invalidating related caches"),e.invalidateQueries({queryKey:a.nutritionalSummary(t)}),e.invalidateQueries({queryKey:a.meals(t)}),e.invalidateQueries({queryKey:a.aiSuggestions(t)}),e.invalidateQueries({queryKey:a.caloricBalance("week")})},onWorkoutAdded:()=>{console.log("💪 Workout added, invalidating related caches"),e.invalidateQueries({queryKey:a.workout()}),e.invalidateQueries({queryKey:a.analytics("7d")}),e.invalidateQueries({queryKey:a.progress()})},onHydrationAdded:t=>{console.log("💧 Hydration added, invalidating related caches"),e.invalidateQueries({queryKey:a.dailyHydration(t)}),e.invalidateQueries({queryKey:a.aiSuggestions(t)})},onProgressAdded:()=>{console.log("📊 Progress added, invalidating related caches"),e.invalidateQueries({queryKey:a.progress()}),e.invalidateQueries({queryKey:a.analytics("30d")})}}}export{P as u};
