"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AuthController", {
    enumerable: true,
    get: function() {
        return AuthController;
    }
});
const _common = require("@nestjs/common");
const _authservice = require("./auth.service");
const _createuserdto = require("./create-user.dto");
const _jwtauthguard = require("./jwt-auth.guard");
const _passport = require("@nestjs/passport");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let AuthController = class AuthController {
    // login
    async login(user) {
        return this.authService.login(user);
    }
    // Rota POST /auth/register
    async register(createUserDto) {
        return this.authService.register(createUserDto);
    }
    // Refresh token
    async refreshToken({ refresh_token, device_uid }) {
        if (!refresh_token) {
            throw new Error('Refresh token is required');
        }
        if (!device_uid) {
            throw new Error('Device UID is required');
        }
        return this.authService.refreshToken(refresh_token, device_uid);
    }
    // Account recovery
    async recoverAccount({ userId }) {
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.authService.recoverAccount(userId);
    }
    getRole(role, req) {
        const userId = req.user.userId;
        return this.authService.getRole(userId, role);
    }
    async requestPasswordReset(body) {
        return this.authService.requestPasswordReset(body.email);
    }
    async resetPassword(body) {
        return this.authService.resetPassword(body.token, body.password);
    }
    async verifyEmail(body) {
        return this.authService.verifyEmail(body.token);
    }
    async resendVerification(body) {
        return this.authService.resendVerification(body.email);
    }
    async getSessions(req) {
        const userId = req.user.userId;
        return this.authService.getUserSessions(userId);
    }
    async terminateSession(sessionId, req) {
        const userId = req.user.userId;
        return this.authService.terminateSession(sessionId, userId);
    }
    async logoutAllSessions(req) {
        const userId = req.user.userId;
        return this.authService.logoutAllSessions(userId);
    }
    // Google OAuth Routes
    async googleAuth(req) {
    // This route initiates the Google OAuth flow
    }
    async googleAuthRedirect(req, res) {
        try {
            const result = await this.authService.loginWithOAuth(req.user, 'google');
            // Redirect to frontend with tokens
            const redirectUrl = `${process.env.GOOGLE_REDIRECT_URL}?access_token=${result.data.access_token}&refresh_token=${result.data.refresh_token}&device_uid=${result.data.device_uid}`;
            return res.redirect(redirectUrl);
        } catch (error) {
            console.error('Google OAuth error:', error);
            return res.redirect(`${process.env.GOOGLE_REDIRECT_URL}?error=oauth_failed`);
        }
    }
    // Apple OAuth Routes
    async appleAuth(req) {
    // This route initiates the Apple OAuth flow
    }
    async appleAuthRedirect(req, res) {
        try {
            const result = await this.authService.loginWithOAuth(req.user, 'apple');
            // Redirect to frontend with tokens
            const redirectUrl = `${process.env.APPLE_REDIRECT_URL}?access_token=${result.data.access_token}&refresh_token=${result.data.refresh_token}&device_uid=${result.data.device_uid}`;
            return res.redirect(redirectUrl);
        } catch (error) {
            console.error('Apple OAuth error:', error);
            return res.redirect(`${process.env.APPLE_REDIRECT_URL}?error=oauth_failed`);
        }
    }
    // Frontend-initiated OAuth endpoints
    async googleOAuthToken(body) {
        try {
            // Validate Google token and extract user info
            const result = await this.authService.validateGoogleToken(body.token, body.userInfo);
            return result;
        } catch (error) {
            console.error('Google OAuth token validation error:', error);
            throw new _common.HttpException('Invalid Google token', _common.HttpStatus.UNAUTHORIZED);
        }
    }
    async appleOAuthToken(body) {
        try {
            // Validate Apple token and extract user info
            const result = await this.authService.validateAppleToken(body.token, body.userInfo);
            return result;
        } catch (error) {
            console.error('Apple OAuth token validation error:', error);
            throw new _common.HttpException('Invalid Apple token', _common.HttpStatus.UNAUTHORIZED);
        }
    }
    constructor(authService){
        this.authService = authService;
    }
};
_ts_decorate([
    (0, _common.Post)('login'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
_ts_decorate([
    (0, _common.Post)('register'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createuserdto.CreateUserDto === "undefined" ? Object : _createuserdto.CreateUserDto
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
_ts_decorate([
    (0, _common.Post)('refresh'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "refreshToken", null);
_ts_decorate([
    (0, _common.Post)('recover-account'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "recoverAccount", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('role/:role'),
    _ts_param(0, (0, _common.Param)('role')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], AuthController.prototype, "getRole", null);
_ts_decorate([
    (0, _common.Post)('request-password-reset'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "requestPasswordReset", null);
_ts_decorate([
    (0, _common.Post)('reset-password'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
_ts_decorate([
    (0, _common.Post)('verify-email'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "verifyEmail", null);
_ts_decorate([
    (0, _common.Post)('resend-verification'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "resendVerification", null);
_ts_decorate([
    (0, _common.Get)('sessions'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "getSessions", null);
_ts_decorate([
    (0, _common.Post)('sessions/:id/terminate'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "terminateSession", null);
_ts_decorate([
    (0, _common.Post)('logout-all'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "logoutAllSessions", null);
_ts_decorate([
    (0, _common.Get)('google'),
    (0, _common.UseGuards)((0, _passport.AuthGuard)('google')),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "googleAuth", null);
_ts_decorate([
    (0, _common.Get)('google/callback'),
    (0, _common.UseGuards)((0, _passport.AuthGuard)('google')),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Res)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "googleAuthRedirect", null);
_ts_decorate([
    (0, _common.Get)('apple'),
    (0, _common.UseGuards)((0, _passport.AuthGuard)('apple')),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "appleAuth", null);
_ts_decorate([
    (0, _common.Post)('apple/callback'),
    (0, _common.UseGuards)((0, _passport.AuthGuard)('apple')),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Res)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "appleAuthRedirect", null);
_ts_decorate([
    (0, _common.Post)('oauth/google'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "googleOAuthToken", null);
_ts_decorate([
    (0, _common.Post)('oauth/apple'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AuthController.prototype, "appleOAuthToken", null);
AuthController = _ts_decorate([
    (0, _common.Controller)('auth'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _authservice.AuthService === "undefined" ? Object : _authservice.AuthService
    ])
], AuthController);

//# sourceMappingURL=auth.controller.js.map