import { IsString, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON>nt, IsN<PERSON>ber, Min, IsBoolean, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CreatePlanDto {
  @IsString()
  @IsNotEmpty()
  name: string; // Nome do plano

  @IsString()
  @IsOptional()
  description?: string; // Descrição do plano

  @IsNumber()
  @Min(0)
  price: number; // Preço do plano

  /*
  @IsString()
  currency: string; // Moeda
  */

  /*
  @IsString()
  frequency: 'daily' | 'weekly' | 'monthly' | 'annually'; // Frequência

  @IsNumber()
  @Min(1)
  interval_value: number; // Valor do intervalo
  */

  @IsString()
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'semiannual' | 'annually'; // Período

  @IsBoolean()
  @IsOptional()
  isActive?: boolean; // Ativo

  @IsNumber()
  @Min(0)
  @IsOptional()
  snaptokens?: number; // Snaptokens

  /*
  @IsBoolean()
  @IsOptional()
  allows_trial?: boolean; // Permite trial

  @IsNumber()
  @Min(0)
  @IsOptional()
  trial_period_days?: number; // Dias de trial
  */

  @IsNumber()
  @Min(0)
  @IsOptional()
  affiliate_master_commission_percent?: number; // Comissão do master

  @IsNumber()
  @Min(0)
  @IsOptional()
  affiliate_commission_percent?: number; // Comissão do afiliado
}