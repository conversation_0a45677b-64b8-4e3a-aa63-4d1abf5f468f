{"version": 3, "sources": ["../../../src/admin/dto/create-exercise.dto.ts"], "sourcesContent": ["// src/admin/dto/create-exercise.dto.ts\r\nimport { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class CreateExerciseDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string; // Nome do exercício\r\n\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  muscle_group_id: number; // ID do grupo muscular\r\n\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  equipment_id: number; // ID do equipamento\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  media_url?: string; // URL da mídia (opcional)\r\n\r\n  @IsArray()\r\n  @IsOptional()\r\n  // @ArrayNotEmpty()\r\n  target_muscles?: string[]; // Músculos-alvo\r\n\r\n  @IsArray()\r\n  @IsOptional()\r\n  // @ArrayNotEmpty()\r\n  synergistic_muscles?: string[]; // Músculos sinergistas\r\n\r\n  @IsArray()\r\n  @IsOptional()\r\n  // @ArrayNotEmpty()\r\n  instructions?: string[]; // Instruções\r\n\r\n  @IsArray()\r\n  @IsOptional()\r\n  tips?: string[]; // Dicas (opcional)\r\n}"], "names": ["CreateExerciseDto", "Number"], "mappings": "AAAA,uCAAuC;;;;;+BAI1BA;;;eAAAA;;;gCAHmE;kCAC3D;;;;;;;;;;AAEd,IAAA,AAAMA,oBAAN,MAAMA;AAmCb;;;;;;;;oCA7BcC;;;;;oCAIAA"}