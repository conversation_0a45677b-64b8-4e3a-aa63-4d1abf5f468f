{"version": 3, "sources": ["../../../src/admin/dto/update-user.dto.ts"], "sourcesContent": ["// src/admin/dto/update-user.dto.ts\r\nimport { IsString, IsEmail, IsNotEmpty, IsInt, IsOptional } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class UpdateUserDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @IsEmail()\r\n  @IsNotEmpty()\r\n  email: string;\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  password?: string; // Senha é opcional\r\n\r\n  @IsInt()\r\n  @Type(() => Number) // Converte role_id para número\r\n  role_id: number;\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  photo?: string;\r\n}"], "names": ["UpdateUserDto", "Number"], "mappings": "AAAA,mCAAmC;;;;;+BAItBA;;;eAAAA;;;gCAHoD;kCAC5C;;;;;;;;;;AAEd,IAAA,AAAMA,gBAAN,MAAMA;AAoBb;;;;;;;;;;;;;;;;;;oCANcC"}