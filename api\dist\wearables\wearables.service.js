"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "WearablesService", {
    enumerable: true,
    get: function() {
        return WearablesService;
    }
});
const _common = require("@nestjs/common");
const _database = require("../database");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let WearablesService = class WearablesService {
    async getConnectedDevices(userId) {
        try {
            const sql = `
        SELECT 
          id,
          device_type as deviceType,
          device_name as deviceName,
          brand,
          model,
          status,
          last_sync as lastSync,
          sync_frequency as syncFrequency,
          connected_at as connectedAt,
          access_token_expires as accessTokenExpires
        FROM user_wearables 
        WHERE user_id = ? AND status = 'connected'
        ORDER BY connected_at DESC
      `;
            const [devices] = await _database.db.execute(sql, [
                userId
            ]);
            return {
                status: 'success',
                data: devices
            };
        } catch (error) {
            console.error('Error getting connected devices:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get connected devices'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSupportedDevices() {
        try {
            const supportedDevices = [
                {
                    id: 'fitbit',
                    name: 'Fitbit',
                    brand: 'Fitbit',
                    types: [
                        'fitness_tracker',
                        'smartwatch'
                    ],
                    features: [
                        'steps',
                        'heart_rate',
                        'sleep',
                        'calories',
                        'distance'
                    ],
                    oauth_required: true,
                    icon: 'fitbit-icon.png'
                },
                {
                    id: 'garmin',
                    name: 'Garmin',
                    brand: 'Garmin',
                    types: [
                        'fitness_tracker',
                        'smartwatch',
                        'gps_watch'
                    ],
                    features: [
                        'steps',
                        'heart_rate',
                        'sleep',
                        'calories',
                        'distance',
                        'gps'
                    ],
                    oauth_required: true,
                    icon: 'garmin-icon.png'
                },
                {
                    id: 'apple_health',
                    name: 'Apple Health',
                    brand: 'Apple',
                    types: [
                        'health_app'
                    ],
                    features: [
                        'steps',
                        'heart_rate',
                        'sleep',
                        'calories',
                        'workouts'
                    ],
                    oauth_required: true,
                    icon: 'apple-health-icon.png'
                },
                {
                    id: 'google_fit',
                    name: 'Google Fit',
                    brand: 'Google',
                    types: [
                        'health_app'
                    ],
                    features: [
                        'steps',
                        'heart_rate',
                        'calories',
                        'workouts'
                    ],
                    oauth_required: true,
                    icon: 'google-fit-icon.png'
                },
                {
                    id: 'samsung_health',
                    name: 'Samsung Health',
                    brand: 'Samsung',
                    types: [
                        'health_app'
                    ],
                    features: [
                        'steps',
                        'heart_rate',
                        'sleep',
                        'calories'
                    ],
                    oauth_required: true,
                    icon: 'samsung-health-icon.png'
                },
                {
                    id: 'polar',
                    name: 'Polar',
                    brand: 'Polar',
                    types: [
                        'fitness_tracker',
                        'heart_rate_monitor'
                    ],
                    features: [
                        'heart_rate',
                        'calories',
                        'workouts'
                    ],
                    oauth_required: true,
                    icon: 'polar-icon.png'
                }
            ];
            return {
                status: 'success',
                data: supportedDevices
            };
        } catch (error) {
            console.error('Error getting supported devices:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get supported devices'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async connectDevice(userId, deviceData) {
        try {
            const { deviceType, deviceName, brand, model, accessToken, refreshToken, expiresIn, syncFrequency = 'hourly' } = deviceData;
            // Check if device is already connected
            const [existing] = await _database.db.execute('SELECT id FROM user_wearables WHERE user_id = ? AND device_type = ? AND status = "connected"', [
                userId,
                deviceType
            ]);
            if (existing[0]) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Device type already connected'
                }, _common.HttpStatus.CONFLICT);
            }
            const expiresAt = expiresIn ? new Date(Date.now() + expiresIn * 1000).toISOString() : null;
            const [result] = await _database.db.execute(`INSERT INTO user_wearables (
          user_id, device_type, device_name, brand, model, status,
          access_token, refresh_token, access_token_expires,
          sync_frequency, connected_at, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, 'connected', ?, ?, ?, ?, NOW(), NOW(), NOW())`, [
                userId,
                deviceType,
                deviceName,
                brand,
                model,
                accessToken,
                refreshToken,
                expiresAt,
                syncFrequency
            ]);
            // Trigger initial sync
            await this.syncData(userId, {
                deviceId: result.insertId
            });
            return {
                status: 'success',
                message: 'Device connected successfully',
                data: {
                    id: result.insertId,
                    deviceType,
                    deviceName,
                    status: 'connected'
                }
            };
        } catch (error) {
            console.error('Error connecting device:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to connect device'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async disconnectDevice(userId, deviceId) {
        try {
            const result = await _database.db.execute('UPDATE user_wearables SET status = "disconnected", updated_at = NOW() WHERE id = ? AND user_id = ?', [
                deviceId,
                userId
            ]);
            if (result.affectedRows === 0) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Device not found or not accessible'
                }, _common.HttpStatus.NOT_FOUND);
            }
            return {
                status: 'success',
                message: 'Device disconnected successfully'
            };
        } catch (error) {
            console.error('Error disconnecting device:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to disconnect device'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSyncedData(userId, query) {
        try {
            const { startDate, endDate, dataType, deviceId, page = 1, limit = 100, type// New parameter for recovery-specific data
             } = query;
            // Handle recovery-specific data request
            if (type === 'recovery') {
                return this.getRecoveryData(userId, query);
            }
            const offset = (page - 1) * limit;
            let sql = `
        SELECT
          wd.id,
          wd.data_type as dataType,
          wd.value,
          wd.unit,
          wd.recorded_at as recordedAt,
          wd.synced_at as syncedAt,
          uw.device_name as deviceName,
          uw.brand
        FROM wearable_data wd
        INNER JOIN user_wearables uw ON wd.device_id = uw.id
        WHERE wd.user_id = ?
      `;
            const params = [
                userId
            ];
            if (startDate) {
                sql += ` AND wd.recorded_at >= ?`;
                params.push(startDate);
            }
            if (endDate) {
                sql += ` AND wd.recorded_at <= ?`;
                params.push(endDate);
            }
            if (dataType) {
                sql += ` AND wd.data_type = ?`;
                params.push(dataType);
            }
            if (deviceId) {
                sql += ` AND wd.device_id = ?`;
                params.push(deviceId);
            }
            sql += ` ORDER BY wd.recorded_at DESC LIMIT ? OFFSET ?`;
            params.push(limit, offset);
            const [data] = await _database.db.execute(sql, params);
            return {
                status: 'success',
                data: {
                    records: data,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit)
                    }
                }
            };
        } catch (error) {
            console.error('Error getting synced data:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get synced data'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getRecoveryData(userId, query) {
        try {
            console.log('🔄 Getting recovery data for user:', userId);
            const { period = 'week' } = query;
            // Calculate date range
            const endDate = new Date();
            const startDate = new Date();
            switch(period){
                case 'week':
                    startDate.setDate(endDate.getDate() - 7);
                    break;
                case 'month':
                    startDate.setMonth(endDate.getMonth() - 1);
                    break;
                default:
                    startDate.setDate(endDate.getDate() - 7);
            }
            // For now, return enhanced mock data that would come from real wearables
            // In production, this would query actual wearable data from the database
            const recoveryData = {
                sleep: {
                    average_duration: 7.2 + (Math.random() - 0.5) * 1.5,
                    quality_score: 75 + Math.random() * 20,
                    deep_sleep_percentage: 20 + Math.random() * 10,
                    rem_sleep_percentage: 15 + Math.random() * 10,
                    efficiency: 80 + Math.random() * 15,
                    trend: Math.random() > 0.5 ? 'improving' : 'stable'
                },
                hrv: {
                    average: 35 + Math.random() * 20,
                    trend: Math.random() > 0.6 ? 'improving' : 'stable',
                    recovery_score: 65 + Math.random() * 25 // 65-90
                },
                stress: {
                    average_level: 20 + Math.random() * 40,
                    trend: Math.random() > 0.7 ? 'decreasing' : 'stable'
                },
                energy: {
                    average_level: 60 + Math.random() * 30,
                    trend: Math.random() > 0.6 ? 'improving' : 'stable'
                },
                heart_rate: {
                    resting_average: 58 + Math.random() * 12,
                    max_recorded: 175 + Math.random() * 20,
                    zones: {
                        zone1: 20 + Math.random() * 10,
                        zone2: 30 + Math.random() * 10,
                        zone3: 25 + Math.random() * 10,
                        zone4: 10 + Math.random() * 10,
                        zone5: 5 + Math.random() * 5
                    }
                },
                period,
                last_updated: new Date().toISOString()
            };
            return {
                status: 'success',
                data: recoveryData
            };
        } catch (error) {
            console.error('Error getting recovery data:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get recovery data'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async syncData(userId, syncOptions) {
        try {
            const { deviceId, dataTypes } = syncOptions || {};
            // Get connected devices
            let sql = `
        SELECT id, device_type, access_token, refresh_token, last_sync
        FROM user_wearables 
        WHERE user_id = ? AND status = 'connected'
      `;
            const params = [
                userId
            ];
            if (deviceId) {
                sql += ` AND id = ?`;
                params.push(deviceId);
            }
            const [devices] = await _database.db.execute(sql, params);
            if (!devices.length) {
                return {
                    status: 'success',
                    message: 'No devices to sync',
                    data: {
                        synced: 0
                    }
                };
            }
            let totalSynced = 0;
            for (const device of devices){
                try {
                    const syncResult = await this.syncDeviceData(device, dataTypes);
                    totalSynced += syncResult.count;
                    // Update last sync time
                    await _database.db.execute('UPDATE user_wearables SET last_sync = NOW() WHERE id = ?', [
                        device.id
                    ]);
                } catch (error) {
                    console.error(`Error syncing device ${device.id}:`, error);
                }
            }
            return {
                status: 'success',
                message: `Synced ${totalSynced} records from ${devices.length} device(s)`,
                data: {
                    synced: totalSynced
                }
            };
        } catch (error) {
            console.error('Error syncing data:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to sync data'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async syncDeviceData(device, dataTypes) {
        // This would implement actual API calls to device providers
        // For now, return mock sync result
        const mockData = [
            {
                type: 'steps',
                value: 8500,
                unit: 'steps'
            },
            {
                type: 'heart_rate',
                value: 72,
                unit: 'bpm'
            },
            {
                type: 'calories',
                value: 2100,
                unit: 'kcal'
            }
        ];
        let syncedCount = 0;
        for (const data of mockData){
            if (!dataTypes || dataTypes.includes(data.type)) {
                // Insert mock data
                await _database.db.execute(`INSERT INTO wearable_data (
            user_id, device_id, data_type, value, unit, 
            recorded_at, synced_at, created_at
          ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), NOW())`, [
                    device.user_id,
                    device.id,
                    data.type,
                    data.value,
                    data.unit
                ]);
                syncedCount++;
            }
        }
        return {
            count: syncedCount
        };
    }
    async getStepsData(userId, query) {
        return this.getDataByType(userId, 'steps', query);
    }
    async getHeartRateData(userId, query) {
        return this.getDataByType(userId, 'heart_rate', query);
    }
    async getSleepData(userId, query) {
        return this.getDataByType(userId, 'sleep', query);
    }
    async getCaloriesData(userId, query) {
        return this.getDataByType(userId, 'calories', query);
    }
    async getDataByType(userId, dataType, query) {
        try {
            const { startDate, endDate, aggregation = 'daily' } = query;
            let sql = `
        SELECT 
          DATE(recorded_at) as date,
          AVG(value) as avgValue,
          MIN(value) as minValue,
          MAX(value) as maxValue,
          COUNT(*) as recordCount,
          unit
        FROM wearable_data 
        WHERE user_id = ? AND data_type = ?
      `;
            const params = [
                userId,
                dataType
            ];
            if (startDate) {
                sql += ` AND recorded_at >= ?`;
                params.push(startDate);
            }
            if (endDate) {
                sql += ` AND recorded_at <= ?`;
                params.push(endDate);
            }
            sql += ` GROUP BY DATE(recorded_at), unit ORDER BY date DESC`;
            const [data] = await _database.db.execute(sql, params);
            return {
                status: 'success',
                data: {
                    dataType,
                    aggregation,
                    records: data
                }
            };
        } catch (error) {
            console.error(`Error getting ${dataType} data:`, error);
            throw new _common.HttpException({
                status: 'error',
                message: `Failed to get ${dataType} data`
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async addManualData(userId, data) {
        try {
            const { dataType, value, unit, recordedAt } = data;
            await _database.db.execute(`INSERT INTO wearable_data (
          user_id, device_id, data_type, value, unit, 
          recorded_at, synced_at, created_at, source
        ) VALUES (?, NULL, ?, ?, ?, ?, NOW(), NOW(), 'manual')`, [
                userId,
                dataType,
                value,
                unit,
                recordedAt || new Date().toISOString()
            ]);
            return {
                status: 'success',
                message: 'Manual data added successfully'
            };
        } catch (error) {
            console.error('Error adding manual data:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to add manual data'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSyncStatus(userId) {
        try {
            const sql = `
        SELECT 
          device_type as deviceType,
          device_name as deviceName,
          status,
          last_sync as lastSync,
          sync_frequency as syncFrequency,
          CASE 
            WHEN last_sync IS NULL THEN 'never'
            WHEN last_sync < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'outdated'
            ELSE 'recent'
          END as syncStatus
        FROM user_wearables 
        WHERE user_id = ?
        ORDER BY last_sync DESC
      `;
            const [devices] = await _database.db.execute(sql, [
                userId
            ]);
            return {
                status: 'success',
                data: devices
            };
        } catch (error) {
            console.error('Error getting sync status:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get sync status'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // OAuth integration methods (to be implemented with actual APIs)
    async connectFitbit(userId, authData) {
        // Implementation for Fitbit OAuth
        return this.connectOAuthDevice(userId, 'fitbit', authData);
    }
    async connectGarmin(userId, authData) {
        // Implementation for Garmin OAuth
        return this.connectOAuthDevice(userId, 'garmin', authData);
    }
    async connectAppleHealth(userId, authData) {
        // Implementation for Apple Health OAuth
        return this.connectOAuthDevice(userId, 'apple_health', authData);
    }
    async connectGoogleFit(userId, authData) {
        // Implementation for Google Fit OAuth
        return this.connectOAuthDevice(userId, 'google_fit', authData);
    }
    async connectOAuthDevice(userId, deviceType, authData) {
        try {
            const { code, redirectUri } = authData;
            // This would implement actual OAuth flow
            // For now, return success with mock data
            return {
                status: 'success',
                message: `${deviceType} connected successfully`,
                data: {
                    deviceType,
                    status: 'connected',
                    features: [
                        'steps',
                        'heart_rate',
                        'calories'
                    ]
                }
            };
        } catch (error) {
            console.error(`Error connecting ${deviceType}:`, error);
            throw new _common.HttpException({
                status: 'error',
                message: `Failed to connect ${deviceType}`
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
WearablesService = _ts_decorate([
    (0, _common.Injectable)()
], WearablesService);

//# sourceMappingURL=wearables.service.js.map