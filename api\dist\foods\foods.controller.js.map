{"version": 3, "sources": ["../../src/foods/foods.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Body, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { FoodsService } from './foods.service';\n\n@Controller('foods')\nexport class FoodsController {\n  constructor(private readonly foodsService: FoodsService) {}\n\n  @Get('search')\n  async searchFoods(@Query() query: { q: string; limit?: number; source?: string }) {\n    return this.foodsService.searchFoods(query);\n  }\n\n  @Get('search/nutritionix')\n  async searchNutritionix(@Query() query: { q: string }) {\n    return this.foodsService.searchNutritionix(query.q);\n  }\n\n  @Get('search/edamam')\n  async searchEdamam(@Query() query: { q: string }) {\n    return this.foodsService.searchEdamam(query.q);\n  }\n\n  @Get('search/openfoodfacts')\n  async searchOpenFoodFacts(@Query() query: { q: string }) {\n    return this.foodsService.searchOpenFoodFacts(query.q);\n  }\n\n  @Post('custom')\n  @UseGuards(JwtAuthGuard)\n  async createCustomFood(@Body() foodData: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.foodsService.createCustomFood(foodData, userId);\n  }\n\n  @Get('custom')\n  @UseGuards(JwtAuthGuard)\n  async getCustomFoods(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.foodsService.getCustomFoods(userId);\n  }\n\n  @Post('import')\n  @UseGuards(JwtAuthGuard)\n  async importFoodDatabase(@Body() importData: any) {\n    return this.foodsService.importFoodDatabase(importData);\n  }\n\n  @Get('categories')\n  async getFoodCategories() {\n    return this.foodsService.getFoodCategories();\n  }\n\n  @Get('popular')\n  async getPopularFoods(@Query() query: { limit?: number }) {\n    return this.foodsService.getPopularFoods(query);\n  }\n\n  @Get('recent')\n  @UseGuards(JwtAuthGuard)\n  async getRecentFoods(@Request() req: any, @Query() query: { limit?: number }) {\n    const userId = req.user.userId;\n    return this.foodsService.getRecentFoods(userId, query);\n  }\n}\n"], "names": ["FoodsController", "searchFoods", "query", "foodsService", "searchNutritionix", "q", "searchEdamam", "searchOpenFoodFacts", "createCustomFood", "foodData", "req", "userId", "user", "getCustomFoods", "importFoodDatabase", "importData", "getFoodCategories", "getPopularFoods", "getRecentFoods", "constructor"], "mappings": ";;;;+BAaaA;;;eAAAA;;;wBALN;8BACsB;8BACA;;;;;;;;;;;;;;;AAGtB,IAAA,AAAMA,kBAAN,MAAMA;IAGX,MACMC,YAAY,AAASC,KAAqD,EAAE;QAChF,OAAO,IAAI,CAACC,YAAY,CAACF,WAAW,CAACC;IACvC;IAEA,MACME,kBAAkB,AAASF,KAAoB,EAAE;QACrD,OAAO,IAAI,CAACC,YAAY,CAACC,iBAAiB,CAACF,MAAMG,CAAC;IACpD;IAEA,MACMC,aAAa,AAASJ,KAAoB,EAAE;QAChD,OAAO,IAAI,CAACC,YAAY,CAACG,YAAY,CAACJ,MAAMG,CAAC;IAC/C;IAEA,MACME,oBAAoB,AAASL,KAAoB,EAAE;QACvD,OAAO,IAAI,CAACC,YAAY,CAACI,mBAAmB,CAACL,MAAMG,CAAC;IACtD;IAEA,MAEMG,iBAAiB,AAAQC,QAAa,EAAE,AAAWC,GAAQ,EAAE;QACjE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACR,YAAY,CAACK,gBAAgB,CAACC,UAAUE;IACtD;IAEA,MAEME,eAAe,AAAWH,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACR,YAAY,CAACU,cAAc,CAACF;IAC1C;IAEA,MAEMG,mBAAmB,AAAQC,UAAe,EAAE;QAChD,OAAO,IAAI,CAACZ,YAAY,CAACW,kBAAkB,CAACC;IAC9C;IAEA,MACMC,oBAAoB;QACxB,OAAO,IAAI,CAACb,YAAY,CAACa,iBAAiB;IAC5C;IAEA,MACMC,gBAAgB,AAASf,KAAyB,EAAE;QACxD,OAAO,IAAI,CAACC,YAAY,CAACc,eAAe,CAACf;IAC3C;IAEA,MAEMgB,eAAe,AAAWR,GAAQ,EAAE,AAASR,KAAyB,EAAE;QAC5E,MAAMS,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACR,YAAY,CAACe,cAAc,CAACP,QAAQT;IAClD;IAzDAiB,YAAY,AAAiBhB,YAA0B,CAAE;aAA5BA,eAAAA;IAA6B;AA0D5D"}