{"version": 3, "sources": ["../../../src/admin/dto/get-coach-clients-query.dto.ts"], "sourcesContent": ["// src/admin/dto/get-all-users-query.dto.ts\r\nimport { Type } from 'class-transformer';\r\nimport { IsOptional, IsString, IsInt } from 'class-validator';\r\n\r\nexport class GetCoachClientsQueryDto {\r\n  @IsOptional()\r\n  @IsString()\r\n  q?: string; // Nome ou email\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  page?: number;\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  limit?: number;\r\n}"], "names": ["GetCoachClientsQueryDto", "Number"], "mappings": "AAAA,2CAA2C;;;;;+BAI9BA;;;eAAAA;;;kCAHQ;gCACuB;;;;;;;;;;AAErC,IAAA,AAAMA,0BAAN,MAAMA;AAYb;;;;;;;;oCANcC;;;;;oCAIAA"}