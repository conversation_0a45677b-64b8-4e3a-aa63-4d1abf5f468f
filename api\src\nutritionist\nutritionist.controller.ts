import { Body, Controller, Get, Param, Post, Query, Request, UseGuards } from '@nestjs/common';
import { NutritionistService } from './nutritionist.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { GetNutritionistClientsQueryDto } from '../admin/dto/get-nutritionist-clients-query.dto';
import { CreateNutritionistProtocolDto } from '../dto/create-nutritionist-protocol.dto';
import { CreateNutritionistClientProtocolDto } from '../dto/create-nutritionist-client-protocol.dto';
import { ImportNutritionistClientProtocolDto } from '../dto/import-nutritionist-client-protocol.dto';

@Controller('nutritionist')
export class NutritionistController {
    constructor(private readonly nutritionistService: NutritionistService) {}

    @Get('stats')
    getStats() {
        return {
            status: 'success',
            data: {
                total_clients: 99,
                active_protocols: 99,
                completion_rate: 99,
            },
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('clients')
    getClients(@Query() query: GetNutritionistClientsQueryDto, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.nutritionistService.getClients(query, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('clients/:id')
    getClient(@Param('id') id: number, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.nutritionistService.getClient(id, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('protocols')
    createProtocol(@Body() createNutritionistProtocolDto: CreateNutritionistProtocolDto, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.nutritionistService.createProtocol(createNutritionistProtocolDto, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('protocols')
    getProtocols(@Request() req: any) {
        const userId: number = req.user?.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.nutritionistService.getProtocols(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('clients/:id/protocols')
    getClientProtocols(@Param('id') id: number, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }
        return this.nutritionistService.clientProtocols(id, userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('clients/:id/protocols')
    createClientProtocol(@Param('id') id: number, @Body() createNutritionistClientProtocolDto: CreateNutritionistClientProtocolDto, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.nutritionistService.createClientProtocol(id, createNutritionistClientProtocolDto, userId);
    }

    // import protocol
    @UseGuards(JwtAuthGuard)
    @Post('clients/:id/protocols/import')
    importClientProtocol(@Param('id') id: number, @Body() importNutritionistClientProtocolDto: ImportNutritionistClientProtocolDto, @Request() req: any) {
        const userId: number = req.user.userId;

        if (!userId) {
            return {
                status: 'error',
                message: 'User not found',
            };
        }

        return this.nutritionistService.importClientProtocol(id, importNutritionistClientProtocolDto, userId);
    }
    
}
