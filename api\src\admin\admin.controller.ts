import { Body, Controller, Delete, Get, NotFoundException, Param, Post, Put, Query, Request, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AdminService } from './admin.service';
import { GetAllUsersQueryDto } from './get-users-query.dto';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { GetAllFoodsQueryDto } from './dto/get-all-foods-query.dto';
import { CreateFoodDto } from './dto/create-food.dto';
import { UpdateFoodDto } from './dto/update-food.dto';
import { CreateExerciseDto } from './dto/create-exercise.dto';
import { GetAllExercisesQueryDto } from './dto/get-all-exercises-query.dto';
import { CreatePlanDto } from './dto/plans/create-plan.dto';
import { UpdatePlanDto } from './dto/plans/update-plan.dto';
import { ConfigPlanDto } from './dto/plans/config-plan.dto';

@Controller('admin')
export class AdminController {
    constructor(private readonly adminService: AdminService) {}

    // Stats
    @Get('stats')
    // @UseGuards(JwtAuthGuard)
    getStats() {
        return this.adminService.getStats();
    }

    // Users
    @Get('users')
    // @UseGuards(JwtAuthGuard)
    getAllUsers(@Query() query: GetAllUsersQueryDto) {
        return this.adminService.getAllUsers(query);
    }

    @Post('users')
    @UseGuards(JwtAuthGuard)
    createUser(@Body() createUserDto: CreateUserDto) {
        return this.adminService.createUser(createUserDto);
    }

    @Put('users/:id')
    @UseGuards(JwtAuthGuard)
    async updateUser(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
      return this.adminService.updateUser(Number(id), updateUserDto);
    }

    @Delete('users/:id')
    async deleteUser(@Param('id') id: string) {
      return this.adminService.deleteUser(Number(id));
    }

    // Foods
    @Get('/select_options/:type')
    @UseGuards(JwtAuthGuard)
    // @UseGuards(JwtAuthGuard)
    async getSelectOptions(@Param('type') type: string) {
        /*
      const types_allowed = ['foods_categories'];

      if (!types_allowed.includes(type)) {
        throw new NotFoundException('Type not found');
      }
        */

      return this.adminService.getSelectOptions(type);
    }

    @Get('foods')
    async getAllFoods(@Query() query: GetAllFoodsQueryDto) {
      return this.adminService.getAllFoods(query);
    }

    @Get('foods/search')
    async searchFoods(@Query() query: { q: string; limit?: number }) {
      return this.adminService.searchFoods(query);
    }

    @Post('foods')
    async createFood(@Body() createFoodDto: CreateFoodDto) {
      return this.adminService.createFood(createFoodDto);
    }

    @Put('foods/:id')
    async updateFood(@Param('id') id: string, @Body() updateFoodDto: UpdateFoodDto) {
      return this.adminService.updateFood(Number(id), updateFoodDto);
    }

    @Delete('foods/:id')
    async deleteFood(@Param('id') id: string) {
      return this.adminService.deleteFood(Number(id));
    }

    // Exercícios
    @Get('exercises')
    async getAllExercises(@Query() query: GetAllExercisesQueryDto) {
      return this.adminService.getAllExercises(query);
    }

    @Post('exercises')
    async createExercise(@Body() createExercise: CreateExerciseDto) {
      return this.adminService.createExercise(createExercise);
    }

    @Delete('exercises/:id')
    async deleteExercise(@Param('id') id: string) {
      return this.adminService.deleteExercise(Number(id));
    }

    // Plans management
    @UseGuards(JwtAuthGuard)
    @Get('plans')
    async getAllPlans() {
      return this.adminService.getAllPlans();
    }

    @UseGuards(JwtAuthGuard)
    @Post('plans')
    async createPlan(@Request() req: any, @Body() createPlanDto: CreatePlanDto) {
      const userId = req.user.userId;
      return this.adminService.createPlan(createPlanDto, userId);
    }

    @Put('plans/:id')
    async updatePlan(@Param('id') id: string, @Body() updatePlanDto: UpdatePlanDto) {
      return this.adminService.updatePlan(Number(id), updatePlanDto);
    }

    @Post('plans/:id/config')
    async configPlan(@Param('id') id: string, @Body() configPlanDto: ConfigPlanDto) {
      return this.adminService.configPlan(Number(id), configPlanDto);
    }

    @Delete('plans/:id')
    async deletePlan(@Param('id') id: string) {
      return this.adminService.deletePlan(Number(id));
    }

    @Delete('plans/config/:id')
    async deletePlanConfig(@Param('id') id: string) {
      return this.adminService.deletePlanConfig(Number(id));
    }

    // Subscriptions with pagination
    @UseGuards(JwtAuthGuard)
    @Get('subscriptions')
    async getAllSubscriptions(@Query() query: any) {
      return this.adminService.getAllSubscriptions(query);
    }

    // Transactions
    @UseGuards(JwtAuthGuard)
    @Get('transactions')
    async getAllTransactions(@Query() query: any) {
      return this.adminService.getAllTransactions(query);
    }

    // Affiliates
    @UseGuards(JwtAuthGuard)
    @Get('affiliates')
    async getAffiliates(@Query() query: any) {
      return this.adminService.getAffiliates(query);
    }

    @UseGuards(JwtAuthGuard)
    @Put('affiliates/:id')
    async updateAffiliate(@Param('id') id: string, @Body() updateAffiliateDto: any) {
      return this.adminService.updateAffiliate(Number(id), updateAffiliateDto);
    }

    // Subscriptions
    @UseGuards(JwtAuthGuard)
    @Get('users/:id/subscriptions')
    async getUserSubscriptions(@Param('id') id: string) {
      return this.adminService.getUserSubscriptions(Number(id));
    }

    @UseGuards(JwtAuthGuard)
    @Get('users/:id/transactions')
    async getUserTransactions(@Param('id') id: string) {
      return this.adminService.getUserTransactions(Number(id));
    }

    @UseGuards(JwtAuthGuard)
    @Get('users/:userId/transactions/:id')
    async getTransactionDetails(@Param('userId') userId: string, @Param('id') id: string) {
      return this.adminService.getTransactionDetails(Number(id), Number(userId));
    }

    // Webhook Stripe
    @Post('webhook')
    async webhook(@Body() body: any, @Request() req: any) {
      // Em produção, você deve verificar a assinatura do webhook
      // const signature = req.headers['stripe-signature'];
      return this.adminService.webhook(body);
    }

    // Registrar comissões manualmente
    @UseGuards(JwtAuthGuard)
    @Post('subscriptions/:id/register-commissions')
    async registerCommissionsForSubscription(@Param('id') id: string) {
      return this.adminService.registerCommissionsForSubscription(Number(id));
    }
}