{"version": 3, "sources": ["../../src/dto/import-nutritionist-client-protocol.dto.ts"], "sourcesContent": ["import { IsInt } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class ImportNutritionistClientProtocolDto {\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  protocol_id: number;\r\n}"], "names": ["ImportNutritionistClientProtocolDto", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAHS;kCACD;;;;;;;;;;AAEd,IAAA,AAAMA,sCAAN,MAAMA;AAIb;;;oCAFcC"}