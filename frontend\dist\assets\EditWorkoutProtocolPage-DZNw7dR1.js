import{t as A,aR as I,aS as z,u as M,r as h,b as E,y as p,j as o,a as q,aO as W,I as Q,aF as R,bl as f}from"./index-D0i1bWZj.js";import{w as D}from"./workoutService-Cfz7oK3X.js";import{w as b}from"./useWorkoutProtocol-DKr0uMl-.js";function F(){const d=A(),{protocolId:n}=I(),{isCoach:k}=z(),l=M(),[N,$]=h.useState(null),[K,w]=h.useState(!0),[y,g]=h.useState(!1),[P,j]=h.useState(null);h.useEffect(()=>{n&&T()},[n]);const T=async()=>{var i,x,v;try{w(!0),j(null),console.log("🔄 Carregando dados do protocolo para edição...",n);let s;if(k){const e=await E.get(`coach/protocols/${n}`);s=(i=e==null?void 0:e.data)==null?void 0:i.data}else if(n){console.log("🔄 Buscando protocolo via workoutService, ID:",n);try{const e=await D.getProtocolById(n);console.log("📋 Dados retornados pelo workoutService:",e),e?(s={id:e.id,name:e.name,type_id:e.type,objective:e.objective,started_at:e.started_at,frequency:e.frequency,split:e.split,general_notes:e.notes,workouts:e.workouts},console.log("✅ Dados mapeados para protocolDataFromAPI:",s)):console.log("❌ workoutService retornou null")}catch(e){throw e}}if(s){const e=(s.workouts||[]).map((r,t)=>{const u=r.name||`Treino ${String.fromCharCode(65+t)}`;return console.log(`🏋️ EditWorkoutProtocolPage: Mapeando workout ${t+1}: "${u}"`),console.log("📋 EditWorkoutProtocolPage: Exercícios brutos do workout:",r.exercises),{name:u,exercises:(r.exercises||[]).map((a,m)=>{console.log(`🎯 EditWorkoutProtocolPage: Processando exercício ${m+1}:`,{exercise_id:a.exercise_id,name:a.name,muscle_group:a.muscle_group});const c={id:a.exercise_id?a.exercise_id.toString():`custom_${a.id||Date.now()}`,name:a.name||`Exercício ${m+1}`,muscle_group:a.muscle_group||"Não especificado",muscleGroup:a.muscle_group||"Não especificado",equipment:a.equipment||"Não especificado",media_url:a.media_url||null,gifUrl:a.media_url||null,targetMuscles:a.muscle_group?[a.muscle_group]:[]};return console.log(`✅ EditWorkoutProtocolPage: Exercício ${m+1} mapeado:`,{id:c.id,name:c.name,muscle_group:c.muscle_group}),{exercise:c,sets:a.sets||3,reps:a.reps||12,rpe:a.rpe||7,restTime:a.rest_seconds||a.restTime||90,notes:a.notes||""}})}});console.log("✅ EditWorkoutProtocolPage: Todos os workouts mapeados:",e.map(r=>({name:r.name,exercises:r.exercises.map(t=>({name:t.exercise.name,id:t.exercise.id}))}))),$({name:s.name||"",type:s.type_id||null,objective:s.objective||"",startDate:s.started_at?new Date(s.started_at).toISOString().split("T")[0]:new Date().toISOString().split("T")[0],frequency:s.frequency||3,split:s.split||"A-B-C",workouts:e,notes:s.general_notes||""})}else{j("Protocolo não encontrado"),p.error("Protocolo não encontrado"),d("/dashboard/workout");return}}catch(s){let e="Erro ao carregar dados do protocolo";s!=null&&s.message&&(e=s.message),j(e),p.error(e),(x=s==null?void 0:s.message)!=null&&x.includes("Sessão expirada")?setTimeout(()=>{localStorage.removeItem("accessToken"),d("/login")},2e3):(v=s==null?void 0:s.message)!=null&&v.includes("não encontrado")&&setTimeout(()=>{d("/dashboard/workout")},2e3)}finally{w(!1)}},C=async i=>{var x,v,s;try{console.log("💾 Salvando protocolo atualizado:",i),g(!0);try{const e={name:i.name,type_id:i.type,split:i.split,frequency:i.frequency,objective:i.objective,general_notes:i.notes,workouts:i.workouts.map(r=>({name:r.name,exercises:r.exercises.map((t,u)=>{const a=t.exercise.id;let m;console.log(`💾 HandleSave: Processando exercício ${u+1}:`,{id:a,name:t.exercise.name,sets:t.sets,reps:t.reps}),typeof a=="string"&&(a.startsWith("custom_")||isNaN(Number(a)))?(console.log(`🎯 Exercício personalizado detectado: ${a}. Será salvo como exercício personalizado.`),m=null):m=Number(a);const c=t.exercise.name||`Exercício ${u+1}`;console.log(`💾 HandleSave: Preparando exercício ${u+1}: "${c}" (ID: ${m})`),t.exercise.name||console.warn(`⚠️ HandleSave: Exercício sem nome no índice ${u}, usando fallback: "${c}"`);const _={exercise_id:m,name:c,exercise_name:c,sets:t.sets,reps:t.reps.toString(),rpe:t.rpe||null,rest_seconds:t.restTime||60,notes:t.notes||null};return console.log(`💾 HandleSave: Exercício ${u+1} preparado para salvar:`,_),_})}))};if(console.log("🔄 Enviando dados para atualização:",e),k){if(console.log("👨‍🏫 Usuário é coach, usando API de coach"),await E.put(`coach/protocols/${n}`,e)){console.log("✅ Protocolo atualizado com sucesso (coach API)"),console.log("🔄 Invalidando cache do React Query..."),l.invalidateQueries({queryKey:f.activeProtocol()}),l.invalidateQueries({queryKey:f.protocols()}),l.invalidateQueries({queryKey:f.protocol(n)}),l.invalidateQueries({queryKey:b.activeProtocol()}),l.invalidateQueries({queryKey:b.protocols()}),l.invalidateQueries({queryKey:["workout"]}),l.invalidateQueries({queryKey:["protocols"]}),p.success("Protocolo de treino atualizado com sucesso!",{position:"bottom-right"}),console.log("🔄 Redirecionando para página de treinos..."),g(!1),d("/dashboard/workout");return}}else{console.log("👤 Usuário normal, usando workoutService");const r=await D.updateProtocol(n,e);if(r.success){console.log("🔄 Invalidando cache do React Query..."),l.invalidateQueries({queryKey:f.activeProtocol()}),l.invalidateQueries({queryKey:f.protocols()}),l.invalidateQueries({queryKey:f.protocol(n)}),l.invalidateQueries({queryKey:b.activeProtocol()}),l.invalidateQueries({queryKey:b.protocols()}),l.invalidateQueries({queryKey:["workout"]}),l.invalidateQueries({queryKey:["protocols"]}),p.success("Protocolo de treino atualizado com sucesso!",{position:"bottom-right"}),console.log("🔄 Redirecionando para página de treinos..."),g(!1),d("/dashboard/workout");return}else{const t=r.message||"Erro ao salvar protocolo. Tente novamente.";p.error(t),g(!1);return}}}catch(e){console.error("❌ Erro ao atualizar protocolo no backend:",e),g(!1);let r="Erro ao salvar protocolo. Tente novamente.";if(e!=null&&e.message)r=e.message;else if((v=(x=e==null?void 0:e.response)==null?void 0:x.data)!=null&&v.message){const t=e.response.data.message;Array.isArray(t)?r=t.join(", "):r=t}p.error(r),(s=e==null?void 0:e.message)!=null&&s.includes("Sessão expirada")&&setTimeout(()=>{localStorage.removeItem("accessToken"),d("/login")},2e3)}}catch(e){console.error("Error saving protocol:",e),p.error("Erro ao salvar protocolo"),g(!1)}},S=()=>{d("/dashboard/workout")};return K?o.jsx("div",{className:"min-h-screen bg-snapfit-black flex items-center justify-center",children:o.jsxs("div",{className:"flex flex-col items-center gap-4",children:[o.jsx(q,{className:"w-8 h-8 text-snapfit-green animate-spin"}),o.jsx("p",{className:"text-gray-400",children:"Carregando protocolo..."})]})}):P?o.jsx("div",{className:"min-h-screen bg-snapfit-black flex items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("p",{className:"text-red-400 mb-4",children:P}),o.jsx("button",{onClick:()=>d("/dashboard/workout"),className:"px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors",children:"Voltar"})]})}):o.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[o.jsx("div",{className:"bg-snapfit-gray border-b border-snapfit-green/20",children:o.jsx("div",{className:"p-4",children:o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("button",{onClick:S,disabled:y,className:"w-10 h-10 bg-snapfit-dark-gray rounded-full flex items-center justify-center border border-snapfit-green/20 hover:bg-snapfit-dark-gray/80 transition-colors disabled:opacity-50",children:o.jsx(W,{className:"w-5 h-5 text-snapfit-green"})}),o.jsxs("div",{className:"flex-1",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("h1",{className:"text-lg sm:text-xl font-bold text-white",children:"Editar Protocolo de Treino"}),o.jsxs("div",{className:"flex items-center gap-2 px-3 py-1 bg-blue-500/20 border border-blue-500/30 rounded-full",children:[o.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),o.jsx("span",{className:"text-xs text-blue-400 font-medium",children:"Modo Edição"})]})]}),o.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400 mt-1",children:[o.jsx(Q,{className:"w-3 h-3 text-snapfit-green"}),o.jsx("span",{children:"Modificar protocolo existente"})]})]})]})})}),o.jsx("div",{className:"p-4 pb-32",children:o.jsxs("div",{className:"max-w-4xl mx-auto",children:[o.jsx("div",{className:"bg-amber-500/10 rounded-xl p-4 border border-amber-500/20 mb-6",children:o.jsxs("div",{className:"flex items-start gap-3",children:[o.jsx("div",{className:"w-8 h-8 bg-amber-500/20 rounded-full flex items-center justify-center border border-amber-500/30 flex-shrink-0 mt-0.5",children:o.jsx(Q,{className:"w-4 h-4 text-amber-500"})}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Editando Protocolo Existente"}),o.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[o.jsx("p",{children:"• Modifique exercícios, séries e repetições"}),o.jsx("p",{children:"• Ajuste frequência e divisão de treino"}),o.jsx("p",{children:"• Atualize objetivos e observações"}),o.jsx("p",{children:"• Salve as alterações para aplicar ao protocolo ativo"})]})]})]})}),o.jsx("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20",children:N&&o.jsx(R,{onSave:C,onCancel:S,initialData:N,saving:y})})]})}),y&&o.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50",children:o.jsxs("div",{className:"flex flex-col items-center gap-4",children:[o.jsx(q,{className:"w-12 h-12 text-snapfit-green animate-spin"}),o.jsx("p",{className:"text-white font-medium",children:"Salvando protocolo..."}),o.jsx("p",{className:"text-gray-400 text-sm",children:"Aguarde um momento"})]})})]})}export{F as EditWorkoutProtocolPage,F as default};
