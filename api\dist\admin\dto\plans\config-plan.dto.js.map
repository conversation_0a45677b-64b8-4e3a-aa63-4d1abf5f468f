{"version": 3, "sources": ["../../../../src/admin/dto/plans/config-plan.dto.ts"], "sourcesContent": ["import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ber, Min, IsOptional } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class ConfigPlanDto {\r\n  /*\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  plan_id: number; // ID do plano\r\n  */\r\n\r\n  /*\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  payment_provider_id: number; // ID do provedor de pagamento\r\n  */\r\n\r\n  @IsString()\r\n  payment_provider: string; // Nome do provedor de pagamento\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  platform: 'web' | 'android' | 'ios'; // Plataforma, com valor padrão 'web'\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  price?: number; // Preço customizado\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  currency?: string; // Moeda\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  snaptokens?: number; // Snaptokens customizados\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  payment_provider_external_id?: string; // Identificadores externos\r\n}\r\n"], "names": ["ConfigPlanDto"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAH0D;;;;;;;;;;AAGhE,IAAA,AAAMA,gBAAN,MAAMA;AAqCb"}