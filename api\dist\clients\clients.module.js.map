{"version": 3, "sources": ["../../src/clients/clients.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { ClientsController } from './clients.controller';\nimport { ClientsService } from './clients.service';\n\n@Module({\n  controllers: [ClientsController],\n  providers: [ClientsService],\n  exports: [ClientsService],\n})\nexport class ClientsModule {}\n"], "names": ["ClientsModule", "controllers", "ClientsController", "providers", "ClientsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;mCACW;gCACH;;;;;;;AAOxB,IAAA,AAAMA,gBAAN,MAAMA;AAAe;;;QAJ1BC,aAAa;YAACC,oCAAiB;SAAC;QAChCC,WAAW;YAACC,8BAAc;SAAC;QAC3BC,SAAS;YAACD,8BAAc;SAAC"}