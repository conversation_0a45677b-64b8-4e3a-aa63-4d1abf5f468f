{"version": 3, "sources": ["../../src/notifications/notifications.controller.ts"], "sourcesContent": ["import { \n  <PERSON>, \n  Get, \n  Post, \n  Put, \n  Body, \n  Param, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { NotificationsService } from './notifications.service';\n\n@Controller('notifications')\n@UseGuards(JwtAuthGuard)\nexport class NotificationsController {\n  constructor(private readonly notificationsService: NotificationsService) {}\n\n  @Get()\n  async getNotifications(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.getNotifications(userId, query);\n  }\n\n  @Get('unread-count')\n  async getUnreadCount(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.getUnreadCount(userId);\n  }\n\n  @Post('mark-read')\n  async markAsRead(@Body() body: { notificationIds: number[] }, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.markAsRead(body.notificationIds, userId);\n  }\n\n  @Post('mark-all-read')\n  async markAllAsRead(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.markAllAsRead(userId);\n  }\n\n  @Get('settings')\n  async getNotificationSettings(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.getNotificationSettings(userId);\n  }\n\n  @Put('settings')\n  async updateNotificationSettings(@Body() settings: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.updateNotificationSettings(userId, settings);\n  }\n\n  @Post('push/subscribe')\n  async subscribeToPushNotifications(@Body() subscription: any, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.subscribeToPushNotifications(userId, subscription);\n  }\n\n  @Post('push/unsubscribe')\n  async unsubscribeFromPushNotifications(@Body() body: { endpoint: string }, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.unsubscribeFromPushNotifications(userId, body.endpoint);\n  }\n\n  @Post('test')\n  async sendTestNotification(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.notificationsService.sendTestNotification(userId);\n  }\n}\n"], "names": ["NotificationsController", "getNotifications", "req", "query", "userId", "user", "notificationsService", "getUnreadCount", "mark<PERSON><PERSON><PERSON>", "body", "notificationIds", "markAllAsRead", "getNotificationSettings", "updateNotificationSettings", "settings", "subscribeToPushNotifications", "subscription", "unsubscribeFromPushNotifications", "endpoint", "sendTestNotification", "constructor"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;wBANN;8BACsB;sCACQ;;;;;;;;;;;;;;;AAI9B,IAAA,AAAMA,0BAAN,MAAMA;IAGX,MACMC,iBAAiB,AAAWC,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC/D,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACL,gBAAgB,CAACG,QAAQD;IAC5D;IAEA,MACMI,eAAe,AAAWL,GAAQ,EAAE;QACxC,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACC,cAAc,CAACH;IAClD;IAEA,MACMI,WAAW,AAAQC,IAAmC,EAAE,AAAWP,GAAQ,EAAE;QACjF,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACE,UAAU,CAACC,KAAKC,eAAe,EAAEN;IACpE;IAEA,MACMO,cAAc,AAAWT,GAAQ,EAAE;QACvC,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACK,aAAa,CAACP;IACjD;IAEA,MACMQ,wBAAwB,AAAWV,GAAQ,EAAE;QACjD,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACM,uBAAuB,CAACR;IAC3D;IAEA,MACMS,2BAA2B,AAAQC,QAAa,EAAE,AAAWZ,GAAQ,EAAE;QAC3E,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACO,0BAA0B,CAACT,QAAQU;IACtE;IAEA,MACMC,6BAA6B,AAAQC,YAAiB,EAAE,AAAWd,GAAQ,EAAE;QACjF,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACS,4BAA4B,CAACX,QAAQY;IACxE;IAEA,MACMC,iCAAiC,AAAQR,IAA0B,EAAE,AAAWP,GAAQ,EAAE;QAC9F,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACW,gCAAgC,CAACb,QAAQK,KAAKS,QAAQ;IACzF;IAEA,MACMC,qBAAqB,AAAWjB,GAAQ,EAAE;QAC9C,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,oBAAoB,CAACa,oBAAoB,CAACf;IACxD;IAtDAgB,YAAY,AAAiBd,oBAA0C,CAAE;aAA5CA,uBAAAA;IAA6C;AAuD5E"}