import { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

class ExerciseUpdateDto {
  @IsOptional()
  exercise_id?: number | string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsNumber()
  @Type(() => Number)
  sets: number;

  @IsNumber()
  @Type(() => Number)
  reps: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  rpe?: number;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  rest_seconds?: number;

  @IsOptional()
  @IsString()
  notes?: string;
}

class WorkoutUpdateDto {
  @IsString()
  name: string;

  @IsArray()
  @ArrayNotEmpty()
  exercises: ExerciseUpdateDto[];
}

export class UpdateProtocolWorkoutDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  type?: number;

  @IsString()
  split: string;

  @IsNumber()
  @Type(() => Number)
  frequency: number;

  @IsString()
  objective: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsArray()
  workouts?: WorkoutUpdateDto[];
}
