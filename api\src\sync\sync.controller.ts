import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { SyncService } from './sync.service';

@Controller('sync')
@UseGuards(JwtAuthGuard)
export class SyncController {
  constructor(private readonly syncService: SyncService) {}

  @Get('status')
  async getSyncStatus(@Request() req: any) {
    const userId = req.user.userId;
    return this.syncService.getSyncStatus(userId);
  }

  @Post('full')
  async performFullSync(@Request() req: any, @Body() options?: any) {
    const userId = req.user.userId;
    return this.syncService.performFullSync(userId, options);
  }

  @Post('incremental')
  async performIncrementalSync(@Request() req: any, @Body() options?: any) {
    const userId = req.user.userId;
    return this.syncService.performIncrementalSync(userId, options);
  }

  @Get('conflicts')
  async getSyncConflicts(@Request() req: any) {
    const userId = req.user.userId;
    return this.syncService.getSyncConflicts(userId);
  }

  @Post('conflicts/resolve')
  async resolveSyncConflicts(@Request() req: any, @Body() resolutions: any) {
    const userId = req.user.userId;
    return this.syncService.resolveSyncConflicts(userId, resolutions);
  }

  @Get('backup/create')
  async createBackup(@Request() req: any, @Query() options?: any) {
    const userId = req.user.userId;
    return this.syncService.createBackup(userId, options);
  }

  @Get('backup/list')
  async listBackups(@Request() req: any) {
    const userId = req.user.userId;
    return this.syncService.listBackups(userId);
  }

  @Post('backup/restore')
  async restoreBackup(@Request() req: any, @Body() restoreData: any) {
    const userId = req.user.userId;
    return this.syncService.restoreBackup(userId, restoreData);
  }

  @Get('export/data')
  async exportUserData(@Request() req: any, @Query() options?: any) {
    const userId = req.user.userId;
    return this.syncService.exportUserData(userId, options);
  }

  @Post('import/data')
  async importUserData(@Request() req: any, @Body() importData: any) {
    const userId = req.user.userId;
    return this.syncService.importUserData(userId, importData);
  }

  @Get('devices')
  async getSyncDevices(@Request() req: any) {
    const userId = req.user.userId;
    return this.syncService.getSyncDevices(userId);
  }

  @Post('devices/register')
  async registerDevice(@Request() req: any, @Body() deviceInfo: any) {
    const userId = req.user.userId;
    return this.syncService.registerDevice(userId, deviceInfo);
  }

  @Post('devices/:deviceId/unregister')
  async unregisterDevice(@Request() req: any, @Body() deviceId: string) {
    const userId = req.user.userId;
    return this.syncService.unregisterDevice(userId, deviceId);
  }
}
