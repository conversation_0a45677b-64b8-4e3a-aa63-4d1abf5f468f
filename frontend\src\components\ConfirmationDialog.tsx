import React, { useState } from 'react';
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react';
import { createPortal } from 'react-dom';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void | Promise<void>;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'warning' | 'danger' | 'info' | 'success';
  requiresTyping?: boolean;
  requiredText?: string;
  isLoading?: boolean;
}

const typeConfig = {
  warning: {
    icon: AlertTriangle,
    iconColor: 'text-yellow-400',
    bgColor: 'border-yellow-500/40',
    headerBg: 'from-yellow-600/10 to-yellow-700/10',
    borderColor: 'border-yellow-500/30',
    buttonColor: 'bg-yellow-500 hover:bg-yellow-600',
  },
  danger: {
    icon: AlertCircle,
    iconColor: 'text-red-400',
    bgColor: 'border-red-500/40',
    headerBg: 'from-red-600/10 to-red-700/10',
    borderColor: 'border-red-500/30',
    buttonColor: 'bg-red-500 hover:bg-red-600',
  },
  info: {
    icon: Info,
    iconColor: 'text-blue-400',
    bgColor: 'border-blue-500/40',
    headerBg: 'from-blue-600/10 to-blue-700/10',
    borderColor: 'border-blue-500/30',
    buttonColor: 'bg-blue-500 hover:bg-blue-600',
  },
  success: {
    icon: CheckCircle,
    iconColor: 'text-green-400',
    bgColor: 'border-green-500/40',
    headerBg: 'from-green-600/10 to-green-700/10',
    borderColor: 'border-green-500/30',
    buttonColor: 'bg-green-500 hover:bg-green-600',
  },
};

export function ConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  type = 'warning',
  requiresTyping = false,
  requiredText = '',
  isLoading = false,
}: ConfirmationDialogProps) {
  const [typedText, setTypedText] = useState('');
  const [isConfirming, setIsConfirming] = useState(false);

  const config = typeConfig[type];
  const IconComponent = config.icon;

  if (!isOpen) return null;

  const handleConfirm = async () => {
    if (requiresTyping && typedText.toLowerCase() !== requiredText.toLowerCase()) {
      return;
    }

    try {
      setIsConfirming(true);
      await onConfirm();
      onClose();
      setTypedText('');
    } catch (error) {
      console.error('Error in confirmation action:', error);
    } finally {
      setIsConfirming(false);
    }
  };

  const handleClose = () => {
    if (isConfirming || isLoading) return;
    setTypedText('');
    onClose();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isConfirming && !isLoading) {
      handleClose();
    }
  };

  const isConfirmDisabled = 
    isConfirming || 
    isLoading || 
    (requiresTyping && typedText.toLowerCase() !== requiredText.toLowerCase());

  return createPortal(
    <div
      className="critical-modal"
      onClick={handleOverlayClick}
    >
      <div
        className={`critical-modal-content bg-snapfit-gray rounded-xl shadow-2xl max-w-md w-full border-2 ${config.bgColor} shadow-${type}-500/20`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${config.borderColor} bg-gradient-to-r ${config.headerBg}`}>
          <div className="flex items-center gap-3">
            <div className={`p-3 bg-${type}-500/20 rounded-lg border ${config.borderColor}`}>
              <IconComponent className={`w-6 h-6 ${config.iconColor}`} />
            </div>
            <div>
              <h2 className={`text-lg font-semibold ${config.iconColor}`}>{title}</h2>
              <p className={`text-sm ${config.iconColor}/80`}>Confirmação necessária</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className={`p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors ${
              (isConfirming || isLoading) ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isConfirming || isLoading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-6">
            <p className="text-gray-300 leading-relaxed">{message}</p>
          </div>

          {requiresTyping && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Para confirmar, digite <span className={`${config.iconColor} font-bold bg-${type}-500/20 px-2 py-1 rounded`}>"{requiredText}"</span>:
              </label>
              <input
                type="text"
                value={typedText}
                onChange={(e) => setTypedText(e.target.value)}
                placeholder={`Digite ${requiredText} para confirmar`}
                className={`w-full px-4 py-3 bg-snapfit-dark-gray border-2 ${config.borderColor} rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-${type}-400 focus:ring-2 focus:ring-${type}-500/20 transition-all`}
                disabled={isConfirming || isLoading}
              />
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-3">
            <button
              onClick={handleClose}
              className="flex-1 px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isConfirming || isLoading}
            >
              {cancelText}
            </button>
            <button
              onClick={handleConfirm}
              className={`flex-1 px-4 py-3 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${config.buttonColor}`}
              disabled={isConfirmDisabled}
            >
              {isConfirming || isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Processando...
                </div>
              ) : (
                confirmText
              )}
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
}
