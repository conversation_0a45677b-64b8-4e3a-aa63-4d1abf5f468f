{"version": 3, "sources": ["../../src/points/points.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { PointsController } from './points.controller';\nimport { PointsService } from './points.service';\n\n@Module({\n  controllers: [PointsController],\n  providers: [PointsService],\n  exports: [PointsService],\n})\nexport class PointsModule {}\n"], "names": ["PointsModule", "controllers", "PointsController", "providers", "PointsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;kCACU;+BACH;;;;;;;AAOvB,IAAA,AAAMA,eAAN,MAAMA;AAAc;;;QAJzBC,aAAa;YAACC,kCAAgB;SAAC;QAC/BC,WAAW;YAACC,4BAAa;SAAC;QAC1BC,SAAS;YAACD,4BAAa;SAAC"}