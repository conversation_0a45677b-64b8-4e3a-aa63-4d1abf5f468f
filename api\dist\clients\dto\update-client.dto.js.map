{"version": 3, "sources": ["../../../src/clients/dto/update-client.dto.ts"], "sourcesContent": ["import { PartialType } from '@nestjs/mapped-types';\nimport { CreateClientDto } from './create-client.dto';\n\nexport class UpdateClientDto extends PartialType(CreateClientDto) {}\n"], "names": ["UpdateClientDto", "PartialType", "CreateClientDto"], "mappings": ";;;;+BAGaA;;;eAAAA;;;6BAHe;iCACI;AAEzB,IAAA,AAAMA,kBAAN,MAAMA,wBAAwBC,IAAAA,wBAAW,EAACC,gCAAe;AAAG"}