"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ClientsController", {
    enumerable: true,
    get: function() {
        return ClientsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _clientsservice = require("./clients.service");
const _createclientdto = require("./dto/create-client.dto");
const _updateclientdto = require("./dto/update-client.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let ClientsController = class ClientsController {
    async getClients(req, query) {
        const userId = req.user.userId;
        return this.clientsService.getClients(userId, query);
    }
    async getClient(id, req) {
        const userId = req.user.userId;
        return this.clientsService.getClient(Number(id), userId);
    }
    async createClient(createClientDto, req) {
        const userId = req.user.userId;
        return this.clientsService.createClient(createClientDto, userId);
    }
    async updateClient(id, updateClientDto, req) {
        const userId = req.user.userId;
        return this.clientsService.updateClient(Number(id), updateClientDto, userId);
    }
    async deleteClient(id, req) {
        const userId = req.user.userId;
        return this.clientsService.deleteClient(Number(id), userId);
    }
    async getClientProtocols(id, req) {
        const userId = req.user.userId;
        return this.clientsService.getClientProtocols(Number(id), userId);
    }
    async getClientAssessments(id, req) {
        const userId = req.user.userId;
        return this.clientsService.getClientAssessments(Number(id), userId);
    }
    async createClientProtocol(id, protocolData, req) {
        const userId = req.user.userId;
        return this.clientsService.createClientProtocol(Number(id), protocolData, userId);
    }
    async getClientProgress(id, req, query) {
        const userId = req.user.userId;
        return this.clientsService.getClientProgress(Number(id), userId, query);
    }
    async getClientNutritionData(id, req, query) {
        const userId = req.user.userId;
        return this.clientsService.getClientNutritionData(Number(id), userId, query);
    }
    async getClientWorkoutData(id, req, query) {
        const userId = req.user.userId;
        return this.clientsService.getClientWorkoutData(Number(id), userId, query);
    }
    constructor(clientsService){
        this.clientsService = clientsService;
    }
};
_ts_decorate([
    (0, _common.Get)(),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "getClients", null);
_ts_decorate([
    (0, _common.Get)(':id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "getClient", null);
_ts_decorate([
    (0, _common.Post)(),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createclientdto.CreateClientDto === "undefined" ? Object : _createclientdto.CreateClientDto,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "createClient", null);
_ts_decorate([
    (0, _common.Put)(':id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _updateclientdto.UpdateClientDto === "undefined" ? Object : _updateclientdto.UpdateClientDto,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "updateClient", null);
_ts_decorate([
    (0, _common.Delete)(':id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "deleteClient", null);
_ts_decorate([
    (0, _common.Get)(':id/protocols'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "getClientProtocols", null);
_ts_decorate([
    (0, _common.Get)(':id/assessments'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "getClientAssessments", null);
_ts_decorate([
    (0, _common.Post)(':id/protocols'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "createClientProtocol", null);
_ts_decorate([
    (0, _common.Get)(':id/progress'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_param(2, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "getClientProgress", null);
_ts_decorate([
    (0, _common.Get)(':id/nutrition-data'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_param(2, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "getClientNutritionData", null);
_ts_decorate([
    (0, _common.Get)(':id/workout-data'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_param(2, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ClientsController.prototype, "getClientWorkoutData", null);
ClientsController = _ts_decorate([
    (0, _common.Controller)('clients'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _clientsservice.ClientsService === "undefined" ? Object : _clientsservice.ClientsService
    ])
], ClientsController);

//# sourceMappingURL=clients.controller.js.map