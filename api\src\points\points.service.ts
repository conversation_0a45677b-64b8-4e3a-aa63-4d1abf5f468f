import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { db } from '../database';

@Injectable()
export class PointsService {

  async getPointsBalance(userId: number) {
    try {
      const sql = `
        SELECT 
          total_points as totalPoints,
          monthly_points as monthlyPoints,
          yearly_points as yearlyPoints,
          updated_at as lastUpdated
        FROM user_points 
        WHERE user_id = ?
      `;

      const [result] = await db.execute(sql, [userId]);

      const balance = result[0] || {
        totalPoints: 0,
        monthlyPoints: 0,
        yearlyPoints: 0,
        lastUpdated: null
      };

      // Get user rank
      const [rankResult] = await db.execute(
        `SELECT COUNT(*) + 1 as rank
         FROM user_points 
         WHERE total_points > (
           SELECT COALESCE(total_points, 0) 
           FROM user_points 
           WHERE user_id = ?
         )`,
        [userId]
      );

      balance.rank = rankResult[0]?.rank || 1;

      return {
        status: 'success',
        data: balance
      };
    } catch (error) {
      console.error('Error getting points balance:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get points balance'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getPointsHistory(userId: number, query: any) {
    try {
      const { page = 1, limit = 20, type, startDate, endDate } = query;
      const offset = (page - 1) * limit;

      let sql = `
        SELECT 
          id,
          points,
          type,
          reason,
          created_at as createdAt
        FROM points_transactions 
        WHERE user_id = ?
      `;

      const params = [userId];

      if (type) {
        sql += ` AND type = ?`;
        params.push(type);
      }

      if (startDate) {
        sql += ` AND created_at >= ?`;
        params.push(startDate);
      }

      if (endDate) {
        sql += ` AND created_at <= ?`;
        params.push(endDate);
      }

      sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const [transactions] = await db.execute(sql, params);

      // Get total count
      let countSql = `SELECT COUNT(*) as total FROM points_transactions WHERE user_id = ?`;
      const countParams = [userId];

      if (type) {
        countSql += ` AND type = ?`;
        countParams.push(type);
      }

      if (startDate) {
        countSql += ` AND created_at >= ?`;
        countParams.push(startDate);
      }

      if (endDate) {
        countSql += ` AND created_at <= ?`;
        countParams.push(endDate);
      }

      const [countResult] = await db.execute(countSql, countParams);
      const total = countResult[0]?.total || 0;

      return {
        status: 'success',
        data: {
          transactions,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      };
    } catch (error) {
      console.error('Error getting points history:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get points history'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async earnPoints(userId: number, points: number, reason: string) {
    try {
      if (points <= 0) {
        throw new HttpException({
          status: 'error',
          message: 'Points must be positive'
        }, HttpStatus.BAD_REQUEST);
      }

      // Start transaction
      await db.execute('START TRANSACTION');

      try {
        // Update user points
        await db.execute(
          `INSERT INTO user_points (user_id, total_points, monthly_points, yearly_points, updated_at)
           VALUES (?, ?, ?, ?, NOW())
           ON DUPLICATE KEY UPDATE
           total_points = total_points + ?,
           monthly_points = monthly_points + ?,
           yearly_points = yearly_points + ?,
           updated_at = NOW()`,
          [userId, points, points, points, points, points, points]
        );

        // Log transaction
        await db.execute(
          `INSERT INTO points_transactions (user_id, points, type, reason, created_at)
           VALUES (?, ?, 'earned', ?, NOW())`,
          [userId, points, reason]
        );

        await db.execute('COMMIT');

        return {
          status: 'success',
          message: `Earned ${points} points for ${reason}`,
          data: {
            points,
            reason,
            type: 'earned'
          }
        };
      } catch (error) {
        await db.execute('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('Error earning points:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to earn points'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async spendPoints(userId: number, points: number, reason: string) {
    try {
      if (points <= 0) {
        throw new HttpException({
          status: 'error',
          message: 'Points must be positive'
        }, HttpStatus.BAD_REQUEST);
      }

      // Check if user has enough points
      const [balance] = await db.execute(
        'SELECT total_points FROM user_points WHERE user_id = ?',
        [userId]
      );

      const currentPoints = balance[0]?.total_points || 0;

      if (currentPoints < points) {
        throw new HttpException({
          status: 'error',
          message: 'Insufficient points'
        }, HttpStatus.BAD_REQUEST);
      }

      // Start transaction
      await db.execute('START TRANSACTION');

      try {
        // Update user points
        await db.execute(
          `UPDATE user_points 
           SET total_points = total_points - ?, updated_at = NOW()
           WHERE user_id = ?`,
          [points, userId]
        );

        // Log transaction
        await db.execute(
          `INSERT INTO points_transactions (user_id, points, type, reason, created_at)
           VALUES (?, ?, 'spent', ?, NOW())`,
          [userId, points, reason]
        );

        await db.execute('COMMIT');

        return {
          status: 'success',
          message: `Spent ${points} points on ${reason}`,
          data: {
            points,
            reason,
            type: 'spent',
            remainingPoints: currentPoints - points
          }
        };
      } catch (error) {
        await db.execute('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('Error spending points:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to spend points'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getLeaderboard(query: any) {
    try {
      const { period = 'all', limit = 50 } = query;
      
      let pointsColumn = 'total_points';
      if (period === 'monthly') pointsColumn = 'monthly_points';
      if (period === 'yearly') pointsColumn = 'yearly_points';

      const sql = `
        SELECT 
          u.id,
          u.name,
          u.username,
          u.photo,
          COALESCE(up.${pointsColumn}, 0) as points,
          u.last_active as lastActive
        FROM users u
        LEFT JOIN user_points up ON u.id = up.user_id
        WHERE u.role = 'client'
        ORDER BY up.${pointsColumn} DESC, u.name ASC
        LIMIT ?
      `;

      const [results] = await db.execute(sql, [limit]);

      // Add ranking
      const leaderboard = results.map((user, index) => ({
        ...user,
        rank: index + 1
      }));

      return {
        status: 'success',
        data: {
          period,
          leaderboard
        }
      };
    } catch (error) {
      console.error('Error getting leaderboard:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get leaderboard'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // Helper method for automatic point earning
  async awardPointsForActivity(userId: number, activity: string, value?: number) {
    try {
      const pointsRules = {
        'workout_completed': 50,
        'meal_logged': 10,
        'water_goal_reached': 25,
        'daily_goal_completed': 100,
        'weekly_goal_completed': 500,
        'friend_added': 20,
        'challenge_joined': 30,
        'challenge_completed': 200,
        'profile_completed': 100,
        'first_workout': 150,
        'streak_7_days': 300,
        'streak_30_days': 1000
      };

      const points = value || pointsRules[activity] || 0;

      if (points > 0) {
        await this.earnPoints(userId, points, activity.replace('_', ' '));
        
        // Create notification
        await this.createPointsNotification(userId, points, activity);
      }

      return points;
    } catch (error) {
      console.error('Error awarding points for activity:', error);
      return 0;
    }
  }

  private async createPointsNotification(userId: number, points: number, activity: string) {
    try {
      const activityNames = {
        'workout_completed': 'treino concluído',
        'meal_logged': 'refeição registrada',
        'water_goal_reached': 'meta de água atingida',
        'daily_goal_completed': 'meta diária concluída',
        'weekly_goal_completed': 'meta semanal concluída',
        'friend_added': 'amigo adicionado',
        'challenge_joined': 'desafio iniciado',
        'challenge_completed': 'desafio concluído',
        'profile_completed': 'perfil completado',
        'first_workout': 'primeiro treino',
        'streak_7_days': 'sequência de 7 dias',
        'streak_30_days': 'sequência de 30 dias'
      };

      const activityName = activityNames[activity] || activity;

      await db.execute(
        `INSERT INTO notifications (user_id, type, title, message, data, created_at)
         VALUES (?, 'points_earned', 'SnapCoins Ganhos!', ?, ?, NOW())`,
        [
          userId,
          `Você ganhou ${points} SnapCoins por ${activityName}!`,
          JSON.stringify({ points, activity, type: 'points_earned' })
        ]
      );
    } catch (error) {
      console.error('Error creating points notification:', error);
    }
  }

  // Method to reset monthly/yearly points (to be called by cron job)
  async resetPeriodicPoints(period: 'monthly' | 'yearly') {
    try {
      const column = period === 'monthly' ? 'monthly_points' : 'yearly_points';
      
      await db.execute(
        `UPDATE user_points SET ${column} = 0, updated_at = NOW()`,
        []
      );

      console.log(`Reset ${period} points for all users`);
      return true;
    } catch (error) {
      console.error(`Error resetting ${period} points:`, error);
      return false;
    }
  }
}
