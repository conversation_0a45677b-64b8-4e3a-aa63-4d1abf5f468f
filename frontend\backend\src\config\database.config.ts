import { TypeOrmModuleOptions } from '@nestjs/typeorm';

const isProduction = process.env.NODE_ENV === 'production';

export const databaseConfig: TypeOrmModuleOptions = {
  type: 'mysql',
  host: isProduction ?
    (process.env.PROD_DB_HOST || '************') :
    (process.env.DB_HOST || 'localhost'),
  port: parseInt(process.env.DB_PORT || '3306'),
  username: isProduction ?
    (process.env.PROD_DB_USERNAME || 'u164938089_snapfit') :
    (process.env.DB_USERNAME || 'root'),
  password: isProduction ?
    (process.env.PROD_DB_PASSWORD || 'Snapfit123') :
    (process.env.DB_PASSWORD || ''),
  database: isProduction ?
    (process.env.PROD_DB_DATABASE || 'u164938089_snapfit') :
    (process.env.DB_DATABASE || 'snapfit_dev'),
  entities: isProduction ?
    ['dist/**/*.entity{.ts,.js}'] :
    [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: !isProduction, // Apenas para desenvolvimento
  migrations: isProduction ? ['dist/migrations/*{.ts,.js}'] : undefined,
  migrationsRun: isProduction,
  logging: process.env.NODE_ENV !== 'production',
  // Configurações adicionais para melhor performance e estabilidade
  extra: {
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
  },
  // Retry logic para conexões
  retryAttempts: 3,
  retryDelay: 3000,
};