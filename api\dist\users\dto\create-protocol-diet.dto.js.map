{"version": 3, "sources": ["../../../src/users/dto/create-protocol-diet.dto.ts"], "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport { IsArray, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';\r\n\r\nclass FoodItemDto {\r\n    @IsNumber()\r\n    @IsOptional()\r\n    @Type(() => Number)\r\n    food_id?: number;\r\n\r\n    @IsString()\r\n    @IsNotEmpty()\r\n    name: string;\r\n  \r\n    @IsNumber()\r\n    @Type(() => Number)\r\n    quantity: number;\r\n\r\n    @IsString()\r\n    unit: string;\r\n\r\n    @IsNumber()\r\n    @Type(() => Number)\r\n    calories: number;\r\n\r\n    @IsNumber()\r\n    @Type(() => Number)\r\n    protein: number;\r\n\r\n    @IsNumber()\r\n    @Type(() => Number)\r\n    carbs: number;\r\n\r\n    @IsNumber()\r\n    @Type(() => Number)\r\n    fat: number;\r\n\r\n    @IsNumber()\r\n    @Type(() => Number)\r\n    fiber: number;\r\n  }\r\n  \r\n\r\nclass MealDto {\r\n    @IsString()\r\n    name: string;\r\n  \r\n    @IsString()\r\n    day_of_week: string;\r\n  \r\n    @IsString()\r\n    meal_time: string;\r\n  \r\n    @IsArray()\r\n    @ValidateNested()\r\n    @Type(() => FoodItemDto)  // Crucial: Transforms plain objects to FoodItemDto instances\r\n    foods: FoodItemDto[];\r\n  }\r\n\r\n  class SupplementDto {\r\n    @IsString()\r\n    name: string;\r\n  \r\n    @IsString()\r\n    dosage: string;\r\n  \r\n    @IsString()\r\n    supplement_time: string;\r\n  \r\n    @IsOptional()\r\n    @IsString()\r\n    notes?: string;\r\n  }\r\n\r\nexport class CreateProtocolDietDto {\r\n    @IsString()\r\n    @IsNotEmpty()\r\n    name: string;\r\n\r\n    @IsNumber()\r\n    @Type(() => Number)\r\n    type_id: number;\r\n\r\n    @IsOptional()\r\n    @IsString()\r\n    objective?: string;\r\n\r\n    @IsOptional()\r\n    @IsString()\r\n    notes?: string;\r\n\r\n    @IsObject()\r\n    nutritional_goals: {\r\n        calories: number;\r\n        protein: number;\r\n        carbs: number;\r\n        fat: number;\r\n        water: number;\r\n    }\r\n\r\n    @IsArray()\r\n    @ValidateNested({ each: true })\r\n    @Type(() => MealDto)\r\n    meals: MealDto[];\r\n\r\n    @IsOptional()\r\n    @IsArray()\r\n    @ValidateNested({ each: true })\r\n    @Type(() => SupplementDto)\r\n    supplements?: SupplementDto[];\r\n\r\n    @IsOptional()\r\n    @IsString()\r\n    general_notes?: string;\r\n}"], "names": ["CreateProtocolDietDto", "FoodItemDto", "Number", "MealDto", "SupplementDto", "each"], "mappings": ";;;;+BAyEaA;;;eAAAA;;;kCAzEQ;gCACyE;;;;;;;;;;AAE9F,IAAA,AAAMC,cAAN,MAAMA;AAoCJ;;;;oCAjCcC;;;;;;;;;;oCAQAA;;;;;;;;;oCAOAA;;;;;oCAIAA;;;;;oCAIAA;;;;;oCAIAA;;;;;oCAIAA;;;AAKhB,IAAA,AAAMC,UAAN,MAAMA;AAcJ;;;;;;;;;;;;;;;;oCAFcF;;;AAId,IAAA,AAAMG,gBAAN,MAAMA;AAaN;;;;;;;;;;;;;;;;;;AAEK,IAAA,AAAMJ,wBAAN,MAAMA;AAwCb;;;;;;;;oCAlCgBE;;;;;;;;;;;;;;;;;;;;QAqBMG,MAAM;;oCACZF;;;;;;;QAKME,MAAM;;oCACZD"}