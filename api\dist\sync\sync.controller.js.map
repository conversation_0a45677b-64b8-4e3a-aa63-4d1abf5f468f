{"version": 3, "sources": ["../../src/sync/sync.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Body, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { SyncService } from './sync.service';\n\n@Controller('sync')\n@UseGuards(JwtAuthGuard)\nexport class SyncController {\n  constructor(private readonly syncService: SyncService) {}\n\n  @Get('status')\n  async getSyncStatus(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.syncService.getSyncStatus(userId);\n  }\n\n  @Post('full')\n  async performFullSync(@Request() req: any, @Body() options?: any) {\n    const userId = req.user.userId;\n    return this.syncService.performFullSync(userId, options);\n  }\n\n  @Post('incremental')\n  async performIncrementalSync(@Request() req: any, @Body() options?: any) {\n    const userId = req.user.userId;\n    return this.syncService.performIncrementalSync(userId, options);\n  }\n\n  @Get('conflicts')\n  async getSyncConflicts(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.syncService.getSyncConflicts(userId);\n  }\n\n  @Post('conflicts/resolve')\n  async resolveSyncConflicts(@Request() req: any, @Body() resolutions: any) {\n    const userId = req.user.userId;\n    return this.syncService.resolveSyncConflicts(userId, resolutions);\n  }\n\n  @Get('backup/create')\n  async createBackup(@Request() req: any, @Query() options?: any) {\n    const userId = req.user.userId;\n    return this.syncService.createBackup(userId, options);\n  }\n\n  @Get('backup/list')\n  async listBackups(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.syncService.listBackups(userId);\n  }\n\n  @Post('backup/restore')\n  async restoreBackup(@Request() req: any, @Body() restoreData: any) {\n    const userId = req.user.userId;\n    return this.syncService.restoreBackup(userId, restoreData);\n  }\n\n  @Get('export/data')\n  async exportUserData(@Request() req: any, @Query() options?: any) {\n    const userId = req.user.userId;\n    return this.syncService.exportUserData(userId, options);\n  }\n\n  @Post('import/data')\n  async importUserData(@Request() req: any, @Body() importData: any) {\n    const userId = req.user.userId;\n    return this.syncService.importUserData(userId, importData);\n  }\n\n  @Get('devices')\n  async getSyncDevices(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.syncService.getSyncDevices(userId);\n  }\n\n  @Post('devices/register')\n  async registerDevice(@Request() req: any, @Body() deviceInfo: any) {\n    const userId = req.user.userId;\n    return this.syncService.registerDevice(userId, deviceInfo);\n  }\n\n  @Post('devices/:deviceId/unregister')\n  async unregisterDevice(@Request() req: any, @Body() deviceId: string) {\n    const userId = req.user.userId;\n    return this.syncService.unregisterDevice(userId, deviceId);\n  }\n}\n"], "names": ["SyncController", "getSyncStatus", "req", "userId", "user", "syncService", "performFullSync", "options", "performIncrementalSync", "getSyncConflicts", "resolveSyncConflicts", "resolutions", "createBackup", "listBackups", "restoreBackup", "restoreData", "exportUserData", "importUserData", "importData", "getSyncDevices", "registerDevice", "deviceInfo", "unregisterDevice", "deviceId", "constructor"], "mappings": ";;;;+BAcaA;;;eAAAA;;;wBANN;8BACsB;6BACD;;;;;;;;;;;;;;;AAIrB,IAAA,AAAMA,iBAAN,MAAMA;IAGX,MACMC,cAAc,AAAWC,GAAQ,EAAE;QACvC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACJ,aAAa,CAACE;IACxC;IAEA,MACMG,gBAAgB,AAAWJ,GAAQ,EAAE,AAAQK,OAAa,EAAE;QAChE,MAAMJ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACC,eAAe,CAACH,QAAQI;IAClD;IAEA,MACMC,uBAAuB,AAAWN,GAAQ,EAAE,AAAQK,OAAa,EAAE;QACvE,MAAMJ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACG,sBAAsB,CAACL,QAAQI;IACzD;IAEA,MACME,iBAAiB,AAAWP,GAAQ,EAAE;QAC1C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACI,gBAAgB,CAACN;IAC3C;IAEA,MACMO,qBAAqB,AAAWR,GAAQ,EAAE,AAAQS,WAAgB,EAAE;QACxE,MAAMR,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACK,oBAAoB,CAACP,QAAQQ;IACvD;IAEA,MACMC,aAAa,AAAWV,GAAQ,EAAE,AAASK,OAAa,EAAE;QAC9D,MAAMJ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACO,YAAY,CAACT,QAAQI;IAC/C;IAEA,MACMM,YAAY,AAAWX,GAAQ,EAAE;QACrC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACQ,WAAW,CAACV;IACtC;IAEA,MACMW,cAAc,AAAWZ,GAAQ,EAAE,AAAQa,WAAgB,EAAE;QACjE,MAAMZ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACS,aAAa,CAACX,QAAQY;IAChD;IAEA,MACMC,eAAe,AAAWd,GAAQ,EAAE,AAASK,OAAa,EAAE;QAChE,MAAMJ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACW,cAAc,CAACb,QAAQI;IACjD;IAEA,MACMU,eAAe,AAAWf,GAAQ,EAAE,AAAQgB,UAAe,EAAE;QACjE,MAAMf,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACY,cAAc,CAACd,QAAQe;IACjD;IAEA,MACMC,eAAe,AAAWjB,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACc,cAAc,CAAChB;IACzC;IAEA,MACMiB,eAAe,AAAWlB,GAAQ,EAAE,AAAQmB,UAAe,EAAE;QACjE,MAAMlB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACe,cAAc,CAACjB,QAAQkB;IACjD;IAEA,MACMC,iBAAiB,AAAWpB,GAAQ,EAAE,AAAQqB,QAAgB,EAAE;QACpE,MAAMpB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,WAAW,CAACiB,gBAAgB,CAACnB,QAAQoB;IACnD;IA9EAC,YAAY,AAAiBnB,WAAwB,CAAE;aAA1BA,cAAAA;IAA2B;AA+E1D"}