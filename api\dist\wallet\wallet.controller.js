"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "WalletController", {
    enumerable: true,
    get: function() {
        return WalletController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _walletservice = require("./wallet.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let WalletController = class WalletController {
    async getUserWallet(req) {
        const userId = req.user.userId;
        return this.walletService.getUserWallet(userId);
    }
    async earnSnapCoins(body, req) {
        const userId = req.user.userId;
        return this.walletService.earnSnapCoins(userId, body.amount, body.reason, body.category);
    }
    async spendSnapCoins(body, req) {
        const userId = req.user.userId;
        return this.walletService.spendSnapCoins(userId, body.amount, body.reason, body.category);
    }
    async useSnapTokens(body, req) {
        const userId = req.user.userId;
        return this.walletService.useSnapTokens(userId, body.amount, body.feature);
    }
    async canUseFeature(req, body) {
        const userId = req.user.userId;
        return this.walletService.canUseFeature(userId, body.feature);
    }
    constructor(walletService){
        this.walletService = walletService;
    }
};
_ts_decorate([
    (0, _common.Get)(),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WalletController.prototype, "getUserWallet", null);
_ts_decorate([
    (0, _common.Post)('earn-coins'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WalletController.prototype, "earnSnapCoins", null);
_ts_decorate([
    (0, _common.Post)('spend-coins'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WalletController.prototype, "spendSnapCoins", null);
_ts_decorate([
    (0, _common.Post)('use-tokens'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WalletController.prototype, "useSnapTokens", null);
_ts_decorate([
    (0, _common.Get)('can-use-feature/:feature'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WalletController.prototype, "canUseFeature", null);
WalletController = _ts_decorate([
    (0, _common.Controller)('wallet'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _walletservice.WalletService === "undefined" ? Object : _walletservice.WalletService
    ])
], WalletController);

//# sourceMappingURL=wallet.controller.js.map