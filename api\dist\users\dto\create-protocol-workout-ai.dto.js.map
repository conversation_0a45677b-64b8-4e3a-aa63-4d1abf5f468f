{"version": 3, "sources": ["../../../src/users/dto/create-protocol-workout-ai.dto.ts"], "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport { IsArray, IsNotEmpty, IsNumber, IsString, ValidateNested, IsOptional } from 'class-validator';\r\n\r\nclass ExerciseDto {\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsNumber()\r\n  sets: number;\r\n\r\n  @IsNumber()\r\n  reps: number;\r\n\r\n  @IsNumber()\r\n  @IsOptional()\r\n  rpe?: number;\r\n\r\n  @IsNumber()\r\n  @IsOptional()\r\n  rest_seconds?: number; // Duracao de descanso em segundos\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  notes?: string;\r\n}\r\n\r\nclass WorkoutDto {\r\n  @IsArray()\r\n  @ValidateNested({ each: true })\r\n  @Type(() => ExerciseDto)\r\n  exercises: ExerciseDto[];\r\n}\r\n\r\nexport class CreateProtocolWorkoutAiDto {\r\n  @IsNotEmpty()\r\n  @IsString()\r\n  name: string;\r\n\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  type_id: number;\r\n\r\n  @IsString()\r\n  split: string;\r\n\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  frequency: number;\r\n\r\n  @IsString()\r\n  objective: string;\r\n\r\n  @IsOptional()\r\n  @IsString()\r\n  general_notes?: string;\r\n\r\n  @IsArray()\r\n  @ValidateNested({ each: true })\r\n  @Type(() => WorkoutDto)\r\n  workouts: WorkoutDto[];\r\n}\r\n"], "names": ["CreateProtocolWorkoutAiDto", "ExerciseDto", "WorkoutDto", "each", "Number"], "mappings": ";;;;+BAiCaA;;;eAAAA;;;kCAjCQ;gCAC+D;;;;;;;;;;AAEpF,IAAA,AAAMC,cAAN,MAAMA;AAqBN;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAA,AAAMC,aAAN,MAAMA;AAKN;;;;QAHoBC,MAAM;;oCACZF;;;AAIP,IAAA,AAAMD,6BAAN,MAAMA;AA2Bb;;;;;;;;oCArBcI;;;;;;;;;oCAOAA;;;;;;;;;;;;;;;QAWMD,MAAM;;oCACZD"}