{"version": 3, "sources": ["../../../src/users/dto/daily-workouts-activities.dto.ts"], "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport { IsNotEmpty, IsNumber } from 'class-validator';\r\n\r\nexport class DailyWorkoutsActivitiesDto {\r\n  @IsNotEmpty()\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  id: number;\r\n\r\n  @IsNotEmpty()\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  duration: number;\r\n}"], "names": ["DailyWorkoutsActivitiesDto", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;kCAHQ;gCACgB;;;;;;;;;;AAE9B,IAAA,AAAMA,6BAAN,MAAMA;AAUb;;;;oCAPcC;;;;;;oCAKAA"}