{"version": 3, "sources": ["../../../../src/admin/dto/plans/create-plan.dto.ts"], "sourcesContent": ["import { IsString, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON>nt, IsN<PERSON>ber, Min, IsBoolean, IsOptional } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class CreatePlanDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string; // Nome do plano\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string; // Descrição do plano\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  price: number; // Preço do plano\r\n\r\n  /*\r\n  @IsString()\r\n  currency: string; // Moeda\r\n  */\r\n\r\n  /*\r\n  @IsString()\r\n  frequency: 'daily' | 'weekly' | 'monthly' | 'annually'; // Frequência\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  interval_value: number; // Valor do intervalo\r\n  */\r\n\r\n  @IsString()\r\n  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'semiannual' | 'annually'; // Período\r\n\r\n  @IsBoolean()\r\n  @IsOptional()\r\n  isActive?: boolean; // Ativo\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  snaptokens?: number; // Snaptokens\r\n\r\n  /*\r\n  @IsBoolean()\r\n  @IsOptional()\r\n  allows_trial?: boolean; // Permite trial\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  trial_period_days?: number; // Dias de trial\r\n  */\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  affiliate_master_commission_percent?: number; // Comissão do master\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  affiliate_commission_percent?: number; // Comissão do afiliado\r\n}"], "names": ["CreatePlanDto"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAHqE;;;;;;;;;;AAG3E,IAAA,AAAMA,gBAAN,MAAMA;AA2Db"}