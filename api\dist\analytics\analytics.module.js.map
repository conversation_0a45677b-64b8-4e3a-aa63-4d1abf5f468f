{"version": 3, "sources": ["../../src/analytics/analytics.module.ts"], "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\nimport { AnalyticsController } from './analytics.controller';\nimport { AnalyticsService } from './analytics.service';\n\n@Module({\n  controllers: [AnalyticsController],\n  providers: [AnalyticsService],\n  exports: [AnalyticsService],\n})\nexport class AnalyticsModule {}\n"], "names": ["AnalyticsModule", "controllers", "AnalyticsController", "providers", "AnalyticsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;qCACa;kCACH;;;;;;;AAO1B,IAAA,AAAMA,kBAAN,MAAMA;AAAiB;;;QAJ5BC,aAAa;YAACC,wCAAmB;SAAC;QAClCC,WAAW;YAACC,kCAAgB;SAAC;QAC7BC,SAAS;YAACD,kCAAgB;SAAC"}