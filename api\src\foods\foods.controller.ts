import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FoodsService } from './foods.service';

@Controller('foods')
export class FoodsController {
  constructor(private readonly foodsService: FoodsService) {}

  @Get('search')
  async searchFoods(@Query() query: { q: string; limit?: number; source?: string }) {
    return this.foodsService.searchFoods(query);
  }

  @Get('search/nutritionix')
  async searchNutritionix(@Query() query: { q: string }) {
    return this.foodsService.searchNutritionix(query.q);
  }

  @Get('search/edamam')
  async searchEdamam(@Query() query: { q: string }) {
    return this.foodsService.searchEdamam(query.q);
  }

  @Get('search/openfoodfacts')
  async searchOpenFoodFacts(@Query() query: { q: string }) {
    return this.foodsService.searchOpenFoodFacts(query.q);
  }

  @Post('custom')
  @UseGuards(JwtAuthGuard)
  async createCustomFood(@Body() foodData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.foodsService.createCustomFood(foodData, userId);
  }

  @Get('custom')
  @UseGuards(JwtAuthGuard)
  async getCustomFoods(@Request() req: any) {
    const userId = req.user.userId;
    return this.foodsService.getCustomFoods(userId);
  }

  @Post('import')
  @UseGuards(JwtAuthGuard)
  async importFoodDatabase(@Body() importData: any) {
    return this.foodsService.importFoodDatabase(importData);
  }

  @Get('categories')
  async getFoodCategories() {
    return this.foodsService.getFoodCategories();
  }

  @Get('popular')
  async getPopularFoods(@Query() query: { limit?: number }) {
    return this.foodsService.getPopularFoods(query);
  }

  @Get('recent')
  @UseGuards(JwtAuthGuard)
  async getRecentFoods(@Request() req: any, @Query() query: { limit?: number }) {
    const userId = req.user.userId;
    return this.foodsService.getRecentFoods(userId, query);
  }
}
