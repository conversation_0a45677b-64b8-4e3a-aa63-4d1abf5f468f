{"version": 3, "sources": ["../../src/users/users.controller.ts"], "sourcesContent": ["import { Body, Controller, Delete, Get, Param, Post, Put, Query, Request, UploadedFile, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common';\r\nimport { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';\r\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\r\nimport { CreateClientDto } from '../dto/create-client.dto';\r\nimport { UsersService } from './users.service';\r\nimport { AuthGuard } from '@nestjs/passport';\r\nimport { DailyWaterDto } from './dto/daily-water.dto';\r\nimport { DailyWorkoutsActivitiesDto } from './dto/daily-workouts-activities.dto';\r\nimport { CreateProtocolWorkoutDto } from './dto/create-protocol-workout.dto';\r\nimport { CreateProtocolDietDto } from './dto/create-protocol-diet.dto';\r\nimport { UpdateProtocolWorkoutDto } from './dto/update-protocol-workout.dto';\r\nimport { convertFromUTC, convertToUTC } from 'src/common/utils/date.util';\r\nconst dayjs = require('dayjs');\r\n\r\n@Controller('users')\r\nexport class UsersController {\r\n  constructor(private readonly usersService: UsersService){}\r\n\r\n    @Get('time')\r\n    getTime() {\r\n      const tz = 'America/Sao_Paulo';\r\n      const startOfDay = convertToUTC(dayjs().startOf('day').toDate(), tz);\r\n      const endOfDay = convertToUTC(dayjs().endOf('day').toDate(), tz);\r\n\r\n      return {\r\n        startOfDay,\r\n        endOfDay\r\n      };\r\n\r\n    }\r\n\r\n    @Get()\r\n    getAllUsers() {\r\n        return [\r\n            {\r\n              \"id\": 1,\r\n              \"name\": \"Alice Johnson\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Admin\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/1.jpg\",\r\n              \"code\": \"f7k3s\",\r\n              \"createdAt\": \"2022-05-14T10:22:31.000Z\"\r\n            },\r\n            {\r\n              \"id\": 2,\r\n              \"name\": \"Bob Smith\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/1.jpg\",\r\n              \"code\": \"g2t7a\",\r\n              \"createdAt\": \"2023-08-01T06:12:45.000Z\"\r\n            },\r\n            {\r\n              \"id\": 3,\r\n              \"name\": \"Charlie Brown\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Editor\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/2.jpg\",\r\n              \"code\": \"j8w4n\",\r\n              \"createdAt\": \"2021-12-30T12:00:00.000Z\"\r\n            },\r\n            {\r\n              \"id\": 4,\r\n              \"name\": \"Daisy Miller\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Admin\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/2.jpg\",\r\n              \"code\": \"h5q2j\",\r\n              \"createdAt\": \"2023-01-15T15:30:10.000Z\"\r\n            },\r\n            {\r\n              \"id\": 5,\r\n              \"name\": \"Edward Wilson\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/3.jpg\",\r\n              \"code\": \"x9r3z\",\r\n              \"createdAt\": \"2022-09-22T19:45:25.000Z\"\r\n            },\r\n            {\r\n              \"id\": 6,\r\n              \"name\": \"Fiona Green\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/3.jpg\",\r\n              \"code\": \"n6k8d\",\r\n              \"createdAt\": \"2020-11-14T08:11:31.000Z\"\r\n            },\r\n            {\r\n              \"id\": 7,\r\n              \"name\": \"George Black\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Admin\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/4.jpg\",\r\n              \"code\": \"v4w7p\",\r\n              \"createdAt\": \"2021-03-10T13:22:45.000Z\"\r\n            },\r\n            {\r\n              \"id\": 8,\r\n              \"name\": \"Hannah White\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/4.jpg\",\r\n              \"code\": \"b1j2k\",\r\n              \"createdAt\": \"2022-02-28T11:15:50.000Z\"\r\n            },\r\n            {\r\n              \"id\": 9,\r\n              \"name\": \"Ian Black\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Editor\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/5.jpg\",\r\n              \"code\": \"x3l4y\",\r\n              \"createdAt\": \"2021-10-09T19:30:12.000Z\"\r\n            },\r\n            {\r\n              \"id\": 10,\r\n              \"name\": \"Jane Doe\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/5.jpg\",\r\n              \"code\": \"m0n6v\",\r\n              \"createdAt\": \"2023-07-01T16:45:30.000Z\"\r\n            }\r\n          ];\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('/:role')\r\n    async create(@Param('role') role: any, @Body() createClientDto: CreateClientDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      if(role=='admin'){\r\n        throw new Error('Admin role is not allowed');\r\n      }\r\n\r\n      const user = await this.usersService.create(createClientDto, userId, role)\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('me')\r\n    async getMe(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const user = await this.usersService.getMe(userId);\r\n\r\n      return user;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('goals')\r\n    async getGoals() {\r\n      return this.usersService.getGoals();\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Put('me')\r\n    async updateMe(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const user = await this.usersService.updateMe(userId, body);\r\n\r\n      return user;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Delete('me')\r\n    async deleteMe(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      return this.usersService.softDeleteAccount(userId);\r\n    }\r\n\r\n    // User photo\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('me/photo')\r\n    @UseInterceptors(FileInterceptor('photo'))\r\n    async updateMePhoto(@Request() req: any, @UploadedFile() file: Express.Multer.File) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      if (!file) {\r\n        throw new Error('Photo file is required');\r\n      }\r\n\r\n      const result = await this.usersService.updateMePhoto(userId, file);\r\n\r\n      return result;\r\n    }\r\n\r\n    // Resumo Nutricional (Aba: Diário)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('diary/nutritional_summary')\r\n    async getDailyNutritionalSummary(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const nutritional_summary = await this.usersService.getDailyNutritionalSummary(query as any, userId);\r\n\r\n      return nutritional_summary;\r\n    }\r\n\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('daily')\r\n    async getDaily(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const daily = await this.usersService.getDaily(query as any, userId);\r\n\r\n      return daily;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('assessments')\r\n    async getAssessments(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const assessments = await this.usersService.getAssessments(userId);\r\n\r\n      return assessments;\r\n    }\r\n\r\n    // Daily activity post\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('daily/water')\r\n    async postDailyWater(@Request() req: any, @Body() dailyWaterDto: DailyWaterDto) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const daily = await this.usersService.postDailyWater(userId, dailyWaterDto);\r\n\r\n      return daily;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('daily/list/workouts_activities')\r\n    getDailyWorkoutsActivities() {\r\n      return this.usersService.getDailyWorkoutsActivities();\r\n    }\r\n\r\n    // Daily Workouts Activities\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('daily/workouts_activities')\r\n    getDailyWorkoutsActivitiesByDate(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      return this.usersService.getDailyWorkoutsActivitiesByDate(userId, query as any);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('daily/workouts_activities')\r\n    postDailyWorkoutsActivities(@Body() dailyWorkoutsActivitiesDto: DailyWorkoutsActivitiesDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      return this.usersService.postDailyWorkoutsActivities(userId, dailyWorkoutsActivitiesDto);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout')\r\n    async postProtocolsWorkout(@Body() createProtocolWorkoutDto: CreateProtocolWorkoutDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocol = await this.usersService.postProtocolsWorkout(userId, createProtocolWorkoutDto);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    // AI\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/ai')\r\n    async postProtocolsWorkoutAi(@Body() userInfo: any, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocol = await this.usersService.postProtocolsWorkoutAi(userId, userInfo);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workouts/active')\r\n    async getActiveProtocolsWorkouts(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocols = await this.usersService.getActiveProtocolsWorkouts(userId);\r\n\r\n      return protocols;\r\n    }\r\n\r\n    // HISTÓRICO DE PROTOCOLOS DE TREINO - DEVE VIR ANTES DA ROTA :id\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/history')\r\n    async getProtocolsWorkoutHistoryFixed(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const options = {\r\n        page: query.page ? parseInt(query.page) : 1,\r\n        limit: query.limit ? parseInt(query.limit) : 10,\r\n        status: query.status || 'all',\r\n        startDate: query.startDate,\r\n        endDate: query.endDate,\r\n        type: query.type\r\n      };\r\n\r\n      console.log(`🏋️ Endpoint getProtocolsWorkoutHistory chamado para usuário ${userId}`, options);\r\n      const result = await this.usersService.getProtocolsWorkoutHistory(userId, options);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Histórico de protocolos de treino recuperado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/:id')\r\n    async getProtocolWorkoutById(@Param('id') id: string, @Request() req: any) {\r\n      console.log('🔍 Controller: req.user:', req.user);\r\n      console.log('🔍 Controller: req.user.userId:', req.user?.userId);\r\n      console.log('🔍 Controller: Protocol ID param:', id);\r\n\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        console.log('❌ Controller: User ID não encontrado no token');\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocolId = parseInt(id, 10);\r\n      if (isNaN(protocolId)) {\r\n        console.log('❌ Controller: Protocol ID inválido:', id);\r\n        throw new Error('Invalid protocol ID');\r\n      }\r\n\r\n      console.log(`✅ Controller: Buscando protocolo ${protocolId} para usuário ${userId}`);\r\n      const protocol = await this.usersService.getProtocolWorkoutById(protocolId, userId);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Put('protocols/workout/:id')\r\n    async updateProtocolWorkout(@Param('id') id: string, @Body() updateData: UpdateProtocolWorkoutDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocolId = parseInt(id, 10);\r\n      if (isNaN(protocolId)) {\r\n        throw new Error('Invalid protocol ID');\r\n      }\r\n\r\n      const protocol = await this.usersService.updateProtocolWorkout(protocolId, updateData, userId);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet')\r\n    async postProtocolsDiet(@Body() createProtocolDietDto: CreateProtocolDietDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocol = await this.usersService.postProtocolsDiet(userId, createProtocolDietDto);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    // AI\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/ai')\r\n    async postProtocolsDietAI(@Body() userInfo: any, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      const protocol = await this.usersService.postProtocolsDietAi(userId, userInfo);\r\n\r\n      return protocol;\r\n    }\r\n\r\n\r\n    // get active protocol of diet\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/active')\r\n    async getActiveProtocolsDiet(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const protocols = await this.usersService.getActiveProtocolsDiet(userId);\r\n      return protocols;\r\n    }\r\n\r\n    // HISTÓRICO DE PROTOCOLOS DE DIETA (DEVE VIR ANTES DA ROTA GENÉRICA :id)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/history')\r\n    async getProtocolsDietHistory(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      try {\r\n        console.log(`📚 Endpoint getProtocolsDietHistory chamado para usuário ${userId}`);\r\n        console.log(`📋 Query params recebidos:`, query);\r\n\r\n        const options = {\r\n          page: query.page ? parseInt(query.page) : 1,\r\n          limit: query.limit ? parseInt(query.limit) : 10,\r\n          status: query.status || 'all',\r\n          startDate: query.startDate,\r\n          endDate: query.endDate,\r\n          type: query.type\r\n        };\r\n\r\n        // Validar parâmetros\r\n        if (options.page < 1) options.page = 1;\r\n        if (options.limit < 1 || options.limit > 100) options.limit = 10;\r\n        if (!['active', 'finished', 'all'].includes(options.status)) {\r\n          options.status = 'all';\r\n        }\r\n\r\n        console.log(`📊 Opções processadas:`, options);\r\n\r\n        const result = await this.usersService.getProtocolsDietHistory(userId, options);\r\n\r\n        console.log(`✅ Histórico recuperado com sucesso: ${result.protocols?.length || 0} protocolos`);\r\n\r\n        return {\r\n          status: 'success',\r\n          message: 'Histórico de protocolos de dieta recuperado com sucesso',\r\n          data: result\r\n        };\r\n\r\n      } catch (error) {\r\n        console.error(`❌ Erro no endpoint getProtocolsDietHistory para usuário ${userId}:`, error);\r\n        console.error(`❌ Query params que causaram erro:`, query);\r\n        throw error;\r\n      }\r\n    }\r\n\r\n    // get specific diet protocol by id (DEVE VIR DEPOIS DAS ROTAS ESPECÍFICAS)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/:id')\r\n    async getProtocolDietById(@Param('id') id: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🔍 Recebido ID do protocolo: \"${id}\" (tipo: ${typeof id})`);\r\n\r\n      // Validar se o ID foi fornecido\r\n      if (!id || id.trim() === '') {\r\n        console.error('❌ ID do protocolo está vazio ou nulo');\r\n        throw new Error('Protocol ID is required');\r\n      }\r\n\r\n      // Tentar converter para número\r\n      const protocolId = parseInt(id.trim(), 10);\r\n      if (isNaN(protocolId) || protocolId <= 0) {\r\n        console.error(`❌ ID do protocolo inválido: \"${id}\" -> ${protocolId}`);\r\n        throw new Error(`Invalid protocol ID: \"${id}\". Must be a positive integer.`);\r\n      }\r\n\r\n      console.log(`🔍 Buscando protocolo de dieta ${protocolId} para usuário ${userId}`);\r\n      const protocol = await this.usersService.getProtocolDietById(protocolId, userId);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo de dieta recuperado com sucesso',\r\n        data: protocol\r\n      };\r\n    }\r\n\r\n    // get active meals of day week\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/meals/active')\r\n    getActiveMealsOfDayWeek(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getActiveMealsOfDayWeek(userId, query as any);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/mealofdayweek')\r\n    getProtocolsDietMealOfDayWeek(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getActiveProtocolsDietMealsDayWeek(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Delete('protocols/diet/:id')\r\n    async deleteProtocolDiet(@Param('id') id: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const protocolId = parseInt(id, 10);\r\n      if (isNaN(protocolId)) {\r\n        throw new Error('Invalid protocol ID');\r\n      }\r\n      const protocol = await this.usersService.deleteProtocolDiet(protocolId, userId);\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Delete('protocols/workout/:id')\r\n    async deleteProtocolWorkout(@Param('id') id: number, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const protocol = await this.usersService.deleteProtocolWorkout(id, userId);\r\n      return protocol;\r\n    }\r\n\r\n    // Check protocol workout\r\n\r\n    // Check protocol diet\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/check')\r\n    async checkProtocolDiet(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.checkProtocolDiet(userId, body);\r\n    }\r\n\r\n    // Uncheck protocol diet\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/uncheck')\r\n    async uncheckProtocolDiet(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.uncheckProtocolDiet(userId, body);\r\n    }\r\n\r\n    // ENDPOINT TEMPORÁRIO PARA CORREÇÃO DE PROTOCOLOS ÓRFÃOS\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/fix-orphans')\r\n    async fixOrphanProtocols(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🔧 Endpoint fixOrphanProtocols chamado para usuário ${userId}`);\r\n      const result = await this.usersService.fixOrphanProtocols(userId);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Correção de protocolos órfãos executada',\r\n        data: result\r\n      };\r\n    }\r\n\r\n\r\n\r\n    // FINALIZAR PROTOCOLO DE DIETA\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/:id/finish')\r\n    async finishProtocolDiet(@Param('id') protocolId: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🏁 Endpoint finishProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.finishProtocolDiet(userId, parseInt(protocolId));\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo finalizado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DUPLICAR PROTOCOLO DE DIETA\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/:id/duplicate')\r\n    async duplicateProtocolDiet(@Param('id') protocolId: string, @Body() body: { startDate?: string }, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`📋 Endpoint duplicateProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.duplicateProtocolDiet(userId, parseInt(protocolId), body.startDate);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo duplicado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // FINALIZAR PROTOCOLO DE TREINO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/:id/finish')\r\n    async finishProtocolWorkout(@Param('id') protocolId: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🏁 Endpoint finishProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.finishProtocolWorkout(userId, parseInt(protocolId));\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo de treino finalizado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DUPLICAR PROTOCOLO DE TREINO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/:id/duplicate')\r\n    async duplicateProtocolWorkout(@Param('id') protocolId: string, @Body() body: { startDate?: string }, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`📋 Endpoint duplicateProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.duplicateProtocolWorkout(userId, parseInt(protocolId), body.startDate);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo de treino duplicado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DEBUG: VERIFICAR DADOS DE PROTOCOLOS NO BANCO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('debug/protocols/raw')\r\n    async debugProtocolsRaw(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      console.log(`🔍 DEBUG: Verificando protocolos raw para usuário ${userId}`);\r\n\r\n      const result = await this.usersService.debugProtocolsRaw(userId);\r\n      return {\r\n        status: 'debug',\r\n        message: 'Dados raw de protocolos',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DEBUG: VERIFICAR DADOS DE TREINOS COMPLETADOS NO BANCO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('debug/workouts/raw')\r\n    async debugWorkoutsRaw(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      console.log(`🔍 DEBUG: Verificando treinos completados raw para usuário ${userId}`);\r\n\r\n      const result = await this.usersService.debugWorkoutsRaw(userId);\r\n      return {\r\n        status: 'debug',\r\n        message: 'Dados raw de treinos completados',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // HISTÓRICO DE PROTOCOLOS DE TREINO - REMOVIDO (DUPLICADO, AGORA ESTÁ ACIMA)\r\n\r\n    // DEBUG: Endpoint para verificar protocolos no banco\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('debug/protocols/raw')\r\n    async getProtocolsRaw(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🔍 DEBUG: Buscando protocolos raw para usuário ${userId}`);\r\n      const result = await this.usersService.getProtocolsRawDebug(userId);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Dados raw dos protocolos',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // get daily checked meal by date_start date_end\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/mealsofdayweekchecked')\r\n    getDailyCheckedMealk(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getDailyCheckedMeal(userId, query as any);\r\n    }\r\n\r\n    // get exercises from protocol coach active\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/exercises')\r\n    getProtocolsWorkoutExercises(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProtocolsWorkoutExercises(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/daily')\r\n    async postProtocolsWorkoutDaily(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.postProtocolsWorkoutDaily(userId, body);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/daily')\r\n    async getProtocolsWorkoutDaily(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProtocolsWorkoutDaily(userId, query as any);\r\n    }\r\n\r\n    // Dias batendo a meta (Sequência) (Aba: Progresso)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/nutritional/days_goal_sequence')\r\n    async getProgressNutritionalDaysGoalSequence(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressNutritionalDaysGoalSequence(userId);\r\n    }\r\n\r\n    // Registrar avaliação física\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('progress/evaluations')\r\n    @UseInterceptors(AnyFilesInterceptor())\r\n    async postProgressEvaluations(\r\n      @Request() req: any,\r\n      @Body() body: any,\r\n      @UploadedFiles() files: Express.Multer.File[],\r\n    ) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const front = files.find(file => file.fieldname === 'front');\r\n      const back = files.find(file => file.fieldname === 'back');\r\n      const side = files.find(file => file.fieldname === 'side');\r\n\r\n      const photos = { front, back, side };\r\n\r\n      return this.usersService.postProgressEvaluations(userId, body, photos);\r\n    }\r\n\r\n    // Registrar mediddas\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('progress/evaluations/measurements')\r\n    async getProgressEvaluationsMeasurements(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.postProgressEvaluationsMeasurements(userId, body);\r\n    }\r\n\r\n    // Histórico de avaliações\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/evaluations')\r\n    async getProgressEvaluations(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressEvaluations(userId, query as any);\r\n    }\r\n\r\n    // Assiduidade\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/attendance')\r\n    async getProgressAttendance(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressAttendance(userId);\r\n    }\r\n\r\n    // Progresso Semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/diet/weekly')\r\n    async getProgressDietWeekly(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressDietWeekly(userId);\r\n    }\r\n\r\n    // Progresso Semanal Consolidado (Treinos, Nutrição, Sono, Água)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/weekly/consolidated')\r\n    async getWeeklyProgressConsolidated(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWeeklyProgressConsolidated(userId);\r\n    }\r\n\r\n    // Aderência semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/adherence')\r\n    async getProgressAdherence(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressAdherence(userId, query);\r\n    }\r\n\r\n    // Histórico\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/diet')\r\n    async getProgressDiet(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressDiet(userId);\r\n    }\r\n\r\n    // Evolução de Peso e Gordura\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/weight_fat')\r\n    async getProgressWeightFat(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressWeightFat(userId, query as any);\r\n    }\r\n\r\n    // Evolução de Força\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/strength')\r\n    async getProgressStrength(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressStrength(userId, query as any);\r\n    }\r\n\r\n    // Análise Nutricional\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/nutritional_analysis')\r\n    async getProgressNutritionalAnalysis(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressNutritionalAnalysis(userId, query as any);\r\n    }\r\n\r\n    // Saldo Calórico\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/caloric_balance')\r\n    async getProgressCaloricBalance(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressCaloricBalance(userId, query as any);\r\n    }\r\n\r\n    // Análise de Treinos progress/chart/workouts\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/workouts')\r\n    async getProgressWorkouts(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressWorkouts(userId, query as any);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/workouts_analysis')\r\n    async getProgressWorkoutsAnalysis(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressWorkoutsAnalysis(userId, query as any);\r\n    }\r\n\r\n\r\n    // Análise Completa\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/complete_analysis')\r\n    async getProgressCompleteAnalysis(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressCompleteAnalysis(userId, query as any);\r\n    }\r\n\r\n    // Endpoint geral de progresso (para compatibilidade com frontend)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress')\r\n    async getProgress(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgress(userId, query as any);\r\n    }\r\n\r\n    // Análise nutricional semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('analytics/nutrition/weekly')\r\n    async getAnalyticsNutritionWeekly(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getAnalyticsNutritionWeekly(userId, query as any);\r\n    }\r\n\r\n    // Análise de treinos semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('analytics/workouts/weekly')\r\n    async getAnalyticsWorkoutsWeekly(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getAnalyticsWorkoutsWeekly(userId, query as any);\r\n    }\r\n\r\n    // Evolução de força\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('strength/evolution')\r\n    async getStrengthEvolution(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getStrengthEvolution(userId, query as any);\r\n    }\r\n\r\n    // WORKOUT SESSION ENDPOINTS\r\n\r\n    // Start workout session\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('workouts/start')\r\n    async startWorkout(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.startWorkout(userId, body);\r\n    }\r\n\r\n    // Get workouts by date\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workouts')\r\n    async getWorkoutsByDate(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWorkoutsByDate(userId, query);\r\n    }\r\n\r\n    // Get workout history\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workouts/history')\r\n    async getWorkoutHistory(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWorkoutHistory(userId, query);\r\n    }\r\n\r\n    // Get workout session details\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workouts/sessions/:id')\r\n    async getWorkoutSession(@Request() req: any, @Param('id') sessionId: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWorkoutSession(userId, sessionId);\r\n    }\r\n\r\n    // Complete workout session\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('workouts/:id/complete')\r\n    async completeWorkout(@Request() req: any, @Param('id') workoutId: string, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.completeWorkout(userId, workoutId, body);\r\n    }\r\n\r\n    // Distribuição de Volume\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/volume_distribution')\r\n    async getProgressVolumeDistribution(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressVolumeDistribution(userId, query as any);\r\n    }\r\n\r\n\r\n    // AI\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('ai/foods-suggestions')\r\n    async getFoodsSuggestions(@Request() req: any, @Query() query: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getFoodsSuggestions(userId, query, body);\r\n    }\r\n\r\n    // User Options\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('options')\r\n    async getUserOptions(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getUserOptions(userId);\r\n    }\r\n\r\n    // Affiliates\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('affiliate/join')\r\n    async createAffiliate(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.createAffiliate(body, userId);\r\n    }\r\n\r\n    // Plans\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('plans')\r\n    async getAllPlans(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getAllPlans(userId);\r\n    }\r\n\r\n    // Check plan stripe\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('plans/checkout/:planId')\r\n    async subscribePlan(@Request() req: any, @Param('planId') planId: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.subscribePlan(userId, Number(planId));\r\n    }\r\n\r\n    // Subscriptions\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('subscriptions')\r\n    async getMySubscriptions(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getMySubscriptions(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('subscriptions/:id/cancel')\r\n    async cancelSubscription(@Request() req: any, @Param('id') id: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.cancelSubscription(Number(id), userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('subscriptions/:id/cancel-immediately')\r\n    async cancelSubscriptionImmediately(@Request() req: any, @Param('id') id: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.cancelSubscriptionImmediately(Number(id), userId);\r\n    }\r\n\r\n    // Transactions\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('transactions')\r\n    async getMyTransactions(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getMyTransactions(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('transactions/:id')\r\n    async getMyTransactionDetails(@Request() req: any, @Param('id') id: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getMyTransactionDetails(Number(id), userId);\r\n    }\r\n}"], "names": ["UsersController", "dayjs", "require", "getTime", "tz", "startOfDay", "convertToUTC", "startOf", "toDate", "endOfDay", "endOf", "getAllUsers", "create", "role", "createClientDto", "req", "userId", "user", "Error", "usersService", "getMe", "getGoals", "updateMe", "body", "deleteMe", "softDeleteAccount", "updateMePhoto", "file", "result", "getDailyNutritionalSummary", "query", "nutritional_summary", "getDaily", "daily", "getAssessments", "assessments", "postDailyWater", "dailyWaterDto", "getDailyWorkoutsActivities", "getDailyWorkoutsActivitiesByDate", "postDailyWorkoutsActivities", "dailyWorkoutsActivitiesDto", "postProtocolsWorkout", "createProtocolWorkoutDto", "protocol", "postProtocolsWorkoutAi", "userInfo", "getActiveProtocolsWorkouts", "protocols", "getProtocolsWorkoutHistoryFixed", "options", "page", "parseInt", "limit", "status", "startDate", "endDate", "type", "console", "log", "getProtocolsWorkoutHistory", "message", "data", "getProtocolWorkoutById", "id", "protocolId", "isNaN", "updateProtocolWorkout", "updateData", "postProtocolsDiet", "createProtocolDietDto", "postProtocolsDietAI", "postProtocolsDietAi", "getActiveProtocolsDiet", "getProtocolsDietHistory", "includes", "length", "error", "getProtocolDietById", "trim", "getActiveMealsOfDayWeek", "getProtocolsDietMealOfDayWeek", "getActiveProtocolsDietMealsDayWeek", "deleteProtocolDiet", "deleteProtocolWorkout", "checkProtocolDiet", "uncheckProtocolDiet", "fixOrphanProtocols", "finishProtocolDiet", "duplicateProtocolDiet", "finishProtocolWorkout", "duplicateProtocolWorkout", "debugProtocolsRaw", "debugWorkoutsRaw", "getProtocolsRaw", "getProtocolsRawDebug", "getDailyCheckedMealk", "getDailyCheckedMeal", "getProtocolsWorkoutExercises", "postProtocolsWorkoutDaily", "getProtocolsWorkoutDaily", "getProgressNutritionalDaysGoalSequence", "postProgressEvaluations", "files", "front", "find", "fieldname", "back", "side", "photos", "getProgressEvaluationsMeasurements", "postProgressEvaluationsMeasurements", "getProgressEvaluations", "getProgressAttendance", "getProgressDietWeekly", "getWeeklyProgressConsolidated", "getProgressAdherence", "getProgressDiet", "getProgressWeightFat", "getProgressStrength", "getProgressNutritionalAnalysis", "getProgressCaloricBalance", "getProgressWorkouts", "getProgressWorkoutsAnalysis", "getProgressCompleteAnalysis", "getProgress", "getAnalyticsNutritionWeekly", "getAnalyticsWorkoutsWeekly", "getStrengthEvolution", "startWorkout", "getWorkoutsByDate", "getWorkoutHistory", "getWorkoutSession", "sessionId", "completeWorkout", "workoutId", "getProgressVolumeDistribution", "getFoodsSuggestions", "getUserOptions", "createAffiliate", "getAllPlans", "subscribePlan", "planId", "Number", "getMySubscriptions", "cancelSubscription", "cancelSubscriptionImmediately", "getMyTransactions", "getMyTransactionDetails", "constructor"], "mappings": ";;;;+BAeaA;;;eAAAA;;;wBAf4H;iCACpF;8BACxB;iCACG;8BACH;+BAEC;4CACa;0CACF;uCACH;0CACG;0BACI;;;;;;;;;;;;;;;AAC7C,MAAMC,QAAQC,QAAQ;AAGf,IAAA,AAAMF,kBAAN,MAAMA;IAITG,UAAU;QACR,MAAMC,KAAK;QACX,MAAMC,aAAaC,IAAAA,sBAAY,EAACL,QAAQM,OAAO,CAAC,OAAOC,MAAM,IAAIJ;QACjE,MAAMK,WAAWH,IAAAA,sBAAY,EAACL,QAAQS,KAAK,CAAC,OAAOF,MAAM,IAAIJ;QAE7D,OAAO;YACLC;YACAI;QACF;IAEF;IAGAE,cAAc;QACV,OAAO;YACH;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;SACD;IACP;IAEA,MAEMC,OAAO,AAAeC,IAAS,EAAE,AAAQC,eAAgC,EAAE,AAAWC,GAAQ,EAAE;QACpG,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAGL,QAAM,SAAQ;YACf,MAAM,IAAIK,MAAM;QAClB;QAEA,MAAMD,OAAO,MAAM,IAAI,CAACE,YAAY,CAACP,MAAM,CAACE,iBAAiBE,QAAQH;IACvE;IAEA,MAEMO,MAAM,AAAWL,GAAQ,EAAE;QAC/B,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMD,OAAO,MAAM,IAAI,CAACE,YAAY,CAACC,KAAK,CAACJ;QAE3C,OAAOC;IACT;IAEA,MAEMI,WAAW;QACf,OAAO,IAAI,CAACF,YAAY,CAACE,QAAQ;IACnC;IAEA,MAEMC,SAAS,AAAWP,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QACrD,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMD,OAAO,MAAM,IAAI,CAACE,YAAY,CAACG,QAAQ,CAACN,QAAQO;QAEtD,OAAON;IACT;IAEA,MAEMO,SAAS,AAAWT,GAAQ,EAAE;QAClC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,OAAO,IAAI,CAACC,YAAY,CAACM,iBAAiB,CAACT;IAC7C;IAEA,aAAa;IACb,MAGMU,cAAc,AAAWX,GAAQ,EAAE,AAAgBY,IAAyB,EAAE;QAClF,MAAMX,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAI,CAACS,MAAM;YACT,MAAM,IAAIT,MAAM;QAClB;QAEA,MAAMU,SAAS,MAAM,IAAI,CAACT,YAAY,CAACO,aAAa,CAACV,QAAQW;QAE7D,OAAOC;IACT;IAEA,mCAAmC;IACnC,MAEMC,2BAA2B,AAAWd,GAAQ,EAAE,AAASe,KAAU,EAAE;QACzE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMa,sBAAsB,MAAM,IAAI,CAACZ,YAAY,CAACU,0BAA0B,CAACC,OAAcd;QAE7F,OAAOe;IACT;IAGA,MAEMC,SAAS,AAAWjB,GAAQ,EAAE,AAASe,KAAU,EAAE;QACvD,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMe,QAAQ,MAAM,IAAI,CAACd,YAAY,CAACa,QAAQ,CAACF,OAAcd;QAE7D,OAAOiB;IACT;IAEA,MAEMC,eAAe,AAAWnB,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMiB,cAAc,MAAM,IAAI,CAAChB,YAAY,CAACe,cAAc,CAAClB;QAE3D,OAAOmB;IACT;IAEA,sBAAsB;IACtB,MAEMC,eAAe,AAAWrB,GAAQ,EAAE,AAAQsB,aAA4B,EAAE;QAC9E,MAAMrB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMe,QAAQ,MAAM,IAAI,CAACd,YAAY,CAACiB,cAAc,CAACpB,QAAQqB;QAE7D,OAAOJ;IACT;IAIAK,6BAA6B;QAC3B,OAAO,IAAI,CAACnB,YAAY,CAACmB,0BAA0B;IACrD;IAEA,4BAA4B;IAG5BC,iCAAiC,AAAWxB,GAAQ,EAAE,AAASe,KAAU,EAAE;QACzE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,OAAO,IAAI,CAACC,YAAY,CAACoB,gCAAgC,CAACvB,QAAQc;IACpE;IAIAU,4BAA4B,AAAQC,0BAAsD,EAAE,AAAW1B,GAAQ,EAAE;QAC/G,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,OAAO,IAAI,CAACC,YAAY,CAACqB,2BAA2B,CAACxB,QAAQyB;IAC/D;IAEA,MAEMC,qBAAqB,AAAQC,wBAAkD,EAAE,AAAW5B,GAAQ,EAAE;QAC1G,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACuB,oBAAoB,CAAC1B,QAAQ2B;QAEtE,OAAOC;IACT;IAEA,KAAK;IACL,MAEMC,uBAAuB,AAAQC,QAAa,EAAE,AAAW/B,GAAQ,EAAE;QACvE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC0B,sBAAsB,CAAC7B,QAAQ8B;QAExE,OAAOF;IACT;IAEA,MAEMG,2BAA2B,AAAWhC,GAAQ,EAAE;QACpD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM8B,YAAY,MAAM,IAAI,CAAC7B,YAAY,CAAC4B,0BAA0B,CAAC/B;QAErE,OAAOgC;IACT;IAEA,iEAAiE;IACjE,MAEMC,gCAAgC,AAAWlC,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC9E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMgC,UAAU;YACdC,MAAMrB,MAAMqB,IAAI,GAAGC,SAAStB,MAAMqB,IAAI,IAAI;YAC1CE,OAAOvB,MAAMuB,KAAK,GAAGD,SAAStB,MAAMuB,KAAK,IAAI;YAC7CC,QAAQxB,MAAMwB,MAAM,IAAI;YACxBC,WAAWzB,MAAMyB,SAAS;YAC1BC,SAAS1B,MAAM0B,OAAO;YACtBC,MAAM3B,MAAM2B,IAAI;QAClB;QAEAC,QAAQC,GAAG,CAAC,CAAC,6DAA6D,EAAE3C,QAAQ,EAAEkC;QACtF,MAAMtB,SAAS,MAAM,IAAI,CAACT,YAAY,CAACyC,0BAA0B,CAAC5C,QAAQkC;QAE1E,OAAO;YACLI,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,MAEMmC,uBAAuB,AAAaC,EAAU,EAAE,AAAWjD,GAAQ,EAAE;QACzE2C,QAAQC,GAAG,CAAC,4BAA4B5C,IAAIE,IAAI;QAChDyC,QAAQC,GAAG,CAAC,mCAAmC5C,IAAIE,IAAI,EAAED;QACzD0C,QAAQC,GAAG,CAAC,qCAAqCK;QAEjD,MAAMhD,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX0C,QAAQC,GAAG,CAAC;YACZ,MAAM,IAAIzC,MAAM;QAClB;QAEA,MAAM+C,aAAab,SAASY,IAAI;QAChC,IAAIE,MAAMD,aAAa;YACrBP,QAAQC,GAAG,CAAC,uCAAuCK;YACnD,MAAM,IAAI9C,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,iCAAiC,EAAEM,WAAW,cAAc,EAAEjD,QAAQ;QACnF,MAAM4B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC4C,sBAAsB,CAACE,YAAYjD;QAE5E,OAAO4B;IACT;IAEA,MAEMuB,sBAAsB,AAAaH,EAAU,EAAE,AAAQI,UAAoC,EAAE,AAAWrD,GAAQ,EAAE;QACtH,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM+C,aAAab,SAASY,IAAI;QAChC,IAAIE,MAAMD,aAAa;YACrB,MAAM,IAAI/C,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACgD,qBAAqB,CAACF,YAAYG,YAAYpD;QAEvF,OAAO4B;IACT;IAEA,MAEMyB,kBAAkB,AAAQC,qBAA4C,EAAE,AAAWvD,GAAQ,EAAE;QACjG,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACkD,iBAAiB,CAACrD,QAAQsD;QAEnE,OAAO1B;IACT;IAEA,KAAK;IACL,MAEM2B,oBAAoB,AAAQzB,QAAa,EAAE,AAAW/B,GAAQ,EAAE;QACpE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,MAAM4B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACqD,mBAAmB,CAACxD,QAAQ8B;QAErE,OAAOF;IACT;IAGA,8BAA8B;IAC9B,MAEM6B,uBAAuB,AAAW1D,GAAQ,EAAE;QAChD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAM8B,YAAY,MAAM,IAAI,CAAC7B,YAAY,CAACsD,sBAAsB,CAACzD;QACjE,OAAOgC;IACT;IAEA,yEAAyE;IACzE,MAEM0B,wBAAwB,AAAW3D,GAAQ,EAAE,AAASe,KAAU,EAAE;QACtE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAI;YACFwC,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAE3C,QAAQ;YAChF0C,QAAQC,GAAG,CAAC,CAAC,0BAA0B,CAAC,EAAE7B;YAE1C,MAAMoB,UAAU;gBACdC,MAAMrB,MAAMqB,IAAI,GAAGC,SAAStB,MAAMqB,IAAI,IAAI;gBAC1CE,OAAOvB,MAAMuB,KAAK,GAAGD,SAAStB,MAAMuB,KAAK,IAAI;gBAC7CC,QAAQxB,MAAMwB,MAAM,IAAI;gBACxBC,WAAWzB,MAAMyB,SAAS;gBAC1BC,SAAS1B,MAAM0B,OAAO;gBACtBC,MAAM3B,MAAM2B,IAAI;YAClB;YAEA,qBAAqB;YACrB,IAAIP,QAAQC,IAAI,GAAG,GAAGD,QAAQC,IAAI,GAAG;YACrC,IAAID,QAAQG,KAAK,GAAG,KAAKH,QAAQG,KAAK,GAAG,KAAKH,QAAQG,KAAK,GAAG;YAC9D,IAAI,CAAC;gBAAC;gBAAU;gBAAY;aAAM,CAACsB,QAAQ,CAACzB,QAAQI,MAAM,GAAG;gBAC3DJ,QAAQI,MAAM,GAAG;YACnB;YAEAI,QAAQC,GAAG,CAAC,CAAC,sBAAsB,CAAC,EAAET;YAEtC,MAAMtB,SAAS,MAAM,IAAI,CAACT,YAAY,CAACuD,uBAAuB,CAAC1D,QAAQkC;YAEvEQ,QAAQC,GAAG,CAAC,CAAC,oCAAoC,EAAE/B,OAAOoB,SAAS,EAAE4B,UAAU,EAAE,WAAW,CAAC;YAE7F,OAAO;gBACLtB,QAAQ;gBACRO,SAAS;gBACTC,MAAMlC;YACR;QAEF,EAAE,OAAOiD,OAAO;YACdnB,QAAQmB,KAAK,CAAC,CAAC,wDAAwD,EAAE7D,OAAO,CAAC,CAAC,EAAE6D;YACpFnB,QAAQmB,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE/C;YACnD,MAAM+C;QACR;IACF;IAEA,2EAA2E;IAC3E,MAEMC,oBAAoB,AAAad,EAAU,EAAE,AAAWjD,GAAQ,EAAE;QACtE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,8BAA8B,EAAEK,GAAG,SAAS,EAAE,OAAOA,GAAG,CAAC,CAAC;QAEvE,gCAAgC;QAChC,IAAI,CAACA,MAAMA,GAAGe,IAAI,OAAO,IAAI;YAC3BrB,QAAQmB,KAAK,CAAC;YACd,MAAM,IAAI3D,MAAM;QAClB;QAEA,+BAA+B;QAC/B,MAAM+C,aAAab,SAASY,GAAGe,IAAI,IAAI;QACvC,IAAIb,MAAMD,eAAeA,cAAc,GAAG;YACxCP,QAAQmB,KAAK,CAAC,CAAC,6BAA6B,EAAEb,GAAG,KAAK,EAAEC,YAAY;YACpE,MAAM,IAAI/C,MAAM,CAAC,sBAAsB,EAAE8C,GAAG,8BAA8B,CAAC;QAC7E;QAEAN,QAAQC,GAAG,CAAC,CAAC,+BAA+B,EAAEM,WAAW,cAAc,EAAEjD,QAAQ;QACjF,MAAM4B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC2D,mBAAmB,CAACb,YAAYjD;QAEzE,OAAO;YACLsC,QAAQ;YACRO,SAAS;YACTC,MAAMlB;QACR;IACF;IAEA,+BAA+B;IAG/BoC,wBAAwB,AAAWjE,GAAQ,EAAE,AAASe,KAAU,EAAE;QAChE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6D,uBAAuB,CAAChE,QAAQc;IAC3D;IAIAmD,8BAA8B,AAAWlE,GAAQ,EAAE;QACjD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+D,kCAAkC,CAAClE;IAC9D;IAEA,MAEMmE,mBAAmB,AAAanB,EAAU,EAAE,AAAWjD,GAAQ,EAAE;QACrE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAM+C,aAAab,SAASY,IAAI;QAChC,IAAIE,MAAMD,aAAa;YACrB,MAAM,IAAI/C,MAAM;QAClB;QACA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACgE,kBAAkB,CAAClB,YAAYjD;QACxE,OAAO4B;IACT;IAEA,MAEMwC,sBAAsB,AAAapB,EAAU,EAAE,AAAWjD,GAAQ,EAAE;QACxE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACiE,qBAAqB,CAACpB,IAAIhD;QACnE,OAAO4B;IACT;IAEA,yBAAyB;IAEzB,sBAAsB;IACtB,MAEMyC,kBAAkB,AAAWtE,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAC9D,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACkE,iBAAiB,CAACrE,QAAQO;IACrD;IAEA,wBAAwB;IACxB,MAEM+D,oBAAoB,AAAWvE,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAChE,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACmE,mBAAmB,CAACtE,QAAQO;IACvD;IAEA,yDAAyD;IACzD,MAEMgE,mBAAmB,AAAWxE,GAAQ,EAAE;QAC5C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,oDAAoD,EAAE3C,QAAQ;QAC3E,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACoE,kBAAkB,CAACvE;QAE1D,OAAO;YACLsC,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAIA,+BAA+B;IAC/B,MAEM4D,mBAAmB,AAAavB,UAAkB,EAAE,AAAWlD,GAAQ,EAAE;QAC7E,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,sDAAsD,EAAEM,WAAW,UAAU,EAAEjD,QAAQ;QACpG,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACqE,kBAAkB,CAACxE,QAAQoC,SAASa;QAE3E,OAAO;YACLX,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,8BAA8B;IAC9B,MAEM6D,sBAAsB,AAAaxB,UAAkB,EAAE,AAAQ1C,IAA4B,EAAE,AAAWR,GAAQ,EAAE;QACtH,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAEM,WAAW,UAAU,EAAEjD,QAAQ;QACvG,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACsE,qBAAqB,CAACzE,QAAQoC,SAASa,aAAa1C,KAAKgC,SAAS;QAEzG,OAAO;YACLD,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,gCAAgC;IAChC,MAEM8D,sBAAsB,AAAazB,UAAkB,EAAE,AAAWlD,GAAQ,EAAE;QAChF,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAEM,WAAW,UAAU,EAAEjD,QAAQ;QACvG,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACuE,qBAAqB,CAAC1E,QAAQoC,SAASa;QAE9E,OAAO;YACLX,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,+BAA+B;IAC/B,MAEM+D,yBAAyB,AAAa1B,UAAkB,EAAE,AAAQ1C,IAA4B,EAAE,AAAWR,GAAQ,EAAE;QACzH,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,4DAA4D,EAAEM,WAAW,UAAU,EAAEjD,QAAQ;QAC1G,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACwE,wBAAwB,CAAC3E,QAAQoC,SAASa,aAAa1C,KAAKgC,SAAS;QAE5G,OAAO;YACLD,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,gDAAgD;IAChD,MAEMgE,kBAAkB,AAAW7E,GAAQ,EAAE;QAC3C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B0C,QAAQC,GAAG,CAAC,CAAC,kDAAkD,EAAE3C,QAAQ;QAEzE,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACyE,iBAAiB,CAAC5E;QACzD,OAAO;YACLsC,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,yDAAyD;IACzD,MAEMiE,iBAAiB,AAAW9E,GAAQ,EAAE;QAC1C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B0C,QAAQC,GAAG,CAAC,CAAC,2DAA2D,EAAE3C,QAAQ;QAElF,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAAC0E,gBAAgB,CAAC7E;QACxD,OAAO;YACLsC,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,6EAA6E;IAE7E,qDAAqD;IACrD,MAEMkE,gBAAgB,AAAW/E,GAAQ,EAAE;QACzC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAwC,QAAQC,GAAG,CAAC,CAAC,+CAA+C,EAAE3C,QAAQ;QACtE,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAAC4E,oBAAoB,CAAC/E;QAE5D,OAAO;YACLsC,QAAQ;YACRO,SAAS;YACTC,MAAMlC;QACR;IACF;IAEA,gDAAgD;IAGhDoE,qBAAqB,AAAWjF,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC7D,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8E,mBAAmB,CAACjF,QAAQc;IACvD;IAEA,2CAA2C;IAG3CoE,6BAA6B,AAAWnF,GAAQ,EAAE;QAChD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+E,4BAA4B,CAAClF;IACxD;IAEA,MAEMmF,0BAA0B,AAAWpF,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QACtE,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgF,yBAAyB,CAACnF,QAAQO;IAC7D;IAEA,MAEM6E,yBAAyB,AAAWrF,GAAQ,EAAE,AAASe,KAAU,EAAE;QACvE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACiF,wBAAwB,CAACpF,QAAQc;IAC5D;IAEA,mDAAmD;IACnD,MAEMuE,uCAAuC,AAAWtF,GAAQ,EAAE;QAChE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACkF,sCAAsC,CAACrF;IAClE;IAEA,6BAA6B;IAC7B,MAGMsF,wBACJ,AAAWvF,GAAQ,EACnB,AAAQQ,IAAS,EACjB,AAAiBgF,KAA4B,EAC7C;QACA,MAAMvF,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAMsF,QAAQD,MAAME,IAAI,CAAC9E,CAAAA,OAAQA,KAAK+E,SAAS,KAAK;QACpD,MAAMC,OAAOJ,MAAME,IAAI,CAAC9E,CAAAA,OAAQA,KAAK+E,SAAS,KAAK;QACnD,MAAME,OAAOL,MAAME,IAAI,CAAC9E,CAAAA,OAAQA,KAAK+E,SAAS,KAAK;QAEnD,MAAMG,SAAS;YAAEL;YAAOG;YAAMC;QAAK;QAEnC,OAAO,IAAI,CAACzF,YAAY,CAACmF,uBAAuB,CAACtF,QAAQO,MAAMsF;IACjE;IAEA,qBAAqB;IACrB,MAEMC,mCAAmC,AAAW/F,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAC/E,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC4F,mCAAmC,CAAC/F,QAAQO;IACvE;IAEA,0BAA0B;IAC1B,MAEMyF,uBAAuB,AAAWjG,GAAQ,EAAE,AAASe,KAAU,EAAE;QACrE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6F,sBAAsB,CAAChG,QAAQc;IAC1D;IAEA,cAAc;IACd,MAEMmF,sBAAsB,AAAWlG,GAAQ,EAAE;QAC/C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8F,qBAAqB,CAACjG;IACjD;IAEA,oBAAoB;IACpB,MAEMkG,sBAAsB,AAAWnG,GAAQ,EAAE;QAC/C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+F,qBAAqB,CAAClG;IACjD;IAEA,gEAAgE;IAChE,MAEMmG,8BAA8B,AAAWpG,GAAQ,EAAE;QACvD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgG,6BAA6B,CAACnG;IACzD;IAEA,oBAAoB;IACpB,MAEMoG,qBAAqB,AAAWrG,GAAQ,EAAE,AAASe,KAAU,EAAE;QACnE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACiG,oBAAoB,CAACpG,QAAQc;IACxD;IAEA,YAAY;IACZ,MAEMuF,gBAAgB,AAAWtG,GAAQ,EAAE;QACzC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACkG,eAAe,CAACrG;IAC3C;IAEA,6BAA6B;IAC7B,MAEMsG,qBAAqB,AAAWvG,GAAQ,EAAE,AAASe,KAAU,EAAE;QACnE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACmG,oBAAoB,CAACtG,QAAQc;IACxD;IAEA,oBAAoB;IACpB,MAEMyF,oBAAoB,AAAWxG,GAAQ,EAAE,AAASe,KAAU,EAAE;QAClE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACoG,mBAAmB,CAACvG,QAAQc;IACvD;IAEA,sBAAsB;IACtB,MAEM0F,+BAA+B,AAAWzG,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC7E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACqG,8BAA8B,CAACxG,QAAQc;IAClE;IAEA,iBAAiB;IACjB,MAEM2F,0BAA0B,AAAW1G,GAAQ,EAAE,AAASe,KAAU,EAAE;QACxE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACsG,yBAAyB,CAACzG,QAAQc;IAC7D;IAEA,6CAA6C;IAC7C,MAEM4F,oBAAoB,AAAW3G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAClE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACuG,mBAAmB,CAAC1G,QAAQc;IACvD;IAEA,MAEM6F,4BAA4B,AAAW5G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACwG,2BAA2B,CAAC3G,QAAQc;IAC/D;IAGA,mBAAmB;IACnB,MAEM8F,4BAA4B,AAAW7G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACyG,2BAA2B,CAAC5G,QAAQc;IAC/D;IAEA,kEAAkE;IAClE,MAEM+F,YAAY,AAAW9G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1D,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC0G,WAAW,CAAC7G,QAAQc;IAC/C;IAEA,8BAA8B;IAC9B,MAEMgG,4BAA4B,AAAW/G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC2G,2BAA2B,CAAC9G,QAAQc;IAC/D;IAEA,6BAA6B;IAC7B,MAEMiG,2BAA2B,AAAWhH,GAAQ,EAAE,AAASe,KAAU,EAAE;QACzE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC4G,0BAA0B,CAAC/G,QAAQc;IAC9D;IAEA,oBAAoB;IACpB,MAEMkG,qBAAqB,AAAWjH,GAAQ,EAAE,AAASe,KAAU,EAAE;QACnE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6G,oBAAoB,CAAChH,QAAQc;IACxD;IAEA,4BAA4B;IAE5B,wBAAwB;IACxB,MAEMmG,aAAa,AAAWlH,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QACzD,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8G,YAAY,CAACjH,QAAQO;IAChD;IAEA,uBAAuB;IACvB,MAEM2G,kBAAkB,AAAWnH,GAAQ,EAAE,AAASe,KAAU,EAAE;QAChE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+G,iBAAiB,CAAClH,QAAQc;IACrD;IAEA,sBAAsB;IACtB,MAEMqG,kBAAkB,AAAWpH,GAAQ,EAAE,AAASe,KAAU,EAAE;QAChE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgH,iBAAiB,CAACnH,QAAQc;IACrD;IAEA,8BAA8B;IAC9B,MAEMsG,kBAAkB,AAAWrH,GAAQ,EAAE,AAAasH,SAAiB,EAAE;QAC3E,MAAMrH,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACiH,iBAAiB,CAACpH,QAAQqH;IACrD;IAEA,2BAA2B;IAC3B,MAEMC,gBAAgB,AAAWvH,GAAQ,EAAE,AAAawH,SAAiB,EAAE,AAAQhH,IAAS,EAAE;QAC5F,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACmH,eAAe,CAACtH,QAAQuH,WAAWhH;IAC9D;IAEA,yBAAyB;IACzB,MAEMiH,8BAA8B,AAAWzH,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC5E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACqH,6BAA6B,CAACxH,QAAQc;IACjE;IAGA,KAAK;IACL,MAEM2G,oBAAoB,AAAW1H,GAAQ,EAAE,AAASe,KAAU,EAAE,AAAQP,IAAS,EAAE;QACrF,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACsH,mBAAmB,CAACzH,QAAQc,OAAOP;IAC9D;IAEA,eAAe;IACf,MAEMmH,eAAe,AAAW3H,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACuH,cAAc,CAAC1H;IAC1C;IAEA,aAAa;IACb,MAEM2H,gBAAgB,AAAW5H,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAC5D,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACwH,eAAe,CAACpH,MAAMP;IACjD;IAEA,QAAQ;IACR,MAEM4H,YAAY,AAAW7H,GAAQ,EAAE;QACrC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACyH,WAAW,CAAC5H;IACvC;IAEA,oBAAoB;IACpB,MAEM6H,cAAc,AAAW9H,GAAQ,EAAE,AAAiB+H,MAAc,EAAE;QACxE,MAAM9H,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC0H,aAAa,CAAC7H,QAAQ+H,OAAOD;IACxD;IAEA,gBAAgB;IAChB,MAEME,mBAAmB,AAAWjI,GAAQ,EAAE;QAC5C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6H,kBAAkB,CAAChI;IAC9C;IAEA,MAEMiI,mBAAmB,AAAWlI,GAAQ,EAAE,AAAaiD,EAAU,EAAE;QACrE,MAAMhD,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8H,kBAAkB,CAACF,OAAO/E,KAAKhD;IAC1D;IAEA,MAEMkI,8BAA8B,AAAWnI,GAAQ,EAAE,AAAaiD,EAAU,EAAE;QAChF,MAAMhD,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+H,6BAA6B,CAACH,OAAO/E,KAAKhD;IACrE;IAEA,eAAe;IACf,MAEMmI,kBAAkB,AAAWpI,GAAQ,EAAE;QAC3C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgI,iBAAiB,CAACnI;IAC7C;IAEA,MAEMoI,wBAAwB,AAAWrI,GAAQ,EAAE,AAAaiD,EAAU,EAAE;QAC1E,MAAMhD,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACiI,uBAAuB,CAACL,OAAO/E,KAAKhD;IAC/D;IAhqCFqI,YAAY,AAAiBlI,YAA0B,CAAC;aAA3BA,eAAAA;IAA4B;AAiqC3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAj/B2E,yCAAA,OAAO,wCAAP,OAAO"}