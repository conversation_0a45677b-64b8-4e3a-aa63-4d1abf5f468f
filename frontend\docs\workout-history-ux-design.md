# Proposta UX/Design - Histórico de Treinos Refatorado

## Visão Geral

Este documento apresenta a proposta de UX/Design para o componente de histórico de treinos refatorado, focando na identificação clara de protocolos e melhor experiência do usuário.

## Objetivos

1. **Sempre visível**: O histórico deve estar sempre acessível, independente do status do protocolo
2. **Identificação clara**: Usuários devem facilmente distinguir treinos de diferentes protocolos
3. **Performance**: Implementar paginação para melhor performance
4. **Consistência visual**: Manter harmonia com o design system existente

## Indicadores Visuais

### 1. Badges de Protocolo

#### Protocolo Atual/Ativo
- **Cor**: <PERSON> (snapfit-green)
- **Ícone**: Dumbbell
- **Texto**: "Protocolo Atual" ou nome do protocolo
- **Uso**: Quando há protocolo ativo e o treino pertence a ele

#### Protocolo Finalizado
- **Cor**: <PERSON><PERSON><PERSON> (#3B82F6)
- **Ícone**: CheckCircle
- **Texto**: Nome do protocolo
- **Uso**: Treinos de protocolos que foram finalizados

#### Protocolo Arquivado
- **Cor**: Cinza (#6B7280)
- **Ícone**: Archive
- **Texto**: Nome do protocolo
- **Uso**: Treinos de protocolos arquivados

### 2. Comportamento Contextual

#### Quando HÁ protocolo ativo:
- Treinos do protocolo atual: **sem badge** (implícito que são do protocolo atual)
- Treinos de protocolos anteriores: **badge com nome do protocolo**
- Diferenciação visual clara entre atual vs. anterior

#### Quando NÃO HÁ protocolo ativo:
- **Todos os treinos mostram o nome do protocolo**
- Badge sempre visível para identificação
- Facilita a escolha de qual protocolo reutilizar

### 3. Tooltip Informativo

Ao passar o mouse sobre o badge, exibe:
- Nome completo do protocolo
- Objetivo do protocolo
- Divisão de treino (split)
- Frequência semanal
- Data de início e fim
- Status atual

## Estrutura de Componentes

### WorkoutProtocolBadge
- Componente reutilizável para badges
- Props configuráveis para tamanho e estilo
- Suporte a diferentes estados de protocolo

### WorkoutProtocolTooltip
- Wrapper para adicionar tooltip informativo
- Informações detalhadas do protocolo
- Design consistente com o tema dark

### EnhancedWorkoutHistory
- Componente principal refatorado
- Paginação com "Carregar mais"
- Integração com badges e tooltips
- Performance otimizada

## Paginação

### Estratégia "Load More"
- **Inicial**: 10 treinos carregados
- **Botão**: "Carregar mais treinos" no final da lista
- **Loading state**: Spinner + texto "Carregando..."
- **Feedback**: "Página X de Y • Z treinos no total"

### Benefícios
- Melhor performance inicial
- UX mais fluida que paginação tradicional
- Mantém contexto visual dos treinos já visualizados

## Estados da Interface

### Loading
- Spinner centralizado com texto explicativo
- Skeleton loading para carregamentos subsequentes

### Empty State
- Ícone de dumbbell
- Mensagem amigável: "Nenhum treino encontrado"
- Subtexto explicativo

### Error State
- Mensagem de erro clara
- Botão "Tentar novamente"
- Fallback gracioso

## Responsividade

### Mobile (< 768px)
- Badges em tamanho menor
- Grid de stats em 2 colunas
- Tooltip adaptado para touch

### Desktop (≥ 768px)
- Layout completo com todas as informações
- Grid de stats em 4 colunas
- Hover states otimizados

## Acessibilidade

- Contraste adequado para todos os badges
- Textos alternativos para ícones
- Navegação por teclado
- Screen reader friendly

## Implementação Técnica

### API Enhancements
- Adicionados campos de protocolo na resposta
- `protocol_status`, `is_current_protocol`
- Informações de data de início/fim

### Performance
- Paginação server-side
- Cache de dados com React Query
- Lazy loading de componentes

### Manutenibilidade
- Componentes modulares e reutilizáveis
- Props bem definidas e tipadas
- Documentação inline

## Métricas de Sucesso

1. **Clareza**: Usuários conseguem identificar facilmente de qual protocolo cada treino pertence
2. **Performance**: Tempo de carregamento inicial < 2s
3. **Usabilidade**: Redução de cliques para acessar informações de protocolo
4. **Satisfação**: Feedback positivo sobre a nova interface

## Próximos Passos

1. ✅ Implementar componentes base
2. ✅ Integrar com API existente
3. 🔄 Testes de usabilidade
4. 📋 Refinamentos baseados em feedback
5. 📋 Documentação para desenvolvedores

---

*Este documento será atualizado conforme feedback e iterações do design.*
