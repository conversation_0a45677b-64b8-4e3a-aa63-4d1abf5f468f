import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, StrategyOptions } from 'passport-google-oauth20';
import { config } from 'dotenv';

config();

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor() {
    // Check if Google OAuth is properly configured
    if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
      console.warn('Google OAuth not properly configured. Some environment variables are missing.');
      // Use dummy values to prevent strategy initialization errors
      super({
        clientID: 'dummy',
        clientSecret: 'dummy',
        callbackURL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3000/auth/google/callback',
        scope: ['email', 'profile'],
      } as StrategyOptions);
      return;
    }

    super({
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: process.env.GOOGLE_CALLBACK_URL,
      scope: ['email', 'profile'],
    } as StrategyOptions);
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
  ): Promise<any> {
    try {
      const { id, name, emails, photos } = profile;

      // Extract user data from Google profile
      const user = {
        googleId: id,
        email: emails[0]?.value,
        firstName: name?.givenName,
        lastName: name?.familyName,
        name: name?.givenName + ' ' + name?.familyName,
        picture: photos[0]?.value,
        accessToken,
        refreshToken,
      };

      // Validate that we have required data
      if (!user.email) {
        throw new Error('Email not provided by Google');
      }

      return user;
    } catch (error) {
      throw error;
    }
  }
}
