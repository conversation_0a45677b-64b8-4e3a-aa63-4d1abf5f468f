import React, { useState, useEffect } from 'react';
import { Calendar, CheckCircle2, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Refresh<PERSON><PERSON>, ChevronDown } from 'lucide-react';
import { formatDate } from '../utils/date';
import { apiService } from '../services/api';
import { WorkoutProtocolBadge, WorkoutProtocolTooltip } from './WorkoutProtocolBadge';

interface WorkoutHistoryItem {
  session_id: string;
  protocol_id: number;
  protocol_name: string;
  workout_name: string;
  date: string;
  duration: number;
  total_calories: number;
  total_weight: number;
  completed: boolean;
  protocol_status: 'active' | 'completed' | 'archived';
  is_current_protocol: boolean;
  objective?: string;
  split?: string;
  protocol_frequency?: number;
  protocol_started_at?: string;
  protocol_ended_at?: string;
}

interface EnhancedWorkoutHistoryProps {
  hasActiveProtocol?: boolean;
  onReuseProtocol?: (protocol: any) => void;
}

export function EnhancedWorkoutHistory({ 
  hasActiveProtocol = false,
  onReuseProtocol 
}: EnhancedWorkoutHistoryProps) {
  const [workouts, setWorkouts] = useState<WorkoutHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });
  const [hasMore, setHasMore] = useState(false);

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h${mins > 0 ? ` ${mins}min` : ''}`;
  };

  const fetchWorkouts = async (page: number = 1, append: boolean = false) => {
    try {
      if (page === 1) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      const response: any = await apiService.get('users/workouts/history', {
        searchParams: { 
          period: 'month',
          page: page.toString(),
          limit: pagination.limit.toString()
        }
      });

      const data = response.data;
      const newWorkouts = data?.workouts || [];
      
      if (append) {
        setWorkouts(prev => [...prev, ...newWorkouts]);
      } else {
        setWorkouts(newWorkouts);
      }

      if (data?.pagination) {
        setPagination(data.pagination);
        setHasMore(data.pagination.page < data.pagination.totalPages);
      }

    } catch (err: any) {
      console.error('Error fetching workout history:', err);
      setError('Erro ao carregar histórico de treinos');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      fetchWorkouts(pagination.page + 1, true);
    }
  };

  useEffect(() => {
    fetchWorkouts();
  }, []);

  if (loading && workouts.length === 0) {
    return (
      <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"></div>
          <span className="ml-2 text-gray-600">Carregando histórico...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
          <p className="text-red-600 text-sm">{error}</p>
          <button
            onClick={() => fetchWorkouts()}
            className="mt-2 text-red-700 underline text-xs"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  if (workouts.length === 0) {
    return (
      <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
        <div className="text-center py-8">
          <Dumbbell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">Nenhum treino encontrado</h3>
          <p className="text-gray-400 text-sm">
            Seus treinos realizados aparecerão aqui
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
            <Calendar className="w-5 h-5 text-snapfit-green" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">
              Histórico de Treinos
            </h2>
            <p className="text-sm text-gray-400">
              {pagination.total} treino{pagination.total !== 1 ? 's' : ''} encontrado{pagination.total !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
        
        <button
          onClick={() => fetchWorkouts()}
          disabled={loading}
          className="flex items-center gap-2 px-3 py-2 bg-snapfit-green/20 text-snapfit-green rounded-lg border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          <span className="text-sm">Atualizar</span>
        </button>
      </div>

      <div className="space-y-3">
        {workouts.map((workout) => (
          <div
            key={workout.session_id}
            className="p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/10"
          >
            {/* Header with workout name and protocol badge */}
            <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3 mb-3">
              <div className="flex items-start gap-3 flex-1">
                <div className={`p-2 rounded-full ${
                  workout.completed ? 'bg-snapfit-green/20 border border-snapfit-green/30' : 'bg-orange-400/10 border border-orange-400/30'
                }`}>
                  <Dumbbell className={`w-5 h-5 ${workout.completed ? 'text-snapfit-green' : 'text-orange-400'}`} />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-white text-sm sm:text-base">
                    {workout.workout_name || 'Treino'}
                  </h3>
                  <p className="text-gray-400 text-xs sm:text-sm">
                    {formatDate(workout.date)}
                  </p>
                  
                  {/* Protocol badge - always show when no active protocol, or when different from current */}
                  {(!hasActiveProtocol || !workout.is_current_protocol) && (
                    <div className="mt-2">
                      <WorkoutProtocolTooltip
                        protocolName={workout.protocol_name}
                        protocolStatus={workout.protocol_status}
                        protocolObjective={workout.objective}
                        protocolSplit={workout.split}
                        protocolFrequency={workout.protocol_frequency}
                        startedAt={workout.protocol_started_at}
                        endedAt={workout.protocol_ended_at}
                      >
                        <WorkoutProtocolBadge
                          protocolName={workout.protocol_name}
                          protocolStatus={workout.protocol_status}
                          isCurrentProtocol={workout.is_current_protocol}
                          showProtocolName={!hasActiveProtocol}
                          size="sm"
                        />
                      </WorkoutProtocolTooltip>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Status badge */}
              {workout.completed && (
                <span className="flex items-center gap-1 px-2 py-1 bg-snapfit-green/20 text-snapfit-green rounded-full text-xs border border-snapfit-green/30 whitespace-nowrap">
                  <CheckCircle2 className="w-3 h-3" />
                  <span>Concluído</span>
                </span>
              )}
            </div>

            {/* Stats grid */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 text-xs sm:text-sm">
              <div className="text-center p-3 bg-snapfit-gray rounded-lg">
                <div className="text-gray-400 mb-1">Volume</div>
                <div className="font-semibold text-white">{workout.total_weight || 0}kg</div>
              </div>
              <div className="text-center p-3 bg-snapfit-gray rounded-lg">
                <div className="text-gray-400 mb-1">Duração</div>
                <div className="font-semibold text-white">{formatDuration(workout.duration || 0)}</div>
              </div>
              <div className="text-center p-3 bg-snapfit-gray rounded-lg">
                <div className="text-gray-400 mb-1">Calorias</div>
                <div className="font-semibold text-white">{workout.total_calories || 0}</div>
              </div>
              <div className="text-center p-3 bg-snapfit-gray rounded-lg">
                <div className="text-gray-400 mb-1">Status</div>
                <div className={`font-semibold ${workout.completed ? 'text-snapfit-green' : 'text-orange-400'}`}>
                  {workout.completed ? 'Concluído' : 'Pendente'}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Load more button */}
      {hasMore && (
        <div className="mt-6 text-center">
          <button
            onClick={handleLoadMore}
            disabled={loadingMore}
            className="flex items-center gap-2 mx-auto px-6 py-3 bg-snapfit-green/20 text-snapfit-green rounded-lg border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors disabled:opacity-50"
          >
            {loadingMore ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-snapfit-green"></div>
                <span>Carregando...</span>
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4" />
                <span>Carregar mais treinos</span>
              </>
            )}
          </button>
        </div>
      )}

      {/* Pagination info */}
      {pagination.totalPages > 1 && (
        <div className="mt-4 text-center text-xs text-gray-400">
          Página {pagination.page} de {pagination.totalPages} • {pagination.total} treinos no total
        </div>
      )}
    </div>
  );
}
