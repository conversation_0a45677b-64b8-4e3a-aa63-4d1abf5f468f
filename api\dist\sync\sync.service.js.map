{"version": 3, "sources": ["../../src/sync/sync.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { db } from '../database';\nimport * as dayjs from 'dayjs';\n\n@Injectable()\nexport class SyncService {\n  \n  async getSyncStatus(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        lastSync: null,\n        syncStatus: 'idle',\n        pendingChanges: 0,\n        conflicts: 0\n      }\n    };\n  }\n\n  async performFullSync(userId: number, options?: any) {\n    return {\n      status: 'success',\n      data: {\n        syncId: 'sync_' + Date.now(),\n        status: 'completed',\n        itemsSynced: 0,\n        conflicts: 0\n      }\n    };\n  }\n\n  async performIncrementalSync(userId: number, options?: any) {\n    return {\n      status: 'success',\n      data: {\n        syncId: 'sync_' + Date.now(),\n        status: 'completed',\n        itemsSynced: 0,\n        conflicts: 0\n      }\n    };\n  }\n\n  async getSyncConflicts(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        conflicts: []\n      }\n    };\n  }\n\n  async resolveSyncConflicts(userId: number, resolutions: any) {\n    return {\n      status: 'success',\n      data: {\n        resolved: resolutions.length || 0,\n        remaining: 0\n      }\n    };\n  }\n\n  async createBackup(userId: number, options?: any) {\n    return {\n      status: 'success',\n      data: {\n        backupId: 'backup_' + Date.now(),\n        size: 0,\n        createdAt: new Date().toISOString()\n      }\n    };\n  }\n\n  async listBackups(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        backups: []\n      }\n    };\n  }\n\n  async restoreBackup(userId: number, restoreData: any) {\n    return {\n      status: 'success',\n      data: {\n        restored: true,\n        itemsRestored: 0\n      }\n    };\n  }\n\n  async exportUserData(userId: number, options?: any) {\n    return {\n      status: 'success',\n      data: {\n        exportId: 'export_' + Date.now(),\n        downloadUrl: null,\n        expiresAt: dayjs().add(24, 'hours').toISOString()\n      }\n    };\n  }\n\n  async importUserData(userId: number, importData: any) {\n    return {\n      status: 'success',\n      data: {\n        imported: true,\n        itemsImported: 0\n      }\n    };\n  }\n\n  async getSyncDevices(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        devices: []\n      }\n    };\n  }\n\n  async registerDevice(userId: number, deviceInfo: any) {\n    return {\n      status: 'success',\n      data: {\n        deviceId: 'device_' + Date.now(),\n        registered: true\n      }\n    };\n  }\n\n  async unregisterDevice(userId: number, deviceId: string) {\n    return {\n      status: 'success',\n      data: {\n        unregistered: true\n      }\n    };\n  }\n}\n"], "names": ["SyncService", "getSyncStatus", "userId", "status", "data", "lastSync", "syncStatus", "pendingChanges", "conflicts", "performFullSync", "options", "syncId", "Date", "now", "itemsSynced", "performIncrementalSync", "getSyncConflicts", "resolveSyncConflicts", "resolutions", "resolved", "length", "remaining", "createBackup", "backupId", "size", "createdAt", "toISOString", "listBackups", "backups", "restoreBackup", "restoreData", "restored", "itemsRestored", "exportUserData", "exportId", "downloadUrl", "expiresAt", "dayjs", "add", "importUserData", "importData", "imported", "itemsImported", "getSyncDevices", "devices", "registerDevice", "deviceInfo", "deviceId", "registered", "unregisterDevice", "unregistered"], "mappings": ";;;;+BAKaA;;;eAAAA;;;wBALc;+DAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGhB,IAAA,AAAMA,cAAN,MAAMA;IAEX,MAAMC,cAAcC,MAAc,EAAE;QAClC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJC,UAAU;gBACVC,YAAY;gBACZC,gBAAgB;gBAChBC,WAAW;YACb;QACF;IACF;IAEA,MAAMC,gBAAgBP,MAAc,EAAEQ,OAAa,EAAE;QACnD,OAAO;YACLP,QAAQ;YACRC,MAAM;gBACJO,QAAQ,UAAUC,KAAKC,GAAG;gBAC1BV,QAAQ;gBACRW,aAAa;gBACbN,WAAW;YACb;QACF;IACF;IAEA,MAAMO,uBAAuBb,MAAc,EAAEQ,OAAa,EAAE;QAC1D,OAAO;YACLP,QAAQ;YACRC,MAAM;gBACJO,QAAQ,UAAUC,KAAKC,GAAG;gBAC1BV,QAAQ;gBACRW,aAAa;gBACbN,WAAW;YACb;QACF;IACF;IAEA,MAAMQ,iBAAiBd,MAAc,EAAE;QACrC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJI,WAAW,EAAE;YACf;QACF;IACF;IAEA,MAAMS,qBAAqBf,MAAc,EAAEgB,WAAgB,EAAE;QAC3D,OAAO;YACLf,QAAQ;YACRC,MAAM;gBACJe,UAAUD,YAAYE,MAAM,IAAI;gBAChCC,WAAW;YACb;QACF;IACF;IAEA,MAAMC,aAAapB,MAAc,EAAEQ,OAAa,EAAE;QAChD,OAAO;YACLP,QAAQ;YACRC,MAAM;gBACJmB,UAAU,YAAYX,KAAKC,GAAG;gBAC9BW,MAAM;gBACNC,WAAW,IAAIb,OAAOc,WAAW;YACnC;QACF;IACF;IAEA,MAAMC,YAAYzB,MAAc,EAAE;QAChC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJwB,SAAS,EAAE;YACb;QACF;IACF;IAEA,MAAMC,cAAc3B,MAAc,EAAE4B,WAAgB,EAAE;QACpD,OAAO;YACL3B,QAAQ;YACRC,MAAM;gBACJ2B,UAAU;gBACVC,eAAe;YACjB;QACF;IACF;IAEA,MAAMC,eAAe/B,MAAc,EAAEQ,OAAa,EAAE;QAClD,OAAO;YACLP,QAAQ;YACRC,MAAM;gBACJ8B,UAAU,YAAYtB,KAAKC,GAAG;gBAC9BsB,aAAa;gBACbC,WAAWC,SAAQC,GAAG,CAAC,IAAI,SAASZ,WAAW;YACjD;QACF;IACF;IAEA,MAAMa,eAAerC,MAAc,EAAEsC,UAAe,EAAE;QACpD,OAAO;YACLrC,QAAQ;YACRC,MAAM;gBACJqC,UAAU;gBACVC,eAAe;YACjB;QACF;IACF;IAEA,MAAMC,eAAezC,MAAc,EAAE;QACnC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJwC,SAAS,EAAE;YACb;QACF;IACF;IAEA,MAAMC,eAAe3C,MAAc,EAAE4C,UAAe,EAAE;QACpD,OAAO;YACL3C,QAAQ;YACRC,MAAM;gBACJ2C,UAAU,YAAYnC,KAAKC,GAAG;gBAC9BmC,YAAY;YACd;QACF;IACF;IAEA,MAAMC,iBAAiB/C,MAAc,EAAE6C,QAAgB,EAAE;QACvD,OAAO;YACL5C,QAAQ;YACRC,MAAM;gBACJ8C,cAAc;YAChB;QACF;IACF;AACF"}