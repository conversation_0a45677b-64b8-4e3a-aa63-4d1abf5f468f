import { Injectable } from '@nestjs/common';
import { GetNutritionistClientsQueryDto } from '../admin/dto/get-nutritionist-clients-query.dto';
import { db } from '../database';
import * as dayjs from 'dayjs';
import { CreateNutritionistProtocolDto } from 'src/dto/create-nutritionist-protocol.dto';
import { CreateNutritionistClientProtocolDto } from '../dto/create-nutritionist-client-protocol.dto';
import { ImportNutritionistClientProtocolDto } from '../dto/import-nutritionist-client-protocol.dto';
import { CreateProtocolDietDto } from 'src/users/dto/create-protocol-diet.dto';

@Injectable()
export class NutritionistService {
    formatDatetime(datetime: any): string {
        return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
    }

    async getClients(query: GetNutritionistClientsQueryDto, userId: number) {
        const { q } = query;
        let { page = 1, limit = 100 } = query;
        const offset = (page - 1) * limit;
            
        let queryBuilder = db
            .selectFrom('users')
            .innerJoin('users_roles', 'users.id', 'users_roles.user_id')
            .innerJoin('clients', 'users.id', 'clients.client_id')
            .leftJoin('nutritionist_protocols', 'nutritionist_protocols.user_id', 'users.id')
            .select([
            'clients.client_id as id',
            'users.name',
            'users.email',
            'users.photo',
            'users.created_at',
            'nutritionist_protocols.name as protocol'
            // db.fn<string>('group_concat', ['roles.name']).as('roles'),
            ])
            .where('clients.role_id', '=', 3)
            .where('clients.user_id', '=', userId)
            .groupBy('users.id')
            .orderBy('users.created_at', 'desc');
    
        if (q) {
            queryBuilder = queryBuilder.where((eb) =>
            eb.or([
                eb('users.name', 'like', `%${q}%`),
                eb('users.email', 'like', `%${q}%`),
            ])
            );
        }
    
        const [data, total] = await Promise.all([
            queryBuilder.limit(limit).offset(offset).execute(),
            db.selectFrom('users')
            .innerJoin('users_roles', 'users.id', 'users_roles.user_id')
            .innerJoin('clients', 'users.id', 'clients.user_id')
            .leftJoin('nutritionist_protocols', 'nutritionist_protocols.user_id', 'users.id')
            .select(db.fn.countAll().as('total'))
            .groupBy('users.id')
            .where('clients.role_id', '=', 3)
            .where('clients.user_id', '=', userId)
            .executeTakeFirst(),
        ]);
    
        return {
            status: 'success',
            data: data.map((row) => ({
            id: row.id,
            name: row.name,
            email: row.email,
            photo: row.photo,
            protocol: row.protocol,
            plan: '',
            plan_status: '',
            attendance: 99,
            date: this.formatDatetime(row.created_at),            
            })),
            pagination: {
            page,
            limit,
            total: Number(total?.total),
            },
        };
    }

    async getClient(id: number, userId: number) {
        const client = await db
            .selectFrom('users')
            .innerJoin('clients as c', 'users.id', 'c.client_id')
            .select([
                'users.id',
                'users.name',
                'users.email',
                'users.photo',
                'users.height',
                'users.weight',
                'c.created_at as client_date',
            ])
            .where('users.id', '=', id)
            .where('c.user_id', '=', userId)
            .where('c.role_id', '=', 3)
            .groupBy('users.id')
            .executeTakeFirst();


            if (!client) {
                return {
                    status: 'error',
                    message: 'Client not found',
                };
            }

            const attendance = {
                week: 999,
                month: 999,
                sequence: 999,
                record: 999
            };

            const lastProtocol = await db
                .selectFrom('nutritionist_protocols as p')
                .where('p.user_id', '=', userId)
                .where('p.client_id', '=', id)
                .where('p.ended_at', 'is', null)
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select([
                    'p.id',
                    'p.name',
                    'p.goal_calories',
                    'p.goal_protein',
                    'p.goal_carbs',
                    'p.goal_fat',
                    'p.goal_water',
                    'p.started_at',
                    'p.objective'
                ])
                .orderBy('p.id', 'desc')
                .limit(1)
                .executeTakeFirst();

                let protocol: any = null;

                if (lastProtocol) {
                    protocol = {
                        id: lastProtocol.id,
                        name: lastProtocol.name,
                        nutritional_goals: {
                            calories: lastProtocol.goal_calories,
                            protein: lastProtocol.goal_protein,
                            carbs: lastProtocol.goal_carbs,
                            fat: lastProtocol.goal_fat,
                            water: lastProtocol.goal_water,
                        },
                        started_at: this.formatDatetime(lastProtocol.started_at),
                        adherence: 999,
                        objective: lastProtocol.objective
                    }
                }

                const clientData = {
                    id,
                    name: client.name,
                    email: client.email,
                    photo: client.photo,
                    height: client.height,
                    weight: client.weight,
                    client_date: this.formatDatetime(client.client_date),
                    // last_protocol_date: this.formatDatetime(client.last_protocol_date),
                    attendance,
                    protocol
                };
                
            return {
                status: 'success',
                data: clientData,
            }
        }

        async createProtocol(createNutritionistProtocolDto: CreateNutritionistProtocolDto, userId: number) {
            const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createNutritionistProtocolDto;

            const new_protocol = await db
                .insertInto('nutritionist_protocols_templates')
                .values({
                    name: name,
                    type_id: type_id,
                    objective: objective,
                    goal_calories: nutritional_goals.calories,
                    goal_protein: nutritional_goals.protein,
                    goal_carbs: nutritional_goals.carbs,
                    goal_fat: nutritional_goals.fat,
                    goal_water: nutritional_goals.water,
                    general_notes: general_notes,
                    user_id: userId
                })
                .executeTakeFirst();

            const new_protocol_id = Number(new_protocol.insertId);

            meals.forEach(async (meal: any) => {
                const new_meal = await db.insertInto('nutritionist_protocols_templates_meals')
                .values({
                    protocol_id: new_protocol_id,
                    name: meal.name,
                    meal_time: meal.meal_time,
                })
                .executeTakeFirst();

                const new_meal_id = Number(new_meal.insertId);

                meal?.foods?.forEach((food: any) => {
                    db.insertInto('nutritionist_protocols_templates_meals_foods')
                    .values({
                        meal_id: new_meal_id,
                        food_id: food.food_id,
                        name: food.name,
                        quantity: food.quantity,
                        unit: food.unit,
                        calories: food.calories,
                        protein: food.protein,
                        carbs: food.carbs,
                        fat: food.fat,
                        fiber: food.fiber,
                    })
                    .execute();
                });
            });

            if (supplements) {
                await db
                    .insertInto('nutritionist_protocols_templates_supplements')
                    .values(supplements.map((supplement) => ({
                        protocol_id: new_protocol_id,
                        name: supplement.name,
                        dosage: supplement.dosage,
                        supplement_time: supplement.supplement_time,
                        notes: supplement.notes,
                    })))
                    .execute();
            }

            return {
                status: 'success',
                data: [],
            };
        }

        async getProtocols(userId: number) {
            // Consulta principal para obter os protocolos com contagens de refeições e supplementos
            const protocols = await db
  .selectFrom('nutritionist_protocols_templates as npt')
  .leftJoin('select_options as so', 'so.id', 'npt.type_id')
  .leftJoin('nutritionist_protocols_templates_meals as nptm', 'nptm.protocol_id', 'npt.id')
  .leftJoin('nutritionist_protocols_templates_supplements as npts', 'npts.protocol_id', 'npt.id')
  .where('npt.user_id', '=', userId)
  .select([
    'npt.id',
    'npt.name',
    (eb) => eb.ref('so.value_option').as('type'),
    (eb) =>
      eb.fn
        .count(eb.ref('nptm.id'))
        .distinct()
        .as('meals_qty'), // Contagem de refeições únicas
    (eb) =>
      eb.fn
        .count(eb.ref('npts.id'))
        .distinct()
        .as('supplements_qty'), // Contagem de supplementos únicos
    'npt.created_at',
  ])
  .groupBy(['npt.id', 'npt.name', 'so.value_option', 'npt.created_at']) // Corrigido aqui
  .orderBy('npt.id', 'desc')
  .execute();
          
            const formattedProtocols = protocols.map((protocol) => ({
              id: protocol.id,
              name: protocol.name,
              type: protocol.type,
              meals_qty: Number(protocol.meals_qty),
              supplements_qty: Number(protocol.supplements_qty),
              created_at: protocol.created_at,
            }));


            return {
              status: 'success',
              data: formattedProtocols,
            };
    }

    async clientProtocols(id: number, userId: number) {
        const protocols = await db
        .selectFrom('nutritionist_protocols as np')
        .leftJoin('users as u', 'u.id', 'np.client_id')
        .where('u.id', '=', id)
        .where('np.user_id', '=', userId)
        .select([
          'np.id',
          'np.name',
          'np.objective',
          'np.goal_calories',
          'np.goal_protein',
          'np.goal_carbs',
          'np.goal_fat',
          'np.goal_water',
          'np.started_at',
          'np.ended_at',
        ])
        .orderBy('np.id', 'desc')
        .execute();

      // Mapeia os resultados para o formato desejado
      const formattedProtocols = protocols.map((protocol: any) => ({
        id: protocol.id,
        name: protocol.name,
        objective: protocol.objective,
        nutritional_goals: {
          calories: protocol.goal_calories,
          protein: protocol.goal_protein,
          carbs: protocol.goal_carbs,
          fat: protocol.goal_fat,
          water: protocol.goal_water,
        },
        started_at: protocol.started_at,
        ended_at: protocol.ended_at,
        adherence: 999
      }));

      return {
        status: 'success',
        data: formattedProtocols,
      };

      }

      async createClientProtocol(id: number, createNutritionistClientProtocolDto: CreateNutritionistClientProtocolDto, userId: number) {
        const client = await db
          .selectFrom('users')
          .select(['id', 'name', 'email', 'weight', 'height'])
          .where('id', '=', id)
          .executeTakeFirst();

        if (!client) {
          return {
            status: 'error',
            message: 'Client not found',
          };
        }

        const initial_weight = client.weight;

        if(!initial_weight) {
          return {
            status: 'error',
            message: 'Client weight not found',
          };
        }


        const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createNutritionistClientProtocolDto;

        const new_protocol = await db
          .insertInto('nutritionist_protocols')
          .values({
            client_id: id,
            user_id: userId,
            name: name,
            type_id: type_id,
            objective: objective,
            goal_calories: nutritional_goals.calories,
            goal_protein: nutritional_goals.protein,
            goal_carbs: nutritional_goals.carbs,
            goal_fat: nutritional_goals.fat,
            goal_water: nutritional_goals.water,
            general_notes,
            started_at: new Date(),
            initial_weight: initial_weight,
          })
          .executeTakeFirst();

        const new_protocol_id = Number(new_protocol.insertId);

        meals?.forEach(async (meal: any) => {
          const new_meal = await db.insertInto('nutritionist_protocols_meals')
            .values({
              protocol_id: new_protocol_id,
              name: meal.name,
              day_of_week: meal.day_of_week,
              meal_time: meal.meal_time,
            })
            .executeTakeFirst();

            const new_meal_id = new_meal.insertId;

            meal?.foods.forEach(async (food: any) => {
              db.insertInto('nutritionist_protocols_meals_foods')
              .values({
                  meal_id: Number(new_meal_id),
                  food_id: food.food_id,
                  name: food.name,
                  quantity: food.quantity,
                  unit: food.unit,
                  calories: food.calories,
                  protein: food.protein,
                  carbs: food.carbs,
                  fat: food.fat,
                  fiber: food.fiber
                })
              .execute();
            })
        });

        if (supplements) {
          await db
            .insertInto('nutritionist_protocols_supplements')
            .values(supplements.map((supplement) => ({
              protocol_id: new_protocol_id,
              name: supplement.name,
              dosage: supplement.dosage,
              supplement_time: supplement.supplement_time,
              notes: supplement.notes,
            })))
            .execute();
        }

        return {
          status: 'success',
          data: [],
        };
      }

      async importClientProtocol(id: number, importNutritionistClientProtocolDto: ImportNutritionistClientProtocolDto, userId: number) {
        const { protocol_id } = importNutritionistClientProtocolDto;

        const protocol_template = await db
          .selectFrom('nutritionist_protocols_templates')
          .where('id', '=', protocol_id)
          .where('user_id', '=', userId)
          .selectAll()
          .executeTakeFirst();

          if (!protocol_template) {
            return {
              status: 'error',
              message: 'Protocol not found',
            };
          }

          const client = await db
            .selectFrom('users')
            .select(['id', 'name', 'email', 'weight', 'height'])
            .where('id', '=', id)
            .executeTakeFirst();

          if (!client) {
            return {
              status: 'error',
              message: 'Client not found',
            };
          }

          const initial_weight = client.weight;

          if(!initial_weight) {
            return {
              status: 'error',
              message: 'Client weight not found',
            };
          }

          const new_protocol = await db
            .insertInto('nutritionist_protocols')
            .values({
              client_id: id,
              user_id: userId,
              name: protocol_template.name,
              type_id: protocol_template.type_id,
              goal_calories: protocol_template.goal_calories,
              goal_protein: protocol_template.goal_protein,
              goal_carbs: protocol_template.goal_carbs,
              goal_fat: protocol_template.goal_fat,
              goal_water: protocol_template.goal_water,
              initial_weight: initial_weight,
              objective: protocol_template.objective,
              ref_id: protocol_template.id ? Number(protocol_template.id) : null,
              started_at: new Date(),
            })
            .executeTakeFirst();

            const new_protocol_id = Number(new_protocol.insertId);

            const protocol_template_meals = await db
              .selectFrom('nutritionist_protocols_templates_meals')
              .where('protocol_id', '=', protocol_id)
              .select(['id', 'meal_time'])
              .execute();

              protocol_template_meals.forEach((meal: any) => {
                db.insertInto('nutritionist_protocols_meals')
                  .values({
                    protocol_id: new_protocol_id,
                    name: meal.name,
                    day_of_week: 'monday',
                    meal_time: meal.meal_time,
                  })
                  .execute();
              });

              const protocol_template_supplements = await db
                .selectFrom('nutritionist_protocols_templates_supplements')
                .where('protocol_id', '=', protocol_id)
                .select(['id', 'name', 'dosage', 'supplement_time', 'notes'])
                .execute();

                protocol_template_supplements.forEach((supplement) => {
                  db.insertInto('nutritionist_protocols_supplements')
                    .values({
                      protocol_id: new_protocol_id,
                      name: supplement.name,
                      dosage: supplement.dosage,
                      supplement_time: supplement.supplement_time,
                      notes: supplement.notes,
                    })
                    .execute();
                });

          return {
            status: 'success',
            data: [],
          };
      }

      async postProtocolsDiet(userId: number, createProtocolDietDto: CreateProtocolDietDto) {
        const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createProtocolDietDto;

        const new_protocol = await db
          .insertInto('nutritionist_protocols')
          .values({
            name: name,
            type_id: type_id,
            initial_weight: 100,
            objective: objective,
            goal_calories: nutritional_goals.calories,
            goal_protein: nutritional_goals.protein,
            goal_carbs: nutritional_goals.carbs,
            goal_fat: nutritional_goals.fat,
            goal_water: nutritional_goals.water,
            general_notes: general_notes,
            started_at: new Date(),
            client_id: userId,
            created_at: new Date(),
            updated_at: new Date(),
          })
          .executeTakeFirst();

        const new_protocol_id = Number(new_protocol.insertId);


        meals.forEach(async (meal: any) => {
          const new_meal = await db.insertInto('nutritionist_protocols_meals')
          .values({
            protocol_id: new_protocol_id,
            name: meal.name,
            day_of_week: meal.day_of_week,
            meal_time: meal.meal_time,
          })
          .executeTakeFirst();

          const new_meal_id = Number(new_meal.insertId);

          meal?.foods?.forEach((food: any) => {
            db.insertInto('nutritionist_protocols_templates_meals_foods')
            .values({
                meal_id: new_meal_id,
                food_id: food.food_id,
                name: food.name,
                quantity: food.quantity,
                unit: food.unit,
                calories: food.calories,
                protein: food.protein,
                carbs: food.carbs,
                fat: food.fat,
                fiber: food.fiber
              })
              .execute();
          });
        });

        if (supplements) {
          await db
            .insertInto('nutritionist_protocols_templates_supplements')
            .values(supplements.map((supplement) => ({
              protocol_id: new_protocol_id,
              name: supplement.name,
              dosage: supplement.dosage,
              supplement_time: supplement.supplement_time,
              notes: supplement.notes,
            })))
            .execute();
        }

        return {
          status: 'success',
          data: [],
        };
      }

        /*
        async getProtocols(userId: number) {
            // Consulta principal para obter os protocolos
            const protocols = await db
              .selectFrom('nutritionist_protocols_templates as npt')
              .leftJoin('select_options as so', 'so.id', 'npt.type_id')
              .leftJoin('nutritionist_protocols_templates_meals as nptm', 'nptm.protocol_id', 'npt.id')
              .leftJoin('nutritionist_protocols_templates_supplements as npts', 'npts.protocol_id', 'npt.id')
              .where('npt.user_id', '=', userId)
              .select([
                'npt.id',
                'npt.name',
                (eb) => eb.ref('so.value_option').as('type'),
                (eb) =>
                  eb.fn
                    .count(eb.ref('nptm.id'))
                    .distinct()
                    .as('meals_qty'), // Contagem de refeições únicas
                (eb) =>
                  eb.fn
                    .count(eb.ref('npts.id'))
                    .distinct()
                    .as('supplements_qty'), // Contagem de supplementos únicos
                'npt.created_at',
              ])
              .groupBy('npt.id')
              .orderBy('npt.id', 'desc')
              .execute();
          
            // Função para buscar refeições associadas a um protocolo
            const fetchMeals = async (protocolId: number) => {
              return await db
                .selectFrom('nutritionist_protocols_templates_meals as nptm')
                .where('nptm.protocol_id', '=', protocolId)
                .select(['nptm.id', 'nptm.food_id', 'nptm.meal_weight', 'nptm.meal_time'])
                .execute();
            };
          
            // Função para buscar supplementos associados a um protocolo
            const fetchSupplements = async (protocolId: number) => {
              return await db
                .selectFrom('nutritionist_protocols_templates_supplements as npts')
                .where('npts.protocol_id', '=', protocolId)
                .select(['npts.id', 'npts.name', 'npts.dosage', 'npts.supplement_time', 'npts.notes'])
                .execute();
            };
          
            // Processar os dados para incluir meals e supplements
            const formattedProtocols = await Promise.all(
              protocols.map(async (protocol) => {
                const meals = await fetchMeals(protocol.id);
                const supplements = await fetchSupplements(protocol.id);
          
                return {
                  id: protocol.id,
                  name: protocol.name,
                  type: protocol.type,
                  meals_qty: Number(protocol.meals_qty),
                  supplements_qty: Number(protocol.supplements_qty),
                  meals: meals || [],
                  supplements: supplements || [],
                  created_at: protocol.created_at,
                };
              })
            );
          
            return {
              status: 'success',
              data: formattedProtocols,
            };
          }
    */

    

}
