{"version": 3, "sources": ["../../src/foods/foods.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db } from '../database';\nimport axios from 'axios';\n\n@Injectable()\nexport class FoodsService {\n\n  async searchFoods(query: { q: string; limit?: number; source?: string }) {\n    try {\n      const { q, limit = 20, source } = query;\n      \n      if (!q || q.length < 2) {\n        return {\n          status: 'success',\n          data: []\n        };\n      }\n\n      // Search in local database first\n      const localResults = await this.searchLocalFoods(q, limit);\n      \n      // If source is specified, search only that source\n      if (source) {\n        switch (source) {\n          case 'nutritionix':\n            return this.searchNutritionix(q);\n          case 'edamam':\n            return this.searchEdamam(q);\n          case 'openfoodfacts':\n            return this.searchOpenFoodFacts(q);\n          default:\n            return localResults;\n        }\n      }\n\n      // Return local results for now\n      return localResults;\n    } catch (error) {\n      console.error('Error searching foods:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to search foods'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  private async searchLocalFoods(query: string, limit: number) {\n    try {\n      const sql = `\n        SELECT \n          id,\n          name,\n          brand,\n          category,\n          calories_per_100g as caloriesPer100g,\n          protein_per_100g as proteinPer100g,\n          carbs_per_100g as carbsPer100g,\n          fat_per_100g as fatPer100g,\n          fiber_per_100g as fiberPer100g,\n          sugar_per_100g as sugarPer100g,\n          sodium_per_100g as sodiumPer100g,\n          serving_size as servingSize,\n          serving_unit as servingUnit,\n          barcode,\n          image_url as imageUrl,\n          source\n        FROM foods \n        WHERE name LIKE ? OR brand LIKE ?\n        ORDER BY \n          CASE \n            WHEN name LIKE ? THEN 1\n            WHEN name LIKE ? THEN 2\n            ELSE 3\n          END,\n          name ASC\n        LIMIT ?\n      `;\n\n      const searchPattern = `%${query}%`;\n      const exactPattern = `${query}%`;\n      \n      const [results] = await db.execute(sql, [\n        searchPattern, searchPattern, exactPattern, searchPattern, limit\n      ]);\n\n      return {\n        status: 'success',\n        data: results\n      };\n    } catch (error) {\n      console.error('Error searching local foods:', error);\n      return {\n        status: 'success',\n        data: []\n      };\n    }\n  }\n\n  async searchNutritionix(query: string) {\n    try {\n      // This would integrate with Nutritionix API\n      // For now, return mock data\n      return {\n        status: 'success',\n        data: [\n          {\n            id: `nutritionix_${Date.now()}`,\n            name: `${query} (Nutritionix)`,\n            brand: 'Nutritionix',\n            caloriesPer100g: 200,\n            proteinPer100g: 10,\n            carbsPer100g: 30,\n            fatPer100g: 5,\n            source: 'nutritionix'\n          }\n        ]\n      };\n    } catch (error) {\n      console.error('Error searching Nutritionix:', error);\n      return {\n        status: 'success',\n        data: []\n      };\n    }\n  }\n\n  async searchEdamam(query: string) {\n    try {\n      // This would integrate with Edamam API\n      // For now, return mock data\n      return {\n        status: 'success',\n        data: [\n          {\n            id: `edamam_${Date.now()}`,\n            name: `${query} (Edamam)`,\n            brand: 'Edamam',\n            caloriesPer100g: 180,\n            proteinPer100g: 8,\n            carbsPer100g: 25,\n            fatPer100g: 7,\n            source: 'edamam'\n          }\n        ]\n      };\n    } catch (error) {\n      console.error('Error searching Edamam:', error);\n      return {\n        status: 'success',\n        data: []\n      };\n    }\n  }\n\n  async searchOpenFoodFacts(query: string) {\n    try {\n      // This would integrate with Open Food Facts API\n      // For now, return mock data\n      return {\n        status: 'success',\n        data: [\n          {\n            id: `openfoodfacts_${Date.now()}`,\n            name: `${query} (Open Food Facts)`,\n            brand: 'Open Food Facts',\n            caloriesPer100g: 220,\n            proteinPer100g: 12,\n            carbsPer100g: 35,\n            fatPer100g: 6,\n            source: 'openfoodfacts'\n          }\n        ]\n      };\n    } catch (error) {\n      console.error('Error searching Open Food Facts:', error);\n      return {\n        status: 'success',\n        data: []\n      };\n    }\n  }\n\n  async createCustomFood(foodData: any, userId: number) {\n    try {\n      const {\n        name,\n        brand,\n        category,\n        caloriesPer100g,\n        proteinPer100g,\n        carbsPer100g,\n        fatPer100g,\n        fiberPer100g,\n        sugarPer100g,\n        sodiumPer100g,\n        servingSize,\n        servingUnit,\n        barcode,\n        imageUrl\n      } = foodData;\n\n      const [result] = await db.execute(\n        `INSERT INTO foods (\n          name, brand, category, calories_per_100g, protein_per_100g,\n          carbs_per_100g, fat_per_100g, fiber_per_100g, sugar_per_100g,\n          sodium_per_100g, serving_size, serving_unit, barcode, image_url,\n          source, created_by, created_at, updated_at\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'custom', ?, NOW(), NOW())`,\n        [\n          name, brand, category, caloriesPer100g, proteinPer100g,\n          carbsPer100g, fatPer100g, fiberPer100g, sugarPer100g,\n          sodiumPer100g, servingSize, servingUnit, barcode, imageUrl, userId\n        ]\n      );\n\n      return {\n        status: 'success',\n        message: 'Custom food created successfully',\n        data: {\n          id: result.insertId,\n          ...foodData\n        }\n      };\n    } catch (error) {\n      console.error('Error creating custom food:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to create custom food'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getCustomFoods(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          id,\n          name,\n          brand,\n          category,\n          calories_per_100g as caloriesPer100g,\n          protein_per_100g as proteinPer100g,\n          carbs_per_100g as carbsPer100g,\n          fat_per_100g as fatPer100g,\n          fiber_per_100g as fiberPer100g,\n          sugar_per_100g as sugarPer100g,\n          sodium_per_100g as sodiumPer100g,\n          serving_size as servingSize,\n          serving_unit as servingUnit,\n          barcode,\n          image_url as imageUrl,\n          created_at as createdAt\n        FROM foods \n        WHERE created_by = ? AND source = 'custom'\n        ORDER BY created_at DESC\n      `;\n\n      const [results] = await db.execute(sql, [userId]);\n\n      return {\n        status: 'success',\n        data: results\n      };\n    } catch (error) {\n      console.error('Error getting custom foods:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get custom foods'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async importFoodDatabase(importData: any) {\n    try {\n      // This would handle bulk import of food data\n      // For now, return success\n      return {\n        status: 'success',\n        message: 'Food database import initiated',\n        data: {\n          imported: 0,\n          skipped: 0,\n          errors: 0\n        }\n      };\n    } catch (error) {\n      console.error('Error importing food database:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to import food database'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getFoodCategories() {\n    try {\n      const sql = `\n        SELECT DISTINCT category\n        FROM foods \n        WHERE category IS NOT NULL AND category != ''\n        ORDER BY category ASC\n      `;\n\n      const [results] = await db.execute(sql);\n\n      return {\n        status: 'success',\n        data: results.map(row => row.category)\n      };\n    } catch (error) {\n      console.error('Error getting food categories:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get food categories'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getPopularFoods(query: { limit?: number }) {\n    try {\n      const { limit = 20 } = query;\n\n      // This would be based on usage statistics\n      // For now, return some common foods\n      const sql = `\n        SELECT \n          id,\n          name,\n          brand,\n          category,\n          calories_per_100g as caloriesPer100g,\n          protein_per_100g as proteinPer100g,\n          carbs_per_100g as carbsPer100g,\n          fat_per_100g as fatPer100g,\n          image_url as imageUrl,\n          source\n        FROM foods \n        WHERE source != 'custom'\n        ORDER BY name ASC\n        LIMIT ?\n      `;\n\n      const [results] = await db.execute(sql, [limit]);\n\n      return {\n        status: 'success',\n        data: results\n      };\n    } catch (error) {\n      console.error('Error getting popular foods:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get popular foods'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getRecentFoods(userId: number, query: { limit?: number }) {\n    try {\n      const { limit = 10 } = query;\n\n      // This would get recently used foods by the user\n      // For now, return recent custom foods\n      const sql = `\n        SELECT DISTINCT\n          f.id,\n          f.name,\n          f.brand,\n          f.category,\n          f.calories_per_100g as caloriesPer100g,\n          f.protein_per_100g as proteinPer100g,\n          f.carbs_per_100g as carbsPer100g,\n          f.fat_per_100g as fatPer100g,\n          f.image_url as imageUrl,\n          f.source,\n          MAX(dl.created_at) as lastUsed\n        FROM foods f\n        LEFT JOIN diary_logs dl ON f.id = dl.food_id AND dl.user_id = ?\n        WHERE dl.food_id IS NOT NULL\n        ORDER BY lastUsed DESC\n        LIMIT ?\n      `;\n\n      const [results] = await db.execute(sql, [userId, limit]);\n\n      return {\n        status: 'success',\n        data: results\n      };\n    } catch (error) {\n      console.error('Error getting recent foods:', error);\n      // Return empty array instead of error for better UX\n      return {\n        status: 'success',\n        data: []\n      };\n    }\n  }\n}\n"], "names": ["FoodsService", "searchFoods", "query", "q", "limit", "source", "length", "status", "data", "localResults", "searchLocalFoods", "searchNutritionix", "searchEdamam", "searchOpenFoodFacts", "error", "console", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "sql", "searchPattern", "exactPattern", "results", "db", "execute", "id", "Date", "now", "name", "brand", "caloriesPer100g", "proteinPer100g", "carbsPer100g", "fatPer100g", "createCustomFood", "foodData", "userId", "category", "fiberPer100g", "sugarPer100g", "sodiumPer100g", "servingSize", "servingUnit", "barcode", "imageUrl", "result", "insertId", "getCustomFoods", "importFoodDatabase", "importData", "imported", "skipped", "errors", "getFoodCategories", "map", "row", "getPopularFoods", "getRecentFoods"], "mappings": ";;;;+BAKaA;;;eAAAA;;;wBALyC;0BACnC;;;;;;;AAIZ,IAAA,AAAMA,eAAN,MAAMA;IAEX,MAAMC,YAAYC,KAAqD,EAAE;QACvE,IAAI;YACF,MAAM,EAAEC,CAAC,EAAEC,QAAQ,EAAE,EAAEC,MAAM,EAAE,GAAGH;YAElC,IAAI,CAACC,KAAKA,EAAEG,MAAM,GAAG,GAAG;gBACtB,OAAO;oBACLC,QAAQ;oBACRC,MAAM,EAAE;gBACV;YACF;YAEA,iCAAiC;YACjC,MAAMC,eAAe,MAAM,IAAI,CAACC,gBAAgB,CAACP,GAAGC;YAEpD,kDAAkD;YAClD,IAAIC,QAAQ;gBACV,OAAQA;oBACN,KAAK;wBACH,OAAO,IAAI,CAACM,iBAAiB,CAACR;oBAChC,KAAK;wBACH,OAAO,IAAI,CAACS,YAAY,CAACT;oBAC3B,KAAK;wBACH,OAAO,IAAI,CAACU,mBAAmB,CAACV;oBAClC;wBACE,OAAOM;gBACX;YACF;YAEA,+BAA+B;YAC/B,OAAOA;QACT,EAAE,OAAOK,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAcT,iBAAiBR,KAAa,EAAEE,KAAa,EAAE;QAC3D,IAAI;YACF,MAAMgB,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA4Bb,CAAC;YAED,MAAMC,gBAAgB,CAAC,CAAC,EAAEnB,MAAM,CAAC,CAAC;YAClC,MAAMoB,eAAe,GAAGpB,MAAM,CAAC,CAAC;YAEhC,MAAM,CAACqB,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACL,KAAK;gBACtCC;gBAAeA;gBAAeC;gBAAcD;gBAAejB;aAC5D;YAED,OAAO;gBACLG,QAAQ;gBACRC,MAAMe;YACR;QACF,EAAE,OAAOT,OAAO;YACdC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;gBACLP,QAAQ;gBACRC,MAAM,EAAE;YACV;QACF;IACF;IAEA,MAAMG,kBAAkBT,KAAa,EAAE;QACrC,IAAI;YACF,4CAA4C;YAC5C,4BAA4B;YAC5B,OAAO;gBACLK,QAAQ;gBACRC,MAAM;oBACJ;wBACEkB,IAAI,CAAC,YAAY,EAAEC,KAAKC,GAAG,IAAI;wBAC/BC,MAAM,GAAG3B,MAAM,cAAc,CAAC;wBAC9B4B,OAAO;wBACPC,iBAAiB;wBACjBC,gBAAgB;wBAChBC,cAAc;wBACdC,YAAY;wBACZ7B,QAAQ;oBACV;iBACD;YACH;QACF,EAAE,OAAOS,OAAO;YACdC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;gBACLP,QAAQ;gBACRC,MAAM,EAAE;YACV;QACF;IACF;IAEA,MAAMI,aAAaV,KAAa,EAAE;QAChC,IAAI;YACF,uCAAuC;YACvC,4BAA4B;YAC5B,OAAO;gBACLK,QAAQ;gBACRC,MAAM;oBACJ;wBACEkB,IAAI,CAAC,OAAO,EAAEC,KAAKC,GAAG,IAAI;wBAC1BC,MAAM,GAAG3B,MAAM,SAAS,CAAC;wBACzB4B,OAAO;wBACPC,iBAAiB;wBACjBC,gBAAgB;wBAChBC,cAAc;wBACdC,YAAY;wBACZ7B,QAAQ;oBACV;iBACD;YACH;QACF,EAAE,OAAOS,OAAO;YACdC,QAAQD,KAAK,CAAC,2BAA2BA;YACzC,OAAO;gBACLP,QAAQ;gBACRC,MAAM,EAAE;YACV;QACF;IACF;IAEA,MAAMK,oBAAoBX,KAAa,EAAE;QACvC,IAAI;YACF,gDAAgD;YAChD,4BAA4B;YAC5B,OAAO;gBACLK,QAAQ;gBACRC,MAAM;oBACJ;wBACEkB,IAAI,CAAC,cAAc,EAAEC,KAAKC,GAAG,IAAI;wBACjCC,MAAM,GAAG3B,MAAM,kBAAkB,CAAC;wBAClC4B,OAAO;wBACPC,iBAAiB;wBACjBC,gBAAgB;wBAChBC,cAAc;wBACdC,YAAY;wBACZ7B,QAAQ;oBACV;iBACD;YACH;QACF,EAAE,OAAOS,OAAO;YACdC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,OAAO;gBACLP,QAAQ;gBACRC,MAAM,EAAE;YACV;QACF;IACF;IAEA,MAAM2B,iBAAiBC,QAAa,EAAEC,MAAc,EAAE;QACpD,IAAI;YACF,MAAM,EACJR,IAAI,EACJC,KAAK,EACLQ,QAAQ,EACRP,eAAe,EACfC,cAAc,EACdC,YAAY,EACZC,UAAU,EACVK,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,WAAW,EACXC,WAAW,EACXC,OAAO,EACPC,QAAQ,EACT,GAAGT;YAEJ,MAAM,CAACU,OAAO,GAAG,MAAMtB,YAAE,CAACC,OAAO,CAC/B,CAAC;;;;;sFAK6E,CAAC,EAC/E;gBACEI;gBAAMC;gBAAOQ;gBAAUP;gBAAiBC;gBACxCC;gBAAcC;gBAAYK;gBAAcC;gBACxCC;gBAAeC;gBAAaC;gBAAaC;gBAASC;gBAAUR;aAC7D;YAGH,OAAO;gBACL9B,QAAQ;gBACRU,SAAS;gBACTT,MAAM;oBACJkB,IAAIoB,OAAOC,QAAQ;oBACnB,GAAGX,QAAQ;gBACb;YACF;QACF,EAAE,OAAOtB,OAAO;YACdC,QAAQD,KAAK,CAAC,+BAA+BA;YAC7C,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM6B,eAAeX,MAAc,EAAE;QACnC,IAAI;YACF,MAAMjB,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;MAqBb,CAAC;YAED,MAAM,CAACG,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACL,KAAK;gBAACiB;aAAO;YAEhD,OAAO;gBACL9B,QAAQ;gBACRC,MAAMe;YACR;QACF,EAAE,OAAOT,OAAO;YACdC,QAAQD,KAAK,CAAC,+BAA+BA;YAC7C,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM8B,mBAAmBC,UAAe,EAAE;QACxC,IAAI;YACF,6CAA6C;YAC7C,0BAA0B;YAC1B,OAAO;gBACL3C,QAAQ;gBACRU,SAAS;gBACTT,MAAM;oBACJ2C,UAAU;oBACVC,SAAS;oBACTC,QAAQ;gBACV;YACF;QACF,EAAE,OAAOvC,OAAO;YACdC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMmC,oBAAoB;QACxB,IAAI;YACF,MAAMlC,MAAM,CAAC;;;;;MAKb,CAAC;YAED,MAAM,CAACG,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACL;YAEnC,OAAO;gBACLb,QAAQ;gBACRC,MAAMe,QAAQgC,GAAG,CAACC,CAAAA,MAAOA,IAAIlB,QAAQ;YACvC;QACF,EAAE,OAAOxB,OAAO;YACdC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMsC,gBAAgBvD,KAAyB,EAAE;QAC/C,IAAI;YACF,MAAM,EAAEE,QAAQ,EAAE,EAAE,GAAGF;YAEvB,0CAA0C;YAC1C,oCAAoC;YACpC,MAAMkB,MAAM,CAAC;;;;;;;;;;;;;;;;MAgBb,CAAC;YAED,MAAM,CAACG,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACL,KAAK;gBAAChB;aAAM;YAE/C,OAAO;gBACLG,QAAQ;gBACRC,MAAMe;YACR;QACF,EAAE,OAAOT,OAAO;YACdC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,MAAM,IAAIE,qBAAa,CAAC;gBACtBT,QAAQ;gBACRU,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMuC,eAAerB,MAAc,EAAEnC,KAAyB,EAAE;QAC9D,IAAI;YACF,MAAM,EAAEE,QAAQ,EAAE,EAAE,GAAGF;YAEvB,iDAAiD;YACjD,sCAAsC;YACtC,MAAMkB,MAAM,CAAC;;;;;;;;;;;;;;;;;;MAkBb,CAAC;YAED,MAAM,CAACG,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACL,KAAK;gBAACiB;gBAAQjC;aAAM;YAEvD,OAAO;gBACLG,QAAQ;gBACRC,MAAMe;YACR;QACF,EAAE,OAAOT,OAAO;YACdC,QAAQD,KAAK,CAAC,+BAA+BA;YAC7C,oDAAoD;YACpD,OAAO;gBACLP,QAAQ;gBACRC,MAAM,EAAE;YACV;QACF;IACF;AACF"}