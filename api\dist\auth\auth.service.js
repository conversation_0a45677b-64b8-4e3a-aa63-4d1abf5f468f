"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AuthService", {
    enumerable: true,
    get: function() {
        return AuthService;
    }
});
const _common = require("@nestjs/common");
const _bcryptjs = /*#__PURE__*/ _interop_require_wildcard(require("bcryptjs"));
const _jwt = require("@nestjs/jwt");
const _uuid = require("uuid");
const _database = require("../database");
const _dotenv = /*#__PURE__*/ _interop_require_wildcard(require("dotenv"));
const _dayjs = /*#__PURE__*/ _interop_require_default(require("dayjs"));
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
_dotenv.config();
let AuthService = class AuthService {
    formatDatetime(datetime) {
        return (0, _dayjs.default)(datetime).format('YYYY-MM-DD HH:mm:ss');
    }
    async login(user) {
        if (!user?.email || !user?.password) {
            throw new _common.HttpException({
                message: [
                    'Email e senha são obrigatórios'
                ],
                status: _common.HttpStatus.BAD_REQUEST
            }, _common.HttpStatus.BAD_REQUEST);
        }
        const checkUser = await _database.db.selectFrom('users').selectAll().where('email', '=', user.email).executeTakeFirst();
        if (!checkUser) {
            throw new _common.HttpException({
                message: [
                    'Usuário não encontrado'
                ],
                status: _common.HttpStatus.NOT_FOUND
            }, _common.HttpStatus.NOT_FOUND);
        }
        // check password
        const isPasswordValid = await _bcryptjs.compare(user.password, checkUser.password);
        if (!isPasswordValid) {
            throw new _common.HttpException({
                message: [
                    'Senha inválida'
                ],
                status: _common.HttpStatus.UNAUTHORIZED
            }, _common.HttpStatus.UNAUTHORIZED);
        }
        // Check if account is soft deleted
        if (checkUser.deleted_at) {
            const deletedDate = new Date(checkUser.deleted_at);
            const now = new Date();
            const daysDifference = Math.floor((now.getTime() - deletedDate.getTime()) / (1000 * 60 * 60 * 24));
            // If deleted more than 30 days ago, treat as permanently deleted
            if (daysDifference > 30) {
                throw new _common.HttpException({
                    message: [
                        'Esta conta foi permanentemente excluída.'
                    ],
                    status: _common.HttpStatus.FORBIDDEN
                }, _common.HttpStatus.FORBIDDEN);
            }
            // Account is within recovery period - return special response
            return {
                status: "account_recovery_required",
                data: {
                    message: `Sua conta foi marcada para exclusão há ${daysDifference} dias. Você tem ${30 - daysDifference} dias restantes para recuperá-la.`,
                    userId: checkUser.id,
                    daysRemaining: 30 - daysDifference,
                    deletedAt: checkUser.deleted_at
                }
            };
        }
        const payload = {
            userId: checkUser.id
        };
        const access_token = this.jwtService.sign(payload, {
            expiresIn: process.env.JWT_EXPIRATION_TIME
        });
        const refresh_token = (0, _uuid.v4)();
        const provider_id = 1;
        const device_uid = (0, _uuid.v4)();
        // save refresh token in database
        const userAuthData = {
            user_id: checkUser.id,
            provider_uid: user.email,
            provider_id: provider_id,
            device_uid,
            refresh_token,
            expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
        };
        await _database.db.insertInto('user_auths').values(userAuthData).executeTakeFirstOrThrow();
        return {
            status: "success",
            data: {
                access_token,
                refresh_token,
                device_uid
            }
        };
    }
    async recoverAccount(userId) {
        // Check if user exists and is deleted
        const checkUser = await _database.db.selectFrom('users').select([
            'id',
            'deleted_at',
            'email'
        ]).where('id', '=', userId).executeTakeFirst();
        if (!checkUser) {
            throw new _common.HttpException({
                message: [
                    'Usuário não encontrado'
                ],
                status: _common.HttpStatus.NOT_FOUND
            }, _common.HttpStatus.NOT_FOUND);
        }
        if (!checkUser.deleted_at) {
            throw new _common.HttpException({
                message: [
                    'Esta conta não está marcada para exclusão'
                ],
                status: _common.HttpStatus.BAD_REQUEST
            }, _common.HttpStatus.BAD_REQUEST);
        }
        // Check if still within recovery period
        const deletedDate = new Date(checkUser.deleted_at);
        const now = new Date();
        const daysDifference = Math.floor((now.getTime() - deletedDate.getTime()) / (1000 * 60 * 60 * 24));
        if (daysDifference > 30) {
            throw new _common.HttpException({
                message: [
                    'O período de recuperação de 30 dias expirou. Esta conta foi permanentemente excluída.'
                ],
                status: _common.HttpStatus.FORBIDDEN
            }, _common.HttpStatus.FORBIDDEN);
        }
        // Clear deleted_at to recover the account
        await _database.db.updateTable('users').set({
            deleted_at: null,
            updated_at: new Date()
        }).where('id', '=', userId).execute();
        // Generate new tokens for the recovered account
        const payload = {
            userId: checkUser.id
        };
        const access_token = this.jwtService.sign(payload, {
            expiresIn: process.env.JWT_EXPIRATION_TIME
        });
        const refresh_token = (0, _uuid.v4)();
        const provider_id = 1;
        const device_uid = (0, _uuid.v4)();
        // save refresh token in database
        const userAuthData = {
            user_id: checkUser.id,
            provider_uid: checkUser.email,
            provider_id: provider_id,
            device_uid,
            refresh_token,
            expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
        };
        await _database.db.insertInto('user_auths').values(userAuthData).executeTakeFirstOrThrow();
        return {
            status: "success",
            data: {
                access_token,
                refresh_token,
                device_uid,
                message: 'Conta recuperada com sucesso!'
            }
        };
    }
    // Método para registrar o usuário
    async register(createUserDto) {
        const { name, email, password, invite } = createUserDto;
        let aff_id = null;
        const existingUser = await _database.db.selectFrom('users').where('email', '=', email).selectAll().executeTakeFirst();
        if (existingUser) {
            throw new _common.HttpException({
                message: [
                    'O email já está cadastrado'
                ],
                status: _common.HttpStatus.CONFLICT
            }, _common.HttpStatus.CONFLICT);
        }
        const hashedPassword = await _bcryptjs.hash(password, 10);
        if (invite) {
            // get invite and user_id
            const inviteData = await _database.db.selectFrom('affiliate_links').where('invite', '=', invite.toLowerCase().trim()).select([
                'user_id'
            ]).executeTakeFirst();
            if (inviteData) {
                aff_id = inviteData.user_id;
            }
            if (!inviteData) {
                throw new _common.HttpException({
                    message: [
                        'Código promocional inválido'
                    ],
                    status: _common.HttpStatus.BAD_REQUEST
                }, _common.HttpStatus.BAD_REQUEST);
            }
        }
        const userData = {
            name: name.trim(),
            email: email.trim(),
            password: hashedPassword,
            invite: invite ? invite.toLowerCase().trim() : null,
            aff_id
        };
        const user = await _database.db.insertInto('users').values(userData).executeTakeFirstOrThrow();
        const userId = parseInt(user.insertId) || user.insertId;
        // Salvar o usuário no banco
        // const savedUser = await this.userRepository.save(user);
        const payload = {
            userId: userId
        };
        const access_token = this.jwtService.sign(payload, {
            expiresIn: process.env.JWT_EXPIRATION_TIME
        });
        const deviceUid = (0, _uuid.v4)();
        const refresh_token = (0, _uuid.v4)();
        const provider_id = 1; // Local
        // insert on user_auths
        /*
    const userAuth = this.userAuthRepository.create({
      user: savedUser,
      provider: { id: provider_id },
      providerUid: email,
      deviceUid,
      refreshToken: refresh_token,
      expireDate: new Date(Date.now() + 60 * 60 * 1000), // 1 hora
    });
    */ const userAuthData = {
            user_id: userId,
            provider_id,
            provider_uid: email,
            device_uid: deviceUid,
            refresh_token,
            expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
        };
        const userAuth = await _database.db.insertInto('user_auths').values(userAuthData).executeTakeFirstOrThrow();
        // const savedUserAuth = await this.userAuthRepository.save(userAuth);
        return {
            "status": "success",
            "data": {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "device_uid": deviceUid
            }
        };
    }
    // Refresh token
    async refreshToken(refresh_token, device_uid) {
        try {
            const userAuth = await _database.db.selectFrom('user_auths').select([
                'user_id',
                'provider_id',
                'provider_uid',
                'device_uid'
            ]).where('refresh_token', '=', refresh_token).where('device_uid', '=', device_uid).executeTakeFirst();
            if (!userAuth) {
                throw new _common.HttpException({
                    message: [
                        'Refresh token inválido'
                    ],
                    status: _common.HttpStatus.UNAUTHORIZED
                }, _common.HttpStatus.UNAUTHORIZED);
            }
            const userId = userAuth.user_id;
            await _database.db.deleteFrom('user_auths').where('user_id', '=', userId).where('device_uid', '=', device_uid).executeTakeFirstOrThrow();
            // generate new refresh token
            const new_refresh_token = (0, _uuid.v4)();
            // add new refresh token to db
            const userAuthData = {
                user_id: userId,
                provider_id: userAuth.provider_id,
                provider_uid: userAuth.provider_uid,
                device_uid,
                refresh_token: new_refresh_token,
                expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hora
            };
            await _database.db.insertInto('user_auths').values(userAuthData).executeTakeFirstOrThrow();
            // generate new access token
            const payload = {
                userId: userId
            };
            const new_access_token = this.jwtService.sign(payload, {
                expiresIn: process.env.JWT_EXPIRATION_TIME
            });
            return {
                "status": "success",
                "data": {
                    "access_token": new_access_token,
                    "refresh_token": new_refresh_token,
                    "device_uid": device_uid
                }
            };
        } catch (error) {
            throw new _common.HttpException({
                message: [
                    'Refresh token inválido'
                ],
                status: _common.HttpStatus.UNAUTHORIZED
            }, _common.HttpStatus.UNAUTHORIZED);
        }
    }
    async getRole(userId, role) {
        const rolesIds = {
            'admin': 1,
            'coach': 2,
            'nutritionist': 3,
            'user': 4
        };
        const userRole = await _database.db.selectFrom('users_roles').select([
            'role_id'
        ]).where('user_id', '=', userId).where('role_id', '=', rolesIds[role]).executeTakeFirst();
        if (!userRole) {
            if (role === 'admin') {
                return {
                    status: 'error',
                    role: null
                };
            }
            // add role
            const newRole = await _database.db.insertInto('users_roles').values({
                user_id: userId,
                role_id: rolesIds[role]
            }).execute();
        }
        return {
            status: 'success',
            role: role
        };
    }
    async requestPasswordReset(email) {
        try {
            const user = await _database.db.selectFrom('users').selectAll().where('email', '=', email).executeTakeFirst();
            if (!user) {
                // Don't reveal if email exists or not for security
                return {
                    status: 'success',
                    message: 'Se o email existir em nossa base, você receberá instruções para redefinir sua senha.'
                };
            }
            // Generate reset token
            const resetToken = (0, _uuid.v4)();
            const expiresAt = new Date(Date.now() + 3600000); // 1 hour
            // Store reset token
            await _database.db.insertInto('password_reset_tokens').values({
                user_id: user.id,
                token: resetToken,
                expires_at: expiresAt,
                created_at: new Date()
            }).execute();
            // Here you would send email with reset link
            // await this.emailService.sendPasswordResetEmail(email, resetToken);
            return {
                status: 'success',
                message: 'Se o email existir em nossa base, você receberá instruções para redefinir sua senha.',
                // For development only - remove in production
                resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined
            };
        } catch (error) {
            console.error('Error requesting password reset:', error);
            throw new _common.HttpException({
                message: [
                    'Erro interno do servidor'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async resetPassword(token, newPassword) {
        try {
            // Find valid reset token
            const resetToken = await _database.db.selectFrom('password_reset_tokens').selectAll().where('token', '=', token).where('expires_at', '>', new Date()).where('used_at', 'is', null).executeTakeFirst();
            if (!resetToken) {
                throw new _common.HttpException({
                    message: [
                        'Token inválido ou expirado'
                    ],
                    status: _common.HttpStatus.BAD_REQUEST
                }, _common.HttpStatus.BAD_REQUEST);
            }
            // Hash new password
            const hashedPassword = await _bcryptjs.hash(newPassword, 10);
            // Update user password
            await _database.db.updateTable('users').set({
                password: hashedPassword,
                updated_at: new Date()
            }).where('id', '=', resetToken.user_id).execute();
            // Mark token as used
            await _database.db.updateTable('password_reset_tokens').set({
                used_at: new Date()
            }).where('id', '=', resetToken.id).execute();
            // Invalidate all refresh tokens for security
            await _database.db.deleteFrom('refresh_tokens').where('user_id', '=', resetToken.user_id).execute();
            return {
                status: 'success',
                message: 'Senha redefinida com sucesso'
            };
        } catch (error) {
            console.error('Error resetting password:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                message: [
                    'Erro interno do servidor'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async verifyEmail(token) {
        try {
            // Find user by verification token
            const user = await _database.db.selectFrom('users').selectAll().where('email_verification_token', '=', token).where('email_verified_at', 'is', null).executeTakeFirst();
            if (!user) {
                throw new _common.HttpException({
                    message: [
                        'Token de verificação inválido'
                    ],
                    status: _common.HttpStatus.BAD_REQUEST
                }, _common.HttpStatus.BAD_REQUEST);
            }
            // Mark email as verified
            await _database.db.updateTable('users').set({
                email_verified_at: new Date(),
                email_verification_token: null,
                updated_at: new Date()
            }).where('id', '=', user.id).execute();
            return {
                status: 'success',
                message: 'Email verificado com sucesso'
            };
        } catch (error) {
            console.error('Error verifying email:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                message: [
                    'Erro interno do servidor'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async resendVerification(email) {
        try {
            const user = await _database.db.selectFrom('users').selectAll().where('email', '=', email).where('email_verified_at', 'is', null).executeTakeFirst();
            if (!user) {
                return {
                    status: 'success',
                    message: 'Se o email existir e não estiver verificado, um novo link será enviado.'
                };
            }
            // Generate new verification token
            const verificationToken = (0, _uuid.v4)();
            await _database.db.updateTable('users').set({
                email_verification_token: verificationToken,
                updated_at: new Date()
            }).where('id', '=', user.id).execute();
            // Here you would send verification email
            // await this.emailService.sendVerificationEmail(email, verificationToken);
            return {
                status: 'success',
                message: 'Se o email existir e não estiver verificado, um novo link será enviado.',
                // For development only - remove in production
                verificationToken: process.env.NODE_ENV === 'development' ? verificationToken : undefined
            };
        } catch (error) {
            console.error('Error resending verification:', error);
            throw new _common.HttpException({
                message: [
                    'Erro interno do servidor'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserSessions(userId) {
        try {
            const sessions = await _database.db.selectFrom('refresh_tokens').select([
                'id',
                'device_uid',
                'created_at',
                'last_used_at',
                'expires_at'
            ]).where('user_id', '=', userId).where('expires_at', '>', new Date()).orderBy('last_used_at', 'desc').execute();
            return {
                status: 'success',
                data: sessions.map((session)=>({
                        id: session.id,
                        deviceId: session.device_uid,
                        createdAt: session.created_at,
                        lastUsedAt: session.last_used_at,
                        expiresAt: session.expires_at
                    }))
            };
        } catch (error) {
            console.error('Error getting user sessions:', error);
            throw new _common.HttpException({
                message: [
                    'Erro interno do servidor'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async terminateSession(sessionId, userId) {
        try {
            const result = await _database.db.deleteFrom('refresh_tokens').where('id', '=', Number(sessionId)).where('user_id', '=', userId).execute();
            if (result.length === 0) {
                throw new _common.HttpException({
                    message: [
                        'Sessão não encontrada'
                    ],
                    status: _common.HttpStatus.NOT_FOUND
                }, _common.HttpStatus.NOT_FOUND);
            }
            return {
                status: 'success',
                message: 'Sessão encerrada com sucesso'
            };
        } catch (error) {
            console.error('Error terminating session:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                message: [
                    'Erro interno do servidor'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async logoutAllSessions(userId) {
        try {
            await _database.db.deleteFrom('refresh_tokens').where('user_id', '=', userId).execute();
            return {
                status: 'success',
                message: 'Todas as sessões foram encerradas'
            };
        } catch (error) {
            console.error('Error logging out all sessions:', error);
            throw new _common.HttpException({
                message: [
                    'Erro interno do servidor'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // OAuth Methods
    async validateOAuthUser(oauthUser, provider) {
        try {
            const { email, name, googleId, appleId } = oauthUser;
            const providerId = provider === 'google' ? 2 : 3;
            const providerUid = provider === 'google' ? googleId : appleId;
            if (!email) {
                throw new _common.HttpException({
                    message: [
                        'Email não fornecido pelo provedor OAuth'
                    ],
                    status: _common.HttpStatus.BAD_REQUEST
                }, _common.HttpStatus.BAD_REQUEST);
            }
            // Check if user exists by email
            let user = await _database.db.selectFrom('users').selectAll().where('email', '=', email).executeTakeFirst();
            if (!user) {
                // Create new user for OAuth
                const userData = {
                    name: name || email.split('@')[0],
                    email: email,
                    password: null,
                    photo: oauthUser.picture || null
                };
                const newUser = await _database.db.insertInto('users').values(userData).executeTakeFirstOrThrow();
                const userId = parseInt(newUser.insertId) || newUser.insertId;
                user = {
                    id: userId,
                    ...userData
                };
            }
            // Check if OAuth provider is already linked
            const existingAuth = await _database.db.selectFrom('user_auths').selectAll().where('user_id', '=', user.id).where('provider_id', '=', providerId).executeTakeFirst();
            if (!existingAuth) {
                // Link OAuth provider to user
                const deviceUid = (0, _uuid.v4)();
                const refreshToken = (0, _uuid.v4)();
                const userAuthData = {
                    user_id: user.id,
                    provider_id: providerId,
                    provider_uid: providerUid,
                    device_uid: deviceUid,
                    refresh_token: refreshToken,
                    expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
                };
                await _database.db.insertInto('user_auths').values(userAuthData).executeTakeFirstOrThrow();
            }
            return user;
        } catch (error) {
            console.error('Error validating OAuth user:', error);
            throw new _common.HttpException({
                message: [
                    'Erro ao validar usuário OAuth'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async loginWithOAuth(user, provider) {
        try {
            const validatedUser = await this.validateOAuthUser(user, provider);
            const providerId = provider === 'google' ? 2 : 3;
            // Generate JWT token
            const payload = {
                userId: validatedUser.id
            };
            const access_token = this.jwtService.sign(payload, {
                expiresIn: process.env.JWT_EXPIRATION_TIME
            });
            const refresh_token = (0, _uuid.v4)();
            const device_uid = (0, _uuid.v4)();
            // Update or create user_auths entry
            await _database.db.deleteFrom('user_auths').where('user_id', '=', validatedUser.id).where('provider_id', '=', providerId).execute();
            const userAuthData = {
                user_id: validatedUser.id,
                provider_id: providerId,
                provider_uid: provider === 'google' ? user.googleId : user.appleId,
                device_uid,
                refresh_token,
                expire_date: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
            };
            await _database.db.insertInto('user_auths').values(userAuthData).executeTakeFirstOrThrow();
            return {
                status: "success",
                data: {
                    access_token,
                    refresh_token,
                    device_uid
                }
            };
        } catch (error) {
            console.error('Error in OAuth login:', error);
            throw new _common.HttpException({
                message: [
                    'Erro no login OAuth'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // Frontend-initiated OAuth token validation methods
    async validateGoogleToken(token, userInfo) {
        try {
            // In a real implementation, you would validate the token with Google's API
            // For now, we'll trust the frontend-provided user info
            // TODO: Add actual Google token validation
            if (!userInfo.email) {
                throw new _common.HttpException({
                    message: [
                        'Email não fornecido pelo Google'
                    ],
                    status: _common.HttpStatus.BAD_REQUEST
                }, _common.HttpStatus.BAD_REQUEST);
            }
            const oauthUser = {
                googleId: userInfo.sub || userInfo.id,
                email: userInfo.email,
                name: userInfo.name,
                picture: userInfo.picture,
                firstName: userInfo.given_name,
                lastName: userInfo.family_name
            };
            return await this.loginWithOAuth(oauthUser, 'google');
        } catch (error) {
            console.error('Error validating Google token:', error);
            throw new _common.HttpException({
                message: [
                    'Erro ao validar token do Google'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async validateAppleToken(token, userInfo) {
        try {
            // In a real implementation, you would validate the token with Apple's API
            // For now, we'll trust the frontend-provided user info
            // TODO: Add actual Apple token validation
            if (!userInfo.email) {
                throw new _common.HttpException({
                    message: [
                        'Email não fornecido pela Apple'
                    ],
                    status: _common.HttpStatus.BAD_REQUEST
                }, _common.HttpStatus.BAD_REQUEST);
            }
            const oauthUser = {
                appleId: userInfo.sub || userInfo.id,
                email: userInfo.email,
                name: userInfo.name || `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim(),
                firstName: userInfo.firstName,
                lastName: userInfo.lastName
            };
            return await this.loginWithOAuth(oauthUser, 'apple');
        } catch (error) {
            console.error('Error validating Apple token:', error);
            throw new _common.HttpException({
                message: [
                    'Erro ao validar token da Apple'
                ],
                status: _common.HttpStatus.INTERNAL_SERVER_ERROR
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    constructor(jwtService){
        this.jwtService = jwtService;
    }
};
AuthService = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _jwt.JwtService === "undefined" ? Object : _jwt.JwtService
    ])
], AuthService);

//# sourceMappingURL=auth.service.js.map