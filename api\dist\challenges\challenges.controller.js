"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ChallengesController", {
    enumerable: true,
    get: function() {
        return ChallengesController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _challengesservice = require("./challenges.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let ChallengesController = class ChallengesController {
    async getChallenges(req, query) {
        const userId = req.user.userId;
        return this.challengesService.getChallenges(userId, query);
    }
    async getMyChallenges(req) {
        const userId = req.user.userId;
        return this.challengesService.getMyChallenges(userId);
    }
    async getChallenge(id, req) {
        const userId = req.user.userId;
        return this.challengesService.getChallenge(Number(id), userId);
    }
    async createChallenge(challengeData, req) {
        const userId = req.user.userId;
        return this.challengesService.createChallenge(challengeData, userId);
    }
    async joinChallenge(id, req) {
        const userId = req.user.userId;
        return this.challengesService.joinChallenge(Number(id), userId);
    }
    async leaveChallenge(id, req) {
        const userId = req.user.userId;
        return this.challengesService.leaveChallenge(Number(id), userId);
    }
    async getChallengeLeaderboard(id, req) {
        const userId = req.user.userId;
        return this.challengesService.getChallengeLeaderboard(Number(id), userId);
    }
    async updateChallengeProgress(id, progressData, req) {
        const userId = req.user.userId;
        return this.challengesService.updateChallengeProgress(Number(id), userId, progressData);
    }
    constructor(challengesService){
        this.challengesService = challengesService;
    }
};
_ts_decorate([
    (0, _common.Get)(),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "getChallenges", null);
_ts_decorate([
    (0, _common.Get)('my'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "getMyChallenges", null);
_ts_decorate([
    (0, _common.Get)(':id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "getChallenge", null);
_ts_decorate([
    (0, _common.Post)(),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "createChallenge", null);
_ts_decorate([
    (0, _common.Post)(':id/join'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "joinChallenge", null);
_ts_decorate([
    (0, _common.Post)(':id/leave'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "leaveChallenge", null);
_ts_decorate([
    (0, _common.Get)(':id/leaderboard'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "getChallengeLeaderboard", null);
_ts_decorate([
    (0, _common.Post)(':id/progress'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ChallengesController.prototype, "updateChallengeProgress", null);
ChallengesController = _ts_decorate([
    (0, _common.Controller)('challenges'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _challengesservice.ChallengesService === "undefined" ? Object : _challengesservice.ChallengesService
    ])
], ChallengesController);

//# sourceMappingURL=challenges.controller.js.map