{"version": 3, "sources": ["../../src/friends/friends.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db } from '../database';\n\n@Injectable()\nexport class FriendsService {\n\n  async searchUsers(query: string, currentUserId: number) {\n    try {\n      if (!query || query.length < 2) {\n        return {\n          status: 'success',\n          data: []\n        };\n      }\n\n      // Remove @ if present at the beginning\n      const searchQuery = query.startsWith('@') ? query.slice(1) : query;\n\n      const sql = `\n        SELECT \n          u.id,\n          u.name,\n          u.username,\n          u.photo,\n          COALESCE(up.total_points, 0) as points,\n          CASE \n            WHEN f.id IS NOT NULL THEN 'friends'\n            WHEN fr_sent.id IS NOT NULL THEN 'pending_sent'\n            WHEN fr_received.id IS NOT NULL THEN 'pending_received'\n            ELSE 'none'\n          END as friendshipStatus\n        FROM users u\n        LEFT JOIN user_points up ON u.id = up.user_id\n        LEFT JOIN users_friends f ON (\n          (f.user_id = ? AND f.friend_id = u.id) OR\n          (f.friend_id = ? AND f.user_id = u.id)\n        ) AND f.status = 1 AND f.deleted_at IS NULL\n        LEFT JOIN friend_requests fr_sent ON fr_sent.from_user_id = ? AND fr_sent.to_user_id = u.id AND fr_sent.status = 'pending'\n        LEFT JOIN friend_requests fr_received ON fr_received.from_user_id = u.id AND fr_received.to_user_id = ? AND fr_received.status = 'pending'\n        WHERE u.id != ? \n        AND (u.name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)\n        AND u.deleted_at IS NULL\n        ORDER BY u.name ASC\n        LIMIT 20\n      `;\n\n      const searchPattern = `%${searchQuery}%`;\n      const [results] = await db.execute(sql, [\n        currentUserId, currentUserId, currentUserId, currentUserId, currentUserId,\n        searchPattern, searchPattern, searchPattern\n      ]);\n\n      return {\n        status: 'success',\n        data: results\n      };\n    } catch (error) {\n      console.error('Error searching users:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to search users'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getFriends(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          u.id,\n          u.name,\n          u.username,\n          u.photo,\n          COALESCE(up.total_points, 0) as points,\n          COALESCE(up.monthly_points, 0) as monthlyPoints,\n          COALESCE(up.yearly_points, 0) as yearlyPoints,\n          u.last_active as lastActive,\n          f.created_at as friendshipDate\n        FROM users_friends f\n        INNER JOIN users u ON (\n          CASE\n            WHEN f.user_id = ? THEN u.id = f.friend_id\n            ELSE u.id = f.user_id\n          END\n        )\n        LEFT JOIN user_points up ON u.id = up.user_id\n        WHERE (f.user_id = ? OR f.friend_id = ?)\n        AND f.status = 1 AND f.deleted_at IS NULL\n        ORDER BY up.total_points DESC, u.name ASC\n      `;\n\n      const [results] = await db.execute(sql, [userId, userId, userId]);\n\n      // Check if results is valid\n      if (!results || !Array.isArray(results)) {\n        console.log('No results or invalid results format for getFriends:', results);\n        return {\n          status: 'success',\n          data: []\n        };\n      }\n\n      // Add ranking\n      const friendsWithRank = results.map((friend, index) => ({\n        ...friend,\n        rank: index + 1\n      }));\n\n      return {\n        status: 'success',\n        data: friendsWithRank\n      };\n    } catch (error) {\n      console.error('Error getting friends:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get friends'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getFriendRequests(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          fr.id,\n          fr.from_user_id as fromUserId,\n          fr.to_user_id as toUserId,\n          fr.status,\n          fr.created_at as createdAt,\n          fr.updated_at as updatedAt,\n          u_from.id as fromUserId,\n          u_from.name as fromUserName,\n          u_from.username as fromUserUsername,\n          u_from.photo as fromUserPhoto,\n          COALESCE(up_from.total_points, 0) as fromUserPoints,\n          u_to.id as toUserId,\n          u_to.name as toUserName,\n          u_to.username as toUserUsername,\n          u_to.photo as toUserPhoto,\n          COALESCE(up_to.total_points, 0) as toUserPoints\n        FROM friend_requests fr\n        INNER JOIN users u_from ON fr.from_user_id = u_from.id\n        INNER JOIN users u_to ON fr.to_user_id = u_to.id\n        LEFT JOIN user_points up_from ON u_from.id = up_from.user_id\n        LEFT JOIN user_points up_to ON u_to.id = up_to.user_id\n        WHERE (fr.from_user_id = ? OR fr.to_user_id = ?) \n        AND fr.status = 'pending'\n        ORDER BY fr.created_at DESC\n      `;\n\n      const [results] = await db.execute(sql, [userId, userId]);\n\n      // Check if results is valid\n      if (!results || !Array.isArray(results)) {\n        console.log('No results or invalid results format for getFriendRequests:', results);\n        return {\n          status: 'success',\n          data: []\n        };\n      }\n\n      // Format the results\n      const formattedResults = results.map((row: any) => ({\n        id: row.id,\n        fromUserId: row.fromUserId,\n        toUserId: row.toUserId,\n        fromUser: {\n          id: row.fromUserId,\n          name: row.fromUserName,\n          username: row.fromUserUsername,\n          photo: row.fromUserPhoto,\n          points: row.fromUserPoints\n        },\n        toUser: {\n          id: row.toUserId,\n          name: row.toUserName,\n          username: row.toUserUsername,\n          photo: row.toUserPhoto,\n          points: row.toUserPoints\n        },\n        status: row.status,\n        createdAt: row.createdAt,\n        updatedAt: row.updatedAt\n      }));\n\n      return {\n        status: 'success',\n        data: formattedResults\n      };\n    } catch (error) {\n      console.error('Error getting friend requests:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get friend requests'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async sendFriendRequest(fromUserId: number, toUserId: number) {\n    try {\n      if (fromUserId === toUserId) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Cannot send friend request to yourself'\n        }, HttpStatus.BAD_REQUEST);\n      }\n\n      // Check if users are already friends\n      const [existingFriendship] = await db.execute(\n        `SELECT id FROM users_friends\n         WHERE ((user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?))\n         AND status = 1 AND deleted_at IS NULL`,\n        [fromUserId, toUserId, toUserId, fromUserId]\n      );\n\n      if (existingFriendship[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Users are already friends'\n        }, HttpStatus.CONFLICT);\n      }\n\n      // Check if there's already a pending request\n      const [existingRequest] = await db.execute(\n        `SELECT id FROM friend_requests \n         WHERE ((from_user_id = ? AND to_user_id = ?) OR (from_user_id = ? AND to_user_id = ?))\n         AND status = 'pending'`,\n        [fromUserId, toUserId, toUserId, fromUserId]\n      );\n\n      if (existingRequest[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Friend request already exists'\n        }, HttpStatus.CONFLICT);\n      }\n\n      // Check if target user exists\n      const [targetUser] = await db.execute(\n        'SELECT id FROM users WHERE id = ?',\n        [toUserId]\n      );\n\n      if (!targetUser[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'User not found'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // Create friend request\n      await db.execute(\n        `INSERT INTO friend_requests (from_user_id, to_user_id, status, created_at, updated_at)\n         VALUES (?, ?, 'pending', NOW(), NOW())`,\n        [fromUserId, toUserId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Friend request sent successfully'\n      };\n    } catch (error) {\n      console.error('Error sending friend request:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to send friend request'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async acceptFriendRequest(requestId: number, userId: number) {\n    try {\n      // Get the friend request\n      const [request] = await db.execute(\n        'SELECT from_user_id, to_user_id FROM friend_requests WHERE id = ? AND to_user_id = ? AND status = \"pending\"',\n        [requestId, userId]\n      );\n\n      if (!request[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Friend request not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      const { from_user_id: fromUserId, to_user_id: toUserId } = request[0];\n\n      // Start transaction\n      await db.execute('START TRANSACTION');\n\n      try {\n        // Update friend request status\n        await db.execute(\n          'UPDATE friend_requests SET status = \"accepted\", updated_at = NOW() WHERE id = ?',\n          [requestId]\n        );\n\n        // Create friendship\n        await db.execute(\n          `INSERT INTO users_friends (user_id, friend_id, status, created_at, updated_at)\n           VALUES (?, ?, 1, NOW(), NOW())`,\n          [fromUserId, toUserId]\n        );\n\n        await db.execute('COMMIT');\n\n        return {\n          status: 'success',\n          message: 'Friend request accepted successfully'\n        };\n      } catch (error) {\n        await db.execute('ROLLBACK');\n        throw error;\n      }\n    } catch (error) {\n      console.error('Error accepting friend request:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to accept friend request'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async rejectFriendRequest(requestId: number, userId: number) {\n    try {\n      // Verify the request belongs to the user\n      const [request] = await db.execute(\n        'SELECT id FROM friend_requests WHERE id = ? AND to_user_id = ? AND status = \"pending\"',\n        [requestId, userId]\n      );\n\n      if (!request[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Friend request not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // Update friend request status\n      await db.execute(\n        'UPDATE friend_requests SET status = \"rejected\", updated_at = NOW() WHERE id = ?',\n        [requestId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Friend request rejected successfully'\n      };\n    } catch (error) {\n      console.error('Error rejecting friend request:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to reject friend request'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async removeFriend(userId: number, friendId: number) {\n    try {\n      // Check if friendship exists\n      const [friendship] = await db.execute(\n        `SELECT id FROM users_friends\n         WHERE ((user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?))\n         AND status = 1 AND deleted_at IS NULL`,\n        [userId, friendId, friendId, userId]\n      );\n\n      if (!friendship[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Friendship not found'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      // Remove friendship (soft delete)\n      await db.execute(\n        `UPDATE users_friends SET deleted_at = NOW()\n         WHERE ((user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?))\n         AND deleted_at IS NULL`,\n        [userId, friendId, friendId, userId]\n      );\n\n      return {\n        status: 'success',\n        message: 'Friend removed successfully'\n      };\n    } catch (error) {\n      console.error('Error removing friend:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to remove friend'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getFriendsRanking(userId: number, query: any) {\n    try {\n      const { period = 'all' } = query;\n      \n      let pointsColumn = 'up.total_points';\n      if (period === 'monthly') pointsColumn = 'up.monthly_points';\n      if (period === 'yearly') pointsColumn = 'up.yearly_points';\n\n      const sql = `\n        SELECT \n          u.id,\n          u.name,\n          u.username,\n          u.photo,\n          COALESCE(${pointsColumn}, 0) as points,\n          u.last_active as lastActive\n        FROM users_friends f\n        INNER JOIN users u ON (\n          CASE \n            WHEN f.user_id = ? THEN u.id = f.friend_id\n            ELSE u.id = f.user_id\n          END\n        )\n        LEFT JOIN user_points up ON u.id = up.user_id\n        WHERE (f.user_id = ? OR f.friend_id = ?)\n        AND f.status = 1 AND f.deleted_at IS NULL\n        ORDER BY ${pointsColumn} DESC, u.name ASC\n      `;\n\n      const [results] = await db.execute(sql, [userId, userId, userId]);\n\n      // Add ranking and include current user\n      const [currentUser] = await db.execute(\n        `SELECT u.id, u.name, u.username, u.photo, COALESCE(${pointsColumn}, 0) as points\n         FROM users u\n         LEFT JOIN user_points up ON u.id = up.user_id\n         WHERE u.id = ?`,\n        [userId]\n      );\n\n      const allUsers = [...results, ...currentUser];\n      allUsers.sort((a, b) => b.points - a.points);\n\n      const rankedUsers = allUsers.map((user, index) => ({\n        ...user,\n        rank: index + 1,\n        isCurrentUser: user.id === userId\n      }));\n\n      return {\n        status: 'success',\n        data: rankedUsers\n      };\n    } catch (error) {\n      console.error('Error getting friends ranking:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get friends ranking'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n}\n"], "names": ["FriendsService", "searchUsers", "query", "currentUserId", "length", "status", "data", "searchQuery", "startsWith", "slice", "sql", "searchPattern", "results", "db", "execute", "error", "console", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "getFriends", "userId", "Array", "isArray", "log", "friendsWithRank", "map", "friend", "index", "rank", "getFriendRequests", "formattedResults", "row", "id", "fromUserId", "toUserId", "fromUser", "name", "fromUserName", "username", "fromUserUsername", "photo", "fromUserPhoto", "points", "fromUserPoints", "to<PERSON><PERSON>", "toUserName", "toUserUsername", "toUserPhoto", "toUserPoints", "createdAt", "updatedAt", "sendFriendRequest", "BAD_REQUEST", "existingFriendship", "CONFLICT", "existingRequest", "targetUser", "NOT_FOUND", "acceptFriendRequest", "requestId", "request", "from_user_id", "to_user_id", "rejectFriendRequest", "removeFriend", "friendId", "friendship", "getFriendsRanking", "period", "pointsColumn", "currentUser", "allUsers", "sort", "a", "b", "rankedUsers", "user", "isCurrentUser"], "mappings": ";;;;+BAIaA;;;eAAAA;;;wBAJyC;0BACnC;;;;;;;AAGZ,IAAA,AAAMA,iBAAN,MAAMA;IAEX,MAAMC,YAAYC,KAAa,EAAEC,aAAqB,EAAE;QACtD,IAAI;YACF,IAAI,CAACD,SAASA,MAAME,MAAM,GAAG,GAAG;gBAC9B,OAAO;oBACLC,QAAQ;oBACRC,MAAM,EAAE;gBACV;YACF;YAEA,uCAAuC;YACvC,MAAMC,cAAcL,MAAMM,UAAU,CAAC,OAAON,MAAMO,KAAK,CAAC,KAAKP;YAE7D,MAAMQ,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;MA0Bb,CAAC;YAED,MAAMC,gBAAgB,CAAC,CAAC,EAAEJ,YAAY,CAAC,CAAC;YACxC,MAAM,CAACK,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACJ,KAAK;gBACtCP;gBAAeA;gBAAeA;gBAAeA;gBAAeA;gBAC5DQ;gBAAeA;gBAAeA;aAC/B;YAED,OAAO;gBACLN,QAAQ;gBACRC,MAAMM;YACR;QACF,EAAE,OAAOG,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMC,WAAWC,MAAc,EAAE;QAC/B,IAAI;YACF,MAAMZ,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBb,CAAC;YAED,MAAM,CAACE,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACJ,KAAK;gBAACY;gBAAQA;gBAAQA;aAAO;YAEhE,4BAA4B;YAC5B,IAAI,CAACV,WAAW,CAACW,MAAMC,OAAO,CAACZ,UAAU;gBACvCI,QAAQS,GAAG,CAAC,wDAAwDb;gBACpE,OAAO;oBACLP,QAAQ;oBACRC,MAAM,EAAE;gBACV;YACF;YAEA,cAAc;YACd,MAAMoB,kBAAkBd,QAAQe,GAAG,CAAC,CAACC,QAAQC,QAAW,CAAA;oBACtD,GAAGD,MAAM;oBACTE,MAAMD,QAAQ;gBAChB,CAAA;YAEA,OAAO;gBACLxB,QAAQ;gBACRC,MAAMoB;YACR;QACF,EAAE,OAAOX,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMW,kBAAkBT,MAAc,EAAE;QACtC,IAAI;YACF,MAAMZ,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;MA0Bb,CAAC;YAED,MAAM,CAACE,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACJ,KAAK;gBAACY;gBAAQA;aAAO;YAExD,4BAA4B;YAC5B,IAAI,CAACV,WAAW,CAACW,MAAMC,OAAO,CAACZ,UAAU;gBACvCI,QAAQS,GAAG,CAAC,+DAA+Db;gBAC3E,OAAO;oBACLP,QAAQ;oBACRC,MAAM,EAAE;gBACV;YACF;YAEA,qBAAqB;YACrB,MAAM0B,mBAAmBpB,QAAQe,GAAG,CAAC,CAACM,MAAc,CAAA;oBAClDC,IAAID,IAAIC,EAAE;oBACVC,YAAYF,IAAIE,UAAU;oBAC1BC,UAAUH,IAAIG,QAAQ;oBACtBC,UAAU;wBACRH,IAAID,IAAIE,UAAU;wBAClBG,MAAML,IAAIM,YAAY;wBACtBC,UAAUP,IAAIQ,gBAAgB;wBAC9BC,OAAOT,IAAIU,aAAa;wBACxBC,QAAQX,IAAIY,cAAc;oBAC5B;oBACAC,QAAQ;wBACNZ,IAAID,IAAIG,QAAQ;wBAChBE,MAAML,IAAIc,UAAU;wBACpBP,UAAUP,IAAIe,cAAc;wBAC5BN,OAAOT,IAAIgB,WAAW;wBACtBL,QAAQX,IAAIiB,YAAY;oBAC1B;oBACA7C,QAAQ4B,IAAI5B,MAAM;oBAClB8C,WAAWlB,IAAIkB,SAAS;oBACxBC,WAAWnB,IAAImB,SAAS;gBAC1B,CAAA;YAEA,OAAO;gBACL/C,QAAQ;gBACRC,MAAM0B;YACR;QACF,EAAE,OAAOjB,OAAO;YACdC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMiC,kBAAkBlB,UAAkB,EAAEC,QAAgB,EAAE;QAC5D,IAAI;YACF,IAAID,eAAeC,UAAU;gBAC3B,MAAM,IAAInB,qBAAa,CAAC;oBACtBZ,QAAQ;oBACRa,SAAS;gBACX,GAAGC,kBAAU,CAACmC,WAAW;YAC3B;YAEA,qCAAqC;YACrC,MAAM,CAACC,mBAAmB,GAAG,MAAM1C,YAAE,CAACC,OAAO,CAC3C,CAAC;;8CAEqC,CAAC,EACvC;gBAACqB;gBAAYC;gBAAUA;gBAAUD;aAAW;YAG9C,IAAIoB,kBAAkB,CAAC,EAAE,EAAE;gBACzB,MAAM,IAAItC,qBAAa,CAAC;oBACtBZ,QAAQ;oBACRa,SAAS;gBACX,GAAGC,kBAAU,CAACqC,QAAQ;YACxB;YAEA,6CAA6C;YAC7C,MAAM,CAACC,gBAAgB,GAAG,MAAM5C,YAAE,CAACC,OAAO,CACxC,CAAC;;+BAEsB,CAAC,EACxB;gBAACqB;gBAAYC;gBAAUA;gBAAUD;aAAW;YAG9C,IAAIsB,eAAe,CAAC,EAAE,EAAE;gBACtB,MAAM,IAAIxC,qBAAa,CAAC;oBACtBZ,QAAQ;oBACRa,SAAS;gBACX,GAAGC,kBAAU,CAACqC,QAAQ;YACxB;YAEA,8BAA8B;YAC9B,MAAM,CAACE,WAAW,GAAG,MAAM7C,YAAE,CAACC,OAAO,CACnC,qCACA;gBAACsB;aAAS;YAGZ,IAAI,CAACsB,UAAU,CAAC,EAAE,EAAE;gBAClB,MAAM,IAAIzC,qBAAa,CAAC;oBACtBZ,QAAQ;oBACRa,SAAS;gBACX,GAAGC,kBAAU,CAACwC,SAAS;YACzB;YAEA,wBAAwB;YACxB,MAAM9C,YAAE,CAACC,OAAO,CACd,CAAC;+CACsC,CAAC,EACxC;gBAACqB;gBAAYC;aAAS;YAGxB,OAAO;gBACL/B,QAAQ;gBACRa,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,iCAAiCA;YAC/C,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMwC,oBAAoBC,SAAiB,EAAEvC,MAAc,EAAE;QAC3D,IAAI;YACF,yBAAyB;YACzB,MAAM,CAACwC,QAAQ,GAAG,MAAMjD,YAAE,CAACC,OAAO,CAChC,+GACA;gBAAC+C;gBAAWvC;aAAO;YAGrB,IAAI,CAACwC,OAAO,CAAC,EAAE,EAAE;gBACf,MAAM,IAAI7C,qBAAa,CAAC;oBACtBZ,QAAQ;oBACRa,SAAS;gBACX,GAAGC,kBAAU,CAACwC,SAAS;YACzB;YAEA,MAAM,EAAEI,cAAc5B,UAAU,EAAE6B,YAAY5B,QAAQ,EAAE,GAAG0B,OAAO,CAAC,EAAE;YAErE,oBAAoB;YACpB,MAAMjD,YAAE,CAACC,OAAO,CAAC;YAEjB,IAAI;gBACF,+BAA+B;gBAC/B,MAAMD,YAAE,CAACC,OAAO,CACd,mFACA;oBAAC+C;iBAAU;gBAGb,oBAAoB;gBACpB,MAAMhD,YAAE,CAACC,OAAO,CACd,CAAC;yCAC8B,CAAC,EAChC;oBAACqB;oBAAYC;iBAAS;gBAGxB,MAAMvB,YAAE,CAACC,OAAO,CAAC;gBAEjB,OAAO;oBACLT,QAAQ;oBACRa,SAAS;gBACX;YACF,EAAE,OAAOH,OAAO;gBACd,MAAMF,YAAE,CAACC,OAAO,CAAC;gBACjB,MAAMC;YACR;QACF,EAAE,OAAOA,OAAO;YACdC,QAAQD,KAAK,CAAC,mCAAmCA;YACjD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM6C,oBAAoBJ,SAAiB,EAAEvC,MAAc,EAAE;QAC3D,IAAI;YACF,yCAAyC;YACzC,MAAM,CAACwC,QAAQ,GAAG,MAAMjD,YAAE,CAACC,OAAO,CAChC,yFACA;gBAAC+C;gBAAWvC;aAAO;YAGrB,IAAI,CAACwC,OAAO,CAAC,EAAE,EAAE;gBACf,MAAM,IAAI7C,qBAAa,CAAC;oBACtBZ,QAAQ;oBACRa,SAAS;gBACX,GAAGC,kBAAU,CAACwC,SAAS;YACzB;YAEA,+BAA+B;YAC/B,MAAM9C,YAAE,CAACC,OAAO,CACd,mFACA;gBAAC+C;aAAU;YAGb,OAAO;gBACLxD,QAAQ;gBACRa,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,mCAAmCA;YACjD,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM8C,aAAa5C,MAAc,EAAE6C,QAAgB,EAAE;QACnD,IAAI;YACF,6BAA6B;YAC7B,MAAM,CAACC,WAAW,GAAG,MAAMvD,YAAE,CAACC,OAAO,CACnC,CAAC;;8CAEqC,CAAC,EACvC;gBAACQ;gBAAQ6C;gBAAUA;gBAAU7C;aAAO;YAGtC,IAAI,CAAC8C,UAAU,CAAC,EAAE,EAAE;gBAClB,MAAM,IAAInD,qBAAa,CAAC;oBACtBZ,QAAQ;oBACRa,SAAS;gBACX,GAAGC,kBAAU,CAACwC,SAAS;YACzB;YAEA,kCAAkC;YAClC,MAAM9C,YAAE,CAACC,OAAO,CACd,CAAC;;+BAEsB,CAAC,EACxB;gBAACQ;gBAAQ6C;gBAAUA;gBAAU7C;aAAO;YAGtC,OAAO;gBACLjB,QAAQ;gBACRa,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,0BAA0BA;YACxC,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMiD,kBAAkB/C,MAAc,EAAEpB,KAAU,EAAE;QAClD,IAAI;YACF,MAAM,EAAEoE,SAAS,KAAK,EAAE,GAAGpE;YAE3B,IAAIqE,eAAe;YACnB,IAAID,WAAW,WAAWC,eAAe;YACzC,IAAID,WAAW,UAAUC,eAAe;YAExC,MAAM7D,MAAM,CAAC;;;;;;mBAMA,EAAE6D,aAAa;;;;;;;;;;;;iBAYjB,EAAEA,aAAa;MAC1B,CAAC;YAED,MAAM,CAAC3D,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACJ,KAAK;gBAACY;gBAAQA;gBAAQA;aAAO;YAEhE,uCAAuC;YACvC,MAAM,CAACkD,YAAY,GAAG,MAAM3D,YAAE,CAACC,OAAO,CACpC,CAAC,mDAAmD,EAAEyD,aAAa;;;uBAGpD,CAAC,EAChB;gBAACjD;aAAO;YAGV,MAAMmD,WAAW;mBAAI7D;mBAAY4D;aAAY;YAC7CC,SAASC,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEhC,MAAM,GAAG+B,EAAE/B,MAAM;YAE3C,MAAMiC,cAAcJ,SAAS9C,GAAG,CAAC,CAACmD,MAAMjD,QAAW,CAAA;oBACjD,GAAGiD,IAAI;oBACPhD,MAAMD,QAAQ;oBACdkD,eAAeD,KAAK5C,EAAE,KAAKZ;gBAC7B,CAAA;YAEA,OAAO;gBACLjB,QAAQ;gBACRC,MAAMuE;YACR;QACF,EAAE,OAAO9D,OAAO;YACdC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,MAAM,IAAIE,qBAAa,CAAC;gBACtBZ,QAAQ;gBACRa,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;AACF"}