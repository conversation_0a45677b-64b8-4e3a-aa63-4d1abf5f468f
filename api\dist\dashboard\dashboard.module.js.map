{"version": 3, "sources": ["../../src/dashboard/dashboard.module.ts"], "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\r\nimport { DashboardController } from './dashboard.controller';\r\nimport { DashboardService } from './dashboard.service';\r\n\r\n@Module({\r\n  controllers: [DashboardController],\r\n  providers: [DashboardService],\r\n  exports: [DashboardService]\r\n})\r\nexport class DashboardModule {}\r\n"], "names": ["DashboardModule", "controllers", "DashboardController", "providers", "DashboardService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;qCACa;kCACH;;;;;;;AAO1B,IAAA,AAAMA,kBAAN,MAAMA;AAAiB;;;QAJ5BC,aAAa;YAACC,wCAAmB;SAAC;QAClCC,WAAW;YAACC,kCAAgB;SAAC;QAC7BC,SAAS;YAACD,kCAAgB;SAAC"}