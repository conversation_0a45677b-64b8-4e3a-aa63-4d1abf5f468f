import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRecipesTables1709001234572 implements MigrationInterface {
  name = 'AddRecipesTables1709001234572';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Recipes table
    await queryRunner.query(`
      CREATE TABLE recipes (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT NOT NULL,
        ingredients JSON NOT NULL,
        instructions TEXT,
        servings INT NOT NULL,
        prep_time INT NOT NULL,
        cook_time INT,
        difficulty ENUM('easy', 'medium', 'hard') NOT NULL,
        category ENUM('breakfast', 'lunch', 'dinner', 'snack', 'dessert', 'drink') NOT NULL,
        total_calories DECIMAL(8,2) NOT NULL,
        total_protein DECIMAL(8,2) NOT NULL,
        total_carbs DECIMAL(8,2) NOT NULL,
        total_fat DECIMAL(8,2) NOT NULL,
        total_fiber DECIMAL(8,2),
        total_sugar DECIMAL(8,2),
        total_sodium DECIMAL(8,2),
        image_url VARCHAR(500),
        is_public BOOLEAN DEFAULT TRUE,
        is_active BOOLEAN DEFAULT TRUE,
        created_by VARCHAR(36),
        average_rating DECIMAL(3,2) DEFAULT 0,
        ratings_count INT DEFAULT 0,
        favorites_count INT DEFAULT 0,
        tags TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // User favorite recipes table
    await queryRunner.query(`
      CREATE TABLE user_favorite_recipes (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        recipe_id VARCHAR(36) NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_recipe (user_id, recipe_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Recipe ratings table
    await queryRunner.query(`
      CREATE TABLE recipe_ratings (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        recipe_id VARCHAR(36) NOT NULL,
        rating INT NOT NULL,
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_recipe_rating (user_id, recipe_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (recipe_id) REFERENCES recipes(id) ON DELETE CASCADE,
        CHECK (rating >= 1 AND rating <= 5)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX idx_recipes_category ON recipes(category);`);
    await queryRunner.query(`CREATE INDEX idx_recipes_difficulty ON recipes(difficulty);`);
    await queryRunner.query(`CREATE INDEX idx_recipes_prep_time ON recipes(prep_time);`);
    await queryRunner.query(`CREATE INDEX idx_recipes_rating ON recipes(average_rating);`);
    await queryRunner.query(`CREATE INDEX idx_recipes_public ON recipes(is_public);`);
    await queryRunner.query(`CREATE INDEX idx_recipes_active ON recipes(is_active);`);
    await queryRunner.query(`CREATE INDEX idx_recipes_created_by ON recipes(created_by);`);
    await queryRunner.query(`CREATE INDEX idx_user_favorite_recipes_user_id ON user_favorite_recipes(user_id);`);
    await queryRunner.query(`CREATE INDEX idx_recipe_ratings_recipe_id ON recipe_ratings(recipe_id);`);

    // Insert some sample recipes
    await queryRunner.query(`
      INSERT INTO recipes (
        id, name, description, ingredients, instructions, servings, prep_time, cook_time,
        difficulty, category, total_calories, total_protein, total_carbs, total_fat,
        total_fiber, total_sugar, total_sodium, is_public, tags
      ) VALUES 
      (
        UUID(),
        'Omelete de Claras com Legumes',
        'Uma omelete rica em proteínas e baixa em gorduras, perfeita para o café da manhã.',
        '[{"id":"1","name":"Claras de ovo","quantity":6,"unit":"unidades","protein":18,"carbs":0,"fat":0,"calories":72},{"id":"2","name":"Espinafre","quantity":30,"unit":"g","protein":0.9,"carbs":1.1,"fat":0.1,"calories":7},{"id":"3","name":"Tomate","quantity":50,"unit":"g","protein":0.5,"carbs":2.2,"fat":0.1,"calories":9},{"id":"4","name":"Cebola","quantity":30,"unit":"g","protein":0.3,"carbs":2.7,"fat":0,"calories":12}]',
        '1. Bata as claras em uma tigela até formar espuma leve. 2. Aqueça uma frigideira antiaderente em fogo médio. 3. Adicione as claras e deixe cozinhar por 2 minutos. 4. Adicione os legumes picados sobre metade da omelete. 5. Dobre a omelete ao meio e sirva imediatamente.',
        1, 10, 5, 'easy', 'breakfast', 100.00, 19.70, 6.00, 0.20, 2.50, 3.00, 150.00, TRUE, 'proteína,baixa gordura,vegetais'
      ),
      (
        UUID(),
        'Bowl de Proteína Completo',
        'Um bowl nutritivo com proteínas, carboidratos complexos e gorduras boas.',
        '[{"id":"1","name":"Peito de frango","quantity":150,"unit":"g","protein":45,"carbs":0,"fat":3,"calories":210},{"id":"2","name":"Arroz integral","quantity":100,"unit":"g","protein":7,"carbs":76,"fat":2.2,"calories":350},{"id":"3","name":"Abacate","quantity":50,"unit":"g","protein":1,"carbs":4,"fat":7.5,"calories":80},{"id":"4","name":"Brócolis","quantity":80,"unit":"g","protein":2.4,"carbs":4,"fat":0.3,"calories":28}]',
        '1. Tempere o frango com sal, pimenta e ervas. 2. Grelhe o frango por 6-8 minutos de cada lado. 3. Cozinhe o arroz integral conforme instruções da embalagem. 4. Cozinhe o brócolis no vapor por 5 minutos. 5. Monte o bowl com todos os ingredientes e finalize com abacate fatiado.',
        1, 20, 15, 'medium', 'lunch', 668.00, 55.40, 84.00, 13.00, 8.00, 6.00, 300.00, TRUE, 'proteína,carboidrato complexo,gordura boa'
      ),
      (
        UUID(),
        'Smoothie Verde Detox',
        'Smoothie refrescante e nutritivo para começar o dia com energia.',
        '[{"id":"1","name":"Espinafre","quantity":50,"unit":"g","protein":1.5,"carbs":1.8,"fat":0.2,"calories":12},{"id":"2","name":"Banana","quantity":100,"unit":"g","protein":1.1,"carbs":23,"fat":0.3,"calories":89},{"id":"3","name":"Maçã","quantity":80,"unit":"g","protein":0.2,"carbs":11,"fat":0.1,"calories":42},{"id":"4","name":"Água de coco","quantity":200,"unit":"ml","protein":0.7,"carbs":3.7,"fat":0.2,"calories":19}]',
        '1. Lave bem o espinafre e a maçã. 2. Descasque a banana e corte em pedaços. 3. Adicione todos os ingredientes no liquidificador. 4. Bata por 2-3 minutos até ficar homogêneo. 5. Sirva imediatamente com gelo se desejar.',
        1, 5, 0, 'easy', 'drink', 162.00, 3.50, 39.50, 0.80, 4.20, 35.00, 50.00, TRUE, 'detox,verde,vitaminas'
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX idx_recipe_ratings_recipe_id ON recipe_ratings;`);
    await queryRunner.query(`DROP INDEX idx_user_favorite_recipes_user_id ON user_favorite_recipes;`);
    await queryRunner.query(`DROP INDEX idx_recipes_created_by ON recipes;`);
    await queryRunner.query(`DROP INDEX idx_recipes_active ON recipes;`);
    await queryRunner.query(`DROP INDEX idx_recipes_public ON recipes;`);
    await queryRunner.query(`DROP INDEX idx_recipes_rating ON recipes;`);
    await queryRunner.query(`DROP INDEX idx_recipes_prep_time ON recipes;`);
    await queryRunner.query(`DROP INDEX idx_recipes_difficulty ON recipes;`);
    await queryRunner.query(`DROP INDEX idx_recipes_category ON recipes;`);

    // Drop tables
    await queryRunner.query(`DROP TABLE recipe_ratings;`);
    await queryRunner.query(`DROP TABLE user_favorite_recipes;`);
    await queryRunner.query(`DROP TABLE recipes;`);
  }
}
