import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { RegisterUserDto } from './dto/register-user.dto';
import { User } from '../users/entities/user.entity';
import { MailService } from '../mail/mail.service';
import { SmsService } from '../sms/sms.service';
import { DeviceInfo } from './interfaces/device-info.interface';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly jwtService: JwtService,
    private readonly mailService: MailService,
    private readonly smsService: SmsService
  ) {}

  async register(registerDto: RegisterUserDto): Promise<User> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      // Register user using database function
      const result = await queryRunner.query(
        'SELECT register_user($1, $2, $3, $4)',
        [registerDto.email, registerDto.password, registerDto.name, registerDto.role]
      );
      
      const userId = result[0].register_user;
      
      // Get created user
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) throw new Error('Failed to create user');

      await queryRunner.commitTransaction();

      // Send verification email
      if (registerDto.email) {
        await this.requestVerification(user.id, 'email');
      }

      // Send verification SMS if phone provided
      if (registerDto.phone) {
        await this.requestVerification(user.id, 'phone');
      }

      return user;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async validateUser(email: string, password: string, deviceInfo?: DeviceInfo): Promise<any> {
    try {
      const result = await this.dataSource.query(
        'SELECT * FROM login_user($1, $2, $3)',
        [email, password, JSON.stringify(deviceInfo || {})]
      );

      return result[0];
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  async login(user: any, deviceInfo?: DeviceInfo) {
    return this.validateUser(user.email, user.password, deviceInfo);
  }

  async requestPasswordReset(email: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const result = await this.dataSource.query(
      'SELECT request_password_reset($1)',
      [email]
    );
    const tokenId = result[0].request_password_reset;

    // Send password reset email
    await this.mailService.sendPasswordResetEmail(user.email, tokenId);
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    const [tokenId, tokenValue] = token.split(':');
    
    const result = await this.dataSource.query(
      'SELECT verify_reset_token($1, $2)',
      [tokenId, tokenValue]
    );
    const isValid = result[0].verify_reset_token;

    if (!isValid) {
      throw new UnauthorizedException('Invalid or expired token');
    }

    const changeResult = await this.dataSource.query(
      'SELECT change_password($1, $2, $3)',
      [tokenId, tokenValue, newPassword]
    );
    const success = changeResult[0].change_password;

    if (!success) {
      throw new UnauthorizedException('Failed to reset password');
    }
  }

  async requestVerification(userId: string, type: 'email' | 'phone'): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const result = await this.dataSource.query(
      'SELECT request_verification($1, $2)',
      [userId, type]
    );
    const codeId = result[0].request_verification;

    if (type === 'email') {
      await this.mailService.sendVerificationEmail(user.email, codeId);
    } else {
      await this.smsService.sendVerificationSms(user.phone, codeId);
    }
  }

  async verifyCode(userId: string, code: string): Promise<boolean> {
    const result = await this.dataSource.query(
      'SELECT verify_code($1, $2)',
      [userId, code]
    );

    return result[0].verify_code;
  }

  async refreshToken(refreshToken: string, deviceInfo?: DeviceInfo) {
    try {
      const result = await this.dataSource.query(
        'SELECT * FROM refresh_token($1, $2)',
        [refreshToken, JSON.stringify(deviceInfo || {})]
      );
      return result[0];
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(sessionId: string, allDevices = false) {
    await this.dataSource.query(
      'SELECT logout_user($1, $2)',
      [sessionId, allDevices]
    );
  }

  async getUserSessions(userId: string) {
    const result = await this.dataSource.query(
      'SELECT * FROM get_user_sessions($1)',
      [userId]
    );
    return result;
  }
}