{"version": 3, "sources": ["../../src/compatibility/compatibility.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { CompatibilityController } from './compatibility.controller';\n\n@Module({\n  controllers: [CompatibilityController],\n})\nexport class CompatibilityModule {}\n"], "names": ["CompatibilityModule", "controllers", "CompatibilityController"], "mappings": ";;;;+BAMaA;;;eAAAA;;;wBANU;yCACiB;;;;;;;AAKjC,IAAA,AAAMA,sBAAN,MAAMA;AAAqB;;;QAFhCC,aAAa;YAACC,gDAAuB;SAAC"}