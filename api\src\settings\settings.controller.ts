import { 
  Controller, 
  Get, 
  Put, 
  Post, 
  Delete, 
  Body, 
  Param, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { SettingsService } from './settings.service';

@Controller('settings')
@UseGuards(JwtAuthGuard)
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Get()
  async getAllSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getAllSettings(userId);
  }

  @Get('profile')
  async getProfileSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getProfileSettings(userId);
  }

  @Put('profile')
  async updateProfileSettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updateProfileSettings(userId, settings);
  }

  @Get('privacy')
  async getPrivacySettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getPrivacySettings(userId);
  }

  @Put('privacy')
  async updatePrivacySettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updatePrivacySettings(userId, settings);
  }

  @Get('goals')
  async getGoalSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getGoalSettings(userId);
  }

  @Put('goals')
  async updateGoalSettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updateGoalSettings(userId, settings);
  }

  @Get('units')
  async getUnitSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getUnitSettings(userId);
  }

  @Put('units')
  async updateUnitSettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updateUnitSettings(userId, settings);
  }

  @Get('integrations')
  async getIntegrationSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getIntegrationSettings(userId);
  }

  @Put('integrations')
  async updateIntegrationSettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updateIntegrationSettings(userId, settings);
  }

  @Get('reminders')
  async getReminderSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getReminderSettings(userId);
  }

  @Put('reminders')
  async updateReminderSettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updateReminderSettings(userId, settings);
  }

  @Post('reminders/test')
  async testReminder(@Request() req: any, @Body() reminderData: any) {
    const userId = req.user.userId;
    return this.settingsService.testReminder(userId, reminderData);
  }

  @Get('themes')
  async getThemeSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getThemeSettings(userId);
  }

  @Put('themes')
  async updateThemeSettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updateThemeSettings(userId, settings);
  }

  @Get('data-retention')
  async getDataRetentionSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.getDataRetentionSettings(userId);
  }

  @Put('data-retention')
  async updateDataRetentionSettings(@Request() req: any, @Body() settings: any) {
    const userId = req.user.userId;
    return this.settingsService.updateDataRetentionSettings(userId, settings);
  }

  @Post('reset')
  async resetSettings(@Request() req: any, @Body() resetOptions: any) {
    const userId = req.user.userId;
    return this.settingsService.resetSettings(userId, resetOptions);
  }

  @Get('export')
  async exportSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.settingsService.exportSettings(userId);
  }

  @Post('import')
  async importSettings(@Request() req: any, @Body() settingsData: any) {
    const userId = req.user.userId;
    return this.settingsService.importSettings(userId, settingsData);
  }
}
