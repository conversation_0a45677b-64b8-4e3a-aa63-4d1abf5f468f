{"version": 3, "sources": ["../../src/admin/affiliates.controller.ts"], "sourcesContent": ["import { Body, Controller, Delete, Get, NotFoundException, Param, Post, Put, Query, Request, UseGuards } from '@nestjs/common';\r\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\r\nimport { AffiliatesService } from './affiliates.service';\r\n\r\n@Controller('affiliates')\r\nexport class AffiliatesController {\r\n    constructor(private readonly affiliatesService: AffiliatesService) {}\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('me')\r\n    async getMe(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      return this.affiliatesService.getAffiliateById(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('onboarding')\r\n    async checkPaymentEligibility(@Request() req: any) {\r\n      const affiliateId = req.user.userId;\r\n      return this.affiliatesService.checkPaymentEligibilityByAffiliateId(affiliateId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('onboarding')\r\n    async startOnboarding(@Request() req: any) {\r\n      const affiliateId = req.user.userId;\r\n      return this.affiliatesService.startOnboarding(affiliateId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('links')\r\n    async getLinks(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      return this.affiliatesService.getLinks(userId);\r\n    }\r\n\r\n    // hit this endpoint when a user clicks on a link\r\n    @Get('links/visit/:id')\r\n    async registerLinkVisit(@Param('id') id: string) {\r\n      return this.affiliatesService.registerLinkVisit(id);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('links')\r\n    async createLink(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      return this.affiliatesService.createLink(userId, body);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('dashboard')\r\n    async getDashboardStats(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      return this.affiliatesService.getDashboardStats(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('stats')\r\n    async getDetailedStats(@Request() req: any, @Query('start_date') startDate?: string, @Query('end_date') endDate?: string) {\r\n      const userId = req.user.userId;\r\n      return this.affiliatesService.getDetailedStats(userId, startDate, endDate);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('payments')\r\n    async getPaymentHistory(@Request() req: any, @Query('page') page?: string, @Query('limit') limit?: string) {\r\n      const userId = req.user.userId;\r\n      const pageNumber = page ? parseInt(page, 10) : 1;\r\n      const limitNumber = limit ? parseInt(limit, 10) : 10;\r\n      return this.affiliatesService.getPaymentHistory(userId, pageNumber, limitNumber);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('transactions')\r\n    async getRecentTransactions(@Request() req: any, @Query('page') page?: string, @Query('limit') limit?: string) {\r\n      const userId = req.user.userId;\r\n      const pageNumber = page ? parseInt(page, 10) : 1;\r\n      const limitNumber = limit ? parseInt(limit, 10) : 10;\r\n      return this.affiliatesService.getRecentTransactions(userId, pageNumber, limitNumber);\r\n    }\r\n}"], "names": ["AffiliatesController", "getMe", "req", "userId", "user", "affiliatesService", "getAffiliateById", "checkPaymentEligibility", "affiliateId", "checkPaymentEligibilityByAffiliateId", "startOnboarding", "getLinks", "registerLinkVisit", "id", "createLink", "body", "getDashboardStats", "getDetailedStats", "startDate", "endDate", "getPaymentHistory", "page", "limit", "pageNumber", "parseInt", "limitNumber", "getRecentTransactions", "constructor"], "mappings": ";;;;+BAKaA;;;eAAAA;;;wBALiG;8BACjF;mCACK;;;;;;;;;;;;;;;AAG3B,IAAA,AAAMA,uBAAN,MAAMA;IAGT,MAEMC,MAAM,AAAWC,GAAQ,EAAE;QAC/B,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACC,gBAAgB,CAACH;IACjD;IAEA,MAEMI,wBAAwB,AAAWL,GAAQ,EAAE;QACjD,MAAMM,cAAcN,IAAIE,IAAI,CAACD,MAAM;QACnC,OAAO,IAAI,CAACE,iBAAiB,CAACI,oCAAoC,CAACD;IACrE;IAEA,MAEME,gBAAgB,AAAWR,GAAQ,EAAE;QACzC,MAAMM,cAAcN,IAAIE,IAAI,CAACD,MAAM;QACnC,OAAO,IAAI,CAACE,iBAAiB,CAACK,eAAe,CAACF;IAChD;IAEA,MAEMG,SAAS,AAAWT,GAAQ,EAAE;QAClC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACM,QAAQ,CAACR;IACzC;IAEA,iDAAiD;IACjD,MACMS,kBAAkB,AAAaC,EAAU,EAAE;QAC/C,OAAO,IAAI,CAACR,iBAAiB,CAACO,iBAAiB,CAACC;IAClD;IAEA,MAEMC,WAAW,AAAWZ,GAAQ,EAAE,AAAQa,IAAS,EAAE;QACvD,MAAMZ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACS,UAAU,CAACX,QAAQY;IACnD;IAEA,MAEMC,kBAAkB,AAAWd,GAAQ,EAAE;QAC3C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACW,iBAAiB,CAACb;IAClD;IAEA,MAEMc,iBAAiB,AAAWf,GAAQ,EAAE,AAAqBgB,SAAkB,EAAE,AAAmBC,OAAgB,EAAE;QACxH,MAAMhB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,iBAAiB,CAACY,gBAAgB,CAACd,QAAQe,WAAWC;IACpE;IAEA,MAEMC,kBAAkB,AAAWlB,GAAQ,EAAE,AAAemB,IAAa,EAAE,AAAgBC,KAAc,EAAE;QACzG,MAAMnB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,MAAMoB,aAAaF,OAAOG,SAASH,MAAM,MAAM;QAC/C,MAAMI,cAAcH,QAAQE,SAASF,OAAO,MAAM;QAClD,OAAO,IAAI,CAACjB,iBAAiB,CAACe,iBAAiB,CAACjB,QAAQoB,YAAYE;IACtE;IAEA,MAEMC,sBAAsB,AAAWxB,GAAQ,EAAE,AAAemB,IAAa,EAAE,AAAgBC,KAAc,EAAE;QAC7G,MAAMnB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,MAAMoB,aAAaF,OAAOG,SAASH,MAAM,MAAM;QAC/C,MAAMI,cAAcH,QAAQE,SAASF,OAAO,MAAM;QAClD,OAAO,IAAI,CAACjB,iBAAiB,CAACqB,qBAAqB,CAACvB,QAAQoB,YAAYE;IAC1E;IAzEAE,YAAY,AAAiBtB,iBAAoC,CAAE;aAAtCA,oBAAAA;IAAuC;AA0ExE"}