import { IsString, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON>al, <PERSON>N<PERSON>ber, IsDate } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateClientDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  phone: string;

  @IsString()
  @IsNotEmpty()
  password: string;

  @IsDate()
  @Type(() => Date)
  @IsOptional()
  date_of_birth?: Date;

  @IsOptional()
  @IsString()
  photo?: string;

  @IsString()
  height: number;

  @IsNumber()
  @Type(() => Number)
  weight: number;

  @IsNumber()
  @Type(() => Number)
  goal_id: number;

  @IsNumber()
  @Type(() => Number)
  activity_level_id: number;

  @IsOptional()
  @IsString()
  medical_conditions?: string;

  @IsOptional()
  @IsString()
  allergies?: string;
}