{"version": 3, "sources": ["../../src/analytics/analytics.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { AnalyticsService } from './analytics.service';\n\n@Controller('analytics')\n@UseGuards(JwtAuthGuard)\nexport class AnalyticsController {\n  constructor(private readonly analyticsService: AnalyticsService) {}\n\n  @Get('dashboard')\n  async getDashboardAnalytics(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getDashboardAnalytics(userId, query);\n  }\n\n  @Get('progress/summary')\n  async getProgressSummary(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getProgressSummary(userId, query);\n  }\n\n  @Get('nutrition/trends')\n  async getNutritionTrends(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getNutritionTrends(userId, query);\n  }\n\n  @Get('workout/trends')\n  async getWorkoutTrends(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getWorkoutTrends(userId, query);\n  }\n\n  @Get('body/composition')\n  async getBodyCompositionAnalysis(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getBodyCompositionAnalysis(userId, query);\n  }\n\n  @Get('habits/analysis')\n  async getHabitsAnalysis(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getHabitsAnalysis(userId, query);\n  }\n\n  @Get('goals/tracking')\n  async getGoalsTracking(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getGoalsTracking(userId, query);\n  }\n\n  @Get('social/stats')\n  async getSocialStats(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getSocialStats(userId);\n  }\n\n  @Get('gamification/stats')\n  async getGamificationStats(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getGamificationStats(userId);\n  }\n\n  @Get('reports/weekly')\n  async getWeeklyReport(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getWeeklyReport(userId, query);\n  }\n\n  @Get('reports/monthly')\n  async getMonthlyReport(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getMonthlyReport(userId, query);\n  }\n\n  @Get('insights/ai')\n  async getAIInsights(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getAIInsights(userId, query);\n  }\n\n  @Get('predictions/weight')\n  async getWeightPredictions(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getWeightPredictions(userId, query);\n  }\n\n  @Get('benchmarks/comparison')\n  async getBenchmarkComparison(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.analyticsService.getBenchmarkComparison(userId, query);\n  }\n}\n"], "names": ["AnalyticsController", "getDashboardAnalytics", "req", "query", "userId", "user", "analyticsService", "getProgressSummary", "getNutritionTrends", "getWorkoutTrends", "getBodyCompositionAnalysis", "getHabitsAnalysis", "getGoalsTracking", "getSocialStats", "getGamificationStats", "getWeeklyReport", "getMonthlyReport", "getAIInsights", "getWeightPredictions", "getBenchmarkComparison", "constructor"], "mappings": ";;;;+BAYaA;;;eAAAA;;;wBANN;8BACsB;kCACI;;;;;;;;;;;;;;;AAI1B,IAAA,AAAMA,sBAAN,MAAMA;IAGX,MACMC,sBAAsB,AAAWC,GAAQ,EAAE,AAASC,KAAU,EAAE;QACpE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACL,qBAAqB,CAACG,QAAQD;IAC7D;IAEA,MACMI,mBAAmB,AAAWL,GAAQ,EAAE,AAASC,KAAU,EAAE;QACjE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACC,kBAAkB,CAACH,QAAQD;IAC1D;IAEA,MACMK,mBAAmB,AAAWN,GAAQ,EAAE,AAASC,KAAU,EAAE;QACjE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACE,kBAAkB,CAACJ,QAAQD;IAC1D;IAEA,MACMM,iBAAiB,AAAWP,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC/D,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACG,gBAAgB,CAACL,QAAQD;IACxD;IAEA,MACMO,2BAA2B,AAAWR,GAAQ,EAAE,AAASC,KAAU,EAAE;QACzE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACI,0BAA0B,CAACN,QAAQD;IAClE;IAEA,MACMQ,kBAAkB,AAAWT,GAAQ,EAAE,AAASC,KAAU,EAAE;QAChE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACK,iBAAiB,CAACP,QAAQD;IACzD;IAEA,MACMS,iBAAiB,AAAWV,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC/D,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACM,gBAAgB,CAACR,QAAQD;IACxD;IAEA,MACMU,eAAe,AAAWX,GAAQ,EAAE;QACxC,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACO,cAAc,CAACT;IAC9C;IAEA,MACMU,qBAAqB,AAAWZ,GAAQ,EAAE;QAC9C,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACQ,oBAAoB,CAACV;IACpD;IAEA,MACMW,gBAAgB,AAAWb,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC9D,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACS,eAAe,CAACX,QAAQD;IACvD;IAEA,MACMa,iBAAiB,AAAWd,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC/D,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACU,gBAAgB,CAACZ,QAAQD;IACxD;IAEA,MACMc,cAAc,AAAWf,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC5D,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACW,aAAa,CAACb,QAAQD;IACrD;IAEA,MACMe,qBAAqB,AAAWhB,GAAQ,EAAE,AAASC,KAAU,EAAE;QACnE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACY,oBAAoB,CAACd,QAAQD;IAC5D;IAEA,MACMgB,uBAAuB,AAAWjB,GAAQ,EAAE,AAASC,KAAU,EAAE;QACrE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,gBAAgB,CAACa,sBAAsB,CAACf,QAAQD;IAC9D;IApFAiB,YAAY,AAAiBd,gBAAkC,CAAE;aAApCA,mBAAAA;IAAqC;AAqFpE"}