import { Kysely, Generated, ColumnType, MysqlDialect, sql } from 'kysely';
import { createPool } from 'mysql2'
import * as dotenv from 'dotenv';

// Extend Kysely interface to include execute method
declare module 'kysely' {
  interface Kysely<DB> {
    execute(sql: string, params?: any[]): Promise<any>;
  }
}

dotenv.config();

interface SelectOptions {
  id: Generated<number>;
  area_key?: string;
  value_option?: string | null;
  sort_order?: number;
  user_id?: string | null;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface User {
  id: Generated<number>;
  name: string | null;
  email: string | null;
  password: string | null;
  username?: string | null;
  phone?: string | null;
  photo?: string | null;
  date_of_birth?: Date | null;
  height?: number | null;
  weight?: number | null;
  bodyfat?: number | null;
  goal_id?: number | null;
  activity_level_id?: number | null;
  medical_conditions?: string | null;
  allergies?: string | null;
  aff_id?: number | null;
  invite?: string | null;
  stripeId?: string | null;
  email_verification_token?: string | null;
  email_verified_at?: Date | null;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface Role {
  id: Generated<number>;
  name: string;
  description: string | null;
  created_at?: Date;
  updated_at?: Date;
}

interface UserRole {
  user_id: number;
  role_id: number;
}

interface AuthProvider {
    id: Generated<number>; // Auto-increment primary key
    name: string; // Nome do provedor (ex.: Google, Facebook)
    created_at?: Date;
    updated_at?: Date;
  }

  // Interface para a tabela `user_auths`
  interface UserAuth {
    id: Generated<number>; // Auto-increment primary key
    user_id: number; // Foreign key para `users(id)`
    provider_id: number; // Foreign key para `auth_providers(id)`
    provider_uid: string; // Identificador único do provedor
    device_uid: string | null; // Identificador do dispositivo
    refresh_token: string | null; // Token de atualização
    expire_date: Date | null; // Data de expiração
    created_at?: Date; // Data de criação
    updated_at?: Date; // Data de atualização
  }

  interface Food {
    id: Generated<number>;
    name: string;
    category_id: number;
    quantity: number;
    unit: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber: number;
    user_id?: string | null;
    created_at?: Date;
    updated_at?: Date;
    deleted_at?: Date | null;
  }



interface Exercises {
  id: Generated<number>;
  name: string;
  muscle_group_id: number;
  equipment_id: number;
  media_url: string | null;
  user_id?: string | null;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface ExercisesItems {
  id: Generated<number>;
  exercise_id: number;
  key_item: string;
  value_item: string;
}

interface Clients {
  id: Generated<number>;
  role_id: number;
  client_id: number;
  user_id: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface CoachProtocolsTemplates {
  id: Generated<number>;
  status?: number;
  name: string;
  type_id: number;
  split: string;
  frequency: number;
  objective: string;
  notes?: string;
  user_id: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface CoachProtocolsTemplatesWorkouts {
  id: Generated<number>;
  protocol_id: number;
  exercise_id: number;
  split_group: number;
  sets: number;
  reps: number;
  rpe: number;
  rest_seconds: number;
  notes?: string;
}

interface CoachProtocols {
  id: Generated<number>;
  status?: number;
  client_id: number;
  user_id?: number;
  name: string;
  type_id: number;
  split: string;
  frequency: number;
  objective: string;
  general_notes?: string;
  started_at: Date;
  ended_at?: Date | null;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface CoachProtocolsWorkouts {
  id: Generated<number>;
  protocol_id: number;
  name: string;
  general_notes?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface CoachProtocolsWorkoutsExercises {
  id: Generated<number>;
  workout_id: number;
  exercise_id?: number | null;
  name?: string | null;
  sets: number;
  reps: number;
  rpe?: number | null;
  rest_seconds?: number | null;
  notes?: string | null;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}


interface NutritionistProtocolsTemplates {
  id: Generated<number>;
  status?: number;
  name: string;
  type_id: number;
  objective: string;
  goal_calories: number;
  goal_protein: number;
  goal_carbs: number;
  goal_fat: number;
  goal_water: number;
  general_notes?: string;
  user_id: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface NutritionistProtocolsTemplatesMeals {
  id: Generated<number>;
  protocol_id: number;
  name: string;
  meal_time: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface NutritionistProtocolsTemplatesMealsFoods {
  id: Generated<number>;
  meal_id: number;
  food_id?: number;
  name: string;
  quantity: number;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface NutritionistProtocolsTemplatessupplements {
  id: Generated<number>;
  protocol_id: number;
  name: string;
  dosage: string;
  supplement_time: string;
  notes?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface NutritionistProtocols {
  id: Generated<number>;
  status?: number;
  client_id: number;
  user_id?: number; // Nullable, pois pode ser `ON DELETE SET NULL`
  name: string;
  type_id: number;
  objective?: string;
  initial_weight: number;
  goal_calories: number;
  goal_protein: number;
  goal_carbs: number;
  goal_fat: number;
  goal_water: number;
  general_notes?: string | null;
  ref_id?: number | null;
  started_at: Date;
  ended_at?: Date | null;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface NutritionistProtocolsMeals {
  id: Generated<number>;
  protocol_id: number;
  name: string;
  day_of_week: string;// 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  meal_time: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface NutritionistProtocolsMealsFoods {
  id: Generated<number>;
  meal_id: number;
  food_id?: number | null;
  name: string;
  quantity: number;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface NutritionistProtocolssupplements {
  id: Generated<number>;
  protocol_id: number;
  name: string;
  dosage: string;
  supplement_time: string;
  notes?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface DailyWater {
  id: Generated<number>;
  user_id: number;
  consumed: number;
  daily_at?: Date;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface DailyWorkoutsActivities {
  id: Generated<number>;
  user_id: number;
  activity_id: number;
  activity_time: Date | string | null;
  calories: number;
  daily_at?: Date;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface WorkoutsActivities {
  id: Generated<number>;
  name: string;
  calories: number;
  sort_order: number;
}

/*
interface DailyNutritionistProtocol {
  id: Generated<number>;
  user_id: number;
  protocol_id: number;
  meal_id: number;
  meal_time: Date;
  calories: number;
  carbs: number;
  protein: number;
  fat: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}
*/

interface DailyCoachProtocol {
  id: Generated<number>;
  user_id: number;
  protocol_id: number;
  protocol_workout_id: number;
  met: number;
  workout_time: Date;
  total_calories: number;
  total_weight?: number;
  daily_at?: Date;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface DailyCoachProtocolSeries {
  id: Generated<number>;
  daily_id: number;
  protocol_exercise_id: number;
  calories: number;
  weight?: number;
  reps?: number;
  created_at?: Date;
  updated_at?: Date;
}

interface DailyMeals {
  id: Generated<number>;
  user_id: number;
  protocol_id?: number | null;
  meal_id?: number | null;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  daily_at?: Date;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface DailyMealsFoods {
  id: Generated<number>;
  meal_id: number;
  food_id?: number;
  name: string;
  quantity: number;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface dailyMealsGoal {
  id: Generated<number>;
  user_id: number;
  goal_date: string;
  meals: number;
  meals_completed: number;
  goal_met: boolean;
  created_at?: Date;
  updated_at?: Date;
};

interface evaluations {
  id: Generated<number>;
  user_id: number;
  weight: number;
  bf: number;
  created_at?: Date;
  updated_at?: Date;
}

interface evaluationsPhotos {
  id: Generated<number>;
  evaluation_id: number;
  media_type: 'image' | 'video';
  media_position: 'front' | 'back' | 'side';
  media_url: string;
}

interface evaluationsMeasurements {
  id: Generated<number>;
  user_id: number;
  shoulders: number;
  chest: number;
  waist: number;
  abdomen: number;
  hips: number;
  biceps_right: number;
  biceps_left: number;
  forearm_right: number;
  forearm_left: number;
  thigh_right: number;
  thigh_left: number;
  calf_right: number;
  calf_left: number;
  created_at?: Date;
  updated_at?: Date;
}

// Plans
interface PaymentProvidersTable {
  id: Generated<number>;
  name: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface PlansTable {
  id: Generated<number>;
  name: string;
  description?: string | null;
  price: number;
  currency: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'annually';
  interval_value: number;
  is_active?: number | null;
  role_id: number;
  user_id: number;
  snaptokens?: number | null;
  allows_trial?: boolean | null;
  trial_period_days?: number | null;
  affiliate_master_commission_percent?: number | null;
  affiliate_commission_percent?: number | null;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface PlansPaymentsProvidersTable {
  id: Generated<number>;
  plan_id: number; // ID do plano
  platform: 'web' | 'android' | 'ios'; // Plataforma, com valor padrão 'web'
  payment_provider_id: number; // ID do provedor de pagamento

  price?: number | null; // Preço customizado
  currency?: string | null; // Moeda

  snaptokens?: number | null; // Snaptokens customizados

  payment_provider_external_id?: string | null; // Identificadores externos

  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
}

// Afiliados
interface Affiliates {
  id: Generated<number>;
  status: string;
  user_id: number;
  ref_user_id: number;
  invite: string;
  is_master?: number | null;
  stripeId?: string | null;
  accepted_at?: Date | null;
  created_at?: Date | null;
  updated_at?: Date | null;
  deleted_at?: Date | null;
}

interface AffiliateCommissions {
  id: Generated<number>;
  aff_level: number;
  aff_user_id: number;
  user_id: number;
  plan_id: number;
  transaction_id: number;
  commission_percent: number;
  commission_value: number;
  status: string;
  metadata: any;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

interface AffiliateLinks {
  id: Generated<number>;
  user_id: number;
  name: string;
  invite: string;
  link_type: 'signup_user' | 'signup_affiliate';
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

interface AffiliateLinksVisits {
  id: Generated<number>;
  link_id: number;
  created_at: Date;
}

// Assinaturas dos usuários
interface UsersSubscriptions {
  id: Generated<number>;
  user_id: number;
  plan_id: number;
  plan_payment_provider_id: number;

  payment_provider_external_id?: string | null;

  platform?: 'web' | 'android' | 'ios';

  status?: 'pending' | 'active' | 'canceled' | 'paused' | 'expired';

  price?: number | null;
  currency?: string | null;

  start_date: Date;
  end_date?: Date | null;
  next_billing_date?: Date | null;
  cancel_at_period_end?: boolean | null;

  // Trial
  is_trial?: boolean | null;
  trial_start_date?: Date | null;
  trial_end_date?: Date | null;

  snaptokens?: number | null;

  created_at?: Date | null;
  updated_at?: Date | null;
  deleted_at?: Date | null;
}

// Transações registradas
interface Transactions {
  id: Generated<number>;
  user_id: number;
  provider_transaction_id?: string | null;
  payment_provider_id: number;

  amount: number;
  currency?: string | null;

  status?: 'pending' | 'paid' | 'failed' | 'refunded';

  source_type: 'subscription' | 'invoice_item';
  source_id: number;

  created_at?: Date | null;
  updated_at?: Date | null;
  deleted_at?: Date | null;
}

// Tabelas de autenticação adicionais
interface PasswordResetTokens {
  id: Generated<number>;
  user_id: number;
  token: string;
  expires_at: Date;
  used_at?: Date | null;
  created_at?: Date;
  updated_at?: Date;
}

interface RefreshTokens {
  id: Generated<number>;
  user_id: number;
  device_uid: string;
  token: string;
  expires_at: Date;
  last_used_at?: Date | null;
  created_at?: Date;
  updated_at?: Date;
}

// Tabelas de gamificação
interface Challenges {
  id: Generated<number>;
  title: string;
  description: string;
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  target_value: number;
  target_unit: string;
  points_reward: number;
  start_date: Date;
  end_date: Date;
  is_active: boolean;
  created_at?: Date;
  updated_at?: Date;
}

interface ChallengeParticipations {
  id: Generated<number>;
  user_id: number;
  challenge_id: number;
  current_progress: number;
  status: 'active' | 'completed' | 'failed';
  completed_at?: Date | null;
  created_at?: Date;
  updated_at?: Date;
}

interface UserPoints {
  id: Generated<number>;
  user_id: number;
  points: number;
  created_at?: Date;
  updated_at?: Date;
}

interface PointsTransactions {
  id: Generated<number>;
  user_id: number;
  points: number;
  type: 'earned' | 'spent';
  description: string;
  reference_type?: string | null;
  reference_id?: number | null;
  created_at?: Date;
}

interface Rewards {
  id: Generated<number>;
  title: string;
  description: string;
  category: string;
  points_cost: number;
  stock_quantity?: number | null;
  max_redemptions_per_user?: number | null;
  is_active: boolean;
  image_url?: string | null;
  created_at?: Date;
  updated_at?: Date;
}

interface UserRewards {
  id: Generated<number>;
  user_id: number;
  reward_id: number;
  redeemed_at: Date;
  status: 'pending' | 'delivered' | 'cancelled';
  created_at?: Date;
  updated_at?: Date;
}

interface TokenUsage {
  id: Generated<number>;
  user_id: number;
  tokens_used: number;
  feature: string;
  created_at?: Date;
}

// Tabelas sociais
interface Friendships {
  id: Generated<number>;
  user_id: number;
  friend_id: number;
  status: 'pending' | 'accepted' | 'blocked';
  created_at?: Date;
  updated_at?: Date;
}

interface FriendRequests {
  id: Generated<number>;
  sender_id: number;
  receiver_id: number;
  status: 'pending' | 'accepted' | 'rejected';
  created_at?: Date;
  updated_at?: Date;
}

// Notificações
interface Notifications {
  id: Generated<number>;
  user_id: number;
  title: string;
  message: string;
  type: string;
  is_read: boolean;
  data?: any;
  created_at?: Date;
  updated_at?: Date;
}

interface NotificationSettings {
  id: Generated<number>;
  user_id: number;
  push_enabled: boolean;
  email_enabled: boolean;
  sms_enabled: boolean;
  challenge_notifications: boolean;
  friend_notifications: boolean;
  reward_notifications: boolean;
  created_at?: Date;
  updated_at?: Date;
}

// Wearables
interface WearableDevices {
  id: Generated<number>;
  user_id: number;
  device_type: string;
  device_name: string;
  device_id: string;
  is_active: boolean;
  last_sync_at?: Date | null;
  created_at?: Date;
  updated_at?: Date;
}

interface WearableData {
  id: Generated<number>;
  device_id: number;
  user_id: number;
  data_type: string;
  value: number;
  unit: string;
  recorded_at: Date;
  synced_at?: Date;
  created_at?: Date;
}

interface Database {
  select_options: SelectOptions;
  users: User;
  roles: Role;
  users_roles: UserRole;
  auth_providers: AuthProvider;
  user_auths: UserAuth;
  foods: Food;
  exercises: Exercises;
  exercises_items: ExercisesItems;
  clients: Clients;
  coach_protocols: CoachProtocols;
  coach_protocols_workouts: CoachProtocolsWorkouts;
  coach_protocols_workouts_exercises: CoachProtocolsWorkoutsExercises;
  coach_protocols_templates: CoachProtocolsTemplates;
  coach_protocols_templates_workouts: CoachProtocolsTemplatesWorkouts;
  nutritionist_protocols: NutritionistProtocols;
  nutritionist_protocols_meals: NutritionistProtocolsMeals;
  nutritionist_protocols_meals_foods: NutritionistProtocolsMealsFoods;
  nutritionist_protocols_supplements: NutritionistProtocolssupplements;
  nutritionist_protocols_templates: NutritionistProtocolsTemplates;
  nutritionist_protocols_templates_meals: NutritionistProtocolsTemplatesMeals;
  nutritionist_protocols_templates_meals_foods: NutritionistProtocolsTemplatesMealsFoods;
  nutritionist_protocols_templates_supplements: NutritionistProtocolsTemplatessupplements;
  workouts_activities: WorkoutsActivities;
  daily_workouts_activities: DailyWorkoutsActivities;
  daily_coach_protocol: DailyCoachProtocol;
  daily_coach_protocol_series: DailyCoachProtocolSeries;
  // daily_nutritionist_protocol: DailyNutritionistProtocol;
  daily_meals: DailyMeals;
  daily_meals_foods: DailyMealsFoods;
  daily_meals_goal: dailyMealsGoal;
  daily_water: DailyWater;
  evaluations: evaluations;
  evaluations_photos: evaluationsPhotos;
  evaluations_measurements: evaluationsMeasurements;
  // Plans
  payment_providers: PaymentProvidersTable;
  plans: PlansTable;
  plans_payments_providers: PlansPaymentsProvidersTable;
  users_subscriptions: UsersSubscriptions;
  transactions: Transactions;
  // Afiliados
  affiliates: Affiliates;
  affiliate_commissions: AffiliateCommissions;
  affiliate_links: AffiliateLinks;
  affiliate_links_visits: AffiliateLinksVisits;
  // Autenticação adicional
  password_reset_tokens: PasswordResetTokens;
  refresh_tokens: RefreshTokens;
  // Gamificação
  challenges: Challenges;
  challenge_participations: ChallengeParticipations;
  user_points: UserPoints;
  points_transactions: PointsTransactions;
  rewards: Rewards;
  user_rewards: UserRewards;
  token_usage: TokenUsage;
  // Social
  friendships: Friendships;
  friend_requests: FriendRequests;
  // Notificações
  notifications: Notifications;
  notification_settings: NotificationSettings;
  // Wearables
  wearable_devices: WearableDevices;
  wearable_data: WearableData;
}

export const db = new Kysely<Database>({
  dialect: new MysqlDialect({
    pool: createPool({
      host: process.env.DB_HOST,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      timezone: '+00:00',
    }),
  }),
});

// Helper function to execute raw SQL with parameters
export async function executeRawSql(sqlQuery: string, params: any[] = []): Promise<any> {
  // For Kysely, we need to use sql template literals
  // This is a simplified approach - in production you'd want more sophisticated parameter binding
  let processedSql = sqlQuery;

  // Replace ? placeholders with actual values (be careful with SQL injection)
  params.forEach((param) => {
    if (typeof param === 'string') {
      processedSql = processedSql.replace('?', `'${param.replace(/'/g, "''")}'`);
    } else if (param === null || param === undefined) {
      processedSql = processedSql.replace('?', 'NULL');
    } else if (typeof param === 'number') {
      // Numbers should not be quoted
      processedSql = processedSql.replace('?', String(param));
    } else {
      // For other types, convert to string without quotes
      processedSql = processedSql.replace('?', String(param));
    }
  });

  // Execute the raw SQL
  const result = await sql.raw(processedSql).execute(db);
  return result.rows;
}

// Add a temporary compatibility layer for db.execute
(db as any).execute = executeRawSql;

// Export Database type for use in other files
export type { Database };