{"version": 3, "sources": ["../../src/dashboard/dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { db } from '../database';\r\nimport { sql } from 'kysely';\r\n\r\n// TypeScript interfaces for dashboard data structures\r\nexport interface ChartDataPoint {\r\n    label: string;\r\n    training_volume: number;\r\n}\r\n\r\nexport interface ExerciseProgressData {\r\n    name: string;\r\n    current: number;\r\n    previous: number;\r\n    change: number;\r\n    history: any;\r\n}\r\n\r\n@Injectable()\r\nexport class DashboardService {\r\n\r\n    /**\r\n     * Get user's weight progress data\r\n     */\r\n    async getWeightProgress(userId: number, period: string = 'month') {\r\n        try {\r\n            const { startDate, endDate } = this.getPeriodDates(period);\r\n\r\n            // Get weight data from evaluations table\r\n            const weightData = await db\r\n                .selectFrom('evaluations')\r\n                .select(['weight', 'bf', 'created_at as date'])\r\n                .where('user_id', '=', userId)\r\n                .where('created_at', '>=', startDate)\r\n                .where('created_at', '<=', endDate)\r\n                .orderBy('created_at', 'asc')\r\n                .execute();\r\n\r\n            // Get current and goal weight\r\n            const user = await db\r\n                .selectFrom('users')\r\n                .select(['weight', 'goal_id'])\r\n                .where('id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            // Calculate trend and change with proper type checking\r\n            const currentRaw = weightData.length > 0 ? weightData[weightData.length - 1].weight : user?.weight || 0;\r\n            const previousRaw = weightData.length > 1 ? weightData[0].weight : currentRaw;\r\n\r\n            // Ensure values are numbers (database might return strings for DECIMAL columns)\r\n            const current = typeof currentRaw === 'string' ? parseFloat(currentRaw) : Number(currentRaw) || 0;\r\n            const previous = typeof previousRaw === 'string' ? parseFloat(previousRaw) : Number(previousRaw) || 0;\r\n            const change = current - previous;\r\n            const trend = change > 0.5 ? 'increasing' : change < -0.5 ? 'decreasing' : 'stable';\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    current: parseFloat(current.toFixed(1)),\r\n                    goal: 70, // TODO: Get from user goals\r\n                    change: parseFloat(change.toFixed(1)),\r\n                    trend,\r\n                    history: weightData.map(item => {\r\n                        // Ensure weight and bf are numbers before calling toFixed\r\n                        const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;\r\n                        const bf = item.bf ? (typeof item.bf === 'string' ? parseFloat(item.bf) : Number(item.bf)) : null;\r\n\r\n                        return {\r\n                            date: item.date,\r\n                            weight: parseFloat(weight.toFixed(1)),\r\n                            bodyFat: bf ? parseFloat(bf.toFixed(1)) : null\r\n                        };\r\n                    })\r\n                }\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting weight progress:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get user's analytics overview\r\n     */\r\n    async getAnalyticsOverview(userId: number, period: string = 'month') {\r\n        try {\r\n            const { startDate, endDate } = this.getPeriodDates(period);\r\n\r\n            // Get nutrition adherence\r\n            const nutritionData = await db\r\n                .selectFrom('daily_meals_goal')\r\n                .select(['meals', 'meals_completed', 'goal_met'])\r\n                .where('user_id', '=', userId)\r\n                .where('goal_date', '>=', startDate.toISOString().split('T')[0])\r\n                .where('goal_date', '<=', endDate.toISOString().split('T')[0])\r\n                .execute();\r\n\r\n            // Get workout consistency\r\n            const workoutData = await db\r\n                .selectFrom('daily_workouts_activities')\r\n                .select(['id', 'calories'])\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startDate)\r\n                .where('daily_at', '<=', endDate)\r\n                .execute();\r\n\r\n            // Get strength gains (from coach protocol series)\r\n            const strengthData = await db\r\n                .selectFrom('daily_coach_protocol_series')\r\n                .innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id')\r\n                .select(['daily_coach_protocol_series.weight', 'daily_coach_protocol_series.reps'])\r\n                .where('daily_coach_protocol.user_id', '=', userId)\r\n                .where('daily_coach_protocol.daily_at', '>=', startDate)\r\n                .where('daily_coach_protocol.daily_at', '<=', endDate)\r\n                .execute();\r\n\r\n            // Calculate metrics\r\n            const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));\r\n            const nutritionAdherence = nutritionData.length > 0 ?\r\n                nutritionData.reduce((sum, day) => sum + (day.goal_met ? 1 : 0), 0) / nutritionData.length * 100 : 0;\r\n            const workoutConsistency = (workoutData.length / totalDays) * 100;\r\n            const strengthGains = strengthData.length > 0 ?\r\n                ((strengthData[strengthData.length - 1]?.weight || 0) - (strengthData[0]?.weight || 0)) : 0;\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    weightTrend: 'stable', // Will be calculated from weight progress\r\n                    calorieAdherence: Math.round(nutritionAdherence),\r\n                    workoutConsistency: Math.round(workoutConsistency),\r\n                    strengthGains: Math.round(strengthGains * 100) / 100,\r\n                    bodyFatPercentage: 18.2, // TODO: Get from latest evaluation\r\n                    recommendations: [\r\n                        'Manter consistência nos treinos',\r\n                        'Aumentar ingestão de proteínas',\r\n                        'Focar em exercícios compostos'\r\n                    ]\r\n                }\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting analytics overview:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get workout analytics data\r\n     */\r\n    async getWorkoutAnalytics(userId: number, period: string = 'week') {\r\n        try {\r\n            const { startDate, endDate } = this.getPeriodDates(period);\r\n\r\n            // Get workout data from daily_workouts_activities\r\n            const workouts = await db\r\n                .selectFrom('daily_workouts_activities')\r\n                .select(['daily_at', 'calories'])\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startDate)\r\n                .where('daily_at', '<=', endDate)\r\n                .orderBy('daily_at', 'asc')\r\n                .execute();\r\n\r\n            // Get training volume from coach protocol series\r\n            const volumeData = await db\r\n                .selectFrom('daily_coach_protocol_series')\r\n                .innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id')\r\n                .select(['daily_coach_protocol.daily_at', 'daily_coach_protocol_series.weight', 'daily_coach_protocol_series.reps'])\r\n                .where('daily_coach_protocol.user_id', '=', userId)\r\n                .where('daily_coach_protocol.daily_at', '>=', startDate)\r\n                .where('daily_coach_protocol.daily_at', '<=', endDate)\r\n                .execute();\r\n\r\n            // Calculate training volume by date with proper type checking\r\n            const volumeByDate = volumeData.reduce((acc, item) => {\r\n                const date = item.daily_at ? item.daily_at.toISOString().split('T')[0] : '';\r\n                // Ensure weight and reps are numbers\r\n                const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;\r\n                const reps = typeof item.reps === 'string' ? parseFloat(item.reps) : Number(item.reps) || 0;\r\n                const volume = weight * reps * 3; // Assume 3 sets\r\n                if (date) {\r\n                    acc[date] = (acc[date] || 0) + volume;\r\n                }\r\n                return acc;\r\n            }, {} as Record<string, number>);\r\n\r\n            // Format chart data based on period\r\n            const chartData = this.formatChartData(period, startDate, endDate, volumeByDate);\r\n\r\n            // Calculate totals\r\n            const totalWorkouts = workouts.length;\r\n            const totalMinutes = totalWorkouts * 45; // Estimate 45 minutes per workout\r\n            const totalCalories = workouts.reduce((sum, w) => sum + (w.calories || 0), 0);\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    chart: chartData,\r\n                    workouts: totalWorkouts,\r\n                    minutes: totalMinutes,\r\n                    calories: totalCalories,\r\n                    period: period\r\n                }\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting workout analytics:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get nutrition analytics data\r\n     */\r\n    async getNutritionAnalytics(userId: number, period: string = 'week') {\r\n        try {\r\n            const { startDate, endDate } = this.getPeriodDates(period);\r\n\r\n            // Get nutrition data from daily_meals table instead\r\n            const nutritionData = await db\r\n                .selectFrom('daily_meals')\r\n                .select(['calories', 'protein', 'carbs', 'fat'])\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startDate)\r\n                .where('daily_at', '<=', endDate)\r\n                .execute();\r\n\r\n            // Calculate averages\r\n            const totalDays = nutritionData.length || 1;\r\n            const avgCalories = nutritionData.reduce((sum, day) => sum + (day.calories || 0), 0) / totalDays;\r\n            const avgProtein = nutritionData.reduce((sum, day) => sum + (day.protein || 0), 0) / totalDays;\r\n            const avgCarbs = nutritionData.reduce((sum, day) => sum + (day.carbs || 0), 0) / totalDays;\r\n            const avgFat = nutritionData.reduce((sum, day) => sum + (day.fat || 0), 0) / totalDays;\r\n\r\n            // Calculate percentages (assuming 2000 cal target)\r\n            const targetCalories = 2000;\r\n            const caloriesPercent = Math.round((avgCalories / targetCalories) * 100);\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    calories: Math.round(avgCalories),\r\n                    calories_percent: caloriesPercent,\r\n                    carbs: Math.round(avgCarbs),\r\n                    carbs_percent: Math.round((avgCarbs * 4 / avgCalories) * 100),\r\n                    protein: Math.round(avgProtein),\r\n                    protein_percent: Math.round((avgProtein * 4 / avgCalories) * 100),\r\n                    fat: Math.round(avgFat),\r\n                    fat_percent: Math.round((avgFat * 9 / avgCalories) * 100)\r\n                }\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting nutrition analytics:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get caloric balance data\r\n     */\r\n    async getCaloricBalance(userId: number, period: string = 'week') {\r\n        try {\r\n            const { startDate, endDate } = this.getPeriodDates(period);\r\n\r\n            // Get daily nutrition and workout data\r\n            const nutritionData = await db\r\n                .selectFrom('daily_meals')\r\n                .select(['daily_at', 'calories'])\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startDate)\r\n                .where('daily_at', '<=', endDate)\r\n                .execute();\r\n\r\n            const workoutData = await db\r\n                .selectFrom('daily_workouts_activities')\r\n                .select(['daily_at', 'calories'])\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startDate)\r\n                .where('daily_at', '<=', endDate)\r\n                .execute();\r\n\r\n            // Combine data by date\r\n            const dataByDate = this.combineCaloricData(nutritionData, workoutData, startDate, endDate);\r\n\r\n            return {\r\n                status: 'success',\r\n                data: dataByDate\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting caloric balance:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get strength progress data\r\n     */\r\n    async getStrengthProgress(userId: number, period: string = 'month') {\r\n        try {\r\n            const { startDate, endDate } = this.getPeriodDates(period);\r\n\r\n            // Get strength data from coach protocol series\r\n            const strengthData = await db\r\n                .selectFrom('daily_coach_protocol_series')\r\n                .innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id')\r\n                .select([\r\n                    'daily_coach_protocol_series.weight',\r\n                    'daily_coach_protocol_series.reps',\r\n                    'daily_coach_protocol.daily_at'\r\n                ])\r\n                .where('daily_coach_protocol.user_id', '=', userId)\r\n                .where('daily_coach_protocol.daily_at', '>=', startDate)\r\n                .where('daily_coach_protocol.daily_at', '<=', endDate)\r\n                .orderBy('daily_coach_protocol.daily_at', 'asc')\r\n                .execute();\r\n\r\n            // Group by exercise and calculate progress\r\n            const exerciseProgress = this.calculateExerciseProgress(strengthData);\r\n            const totalVolume = strengthData.reduce((sum, item) => {\r\n                // Ensure weight and reps are numbers\r\n                const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;\r\n                const reps = typeof item.reps === 'string' ? parseFloat(item.reps) : Number(item.reps) || 0;\r\n                return sum + (weight * reps);\r\n            }, 0);\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    exercises: exerciseProgress,\r\n                    total_volume: totalVolume,\r\n                    volume_change: 150, // TODO: Calculate actual change\r\n                    personal_records: [], // TODO: Calculate PRs\r\n                    period: period\r\n                }\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting strength progress:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get body composition data\r\n     */\r\n    async getBodyComposition(userId: number, period: string = 'month') {\r\n        try {\r\n            const { startDate, endDate } = this.getPeriodDates(period);\r\n\r\n            // Get body composition data from evaluations\r\n            const evaluations = await db\r\n                .selectFrom('evaluations')\r\n                .select(['weight', 'bf', 'created_at as date'])\r\n                .where('user_id', '=', userId)\r\n                .where('created_at', '>=', startDate)\r\n                .where('created_at', '<=', endDate)\r\n                .orderBy('created_at', 'asc')\r\n                .execute();\r\n\r\n            // Get measurements data\r\n            const measurements = await db\r\n                .selectFrom('evaluations_measurements')\r\n                .selectAll()\r\n                .where('user_id', '=', userId)\r\n                .where('created_at', '>=', startDate)\r\n                .where('created_at', '<=', endDate)\r\n                .orderBy('created_at', 'asc')\r\n                .execute();\r\n\r\n            // Format data for charts with proper type checking\r\n            const weightData = evaluations.map(item => {\r\n                // Ensure weight and bf are numbers before calling toFixed\r\n                const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;\r\n                const bf = item.bf ? (typeof item.bf === 'string' ? parseFloat(item.bf) : Number(item.bf)) : null;\r\n\r\n                return {\r\n                    date: item.date,\r\n                    weight: parseFloat(weight.toFixed(1)),\r\n                    bodyFat: bf ? parseFloat(bf.toFixed(1)) : null\r\n                };\r\n            });\r\n\r\n            const measurementData = measurements.map(item => ({\r\n                date: item.created_at,\r\n                chest: item.chest,\r\n                waist: item.waist,\r\n                hips: item.hips,\r\n                biceps: (item.biceps_right + item.biceps_left) / 2,\r\n                thigh: (item.thigh_right + item.thigh_left) / 2\r\n            }));\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    weight_progress: weightData,\r\n                    measurements_progress: measurementData,\r\n                    period: period\r\n                }\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting body composition:', error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Helper method to get period dates\r\n     */\r\n    private getPeriodDates(period: string): { startDate: Date; endDate: Date } {\r\n        const endDate = new Date();\r\n        const startDate = new Date();\r\n\r\n        switch (period) {\r\n            case 'week':\r\n                startDate.setDate(endDate.getDate() - 7);\r\n                break;\r\n            case 'month':\r\n                startDate.setMonth(endDate.getMonth() - 1);\r\n                break;\r\n            case 'semester':\r\n                startDate.setMonth(endDate.getMonth() - 6);\r\n                break;\r\n            case 'year':\r\n                startDate.setFullYear(endDate.getFullYear() - 1);\r\n                break;\r\n            case 'all':\r\n                startDate.setFullYear(endDate.getFullYear() - 2);\r\n                break;\r\n            default:\r\n                startDate.setMonth(endDate.getMonth() - 1);\r\n        }\r\n\r\n        return { startDate, endDate };\r\n    }\r\n\r\n    /**\r\n     * Helper method to format chart data based on period\r\n     */\r\n    private formatChartData(period: string, startDate: Date, endDate: Date, volumeByDate: Record<string, number>): ChartDataPoint[] {\r\n        const chartData: ChartDataPoint[] = [];\r\n\r\n        switch (period) {\r\n            case 'week':\r\n                const days = ['sáb', 'dom', 'seg', 'ter', 'qua', 'qui', 'sex'];\r\n                for (let i = 0; i < 7; i++) {\r\n                    const date = new Date(startDate);\r\n                    date.setDate(startDate.getDate() + i);\r\n                    const dateStr = date.toISOString().split('T')[0];\r\n                    chartData.push({\r\n                        label: days[i],\r\n                        training_volume: volumeByDate[dateStr] || 0\r\n                    });\r\n                }\r\n                break;\r\n            default:\r\n                // For other periods, use actual dates\r\n                const currentDate = new Date(startDate);\r\n                while (currentDate <= endDate) {\r\n                    const dateStr = currentDate.toISOString().split('T')[0];\r\n                    chartData.push({\r\n                        label: dateStr,\r\n                        training_volume: volumeByDate[dateStr] || 0\r\n                    });\r\n                    currentDate.setDate(currentDate.getDate() + 1);\r\n                }\r\n        }\r\n\r\n        return chartData;\r\n    }\r\n\r\n    /**\r\n     * Helper method to combine caloric data\r\n     */\r\n    private combineCaloricData(nutritionData: any[], workoutData: any[], startDate: Date, endDate: Date) {\r\n        const dataByDate: any[] = [];\r\n        const currentDate = new Date(startDate);\r\n\r\n        while (currentDate <= endDate) {\r\n            const dateStr = currentDate.toISOString().split('T')[0];\r\n            const nutrition = nutritionData.find(n => n.date === dateStr);\r\n            const workout = workoutData.find(w => w.date === dateStr);\r\n\r\n            dataByDate.push({\r\n                date: dateStr,\r\n                consumed: nutrition?.goal_calories || 0,\r\n                burned: workout?.calories_burned || 0,\r\n                balance: (nutrition?.goal_calories || 0) - (workout?.calories_burned || 0)\r\n            });\r\n\r\n            currentDate.setDate(currentDate.getDate() + 1);\r\n        }\r\n\r\n        return dataByDate;\r\n    }\r\n\r\n    /**\r\n     * Helper method to calculate exercise progress\r\n     */\r\n    private calculateExerciseProgress(strengthData: any[]): ExerciseProgressData[] {\r\n        const exerciseMap = new Map();\r\n\r\n        strengthData.forEach(item => {\r\n            const exerciseName = `Exercise ${item.daily_at}`;\r\n            if (!exerciseMap.has(exerciseName)) {\r\n                exerciseMap.set(exerciseName, []);\r\n            }\r\n\r\n            // Ensure weight and reps are numbers\r\n            const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;\r\n            const reps = typeof item.reps === 'string' ? parseFloat(item.reps) : Number(item.reps) || 0;\r\n\r\n            exerciseMap.get(exerciseName).push({\r\n                weight: weight,\r\n                reps: reps,\r\n                date: item.daily_at\r\n            });\r\n        });\r\n\r\n        const exerciseProgress: ExerciseProgressData[] = [];\r\n        exerciseMap.forEach((data, exerciseName) => {\r\n            const sortedData = data.sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime());\r\n\r\n            // Ensure weight values are numbers\r\n            const currentRaw = sortedData[sortedData.length - 1]?.weight || 0;\r\n            const previousRaw = sortedData[0]?.weight || 0;\r\n            const current = typeof currentRaw === 'string' ? parseFloat(currentRaw) : Number(currentRaw) || 0;\r\n            const previous = typeof previousRaw === 'string' ? parseFloat(previousRaw) : Number(previousRaw) || 0;\r\n\r\n            exerciseProgress.push({\r\n                name: exerciseName,\r\n                current: current,\r\n                previous: previous,\r\n                change: current - previous,\r\n                history: sortedData\r\n            });\r\n        });\r\n\r\n        return exerciseProgress;\r\n    }\r\n\r\n    /**\r\n     * Get workout dashboard data\r\n     */\r\n    async getWorkoutDashboardData(userId: number) {\r\n        try {\r\n            console.log('🏋️ getWorkoutDashboardData: Getting workout dashboard data for user:', userId);\r\n\r\n            // Check if user has active workout protocol\r\n            const activeProtocol = await db\r\n                .selectFrom('coach_protocols')\r\n                .selectAll()\r\n                .where('client_id', '=', userId)\r\n                .where('started_at', '<=', new Date())\r\n                .where((eb) => eb.or([\r\n                    eb('ended_at', 'is', null),\r\n                    eb('ended_at', '>', new Date())\r\n                ]))\r\n                .executeTakeFirst();\r\n\r\n            const hasProtocol = !!activeProtocol;\r\n            const protocolFrequency = activeProtocol?.frequency || 0;\r\n\r\n            // Get workout statistics for the last 30 days\r\n            const thirtyDaysAgo = new Date();\r\n            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n\r\n            const workoutStats = await db\r\n                .selectFrom('daily_coach_protocol')\r\n                .select([\r\n                    db.fn.count('id').as('total_workouts'),\r\n                    db.fn.sum('workout_time').as('total_minutes'),\r\n                    db.fn.sum('total_calories').as('total_calories')\r\n                ])\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', thirtyDaysAgo)\r\n                .executeTakeFirst();\r\n\r\n            // Get weekly frequency (last 7 days)\r\n            const sevenDaysAgo = new Date();\r\n            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\r\n\r\n            const weeklyWorkouts = await db\r\n                .selectFrom('daily_coach_protocol')\r\n                .select(db.fn.count('id').as('weekly_workouts'))\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', sevenDaysAgo)\r\n                .executeTakeFirst();\r\n\r\n            // Get last workout\r\n            const lastWorkout = await db\r\n                .selectFrom('daily_coach_protocol as dcp')\r\n                .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')\r\n                .select([\r\n                    'dcp.daily_at',\r\n                    'cpw.name as workout_name',\r\n                    'dcp.workout_time',\r\n                    'dcp.total_calories'\r\n                ])\r\n                .where('dcp.user_id', '=', userId)\r\n                .orderBy('dcp.daily_at', 'desc')\r\n                .executeTakeFirst();\r\n\r\n            // Calculate current streak (consecutive days with workouts)\r\n            const allWorkoutDays = await db\r\n                .selectFrom('daily_coach_protocol')\r\n                .select(['daily_at'])\r\n                .where('user_id', '=', userId)\r\n                .orderBy('daily_at', 'desc')\r\n                .execute();\r\n\r\n            let currentStreak = 0;\r\n            let bestStreak = 0;\r\n            let tempStreak = 0;\r\n\r\n            if (allWorkoutDays.length > 0) {\r\n                const workoutDates = allWorkoutDays.map(w =>\r\n                    new Date(w.daily_at || new Date()).toDateString()\r\n                );\r\n                const uniqueDates = [...new Set(workoutDates)].sort((a, b) =>\r\n                    new Date(b).getTime() - new Date(a).getTime()\r\n                );\r\n\r\n                // Calculate current streak\r\n                const today = new Date().toDateString();\r\n                const yesterday = new Date();\r\n                yesterday.setDate(yesterday.getDate() - 1);\r\n                const yesterdayStr = yesterday.toDateString();\r\n\r\n                if (uniqueDates.includes(today) || uniqueDates.includes(yesterdayStr)) {\r\n                    let checkDate = new Date();\r\n                    for (let i = 0; i < uniqueDates.length; i++) {\r\n                        const dateStr = checkDate.toDateString();\r\n                        if (uniqueDates.includes(dateStr)) {\r\n                            currentStreak++;\r\n                            checkDate.setDate(checkDate.getDate() - 1);\r\n                        } else {\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                // Calculate best streak\r\n                tempStreak = 1;\r\n                for (let i = 1; i < uniqueDates.length; i++) {\r\n                    const currentDate = new Date(uniqueDates[i]);\r\n                    const previousDate = new Date(uniqueDates[i - 1]);\r\n                    const dayDiff = Math.abs(previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);\r\n\r\n                    if (dayDiff === 1) {\r\n                        tempStreak++;\r\n                    } else {\r\n                        bestStreak = Math.max(bestStreak, tempStreak);\r\n                        tempStreak = 1;\r\n                    }\r\n                }\r\n                bestStreak = Math.max(bestStreak, tempStreak);\r\n            }\r\n\r\n            // Calculate progress percentages\r\n            const weeklyGoal = protocolFrequency || 3; // Use protocol frequency or default to 3\r\n            const weeklyFrequency = Number(weeklyWorkouts?.weekly_workouts || 0);\r\n            const weeklyProgressPercentage = Math.min(100, (weeklyFrequency / weeklyGoal) * 100);\r\n\r\n            // Calculate overall protocol completion percentage\r\n            let protocolCompletionPercentage = 0;\r\n            if (activeProtocol && protocolFrequency > 0) {\r\n                const protocolStartDate = new Date(activeProtocol.started_at);\r\n                const currentDate = new Date();\r\n                const daysSinceStart = Math.floor((currentDate.getTime() - protocolStartDate.getTime()) / (1000 * 60 * 60 * 24));\r\n                const weeksSinceStart = Math.max(1, Math.ceil(daysSinceStart / 7)); // At least 1 week\r\n\r\n                const expectedTotalWorkouts = weeksSinceStart * protocolFrequency;\r\n                const actualTotalWorkouts = Number(workoutStats?.total_workouts || 0);\r\n\r\n                protocolCompletionPercentage = expectedTotalWorkouts > 0\r\n                    ? Math.min(100, (actualTotalWorkouts / expectedTotalWorkouts) * 100)\r\n                    : 0;\r\n            }\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    has_protocol: hasProtocol,\r\n                    protocol_frequency: protocolFrequency,\r\n                    total_workouts: Number(workoutStats?.total_workouts || 0),\r\n                    total_minutes: Number(workoutStats?.total_minutes || 0),\r\n                    total_calories: Number(workoutStats?.total_calories || 0),\r\n                    weekly_frequency: weeklyFrequency,\r\n                    current_streak: currentStreak,\r\n                    best_streak: bestStreak,\r\n                    last_workout: lastWorkout ? {\r\n                        date: lastWorkout.daily_at,\r\n                        name: lastWorkout.workout_name,\r\n                        duration: lastWorkout.workout_time,\r\n                        calories: lastWorkout.total_calories\r\n                    } : null,\r\n                    next_workout: hasProtocol ? 'Próximo treino disponível' : null,\r\n                    weekly_progress_percentage: Math.round(weeklyProgressPercentage),\r\n                    protocol_completion_percentage: Math.round(protocolCompletionPercentage)\r\n                }\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('❌ Error getting workout dashboard data:', error);\r\n            // Return default data on error\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    has_protocol: false,\r\n                    protocol_frequency: 0,\r\n                    total_workouts: 0,\r\n                    total_minutes: 0,\r\n                    total_calories: 0,\r\n                    weekly_frequency: 0,\r\n                    current_streak: 0,\r\n                    best_streak: 0,\r\n                    last_workout: null,\r\n                    next_workout: null,\r\n                    weekly_progress_percentage: 0,\r\n                    protocol_completion_percentage: 0\r\n                }\r\n            };\r\n        }\r\n    }\r\n\r\n    async getNutritionalSummary(userId: number, query: any) {\r\n        const today = query.date ? new Date(query.date) : new Date();\r\n        const dateStr = today.toISOString().split('T')[0];\r\n\r\n        console.log('🔄 getNutritionalSummary: Buscando resumo nutricional para usuário', userId, 'data:', dateStr);\r\n\r\n        try {\r\n            // 1. Buscar protocolo ativo para obter metas\r\n            const protocol = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .select([\r\n                    'p.goal_calories as calories',\r\n                    'p.goal_protein as protein',\r\n                    'p.goal_carbs as carbs',\r\n                    'p.goal_fat as fat',\r\n                    'p.goal_water as water'\r\n                ])\r\n                .where('p.client_id', '=', userId)\r\n                .where('p.started_at', '<=', today)\r\n                .where('p.ended_at', 'is', null)\r\n                .orderBy('p.started_at', 'desc')\r\n                .executeTakeFirst();\r\n\r\n            // 2. Buscar refeições consumidas no dia\r\n            const startOfDay = new Date(today);\r\n            startOfDay.setHours(0, 0, 0, 0);\r\n            const endOfDay = new Date(today);\r\n            endOfDay.setHours(23, 59, 59, 999);\r\n\r\n            const consumedMeals = await db\r\n                .selectFrom('daily_meals as dm')\r\n                .leftJoin('daily_meals_foods as dmf', 'dmf.meal_id', 'dm.id')\r\n                .select([\r\n                    sql`SUM(COALESCE(dmf.calories, dm.calories))`.as('total_calories'),\r\n                    sql`SUM(COALESCE(dmf.protein, dm.protein))`.as('total_protein'),\r\n                    sql`SUM(COALESCE(dmf.carbs, dm.carbs))`.as('total_carbs'),\r\n                    sql`SUM(COALESCE(dmf.fat, dm.fat))`.as('total_fat')\r\n                ])\r\n                .where('dm.user_id', '=', userId)\r\n                .where('dm.daily_at', '>=', startOfDay)\r\n                .where('dm.daily_at', '<=', endOfDay)\r\n                .executeTakeFirst();\r\n\r\n            // 3. Buscar calorias queimadas (treinos do dia)\r\n            const caloriesBurned = await db\r\n                .selectFrom('daily_workouts_activities')\r\n                .select([\r\n                    sql`SUM(calories)`.as('total_burned')\r\n                ])\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startOfDay)\r\n                .where('daily_at', '<=', endOfDay)\r\n                .executeTakeFirst();\r\n\r\n            // 4. Calcular valores\r\n            const targets = {\r\n                calories: Number(protocol?.calories) || 2000,\r\n                protein: Number(protocol?.protein) || 150,\r\n                carbs: Number(protocol?.carbs) || 220,\r\n                fat: Number(protocol?.fat) || 70\r\n            };\r\n\r\n            const consumed = {\r\n                calories: Number(consumedMeals?.total_calories) || 0,\r\n                protein: Number(consumedMeals?.total_protein) || 0,\r\n                carbs: Number(consumedMeals?.total_carbs) || 0,\r\n                fat: Number(consumedMeals?.total_fat) || 0\r\n            };\r\n\r\n            const burned = Number(caloriesBurned?.total_burned) || 0;\r\n            const remaining = {\r\n                calories: Math.max(0, targets.calories - consumed.calories),\r\n                protein: Math.max(0, targets.protein - consumed.protein),\r\n                carbs: Math.max(0, targets.carbs - consumed.carbs),\r\n                fat: Math.max(0, targets.fat - consumed.fat)\r\n            };\r\n\r\n            console.log('✅ getNutritionalSummary: Resumo calculado:', {\r\n                targets,\r\n                consumed,\r\n                burned,\r\n                remaining\r\n            });\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    calories: {\r\n                        current: consumed.calories,\r\n                        target: targets.calories,\r\n                        remaining: remaining.calories,\r\n                        burned: burned\r\n                    },\r\n                    protein: {\r\n                        current: consumed.protein,\r\n                        target: targets.protein\r\n                    },\r\n                    carbs: {\r\n                        current: consumed.carbs,\r\n                        target: targets.carbs\r\n                    },\r\n                    fat: {\r\n                        current: consumed.fat,\r\n                        target: targets.fat\r\n                    },\r\n                    date: dateStr\r\n                }\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('❌ Erro ao buscar resumo nutricional:', error);\r\n\r\n            // Fallback para dados padrão em caso de erro\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    calories: {\r\n                        current: 0,\r\n                        target: 2000,\r\n                        remaining: 2000,\r\n                        burned: 0\r\n                    },\r\n                    protein: {\r\n                        current: 0,\r\n                        target: 150\r\n                    },\r\n                    carbs: {\r\n                        current: 0,\r\n                        target: 220\r\n                    },\r\n                    fat: {\r\n                        current: 0,\r\n                        target: 70\r\n                    },\r\n                    date: dateStr\r\n                }\r\n            };\r\n        }\r\n    }\r\n\r\n}\r\n"], "names": ["DashboardService", "getWeightProgress", "userId", "period", "startDate", "endDate", "getPeriodDates", "weightData", "db", "selectFrom", "select", "where", "orderBy", "execute", "user", "executeTakeFirst", "currentRaw", "length", "weight", "previousRaw", "current", "parseFloat", "Number", "previous", "change", "trend", "status", "data", "toFixed", "goal", "history", "map", "item", "bf", "date", "bodyFat", "error", "console", "getAnalyticsOverview", "nutritionData", "toISOString", "split", "workoutData", "strengthData", "innerJoin", "totalDays", "Math", "ceil", "getTime", "nutritionAdherence", "reduce", "sum", "day", "goal_met", "workoutConsistency", "strengthGains", "weightTrend", "calorieAdherence", "round", "bodyFatPercentage", "recommendations", "getWorkoutAnalytics", "workouts", "volumeData", "volumeByDate", "acc", "daily_at", "reps", "volume", "chartData", "formatChartData", "totalWorkouts", "totalMinutes", "totalCalories", "w", "calories", "chart", "minutes", "getNutritionAnalytics", "avgCalories", "avgProtein", "protein", "avgCarbs", "carbs", "avgFat", "fat", "targetCalories", "caloriesPercent", "calories_percent", "carbs_percent", "protein_percent", "fat_percent", "getCaloricBalance", "dataByDate", "combineCaloricData", "getStrengthProgress", "exerciseProgress", "calculateExerciseProgress", "totalVolume", "exercises", "total_volume", "volume_change", "personal_records", "getBodyComposition", "evaluations", "measurements", "selectAll", "measurementData", "created_at", "chest", "waist", "hips", "biceps", "biceps_right", "biceps_left", "thigh", "thigh_right", "thigh_left", "weight_progress", "measurements_progress", "Date", "setDate", "getDate", "setMonth", "getMonth", "setFullYear", "getFullYear", "days", "i", "dateStr", "push", "label", "training_volume", "currentDate", "nutrition", "find", "n", "workout", "consumed", "goal_calories", "burned", "calories_burned", "balance", "exerciseMap", "Map", "for<PERSON>ach", "exercise<PERSON>ame", "has", "set", "get", "sortedData", "sort", "a", "b", "name", "getWorkoutDashboardData", "log", "activeProtocol", "eb", "or", "hasProtocol", "protocolFrequency", "frequency", "thirtyDaysAgo", "workoutStats", "fn", "count", "as", "sevenDaysAgo", "weeklyWorkouts", "lastWorkout", "allWorkoutDays", "currentStreak", "bestStreak", "tempStreak", "workoutDates", "toDateString", "uniqueDates", "Set", "today", "yesterday", "yesterdayStr", "includes", "checkDate", "previousDate", "dayDiff", "abs", "max", "weeklyGoal", "weeklyFrequency", "weekly_workouts", "weeklyProgressPercentage", "min", "protocolCompletionPercentage", "protocolStartDate", "started_at", "daysSinceStart", "floor", "weeksSinceStart", "expectedTotalWorkouts", "actualTotalWorkouts", "total_workouts", "has_protocol", "protocol_frequency", "total_minutes", "total_calories", "weekly_frequency", "current_streak", "best_streak", "last_workout", "workout_name", "duration", "workout_time", "next_workout", "weekly_progress_percentage", "protocol_completion_percentage", "getNutritionalSummary", "query", "protocol", "startOfDay", "setHours", "endOfDay", "consumedMeals", "leftJoin", "sql", "caloriesBurned", "targets", "total_protein", "total_carbs", "total_fat", "total_burned", "remaining", "target"], "mappings": ";;;;+BAmBaA;;;eAAAA;;;wBAnBc;0BACR;wBACC;;;;;;;AAiBb,IAAA,AAAMA,mBAAN,MAAMA;IAET;;KAEC,GACD,MAAMC,kBAAkBC,MAAc,EAAEC,SAAiB,OAAO,EAAE;QAC9D,IAAI;YACA,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,cAAc,CAACH;YAEnD,yCAAyC;YACzC,MAAMI,aAAa,MAAMC,YAAE,CACtBC,UAAU,CAAC,eACXC,MAAM,CAAC;gBAAC;gBAAU;gBAAM;aAAqB,EAC7CC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,cAAc,MAAMP,WAC1BO,KAAK,CAAC,cAAc,MAAMN,SAC1BO,OAAO,CAAC,cAAc,OACtBC,OAAO;YAEZ,8BAA8B;YAC9B,MAAMC,OAAO,MAAMN,YAAE,CAChBC,UAAU,CAAC,SACXC,MAAM,CAAC;gBAAC;gBAAU;aAAU,EAC5BC,KAAK,CAAC,MAAM,KAAKT,QACjBa,gBAAgB;YAErB,uDAAuD;YACvD,MAAMC,aAAaT,WAAWU,MAAM,GAAG,IAAIV,UAAU,CAACA,WAAWU,MAAM,GAAG,EAAE,CAACC,MAAM,GAAGJ,MAAMI,UAAU;YACtG,MAAMC,cAAcZ,WAAWU,MAAM,GAAG,IAAIV,UAAU,CAAC,EAAE,CAACW,MAAM,GAAGF;YAEnE,gFAAgF;YAChF,MAAMI,UAAU,OAAOJ,eAAe,WAAWK,WAAWL,cAAcM,OAAON,eAAe;YAChG,MAAMO,WAAW,OAAOJ,gBAAgB,WAAWE,WAAWF,eAAeG,OAAOH,gBAAgB;YACpG,MAAMK,SAASJ,UAAUG;YACzB,MAAME,QAAQD,SAAS,MAAM,eAAeA,SAAS,CAAC,MAAM,eAAe;YAE3E,OAAO;gBACHE,QAAQ;gBACRC,MAAM;oBACFP,SAASC,WAAWD,QAAQQ,OAAO,CAAC;oBACpCC,MAAM;oBACNL,QAAQH,WAAWG,OAAOI,OAAO,CAAC;oBAClCH;oBACAK,SAASvB,WAAWwB,GAAG,CAACC,CAAAA;wBACpB,0DAA0D;wBAC1D,MAAMd,SAAS,OAAOc,KAAKd,MAAM,KAAK,WAAWG,WAAWW,KAAKd,MAAM,IAAII,OAAOU,KAAKd,MAAM,KAAK;wBAClG,MAAMe,KAAKD,KAAKC,EAAE,GAAI,OAAOD,KAAKC,EAAE,KAAK,WAAWZ,WAAWW,KAAKC,EAAE,IAAIX,OAAOU,KAAKC,EAAE,IAAK;wBAE7F,OAAO;4BACHC,MAAMF,KAAKE,IAAI;4BACfhB,QAAQG,WAAWH,OAAOU,OAAO,CAAC;4BAClCO,SAASF,KAAKZ,WAAWY,GAAGL,OAAO,CAAC,MAAM;wBAC9C;oBACJ;gBACJ;YACJ;QACJ,EAAE,OAAOQ,OAAO;YACZC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,MAAMA;QACV;IACJ;IAEA;;KAEC,GACD,MAAME,qBAAqBpC,MAAc,EAAEC,SAAiB,OAAO,EAAE;QACjE,IAAI;YACA,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,cAAc,CAACH;YAEnD,0BAA0B;YAC1B,MAAMoC,gBAAgB,MAAM/B,YAAE,CACzBC,UAAU,CAAC,oBACXC,MAAM,CAAC;gBAAC;gBAAS;gBAAmB;aAAW,EAC/CC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,aAAa,MAAMP,UAAUoC,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE,EAC9D9B,KAAK,CAAC,aAAa,MAAMN,QAAQmC,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE,EAC5D5B,OAAO;YAEZ,0BAA0B;YAC1B,MAAM6B,cAAc,MAAMlC,YAAE,CACvBC,UAAU,CAAC,6BACXC,MAAM,CAAC;gBAAC;gBAAM;aAAW,EACzBC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAMP,WACxBO,KAAK,CAAC,YAAY,MAAMN,SACxBQ,OAAO;YAEZ,kDAAkD;YAClD,MAAM8B,eAAe,MAAMnC,YAAE,CACxBC,UAAU,CAAC,+BACXmC,SAAS,CAAC,wBAAwB,2BAA2B,wCAC7DlC,MAAM,CAAC;gBAAC;gBAAsC;aAAmC,EACjFC,KAAK,CAAC,gCAAgC,KAAKT,QAC3CS,KAAK,CAAC,iCAAiC,MAAMP,WAC7CO,KAAK,CAAC,iCAAiC,MAAMN,SAC7CQ,OAAO;YAEZ,oBAAoB;YACpB,MAAMgC,YAAYC,KAAKC,IAAI,CAAC,AAAC1C,CAAAA,QAAQ2C,OAAO,KAAK5C,UAAU4C,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;YAC3F,MAAMC,qBAAqBV,cAActB,MAAM,GAAG,IAC9CsB,cAAcW,MAAM,CAAC,CAACC,KAAKC,MAAQD,MAAOC,CAAAA,IAAIC,QAAQ,GAAG,IAAI,CAAA,GAAI,KAAKd,cAActB,MAAM,GAAG,MAAM;YACvG,MAAMqC,qBAAqB,AAACZ,YAAYzB,MAAM,GAAG4B,YAAa;YAC9D,MAAMU,gBAAgBZ,aAAa1B,MAAM,GAAG,IACvC,AAAC0B,CAAAA,YAAY,CAACA,aAAa1B,MAAM,GAAG,EAAE,EAAEC,UAAU,CAAA,IAAMyB,CAAAA,YAAY,CAAC,EAAE,EAAEzB,UAAU,CAAA,IAAM;YAE9F,OAAO;gBACHQ,QAAQ;gBACRC,MAAM;oBACF6B,aAAa;oBACbC,kBAAkBX,KAAKY,KAAK,CAACT;oBAC7BK,oBAAoBR,KAAKY,KAAK,CAACJ;oBAC/BC,eAAeT,KAAKY,KAAK,CAACH,gBAAgB,OAAO;oBACjDI,mBAAmB;oBACnBC,iBAAiB;wBACb;wBACA;wBACA;qBACH;gBACL;YACJ;QACJ,EAAE,OAAOxB,OAAO;YACZC,QAAQD,KAAK,CAAC,qCAAqCA;YACnD,MAAMA;QACV;IACJ;IAEA;;KAEC,GACD,MAAMyB,oBAAoB3D,MAAc,EAAEC,SAAiB,MAAM,EAAE;QAC/D,IAAI;YACA,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,cAAc,CAACH;YAEnD,kDAAkD;YAClD,MAAM2D,WAAW,MAAMtD,YAAE,CACpBC,UAAU,CAAC,6BACXC,MAAM,CAAC;gBAAC;gBAAY;aAAW,EAC/BC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAMP,WACxBO,KAAK,CAAC,YAAY,MAAMN,SACxBO,OAAO,CAAC,YAAY,OACpBC,OAAO;YAEZ,iDAAiD;YACjD,MAAMkD,aAAa,MAAMvD,YAAE,CACtBC,UAAU,CAAC,+BACXmC,SAAS,CAAC,wBAAwB,2BAA2B,wCAC7DlC,MAAM,CAAC;gBAAC;gBAAiC;gBAAsC;aAAmC,EAClHC,KAAK,CAAC,gCAAgC,KAAKT,QAC3CS,KAAK,CAAC,iCAAiC,MAAMP,WAC7CO,KAAK,CAAC,iCAAiC,MAAMN,SAC7CQ,OAAO;YAEZ,8DAA8D;YAC9D,MAAMmD,eAAeD,WAAWb,MAAM,CAAC,CAACe,KAAKjC;gBACzC,MAAME,OAAOF,KAAKkC,QAAQ,GAAGlC,KAAKkC,QAAQ,CAAC1B,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBACzE,qCAAqC;gBACrC,MAAMvB,SAAS,OAAOc,KAAKd,MAAM,KAAK,WAAWG,WAAWW,KAAKd,MAAM,IAAII,OAAOU,KAAKd,MAAM,KAAK;gBAClG,MAAMiD,OAAO,OAAOnC,KAAKmC,IAAI,KAAK,WAAW9C,WAAWW,KAAKmC,IAAI,IAAI7C,OAAOU,KAAKmC,IAAI,KAAK;gBAC1F,MAAMC,SAASlD,SAASiD,OAAO,GAAG,gBAAgB;gBAClD,IAAIjC,MAAM;oBACN+B,GAAG,CAAC/B,KAAK,GAAG,AAAC+B,CAAAA,GAAG,CAAC/B,KAAK,IAAI,CAAA,IAAKkC;gBACnC;gBACA,OAAOH;YACX,GAAG,CAAC;YAEJ,oCAAoC;YACpC,MAAMI,YAAY,IAAI,CAACC,eAAe,CAACnE,QAAQC,WAAWC,SAAS2D;YAEnE,mBAAmB;YACnB,MAAMO,gBAAgBT,SAAS7C,MAAM;YACrC,MAAMuD,eAAeD,gBAAgB,IAAI,kCAAkC;YAC3E,MAAME,gBAAgBX,SAASZ,MAAM,CAAC,CAACC,KAAKuB,IAAMvB,MAAOuB,CAAAA,EAAEC,QAAQ,IAAI,CAAA,GAAI;YAE3E,OAAO;gBACHjD,QAAQ;gBACRC,MAAM;oBACFiD,OAAOP;oBACPP,UAAUS;oBACVM,SAASL;oBACTG,UAAUF;oBACVtE,QAAQA;gBACZ;YACJ;QACJ,EAAE,OAAOiC,OAAO;YACZC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,MAAMA;QACV;IACJ;IAEA;;KAEC,GACD,MAAM0C,sBAAsB5E,MAAc,EAAEC,SAAiB,MAAM,EAAE;QACjE,IAAI;YACA,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,cAAc,CAACH;YAEnD,oDAAoD;YACpD,MAAMoC,gBAAgB,MAAM/B,YAAE,CACzBC,UAAU,CAAC,eACXC,MAAM,CAAC;gBAAC;gBAAY;gBAAW;gBAAS;aAAM,EAC9CC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAMP,WACxBO,KAAK,CAAC,YAAY,MAAMN,SACxBQ,OAAO;YAEZ,qBAAqB;YACrB,MAAMgC,YAAYN,cAActB,MAAM,IAAI;YAC1C,MAAM8D,cAAcxC,cAAcW,MAAM,CAAC,CAACC,KAAKC,MAAQD,MAAOC,CAAAA,IAAIuB,QAAQ,IAAI,CAAA,GAAI,KAAK9B;YACvF,MAAMmC,aAAazC,cAAcW,MAAM,CAAC,CAACC,KAAKC,MAAQD,MAAOC,CAAAA,IAAI6B,OAAO,IAAI,CAAA,GAAI,KAAKpC;YACrF,MAAMqC,WAAW3C,cAAcW,MAAM,CAAC,CAACC,KAAKC,MAAQD,MAAOC,CAAAA,IAAI+B,KAAK,IAAI,CAAA,GAAI,KAAKtC;YACjF,MAAMuC,SAAS7C,cAAcW,MAAM,CAAC,CAACC,KAAKC,MAAQD,MAAOC,CAAAA,IAAIiC,GAAG,IAAI,CAAA,GAAI,KAAKxC;YAE7E,mDAAmD;YACnD,MAAMyC,iBAAiB;YACvB,MAAMC,kBAAkBzC,KAAKY,KAAK,CAAC,AAACqB,cAAcO,iBAAkB;YAEpE,OAAO;gBACH5D,QAAQ;gBACRC,MAAM;oBACFgD,UAAU7B,KAAKY,KAAK,CAACqB;oBACrBS,kBAAkBD;oBAClBJ,OAAOrC,KAAKY,KAAK,CAACwB;oBAClBO,eAAe3C,KAAKY,KAAK,CAAC,AAACwB,WAAW,IAAIH,cAAe;oBACzDE,SAASnC,KAAKY,KAAK,CAACsB;oBACpBU,iBAAiB5C,KAAKY,KAAK,CAAC,AAACsB,aAAa,IAAID,cAAe;oBAC7DM,KAAKvC,KAAKY,KAAK,CAAC0B;oBAChBO,aAAa7C,KAAKY,KAAK,CAAC,AAAC0B,SAAS,IAAIL,cAAe;gBACzD;YACJ;QACJ,EAAE,OAAO3C,OAAO;YACZC,QAAQD,KAAK,CAAC,sCAAsCA;YACpD,MAAMA;QACV;IACJ;IAEA;;KAEC,GACD,MAAMwD,kBAAkB1F,MAAc,EAAEC,SAAiB,MAAM,EAAE;QAC7D,IAAI;YACA,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,cAAc,CAACH;YAEnD,uCAAuC;YACvC,MAAMoC,gBAAgB,MAAM/B,YAAE,CACzBC,UAAU,CAAC,eACXC,MAAM,CAAC;gBAAC;gBAAY;aAAW,EAC/BC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAMP,WACxBO,KAAK,CAAC,YAAY,MAAMN,SACxBQ,OAAO;YAEZ,MAAM6B,cAAc,MAAMlC,YAAE,CACvBC,UAAU,CAAC,6BACXC,MAAM,CAAC;gBAAC;gBAAY;aAAW,EAC/BC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAMP,WACxBO,KAAK,CAAC,YAAY,MAAMN,SACxBQ,OAAO;YAEZ,uBAAuB;YACvB,MAAMgF,aAAa,IAAI,CAACC,kBAAkB,CAACvD,eAAeG,aAAatC,WAAWC;YAElF,OAAO;gBACHqB,QAAQ;gBACRC,MAAMkE;YACV;QACJ,EAAE,OAAOzD,OAAO;YACZC,QAAQD,KAAK,CAAC,kCAAkCA;YAChD,MAAMA;QACV;IACJ;IAEA;;KAEC,GACD,MAAM2D,oBAAoB7F,MAAc,EAAEC,SAAiB,OAAO,EAAE;QAChE,IAAI;YACA,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,cAAc,CAACH;YAEnD,+CAA+C;YAC/C,MAAMwC,eAAe,MAAMnC,YAAE,CACxBC,UAAU,CAAC,+BACXmC,SAAS,CAAC,wBAAwB,2BAA2B,wCAC7DlC,MAAM,CAAC;gBACJ;gBACA;gBACA;aACH,EACAC,KAAK,CAAC,gCAAgC,KAAKT,QAC3CS,KAAK,CAAC,iCAAiC,MAAMP,WAC7CO,KAAK,CAAC,iCAAiC,MAAMN,SAC7CO,OAAO,CAAC,iCAAiC,OACzCC,OAAO;YAEZ,2CAA2C;YAC3C,MAAMmF,mBAAmB,IAAI,CAACC,yBAAyB,CAACtD;YACxD,MAAMuD,cAAcvD,aAAaO,MAAM,CAAC,CAACC,KAAKnB;gBAC1C,qCAAqC;gBACrC,MAAMd,SAAS,OAAOc,KAAKd,MAAM,KAAK,WAAWG,WAAWW,KAAKd,MAAM,IAAII,OAAOU,KAAKd,MAAM,KAAK;gBAClG,MAAMiD,OAAO,OAAOnC,KAAKmC,IAAI,KAAK,WAAW9C,WAAWW,KAAKmC,IAAI,IAAI7C,OAAOU,KAAKmC,IAAI,KAAK;gBAC1F,OAAOhB,MAAOjC,SAASiD;YAC3B,GAAG;YAEH,OAAO;gBACHzC,QAAQ;gBACRC,MAAM;oBACFwE,WAAWH;oBACXI,cAAcF;oBACdG,eAAe;oBACfC,kBAAkB,EAAE;oBACpBnG,QAAQA;gBACZ;YACJ;QACJ,EAAE,OAAOiC,OAAO;YACZC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,MAAMA;QACV;IACJ;IAEA;;KAEC,GACD,MAAMmE,mBAAmBrG,MAAc,EAAEC,SAAiB,OAAO,EAAE;QAC/D,IAAI;YACA,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE,GAAG,IAAI,CAACC,cAAc,CAACH;YAEnD,6CAA6C;YAC7C,MAAMqG,cAAc,MAAMhG,YAAE,CACvBC,UAAU,CAAC,eACXC,MAAM,CAAC;gBAAC;gBAAU;gBAAM;aAAqB,EAC7CC,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,cAAc,MAAMP,WAC1BO,KAAK,CAAC,cAAc,MAAMN,SAC1BO,OAAO,CAAC,cAAc,OACtBC,OAAO;YAEZ,wBAAwB;YACxB,MAAM4F,eAAe,MAAMjG,YAAE,CACxBC,UAAU,CAAC,4BACXiG,SAAS,GACT/F,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,cAAc,MAAMP,WAC1BO,KAAK,CAAC,cAAc,MAAMN,SAC1BO,OAAO,CAAC,cAAc,OACtBC,OAAO;YAEZ,mDAAmD;YACnD,MAAMN,aAAaiG,YAAYzE,GAAG,CAACC,CAAAA;gBAC/B,0DAA0D;gBAC1D,MAAMd,SAAS,OAAOc,KAAKd,MAAM,KAAK,WAAWG,WAAWW,KAAKd,MAAM,IAAII,OAAOU,KAAKd,MAAM,KAAK;gBAClG,MAAMe,KAAKD,KAAKC,EAAE,GAAI,OAAOD,KAAKC,EAAE,KAAK,WAAWZ,WAAWW,KAAKC,EAAE,IAAIX,OAAOU,KAAKC,EAAE,IAAK;gBAE7F,OAAO;oBACHC,MAAMF,KAAKE,IAAI;oBACfhB,QAAQG,WAAWH,OAAOU,OAAO,CAAC;oBAClCO,SAASF,KAAKZ,WAAWY,GAAGL,OAAO,CAAC,MAAM;gBAC9C;YACJ;YAEA,MAAM+E,kBAAkBF,aAAa1E,GAAG,CAACC,CAAAA,OAAS,CAAA;oBAC9CE,MAAMF,KAAK4E,UAAU;oBACrBC,OAAO7E,KAAK6E,KAAK;oBACjBC,OAAO9E,KAAK8E,KAAK;oBACjBC,MAAM/E,KAAK+E,IAAI;oBACfC,QAAQ,AAAChF,CAAAA,KAAKiF,YAAY,GAAGjF,KAAKkF,WAAW,AAAD,IAAK;oBACjDC,OAAO,AAACnF,CAAAA,KAAKoF,WAAW,GAAGpF,KAAKqF,UAAU,AAAD,IAAK;gBAClD,CAAA;YAEA,OAAO;gBACH3F,QAAQ;gBACRC,MAAM;oBACF2F,iBAAiB/G;oBACjBgH,uBAAuBZ;oBACvBxG,QAAQA;gBACZ;YACJ;QACJ,EAAE,OAAOiC,OAAO;YACZC,QAAQD,KAAK,CAAC,mCAAmCA;YACjD,MAAMA;QACV;IACJ;IAEA;;KAEC,GACD,AAAQ9B,eAAeH,MAAc,EAAsC;QACvE,MAAME,UAAU,IAAImH;QACpB,MAAMpH,YAAY,IAAIoH;QAEtB,OAAQrH;YACJ,KAAK;gBACDC,UAAUqH,OAAO,CAACpH,QAAQqH,OAAO,KAAK;gBACtC;YACJ,KAAK;gBACDtH,UAAUuH,QAAQ,CAACtH,QAAQuH,QAAQ,KAAK;gBACxC;YACJ,KAAK;gBACDxH,UAAUuH,QAAQ,CAACtH,QAAQuH,QAAQ,KAAK;gBACxC;YACJ,KAAK;gBACDxH,UAAUyH,WAAW,CAACxH,QAAQyH,WAAW,KAAK;gBAC9C;YACJ,KAAK;gBACD1H,UAAUyH,WAAW,CAACxH,QAAQyH,WAAW,KAAK;gBAC9C;YACJ;gBACI1H,UAAUuH,QAAQ,CAACtH,QAAQuH,QAAQ,KAAK;QAChD;QAEA,OAAO;YAAExH;YAAWC;QAAQ;IAChC;IAEA;;KAEC,GACD,AAAQiE,gBAAgBnE,MAAc,EAAEC,SAAe,EAAEC,OAAa,EAAE2D,YAAoC,EAAoB;QAC5H,MAAMK,YAA8B,EAAE;QAEtC,OAAQlE;YACJ,KAAK;gBACD,MAAM4H,OAAO;oBAAC;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;oBAAO;iBAAM;gBAC9D,IAAK,IAAIC,IAAI,GAAGA,IAAI,GAAGA,IAAK;oBACxB,MAAM9F,OAAO,IAAIsF,KAAKpH;oBACtB8B,KAAKuF,OAAO,CAACrH,UAAUsH,OAAO,KAAKM;oBACnC,MAAMC,UAAU/F,KAAKM,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;oBAChD4B,UAAU6D,IAAI,CAAC;wBACXC,OAAOJ,IAAI,CAACC,EAAE;wBACdI,iBAAiBpE,YAAY,CAACiE,QAAQ,IAAI;oBAC9C;gBACJ;gBACA;YACJ;gBACI,sCAAsC;gBACtC,MAAMI,cAAc,IAAIb,KAAKpH;gBAC7B,MAAOiI,eAAehI,QAAS;oBAC3B,MAAM4H,UAAUI,YAAY7F,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACvD4B,UAAU6D,IAAI,CAAC;wBACXC,OAAOF;wBACPG,iBAAiBpE,YAAY,CAACiE,QAAQ,IAAI;oBAC9C;oBACAI,YAAYZ,OAAO,CAACY,YAAYX,OAAO,KAAK;gBAChD;QACR;QAEA,OAAOrD;IACX;IAEA;;KAEC,GACD,AAAQyB,mBAAmBvD,aAAoB,EAAEG,WAAkB,EAAEtC,SAAe,EAAEC,OAAa,EAAE;QACjG,MAAMwF,aAAoB,EAAE;QAC5B,MAAMwC,cAAc,IAAIb,KAAKpH;QAE7B,MAAOiI,eAAehI,QAAS;YAC3B,MAAM4H,UAAUI,YAAY7F,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;YACvD,MAAM6F,YAAY/F,cAAcgG,IAAI,CAACC,CAAAA,IAAKA,EAAEtG,IAAI,KAAK+F;YACrD,MAAMQ,UAAU/F,YAAY6F,IAAI,CAAC7D,CAAAA,IAAKA,EAAExC,IAAI,KAAK+F;YAEjDpC,WAAWqC,IAAI,CAAC;gBACZhG,MAAM+F;gBACNS,UAAUJ,WAAWK,iBAAiB;gBACtCC,QAAQH,SAASI,mBAAmB;gBACpCC,SAAS,AAACR,CAAAA,WAAWK,iBAAiB,CAAA,IAAMF,CAAAA,SAASI,mBAAmB,CAAA;YAC5E;YAEAR,YAAYZ,OAAO,CAACY,YAAYX,OAAO,KAAK;QAChD;QAEA,OAAO7B;IACX;IAEA;;KAEC,GACD,AAAQI,0BAA0BtD,YAAmB,EAA0B;QAC3E,MAAMoG,cAAc,IAAIC;QAExBrG,aAAasG,OAAO,CAACjH,CAAAA;YACjB,MAAMkH,eAAe,CAAC,SAAS,EAAElH,KAAKkC,QAAQ,EAAE;YAChD,IAAI,CAAC6E,YAAYI,GAAG,CAACD,eAAe;gBAChCH,YAAYK,GAAG,CAACF,cAAc,EAAE;YACpC;YAEA,qCAAqC;YACrC,MAAMhI,SAAS,OAAOc,KAAKd,MAAM,KAAK,WAAWG,WAAWW,KAAKd,MAAM,IAAII,OAAOU,KAAKd,MAAM,KAAK;YAClG,MAAMiD,OAAO,OAAOnC,KAAKmC,IAAI,KAAK,WAAW9C,WAAWW,KAAKmC,IAAI,IAAI7C,OAAOU,KAAKmC,IAAI,KAAK;YAE1F4E,YAAYM,GAAG,CAACH,cAAchB,IAAI,CAAC;gBAC/BhH,QAAQA;gBACRiD,MAAMA;gBACNjC,MAAMF,KAAKkC,QAAQ;YACvB;QACJ;QAEA,MAAM8B,mBAA2C,EAAE;QACnD+C,YAAYE,OAAO,CAAC,CAACtH,MAAMuH;YACvB,MAAMI,aAAa3H,KAAK4H,IAAI,CAAC,CAACC,GAAQC,IAAW,IAAIjC,KAAKgC,EAAEtH,IAAI,EAAEc,OAAO,KAAK,IAAIwE,KAAKiC,EAAEvH,IAAI,EAAEc,OAAO;YAEtG,mCAAmC;YACnC,MAAMhC,aAAasI,UAAU,CAACA,WAAWrI,MAAM,GAAG,EAAE,EAAEC,UAAU;YAChE,MAAMC,cAAcmI,UAAU,CAAC,EAAE,EAAEpI,UAAU;YAC7C,MAAME,UAAU,OAAOJ,eAAe,WAAWK,WAAWL,cAAcM,OAAON,eAAe;YAChG,MAAMO,WAAW,OAAOJ,gBAAgB,WAAWE,WAAWF,eAAeG,OAAOH,gBAAgB;YAEpG6E,iBAAiBkC,IAAI,CAAC;gBAClBwB,MAAMR;gBACN9H,SAASA;gBACTG,UAAUA;gBACVC,QAAQJ,UAAUG;gBAClBO,SAASwH;YACb;QACJ;QAEA,OAAOtD;IACX;IAEA;;KAEC,GACD,MAAM2D,wBAAwBzJ,MAAc,EAAE;QAC1C,IAAI;YACAmC,QAAQuH,GAAG,CAAC,yEAAyE1J;YAErF,4CAA4C;YAC5C,MAAM2J,iBAAiB,MAAMrJ,YAAE,CAC1BC,UAAU,CAAC,mBACXiG,SAAS,GACT/F,KAAK,CAAC,aAAa,KAAKT,QACxBS,KAAK,CAAC,cAAc,MAAM,IAAI6G,QAC9B7G,KAAK,CAAC,CAACmJ,KAAOA,GAAGC,EAAE,CAAC;oBACjBD,GAAG,YAAY,MAAM;oBACrBA,GAAG,YAAY,KAAK,IAAItC;iBAC3B,GACAzG,gBAAgB;YAErB,MAAMiJ,cAAc,CAAC,CAACH;YACtB,MAAMI,oBAAoBJ,gBAAgBK,aAAa;YAEvD,8CAA8C;YAC9C,MAAMC,gBAAgB,IAAI3C;YAC1B2C,cAAc1C,OAAO,CAAC0C,cAAczC,OAAO,KAAK;YAEhD,MAAM0C,eAAe,MAAM5J,YAAE,CACxBC,UAAU,CAAC,wBACXC,MAAM,CAAC;gBACJF,YAAE,CAAC6J,EAAE,CAACC,KAAK,CAAC,MAAMC,EAAE,CAAC;gBACrB/J,YAAE,CAAC6J,EAAE,CAAClH,GAAG,CAAC,gBAAgBoH,EAAE,CAAC;gBAC7B/J,YAAE,CAAC6J,EAAE,CAAClH,GAAG,CAAC,kBAAkBoH,EAAE,CAAC;aAClC,EACA5J,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAMwJ,eACxBpJ,gBAAgB;YAErB,qCAAqC;YACrC,MAAMyJ,eAAe,IAAIhD;YACzBgD,aAAa/C,OAAO,CAAC+C,aAAa9C,OAAO,KAAK;YAE9C,MAAM+C,iBAAiB,MAAMjK,YAAE,CAC1BC,UAAU,CAAC,wBACXC,MAAM,CAACF,YAAE,CAAC6J,EAAE,CAACC,KAAK,CAAC,MAAMC,EAAE,CAAC,oBAC5B5J,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAM6J,cACxBzJ,gBAAgB;YAErB,mBAAmB;YACnB,MAAM2J,cAAc,MAAMlK,YAAE,CACvBC,UAAU,CAAC,+BACXmC,SAAS,CAAC,mCAAmC,UAAU,2BACvDlC,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;aACH,EACAC,KAAK,CAAC,eAAe,KAAKT,QAC1BU,OAAO,CAAC,gBAAgB,QACxBG,gBAAgB;YAErB,4DAA4D;YAC5D,MAAM4J,iBAAiB,MAAMnK,YAAE,CAC1BC,UAAU,CAAC,wBACXC,MAAM,CAAC;gBAAC;aAAW,EACnBC,KAAK,CAAC,WAAW,KAAKT,QACtBU,OAAO,CAAC,YAAY,QACpBC,OAAO;YAEZ,IAAI+J,gBAAgB;YACpB,IAAIC,aAAa;YACjB,IAAIC,aAAa;YAEjB,IAAIH,eAAe1J,MAAM,GAAG,GAAG;gBAC3B,MAAM8J,eAAeJ,eAAe5I,GAAG,CAAC2C,CAAAA,IACpC,IAAI8C,KAAK9C,EAAER,QAAQ,IAAI,IAAIsD,QAAQwD,YAAY;gBAEnD,MAAMC,cAAc;uBAAI,IAAIC,IAAIH;iBAAc,CAACxB,IAAI,CAAC,CAACC,GAAGC,IACpD,IAAIjC,KAAKiC,GAAGzG,OAAO,KAAK,IAAIwE,KAAKgC,GAAGxG,OAAO;gBAG/C,2BAA2B;gBAC3B,MAAMmI,QAAQ,IAAI3D,OAAOwD,YAAY;gBACrC,MAAMI,YAAY,IAAI5D;gBACtB4D,UAAU3D,OAAO,CAAC2D,UAAU1D,OAAO,KAAK;gBACxC,MAAM2D,eAAeD,UAAUJ,YAAY;gBAE3C,IAAIC,YAAYK,QAAQ,CAACH,UAAUF,YAAYK,QAAQ,CAACD,eAAe;oBACnE,IAAIE,YAAY,IAAI/D;oBACpB,IAAK,IAAIQ,IAAI,GAAGA,IAAIiD,YAAYhK,MAAM,EAAE+G,IAAK;wBACzC,MAAMC,UAAUsD,UAAUP,YAAY;wBACtC,IAAIC,YAAYK,QAAQ,CAACrD,UAAU;4BAC/B2C;4BACAW,UAAU9D,OAAO,CAAC8D,UAAU7D,OAAO,KAAK;wBAC5C,OAAO;4BACH;wBACJ;oBACJ;gBACJ;gBAEA,wBAAwB;gBACxBoD,aAAa;gBACb,IAAK,IAAI9C,IAAI,GAAGA,IAAIiD,YAAYhK,MAAM,EAAE+G,IAAK;oBACzC,MAAMK,cAAc,IAAIb,KAAKyD,WAAW,CAACjD,EAAE;oBAC3C,MAAMwD,eAAe,IAAIhE,KAAKyD,WAAW,CAACjD,IAAI,EAAE;oBAChD,MAAMyD,UAAU3I,KAAK4I,GAAG,CAACF,aAAaxI,OAAO,KAAKqF,YAAYrF,OAAO,MAAO,CAAA,OAAO,KAAK,KAAK,EAAC;oBAE9F,IAAIyI,YAAY,GAAG;wBACfX;oBACJ,OAAO;wBACHD,aAAa/H,KAAK6I,GAAG,CAACd,YAAYC;wBAClCA,aAAa;oBACjB;gBACJ;gBACAD,aAAa/H,KAAK6I,GAAG,CAACd,YAAYC;YACtC;YAEA,iCAAiC;YACjC,MAAMc,aAAa3B,qBAAqB,GAAG,yCAAyC;YACpF,MAAM4B,kBAAkBvK,OAAOmJ,gBAAgBqB,mBAAmB;YAClE,MAAMC,2BAA2BjJ,KAAKkJ,GAAG,CAAC,KAAK,AAACH,kBAAkBD,aAAc;YAEhF,mDAAmD;YACnD,IAAIK,+BAA+B;YACnC,IAAIpC,kBAAkBI,oBAAoB,GAAG;gBACzC,MAAMiC,oBAAoB,IAAI1E,KAAKqC,eAAesC,UAAU;gBAC5D,MAAM9D,cAAc,IAAIb;gBACxB,MAAM4E,iBAAiBtJ,KAAKuJ,KAAK,CAAC,AAAChE,CAAAA,YAAYrF,OAAO,KAAKkJ,kBAAkBlJ,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;gBAC7G,MAAMsJ,kBAAkBxJ,KAAK6I,GAAG,CAAC,GAAG7I,KAAKC,IAAI,CAACqJ,iBAAiB,KAAK,kBAAkB;gBAEtF,MAAMG,wBAAwBD,kBAAkBrC;gBAChD,MAAMuC,sBAAsBlL,OAAO8I,cAAcqC,kBAAkB;gBAEnER,+BAA+BM,wBAAwB,IACjDzJ,KAAKkJ,GAAG,CAAC,KAAK,AAACQ,sBAAsBD,wBAAyB,OAC9D;YACV;YAEA,OAAO;gBACH7K,QAAQ;gBACRC,MAAM;oBACF+K,cAAc1C;oBACd2C,oBAAoB1C;oBACpBwC,gBAAgBnL,OAAO8I,cAAcqC,kBAAkB;oBACvDG,eAAetL,OAAO8I,cAAcwC,iBAAiB;oBACrDC,gBAAgBvL,OAAO8I,cAAcyC,kBAAkB;oBACvDC,kBAAkBjB;oBAClBkB,gBAAgBnC;oBAChBoC,aAAanC;oBACboC,cAAcvC,cAAc;wBACxBxI,MAAMwI,YAAYxG,QAAQ;wBAC1BwF,MAAMgB,YAAYwC,YAAY;wBAC9BC,UAAUzC,YAAY0C,YAAY;wBAClCzI,UAAU+F,YAAYmC,cAAc;oBACxC,IAAI;oBACJQ,cAAcrD,cAAc,8BAA8B;oBAC1DsD,4BAA4BxK,KAAKY,KAAK,CAACqI;oBACvCwB,gCAAgCzK,KAAKY,KAAK,CAACuI;gBAC/C;YACJ;QAEJ,EAAE,OAAO7J,OAAO;YACZC,QAAQD,KAAK,CAAC,2CAA2CA;YACzD,+BAA+B;YAC/B,OAAO;gBACHV,QAAQ;gBACRC,MAAM;oBACF+K,cAAc;oBACdC,oBAAoB;oBACpBF,gBAAgB;oBAChBG,eAAe;oBACfC,gBAAgB;oBAChBC,kBAAkB;oBAClBC,gBAAgB;oBAChBC,aAAa;oBACbC,cAAc;oBACdI,cAAc;oBACdC,4BAA4B;oBAC5BC,gCAAgC;gBACpC;YACJ;QACJ;IACJ;IAEA,MAAMC,sBAAsBtN,MAAc,EAAEuN,KAAU,EAAE;QACpD,MAAMtC,QAAQsC,MAAMvL,IAAI,GAAG,IAAIsF,KAAKiG,MAAMvL,IAAI,IAAI,IAAIsF;QACtD,MAAMS,UAAUkD,MAAM3I,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE;QAEjDJ,QAAQuH,GAAG,CAAC,sEAAsE1J,QAAQ,SAAS+H;QAEnG,IAAI;YACA,6CAA6C;YAC7C,MAAMyF,WAAW,MAAMlN,YAAE,CACpBC,UAAU,CAAC,+BACXC,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;aACH,EACAC,KAAK,CAAC,eAAe,KAAKT,QAC1BS,KAAK,CAAC,gBAAgB,MAAMwK,OAC5BxK,KAAK,CAAC,cAAc,MAAM,MAC1BC,OAAO,CAAC,gBAAgB,QACxBG,gBAAgB;YAErB,wCAAwC;YACxC,MAAM4M,aAAa,IAAInG,KAAK2D;YAC5BwC,WAAWC,QAAQ,CAAC,GAAG,GAAG,GAAG;YAC7B,MAAMC,WAAW,IAAIrG,KAAK2D;YAC1B0C,SAASD,QAAQ,CAAC,IAAI,IAAI,IAAI;YAE9B,MAAME,gBAAgB,MAAMtN,YAAE,CACzBC,UAAU,CAAC,qBACXsN,QAAQ,CAAC,4BAA4B,eAAe,SACpDrN,MAAM,CAAC;gBACJsN,IAAAA,WAAG,CAAA,CAAC,wCAAwC,CAAC,CAACzD,EAAE,CAAC;gBACjDyD,IAAAA,WAAG,CAAA,CAAC,sCAAsC,CAAC,CAACzD,EAAE,CAAC;gBAC/CyD,IAAAA,WAAG,CAAA,CAAC,kCAAkC,CAAC,CAACzD,EAAE,CAAC;gBAC3CyD,IAAAA,WAAG,CAAA,CAAC,8BAA8B,CAAC,CAACzD,EAAE,CAAC;aAC1C,EACA5J,KAAK,CAAC,cAAc,KAAKT,QACzBS,KAAK,CAAC,eAAe,MAAMgN,YAC3BhN,KAAK,CAAC,eAAe,MAAMkN,UAC3B9M,gBAAgB;YAErB,gDAAgD;YAChD,MAAMkN,iBAAiB,MAAMzN,YAAE,CAC1BC,UAAU,CAAC,6BACXC,MAAM,CAAC;gBACJsN,IAAAA,WAAG,CAAA,CAAC,aAAa,CAAC,CAACzD,EAAE,CAAC;aACzB,EACA5J,KAAK,CAAC,WAAW,KAAKT,QACtBS,KAAK,CAAC,YAAY,MAAMgN,YACxBhN,KAAK,CAAC,YAAY,MAAMkN,UACxB9M,gBAAgB;YAErB,sBAAsB;YACtB,MAAMmN,UAAU;gBACZvJ,UAAUrD,OAAOoM,UAAU/I,aAAa;gBACxCM,SAAS3D,OAAOoM,UAAUzI,YAAY;gBACtCE,OAAO7D,OAAOoM,UAAUvI,UAAU;gBAClCE,KAAK/D,OAAOoM,UAAUrI,QAAQ;YAClC;YAEA,MAAMqD,WAAW;gBACb/D,UAAUrD,OAAOwM,eAAejB,mBAAmB;gBACnD5H,SAAS3D,OAAOwM,eAAeK,kBAAkB;gBACjDhJ,OAAO7D,OAAOwM,eAAeM,gBAAgB;gBAC7C/I,KAAK/D,OAAOwM,eAAeO,cAAc;YAC7C;YAEA,MAAMzF,SAAStH,OAAO2M,gBAAgBK,iBAAiB;YACvD,MAAMC,YAAY;gBACd5J,UAAU7B,KAAK6I,GAAG,CAAC,GAAGuC,QAAQvJ,QAAQ,GAAG+D,SAAS/D,QAAQ;gBAC1DM,SAASnC,KAAK6I,GAAG,CAAC,GAAGuC,QAAQjJ,OAAO,GAAGyD,SAASzD,OAAO;gBACvDE,OAAOrC,KAAK6I,GAAG,CAAC,GAAGuC,QAAQ/I,KAAK,GAAGuD,SAASvD,KAAK;gBACjDE,KAAKvC,KAAK6I,GAAG,CAAC,GAAGuC,QAAQ7I,GAAG,GAAGqD,SAASrD,GAAG;YAC/C;YAEAhD,QAAQuH,GAAG,CAAC,8CAA8C;gBACtDsE;gBACAxF;gBACAE;gBACA2F;YACJ;YAEA,OAAO;gBACH7M,QAAQ;gBACRC,MAAM;oBACFgD,UAAU;wBACNvD,SAASsH,SAAS/D,QAAQ;wBAC1B6J,QAAQN,QAAQvJ,QAAQ;wBACxB4J,WAAWA,UAAU5J,QAAQ;wBAC7BiE,QAAQA;oBACZ;oBACA3D,SAAS;wBACL7D,SAASsH,SAASzD,OAAO;wBACzBuJ,QAAQN,QAAQjJ,OAAO;oBAC3B;oBACAE,OAAO;wBACH/D,SAASsH,SAASvD,KAAK;wBACvBqJ,QAAQN,QAAQ/I,KAAK;oBACzB;oBACAE,KAAK;wBACDjE,SAASsH,SAASrD,GAAG;wBACrBmJ,QAAQN,QAAQ7I,GAAG;oBACvB;oBACAnD,MAAM+F;gBACV;YACJ;QAEJ,EAAE,OAAO7F,OAAO;YACZC,QAAQD,KAAK,CAAC,wCAAwCA;YAEtD,6CAA6C;YAC7C,OAAO;gBACHV,QAAQ;gBACRC,MAAM;oBACFgD,UAAU;wBACNvD,SAAS;wBACToN,QAAQ;wBACRD,WAAW;wBACX3F,QAAQ;oBACZ;oBACA3D,SAAS;wBACL7D,SAAS;wBACToN,QAAQ;oBACZ;oBACArJ,OAAO;wBACH/D,SAAS;wBACToN,QAAQ;oBACZ;oBACAnJ,KAAK;wBACDjE,SAAS;wBACToN,QAAQ;oBACZ;oBACAtM,MAAM+F;gBACV;YACJ;QACJ;IACJ;AAEJ"}