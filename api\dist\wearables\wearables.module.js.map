{"version": 3, "sources": ["../../src/wearables/wearables.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { WearablesController } from './wearables.controller';\nimport { WearablesService } from './wearables.service';\n\n@Module({\n  controllers: [WearablesController],\n  providers: [WearablesService],\n  exports: [WearablesService],\n})\nexport class WearablesModule {}\n"], "names": ["WearablesModule", "controllers", "WearablesController", "providers", "WearablesService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;qCACa;kCACH;;;;;;;AAO1B,IAAA,AAAMA,kBAAN,MAAMA;AAAiB;;;QAJ5BC,aAAa;YAACC,wCAAmB;SAAC;QAClCC,WAAW;YAACC,kCAAgB;SAAC;QAC7BC,SAAS;YAACD,kCAAgB;SAAC"}