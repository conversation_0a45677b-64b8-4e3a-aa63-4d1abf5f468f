import { IsString, <PERSON>NotEmpty, <PERSON>Int, IsN<PERSON>ber, Min, IsBoolean, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdatePlanDto {
  @IsString()
  @IsNotEmpty()
  name: string; // Nome do plano

  @IsString()
  @IsOptional()
  description?: string; // Descrição do plano

  @IsNumber()
  @Min(0)
  @IsOptional()
  price?: number; // Preço do plano

  @IsString()
  @IsOptional()
  currency?: string; // Moeda

  @IsString()
  @IsOptional()
  frequency?: 'daily' | 'weekly' | 'monthly' | 'annually'; // Frequência

  @IsNumber()
  @Min(1)
  @IsOptional()
  interval_value?: number; // Valor do intervalo

  @IsBoolean()
  @IsOptional()
  isActive?: boolean | null; // Ativo

  @IsInt()
  @Type(() => Number)
  @IsOptional()
  role_id?: number; // ID da role

  @IsInt()
  @Type(() => Number)
  @IsOptional()
  user_id?: number; // ID do usuário

  @IsNumber()
  @Min(0)
  @IsOptional()
  snaptokens?: number; // Snaptokens

  @IsBoolean()
  @IsOptional()
  allows_trial?: boolean; // Permite trial

  @IsNumber()
  @Min(0)
  @IsOptional()
  trial_period_days?: number; // Dias de trial

  @IsNumber()
  @Min(0)
  @IsOptional()
  affiliate_master_commission_percent?: number; // Comissão do master

  @IsNumber()
  @Min(0)
  @IsOptional()
  affiliate_commission_percent?: number; // Comissão do afiliado
}