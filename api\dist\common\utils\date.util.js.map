{"version": 3, "sources": ["../../../src/common/utils/date.util.ts"], "sourcesContent": ["const dayjs = require('dayjs');\r\nconst utc = require('dayjs/plugin/utc');\r\nconst timezone = require('dayjs/plugin/timezone');\r\n\r\ndayjs.extend(utc);\r\ndayjs.extend(timezone);\r\n\r\n/**\r\n * Converte uma data de um timezone específico para UTC.\r\n * @param date Data no formato 'YYYY-MM-DD HH:mm:ss' ou ISO.\r\n * @param tz Timezone da data original (ex: 'America/Sao_Paulo').\r\n * @returns Data convertida para UTC em formato 'YYYY-MM-DD HH:mm:ss'.\r\n */\r\nexport function convertToUTC(date: string, tz: string): string {\r\n  return dayjs.tz(date, tz).utc().format('YYYY-MM-DD HH:mm:ss');\r\n}\r\n\r\n/**\r\n * Converte uma data UTC para um timezone específico.\r\n * @param date Data UTC no formato 'YYYY-MM-DD HH:mm:ss' ou ISO.\r\n * @param tz Timezone desejado para conversão (ex: 'America/Sao_Paulo').\r\n * @returns Data convertida para o timezone especificado.\r\n */\r\nexport function convertFromUTC(date: string, tz: string): string {\r\n  return dayjs.utc(date).tz(tz).format('YYYY-MM-DD HH:mm:ss');\r\n}\r\n"], "names": ["convertFromUTC", "convertToUTC", "dayjs", "require", "utc", "timezone", "extend", "date", "tz", "format"], "mappings": ";;;;;;;;;;;IAuBgBA,cAAc;eAAdA;;IAVAC,YAAY;eAAZA;;;AAbhB,MAAMC,QAAQC,QAAQ;AACtB,MAAMC,MAAMD,QAAQ;AACpB,MAAME,WAAWF,QAAQ;AAEzBD,MAAMI,MAAM,CAACF;AACbF,MAAMI,MAAM,CAACD;AAQN,SAASJ,aAAaM,IAAY,EAAEC,EAAU;IACnD,OAAON,MAAMM,EAAE,CAACD,MAAMC,IAAIJ,GAAG,GAAGK,MAAM,CAAC;AACzC;AAQO,SAAST,eAAeO,IAAY,EAAEC,EAAU;IACrD,OAAON,MAAME,GAAG,CAACG,MAAMC,EAAE,CAACA,IAAIC,MAAM,CAAC;AACvC"}