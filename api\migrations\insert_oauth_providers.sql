-- Insert OAuth providers into auth_providers table
-- This script adds Google and Apple OAuth providers to support OAuth authentication

-- Insert Local provider (if not exists)
INSERT IGNORE INTO auth_providers (id, name) VALUES (1, 'local');

-- Insert Google OAuth provider
INSERT IGNORE INTO auth_providers (id, name) VALUES (2, 'google');

-- Insert Apple OAuth provider  
INSERT IGNORE INTO auth_providers (id, name) VALUES (3, 'apple');

-- Verify the providers were inserted
SELECT * FROM auth_providers ORDER BY id;
