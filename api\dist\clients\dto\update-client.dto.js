"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UpdateClientDto", {
    enumerable: true,
    get: function() {
        return UpdateClientDto;
    }
});
const _mappedtypes = require("@nestjs/mapped-types");
const _createclientdto = require("./create-client.dto");
let UpdateClientDto = class UpdateClientDto extends (0, _mappedtypes.PartialType)(_createclientdto.CreateClientDto) {
};

//# sourceMappingURL=update-client.dto.js.map