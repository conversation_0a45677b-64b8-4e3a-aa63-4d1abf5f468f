import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUsernameToUsers1709001234569 implements MigrationInterface {
  name = 'AddUsernameToUsers1709001234569';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE users 
      ADD COLUMN username VARCHAR(255) UNIQUE NULL
    `);

    // Create index for username
    await queryRunner.query(`
      CREATE INDEX IDX_users_username ON users(username)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DROP INDEX IDX_users_username
    `);
    
    await queryRunner.query(`
      ALTER TABLE users 
      DROP COLUMN username
    `);
  }
}
