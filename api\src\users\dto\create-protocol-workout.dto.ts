import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsString, ValidateNested, IsOptional } from 'class-validator';

class ExerciseDto {
  @IsNumber()
  exercise_id: number;

  @IsNumber()
  sets: number;

  @IsNumber()
  reps: number;

  @IsNumber()
  @IsOptional()
  rpe?: number;

  @IsNumber()
  @IsOptional()
  rest_seconds?: number;

  @IsString()
  @IsOptional()
  notes?: string;
}

class WorkoutDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExerciseDto)
  exercises: ExerciseDto[];
}

export class CreateProtocolWorkoutDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNumber()
  @Type(() => Number)
  type_id: number;

  @IsString()
  split: string;

  @IsNumber()
  @Type(() => Number)
  frequency: number;

  @IsString()
  objective: string;

  @IsOptional()
  @IsString()
  general_notes?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkoutDto)
  workouts: WorkoutDto[];
}
