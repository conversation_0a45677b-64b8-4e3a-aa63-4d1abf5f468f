const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Converte uma data de um timezone específico para UTC.
 * @param date Data no formato 'YYYY-MM-DD HH:mm:ss' ou ISO.
 * @param tz Timezone da data original (ex: 'America/Sao_Paulo').
 * @returns Data convertida para UTC em formato 'YYYY-MM-DD HH:mm:ss'.
 */
export function convertToUTC(date: string, tz: string): string {
  return dayjs.tz(date, tz).utc().format('YYYY-MM-DD HH:mm:ss');
}

/**
 * Converte uma data UTC para um timezone específico.
 * @param date Data UTC no formato 'YYYY-MM-DD HH:mm:ss' ou ISO.
 * @param tz Timezone desejado para conversão (ex: 'America/Sao_Paulo').
 * @returns Data convertida para o timezone especificado.
 */
export function convertFromUTC(date: string, tz: string): string {
  return dayjs.utc(date).tz(tz).format('YYYY-MM-DD HH:mm:ss');
}
