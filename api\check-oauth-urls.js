require('dotenv').config();

console.log('🔍 Verificando URLs OAuth...\n');

console.log('📋 URLs Configuradas no Backend:');
console.log(`API_URL: ${process.env.API_URL}`);
console.log(`GOOGLE_CALLBACK_URL: ${process.env.GOOGLE_CALLBACK_URL}`);
console.log(`APPLE_CALLBACK_URL: ${process.env.APPLE_CALLBACK_URL}`);
console.log(`GOOGLE_REDIRECT_URL: ${process.env.GOOGLE_REDIRECT_URL}`);
console.log(`APPLE_REDIRECT_URL: ${process.env.APPLE_REDIRECT_URL}`);

console.log('\n🔗 URLs OAuth Completas:');
console.log(`Google OAuth Initiation: ${process.env.API_URL}/auth/google`);
console.log(`Google OAuth Callback: ${process.env.GOOGLE_CALLBACK_URL}`);
console.log(`Apple OAuth Initiation: ${process.env.API_URL}/auth/apple`);
console.log(`Apple OAuth Callback: ${process.env.APPLE_CALLBACK_URL}`);

console.log('\n📱 Frontend Redirects:');
console.log(`Google Frontend Redirect: ${process.env.GOOGLE_REDIRECT_URL}`);
console.log(`Apple Frontend Redirect: ${process.env.APPLE_REDIRECT_URL}`);

console.log('\n⚙️ Configurações necessárias no Google Cloud Console:');
console.log('1. Authorized JavaScript origins:');
console.log('   - https://app.mysnapfit.com.br');
console.log('   - https://mysnapfit.com.br');
console.log('');
console.log('2. Authorized redirect URIs:');
console.log(`   - ${process.env.GOOGLE_CALLBACK_URL}`);

console.log('\n⚙️ Configurações necessárias no Apple Developer Console:');
console.log('1. Domains and Subdomains:');
console.log('   - app.mysnapfit.com.br');
console.log('   - mysnapfit.com.br');
console.log('');
console.log('2. Return URLs:');
console.log(`   - ${process.env.APPLE_CALLBACK_URL}`);

console.log('\n🚨 Checklist de Verificação:');
console.log('□ Google Client ID configurado no .env');
console.log('□ Google Client Secret configurado no .env');
console.log('□ Google OAuth app tem a redirect URI correta');
console.log('□ Apple Client ID configurado no .env');
console.log('□ Apple Team ID configurado no .env');
console.log('□ Apple Key ID configurado no .env');
console.log('□ Apple Private Key configurado no .env');
console.log('□ Apple OAuth app tem a return URL correta');

console.log('\n💡 Dica:');
console.log('Se o Google está redirecionando para a URL de callback mas dando erro,');
console.log('verifique se a URL está exatamente igual no Google Cloud Console.');
console.log('URLs são case-sensitive e devem incluir https://');

console.log('\n🔧 Para testar:');
console.log('1. Configure as URLs no Google Cloud Console');
console.log('2. Reinicie o servidor backend');
console.log('3. Teste o fluxo OAuth completo');
console.log('4. Verifique os logs do servidor para erros');
