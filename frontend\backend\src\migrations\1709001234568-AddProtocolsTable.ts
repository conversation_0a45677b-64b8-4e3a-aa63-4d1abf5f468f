import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProtocolsTable1709001234568 implements MigrationInterface {
  name = 'AddProtocolsTable1709001234568';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE protocols (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type ENUM('workout', 'diet') NOT NULL,
        objective TEXT NOT NULL,
        content JSON NOT NULL,
        notes TEXT,
        start_date DATE NOT NULL,
        end_date DATE,
        user_id VARCHAR(36),
        created_by_id VARCHAR(36) NOT NULL,
        status ENUM('active', 'completed', 'archived') NOT NULL DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id),
        FOREIG<PERSON> KEY (created_by_id) REFERENCES users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Add relations to users table
    await queryRunner.query(`
      ALTER TABLE users
      ADD COLUMN current_workout_protocol_id VARCHAR(36),
      ADD COLUMN current_diet_protocol_id VARCHAR(36),
      ADD FOREIGN KEY (current_workout_protocol_id) REFERENCES protocols(id),
      ADD FOREIGN KEY (current_diet_protocol_id) REFERENCES protocols(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE users DROP FOREIGN KEY current_workout_protocol_id');
    await queryRunner.query('ALTER TABLE users DROP FOREIGN KEY current_diet_protocol_id');
    await queryRunner.query('ALTER TABLE users DROP COLUMN current_workout_protocol_id');
    await queryRunner.query('ALTER TABLE users DROP COLUMN current_diet_protocol_id');
    await queryRunner.query('DROP TABLE protocols');
  }
}