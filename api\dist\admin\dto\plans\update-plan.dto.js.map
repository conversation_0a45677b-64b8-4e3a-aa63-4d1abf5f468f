{"version": 3, "sources": ["../../../../src/admin/dto/plans/update-plan.dto.ts"], "sourcesContent": ["import { IsString, <PERSON>NotEmpty, <PERSON>Int, IsN<PERSON>ber, Min, IsBoolean, IsOptional } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class UpdatePlanDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string; // Nome do plano\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string; // Descrição do plano\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  price?: number; // Preço do plano\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  currency?: string; // Moeda\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  frequency?: 'daily' | 'weekly' | 'monthly' | 'annually'; // Frequência\r\n\r\n  @IsNumber()\r\n  @Min(1)\r\n  @IsOptional()\r\n  interval_value?: number; // Valor do intervalo\r\n\r\n  @IsBoolean()\r\n  @IsOptional()\r\n  isActive?: boolean | null; // Ativo\r\n\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  @IsOptional()\r\n  role_id?: number; // ID da role\r\n\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  @IsOptional()\r\n  user_id?: number; // ID do usuário\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  snaptokens?: number; // Snaptokens\r\n\r\n  @IsBoolean()\r\n  @IsOptional()\r\n  allows_trial?: boolean; // Permite trial\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  trial_period_days?: number; // Dias de trial\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  affiliate_master_commission_percent?: number; // Comissão do master\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  @IsOptional()\r\n  affiliate_commission_percent?: number; // Comissão do afiliado\r\n}"], "names": ["UpdatePlanDto", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAHqE;kCAC7D;;;;;;;;;;AAEd,IAAA,AAAMA,gBAAN,MAAMA;AAgEb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAhCcC;;;;;;oCAKAA"}