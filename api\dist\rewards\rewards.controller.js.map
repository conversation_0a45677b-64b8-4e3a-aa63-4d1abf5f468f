{"version": 3, "sources": ["../../src/rewards/rewards.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Param, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { RewardsService } from './rewards.service';\n\n@Controller('rewards')\n@UseGuards(JwtAuthGuard)\nexport class RewardsController {\n  constructor(private readonly rewardsService: RewardsService) {}\n\n  @Get('available')\n  async getAvailableRewards(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.rewardsService.getAvailableRewards(userId, query);\n  }\n\n  @Get('my')\n  async getMyRewards(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.rewardsService.getMyRewards(userId);\n  }\n\n  @Post(':id/redeem')\n  async redeemReward(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.rewardsService.redeemReward(Number(id), userId);\n  }\n\n  @Get('categories')\n  async getRewardCategories() {\n    return this.rewardsService.getRewardCategories();\n  }\n}\n"], "names": ["RewardsController", "getAvailableRewards", "req", "query", "userId", "user", "rewardsService", "getMyRewards", "redeemReward", "id", "Number", "getRewardCategories", "constructor"], "mappings": ";;;;+BAcaA;;;eAAAA;;;wBANN;8BACsB;gCACE;;;;;;;;;;;;;;;AAIxB,IAAA,AAAMA,oBAAN,MAAMA;IAGX,MACMC,oBAAoB,AAAWC,GAAQ,EAAE,AAASC,KAAU,EAAE;QAClE,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACL,mBAAmB,CAACG,QAAQD;IACzD;IAEA,MACMI,aAAa,AAAWL,GAAQ,EAAE;QACtC,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACC,YAAY,CAACH;IAC1C;IAEA,MACMI,aAAa,AAAaC,EAAU,EAAE,AAAWP,GAAQ,EAAE;QAC/D,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACE,YAAY,CAACE,OAAOD,KAAKL;IACtD;IAEA,MACMO,sBAAsB;QAC1B,OAAO,IAAI,CAACL,cAAc,CAACK,mBAAmB;IAChD;IAvBAC,YAAY,AAAiBN,cAA8B,CAAE;aAAhCA,iBAAAA;IAAiC;AAwBhE"}