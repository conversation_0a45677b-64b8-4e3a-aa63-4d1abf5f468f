const axios = require('axios');
const mysql = require('mysql2/promise');
require('dotenv').config();

const API_URL = process.env.API_URL || 'http://localhost:3000';

// Database connection
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE
};

async function testOAuthUserRegistration() {
  console.log('🧪 Testing OAuth User Registration Functionality...\n');

  const connection = await mysql.createConnection(dbConfig);

  try {
    // Test data for new OAuth users
    const testUsers = [
      {
        provider: 'google',
        userInfo: {
          sub: 'google_test_123',
          email: '<EMAIL>',
          name: 'Google Test User',
          given_name: '<PERSON>',
          family_name: 'User',
          picture: 'https://example.com/google-avatar.jpg'
        }
      },
      {
        provider: 'apple',
        userInfo: {
          sub: 'apple_test_456',
          email: '<EMAIL>',
          name: 'Apple Test User',
          firstName: 'Apple',
          lastName: 'User'
        }
      }
    ];

    for (const testUser of testUsers) {
      console.log(`\n📱 Testing ${testUser.provider.toUpperCase()} OAuth Registration:`);
      
      // Check if user already exists (cleanup from previous tests)
      const [existingUsers] = await connection.execute(
        'SELECT id FROM users WHERE email = ?',
        [testUser.userInfo.email]
      );

      if (existingUsers.length > 0) {
        console.log(`   🧹 Cleaning up existing test user...`);
        await connection.execute('DELETE FROM user_auths WHERE user_id = ?', [existingUsers[0].id]);
        await connection.execute('DELETE FROM users WHERE id = ?', [existingUsers[0].id]);
      }

      // Test OAuth token validation (simulating frontend OAuth flow)
      try {
        console.log(`   🔄 Testing ${testUser.provider} OAuth token validation...`);
        
        const response = await axios.post(`${API_URL}/auth/oauth/${testUser.provider}`, {
          token: 'mock_token_for_testing',
          userInfo: testUser.userInfo
        }, {
          validateStatus: (status) => status >= 200 && status < 500
        });

        if (response.status === 200 && response.data.status === 'success') {
          console.log(`   ✅ ${testUser.provider} OAuth successful`);
          console.log(`   📊 Response:`, {
            status: response.data.status,
            hasAccessToken: !!response.data.data.access_token,
            hasRefreshToken: !!response.data.data.refresh_token,
            hasDeviceUid: !!response.data.data.device_uid
          });

          // Verify user was created in database
          const [newUsers] = await connection.execute(
            'SELECT id, name, email, photo, password FROM users WHERE email = ?',
            [testUser.userInfo.email]
          );

          if (newUsers.length > 0) {
            const user = newUsers[0];
            console.log(`   ✅ User created in database:`, {
              id: user.id,
              name: user.name,
              email: user.email,
              hasPhoto: !!user.photo,
              passwordIsNull: user.password === null
            });

            // Verify OAuth provider link
            const [userAuths] = await connection.execute(
              'SELECT provider_id, provider_uid FROM user_auths WHERE user_id = ?',
              [user.id]
            );

            if (userAuths.length > 0) {
              const auth = userAuths[0];
              const expectedProviderId = testUser.provider === 'google' ? 2 : 3;
              console.log(`   ✅ OAuth provider linked:`, {
                providerId: auth.provider_id,
                expectedProviderId,
                providerUid: auth.provider_uid,
                isCorrectProvider: auth.provider_id === expectedProviderId
              });
            } else {
              console.log(`   ❌ OAuth provider not linked`);
            }

            // Test existing user login (second OAuth attempt)
            console.log(`   🔄 Testing existing user OAuth login...`);
            const secondResponse = await axios.post(`${API_URL}/auth/oauth/${testUser.provider}`, {
              token: 'mock_token_for_testing_2',
              userInfo: testUser.userInfo
            });

            if (secondResponse.status === 200) {
              console.log(`   ✅ Existing user OAuth login successful`);
            } else {
              console.log(`   ❌ Existing user OAuth login failed`);
            }

          } else {
            console.log(`   ❌ User not created in database`);
          }

        } else {
          console.log(`   ❌ ${testUser.provider} OAuth failed:`, response.data);
        }

      } catch (error) {
        console.log(`   ❌ ${testUser.provider} OAuth error:`, error.response?.data || error.message);
      }
    }

    // Test error scenarios
    console.log(`\n🚨 Testing Error Scenarios:`);

    // Test missing email
    try {
      console.log(`   🔄 Testing OAuth without email...`);
      const response = await axios.post(`${API_URL}/auth/oauth/google`, {
        token: 'mock_token',
        userInfo: {
          sub: 'test_no_email',
          name: 'No Email User'
          // email is missing
        }
      }, {
        validateStatus: (status) => status >= 200 && status < 500
      });

      if (response.status === 400) {
        console.log(`   ✅ Correctly rejected OAuth without email`);
      } else {
        console.log(`   ❌ Should have rejected OAuth without email`);
      }
    } catch (error) {
      console.log(`   ✅ Correctly rejected OAuth without email`);
    }

    console.log(`\n🎉 OAuth User Registration Testing Completed!`);

  } catch (error) {
    console.error('❌ Error during testing:', error);
  } finally {
    await connection.end();
  }
}

testOAuthUserRegistration();
