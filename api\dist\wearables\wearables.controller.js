"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "WearablesController", {
    enumerable: true,
    get: function() {
        return WearablesController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _wearablesservice = require("./wearables.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let WearablesController = class WearablesController {
    async getConnectedDevices(req) {
        const userId = req.user.userId;
        return this.wearablesService.getConnectedDevices(userId);
    }
    async getSupportedDevices() {
        return this.wearablesService.getSupportedDevices();
    }
    async connectDevice(deviceData, req) {
        const userId = req.user.userId;
        return this.wearablesService.connectDevice(userId, deviceData);
    }
    async disconnectDevice(deviceId, req) {
        const userId = req.user.userId;
        return this.wearablesService.disconnectDevice(userId, Number(deviceId));
    }
    async getSyncedData(req, query) {
        const userId = req.user.userId;
        return this.wearablesService.getSyncedData(userId, query);
    }
    async syncData(req, syncOptions) {
        const userId = req.user.userId;
        return this.wearablesService.syncData(userId, syncOptions);
    }
    async getStepsData(req, query) {
        const userId = req.user.userId;
        return this.wearablesService.getStepsData(userId, query);
    }
    async getHeartRateData(req, query) {
        const userId = req.user.userId;
        return this.wearablesService.getHeartRateData(userId, query);
    }
    async getSleepData(req, query) {
        const userId = req.user.userId;
        return this.wearablesService.getSleepData(userId, query);
    }
    async getCaloriesData(req, query) {
        const userId = req.user.userId;
        return this.wearablesService.getCaloriesData(userId, query);
    }
    async addManualData(data, req) {
        const userId = req.user.userId;
        return this.wearablesService.addManualData(userId, data);
    }
    async getSyncStatus(req) {
        const userId = req.user.userId;
        return this.wearablesService.getSyncStatus(userId);
    }
    async connectFitbit(authData, req) {
        const userId = req.user.userId;
        return this.wearablesService.connectFitbit(userId, authData);
    }
    async connectGarmin(authData, req) {
        const userId = req.user.userId;
        return this.wearablesService.connectGarmin(userId, authData);
    }
    async connectAppleHealth(authData, req) {
        const userId = req.user.userId;
        return this.wearablesService.connectAppleHealth(userId, authData);
    }
    async connectGoogleFit(authData, req) {
        const userId = req.user.userId;
        return this.wearablesService.connectGoogleFit(userId, authData);
    }
    constructor(wearablesService){
        this.wearablesService = wearablesService;
    }
};
_ts_decorate([
    (0, _common.Get)('devices'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getConnectedDevices", null);
_ts_decorate([
    (0, _common.Get)('supported'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getSupportedDevices", null);
_ts_decorate([
    (0, _common.Post)('connect'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "connectDevice", null);
_ts_decorate([
    (0, _common.Delete)(':id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "disconnectDevice", null);
_ts_decorate([
    (0, _common.Get)('data'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getSyncedData", null);
_ts_decorate([
    (0, _common.Post)('sync'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "syncData", null);
_ts_decorate([
    (0, _common.Get)('data/steps'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getStepsData", null);
_ts_decorate([
    (0, _common.Get)('data/heart-rate'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getHeartRateData", null);
_ts_decorate([
    (0, _common.Get)('data/sleep'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getSleepData", null);
_ts_decorate([
    (0, _common.Get)('data/calories'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getCaloriesData", null);
_ts_decorate([
    (0, _common.Post)('data/manual'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "addManualData", null);
_ts_decorate([
    (0, _common.Get)('sync-status'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "getSyncStatus", null);
_ts_decorate([
    (0, _common.Post)('oauth/fitbit'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "connectFitbit", null);
_ts_decorate([
    (0, _common.Post)('oauth/garmin'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "connectGarmin", null);
_ts_decorate([
    (0, _common.Post)('oauth/apple-health'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "connectAppleHealth", null);
_ts_decorate([
    (0, _common.Post)('oauth/google-fit'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], WearablesController.prototype, "connectGoogleFit", null);
WearablesController = _ts_decorate([
    (0, _common.Controller)('wearables'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _wearablesservice.WearablesService === "undefined" ? Object : _wearablesservice.WearablesService
    ])
], WearablesController);

//# sourceMappingURL=wearables.controller.js.map