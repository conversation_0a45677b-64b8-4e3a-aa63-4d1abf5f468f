// Service Worker for Push Notifications
const CACHE_NAME = 'snapfit-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
];

// Install event
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event
self.addEventListener('fetch', (event) => {
  // Skip API calls and external requests
  if (event.request.url.includes('/api/') ||
      event.request.url.includes('/users/') ||
      !event.request.url.startsWith(self.location.origin)) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request).catch(() => {
          // Return a basic response for failed requests
          if (event.request.mode === 'navigate') {
            return new Response('App offline', {
              status: 200,
              statusText: 'OK',
              headers: { 'Content-Type': 'text/html' }
            });
          }
        });
      })
  );
});

// Push event - Handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);
  
  let notificationData = {
    title: 'SnapFit',
    body: 'Nova notificação disponível',
    icon: '/icon-192x192.svg',
    badge: '/badge-72x72.png',
    tag: 'snapfit-notification',
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'Ver',
        icon: '/icon-view.png'
      },
      {
        action: 'dismiss',
        title: 'Dispensar',
        icon: '/icon-dismiss.png'
      }
    ],
    data: {
      url: '/dashboard/professional',
      timestamp: Date.now()
    }
  };

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = {
        ...notificationData,
        ...pushData
      };
    } catch (error) {
      console.error('Error parsing push data:', error);
    }
  }

  // Show notification
  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Handle notification click
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        const url = event.notification.data?.url || '/dashboard/professional';
        
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes(url) && 'focus' in client) {
            return client.focus();
          }
        }
        
        // Open new window if app is not open
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
  );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync
      console.log('Background sync triggered')
    );
  }
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event);
  
  // Track notification dismissal
  event.waitUntil(
    fetch('/api/notifications/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'dismissed',
        notificationId: event.notification.tag,
        timestamp: Date.now()
      })
    }).catch(error => {
      console.error('Error tracking notification dismissal:', error);
    })
  );
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'check-notifications') {
    event.waitUntil(
      checkForNewNotifications()
    );
  }
});

// Function to check for new notifications
async function checkForNewNotifications() {
  try {
    const response = await fetch('/api/notifications/check', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${await getStoredToken()}`
      }
    });
    
    if (response.ok) {
      const notifications = await response.json();
      
      // Show notifications for high priority items
      notifications
        .filter(n => n.priority === 'high' && !n.isRead)
        .forEach(notification => {
          self.registration.showNotification(notification.title, {
            body: notification.message,
            icon: '/icon-192x192.svg',
            badge: '/badge-72x72.png',
            tag: `notification-${notification.id}`,
            requireInteraction: true,
            data: {
              notificationId: notification.id,
              url: '/dashboard/professional',
              timestamp: Date.now()
            }
          });
        });
    }
  } catch (error) {
    console.error('Error checking for notifications:', error);
  }
}

// Helper function to get stored auth token
async function getStoredToken() {
  try {
    const cache = await caches.open('snapfit-auth');
    const response = await cache.match('/auth-token');
    if (response) {
      const data = await response.json();
      return data.token;
    }
  } catch (error) {
    console.error('Error getting stored token:', error);
  }
  return null;
}

// Message event for communication with main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

// Activate event
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

console.log('SnapFit Service Worker loaded');
