{"version": 3, "sources": ["../src/main.ts"], "sourcesContent": ["import { NestFactory } from '@nestjs/core';\r\nimport { AppModule } from './app.module';\r\nimport { ValidationPipe } from '@nestjs/common';\r\nimport { HttpExceptionFilter } from './http-exception/http-exception.filter';\r\nimport * as dotenv from 'dotenv';\r\nimport { join } from 'path';\r\nimport { NestExpressApplication } from '@nestjs/platform-express';\r\nimport * as bodyParser from 'body-parser';\r\n\r\ndotenv.config();\r\n\r\nasync function bootstrap() {\r\n  const app = await NestFactory.create<NestExpressApplication>(AppModule);\r\n\r\n  \r\n  // app.use(express.static(join(__dirname, 'storage')));\r\n  // app.use(express.static(join(__dirname, '__temp')));\r\n\r\n  // Aumentar o limite para ~50MB\r\n  app.use(bodyParser.json({ limit: '50mb' }));\r\n  app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));\r\n\r\n  // Configurar CORS\r\n  app.enableCors({\r\n    // origin: 'http://localhost:3000',\r\n    origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5177', 'http://localhost:5178', 'http://localhost:5179', 'https://api.mysnapfit.com.br', 'https://api2.mysnapfit.com.br', 'https://app.mysnapfit.com.br'],\r\n    // origin: ['*'],\r\n    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',\r\n    credentials: true,\r\n  });\r\n\r\n  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));\r\n\r\n  app.useGlobalFilters(new HttpExceptionFilter());\r\n\r\n  // Serve arquivos estáticos media/...\r\n  app.useStaticAssets(join(__dirname, '..', 'media'), {\r\n    prefix: '/media/',\r\n  });\r\n\r\n  /*\r\n  // Serve arquivos estáticos storage/...\r\n  app.useStaticAssets(join(__dirname, '..', 'storage'), {\r\n    prefix: '/storage/',\r\n  });\r\n\r\n  // Serve aquivos estáticos __temp/...\r\n  app.useStaticAssets(join(__dirname, '..', '__temp'), {\r\n    prefix: '/__temp/',\r\n  });\r\n  */\r\n\r\n\r\n  await app.listen(process.env.PORT ?? 3000);\r\n}\r\nbootstrap();\r\n"], "names": ["dotenv", "config", "bootstrap", "app", "NestFactory", "create", "AppModule", "use", "<PERSON><PERSON><PERSON><PERSON>", "json", "limit", "u<PERSON><PERSON><PERSON>", "extended", "enableCors", "origin", "methods", "credentials", "useGlobalPipes", "ValidationPipe", "transform", "whitelist", "useGlobalFilters", "HttpExceptionFilter", "useStaticAssets", "join", "__dirname", "prefix", "listen", "process", "env", "PORT"], "mappings": ";;;;sBAA4B;2BACF;wBACK;qCACK;gEACZ;sBACH;oEAEO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5BA,QAAOC,MAAM;AAEb,eAAeC;IACb,MAAMC,MAAM,MAAMC,iBAAW,CAACC,MAAM,CAAyBC,oBAAS;IAGtE,uDAAuD;IACvD,sDAAsD;IAEtD,+BAA+B;IAC/BH,IAAII,GAAG,CAACC,YAAWC,IAAI,CAAC;QAAEC,OAAO;IAAO;IACxCP,IAAII,GAAG,CAACC,YAAWG,UAAU,CAAC;QAAEC,UAAU;QAAMF,OAAO;IAAO;IAE9D,kBAAkB;IAClBP,IAAIU,UAAU,CAAC;QACb,mCAAmC;QACnCC,QAAQ;YAAC;YAAyB;YAAyB;YAAyB;YAAyB;YAAyB;YAAgC;YAAiC;SAA+B;QACtO,iBAAiB;QACjBC,SAAS;QACTC,aAAa;IACf;IAEAb,IAAIc,cAAc,CAAC,IAAIC,sBAAc,CAAC;QAAEC,WAAW;QAAMC,WAAW;IAAK;IAEzEjB,IAAIkB,gBAAgB,CAAC,IAAIC,wCAAmB;IAE5C,qCAAqC;IACrCnB,IAAIoB,eAAe,CAACC,IAAAA,UAAI,EAACC,WAAW,MAAM,UAAU;QAClDC,QAAQ;IACV;IAEA;;;;;;;;;;EAUA,GAGA,MAAMvB,IAAIwB,MAAM,CAACC,QAAQC,GAAG,CAACC,IAAI,IAAI;AACvC;AACA5B"}