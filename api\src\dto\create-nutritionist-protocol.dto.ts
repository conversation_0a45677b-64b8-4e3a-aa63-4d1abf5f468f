import { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional, IsNumber, IsObject } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateNutritionistProtocolDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsInt()
  @Type(() => Number)
  type_id: number;

  @IsString()
  objective: string;

  @IsObject()
  nutritional_goals: {
    calories: number,
    protein: number,
    carbs: number,
    fat: number,
    water: number
  }
  
  @IsArray()
  @ArrayNotEmpty()
  meals: {
    food_id: number,
    meal_weight: number,
    meal_time: string
  }[];

  @IsOptional()
  @IsArray()
  supplements?: {
    name: string,
    dosage: string,
    supplement_time: string,
    notes?: string
  }[];

  @IsString()
  @IsOptional()
  general_notes?: string;
}