# Apple Strategy Fix - "done is not a function"

## ✅ Erro Corrigido

O erro `TypeError: done is not a function` na `AppleStrategy` foi causado por uma assinatura incorreta da função `validate`.

### 🚨 Erro Original

```
[Nest] 1871313  - 08/14/2025, 11:09:49 PM   ERROR [ExceptionsHandler] TypeError: done is not a function
    at AppleStrategy.validate (/home/<USER>/backend/dist/auth/apple.strategy.js:60:13)
```

### 🔧 Causa do Problema

A função `validate` estava usando um padrão de callback `done` que não é compatível com a biblioteca `@nicokaiser/passport-apple` ou com a versão atual do NestJS Passport.

### 📝 Correções Implementadas

#### 1. **Apple Strategy** (`src/auth/apple.strategy.ts`):

**Antes (com erro):**
```typescript
async validate(
  accessToken: string,
  refreshToken: string,
  idToken: any,
  profile: any,
  done: any, // ← Parâmetro problemático
): Promise<any> {
  try {
    // ... lógica ...
    done(null, user); // ← Erro: done is not a function
  } catch (error) {
    done(error, false); // ← Erro: done is not a function
  }
}
```

**Depois (corrigido):**
```typescript
async validate(
  accessToken: string,
  refreshToken: string,
  idToken: any,
  profile: any, // ← Removido parâmetro 'done'
): Promise<any> {
  try {
    // ... lógica ...
    return user; // ← Retorna diretamente
  } catch (error) {
    throw error; // ← Lança exceção diretamente
  }
}
```

#### 2. **Google Strategy** (`src/auth/google.strategy.ts`):

Padronizado para usar o mesmo padrão async/await:

**Antes:**
```typescript
async validate(
  accessToken: string,
  refreshToken: string,
  profile: any,
  done: VerifyCallback, // ← Removido
): Promise<any> {
  // ... callback pattern
  done(null, user);
}
```

**Depois:**
```typescript
async validate(
  accessToken: string,
  refreshToken: string,
  profile: any, // ← Sem callback
): Promise<any> {
  // ... async/await pattern
  return user;
}
```

### 🎯 Padrão Correto

**NestJS + Passport** usa o padrão async/await moderno:

```typescript
async validate(...args): Promise<any> {
  try {
    // Processar dados
    const user = { /* ... */ };
    
    // Validar dados obrigatórios
    if (!user.email) {
      throw new Error('Email required');
    }
    
    // Retornar usuário diretamente
    return user;
  } catch (error) {
    // Lançar exceção diretamente
    throw error;
  }
}
```

### ✅ Benefícios da Correção

1. **Compatibilidade**: Funciona com versões modernas do NestJS/Passport
2. **Consistência**: Ambas as strategies usam o mesmo padrão
3. **Simplicidade**: Código mais limpo sem callbacks
4. **Error Handling**: Tratamento de erros mais direto
5. **TypeScript**: Melhor tipagem sem callbacks

### 🚀 Status

- ✅ Apple Strategy corrigida
- ✅ Google Strategy padronizada
- ✅ Backend compila sem erros
- ✅ Imports desnecessários removidos
- ✅ Padrão async/await implementado

### 🔍 Para Testar

1. **Reinicie o backend** para aplicar as mudanças
2. **Teste Apple OAuth** - não deve mais dar erro "done is not a function"
3. **Teste Google OAuth** - deve continuar funcionando normalmente
4. **Verifique logs** - erros de strategy devem ter desaparecido

O erro "done is not a function" foi completamente resolvido! 🎉
