const axios = require('axios');

async function testAuth() {
  try {
    // Test login first
    console.log('🔐 Testing login...');
    const loginResponse = await axios.post('http://localhost:3000/auth/login', {
      email: '<EMAIL>', // Replace with a valid test email
      password: 'password123'    // Replace with a valid test password
    });

    console.log('✅ Login successful');
    const { access_token } = loginResponse.data.data;
    console.log('Access token:', access_token.substring(0, 20) + '...');

    // Test notifications endpoint with token
    console.log('\n📱 Testing notifications endpoint...');
    const notificationsResponse = await axios.get('http://localhost:3000/notifications?limit=50', {
      headers: {
        'Authorization': `Bearer ${access_token}`
      }
    });

    console.log('✅ Notifications endpoint working');
    console.log('Response:', JSON.stringify(notificationsResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 This is an authentication error. Make sure you have:');
      console.log('1. A valid user account in the database');
      console.log('2. Correct email and password in this test script');
    }
    
    if (error.response?.status === 500) {
      console.log('\n💡 This is a server error. Check:');
      console.log('1. Database connection');
      console.log('2. If notifications table exists');
      console.log('3. Server logs for more details');
    }
  }
}

// Test without authentication first
async function testNotificationsWithoutAuth() {
  try {
    console.log('🚫 Testing notifications without authentication...');
    await axios.get('http://localhost:3000/notifications?limit=50');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Correctly returns 401 Unauthorized without token');
    } else {
      console.log('❌ Unexpected error:', error.response?.data || error.message);
    }
  }
}

async function runTests() {
  await testNotificationsWithoutAuth();
  console.log('\n' + '='.repeat(50) + '\n');
  await testAuth();
}

runTests();
