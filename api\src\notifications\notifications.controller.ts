import { 
  <PERSON>, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { NotificationsService } from './notifications.service';

@Controller('notifications')
@UseGuards(JwtAuthGuard)
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Get()
  async getNotifications(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.notificationsService.getNotifications(userId, query);
  }

  @Get('unread-count')
  async getUnreadCount(@Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.getUnreadCount(userId);
  }

  @Post('mark-read')
  async markAsRead(@Body() body: { notificationIds: number[] }, @Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.markAsRead(body.notificationIds, userId);
  }

  @Post('mark-all-read')
  async markAllAsRead(@Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.markAllAsRead(userId);
  }

  @Get('settings')
  async getNotificationSettings(@Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.getNotificationSettings(userId);
  }

  @Put('settings')
  async updateNotificationSettings(@Body() settings: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.updateNotificationSettings(userId, settings);
  }

  @Post('push/subscribe')
  async subscribeToPushNotifications(@Body() subscription: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.subscribeToPushNotifications(userId, subscription);
  }

  @Post('push/unsubscribe')
  async unsubscribeFromPushNotifications(@Body() body: { endpoint: string }, @Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.unsubscribeFromPushNotifications(userId, body.endpoint);
  }

  @Post('test')
  async sendTestNotification(@Request() req: any) {
    const userId = req.user.userId;
    return this.notificationsService.sendTestNotification(userId);
  }
}
