{"version": 3, "sources": ["../src/app.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { AppController } from './app.controller';\r\nimport { AppService } from './app.service';\r\nimport * as dotenv from 'dotenv';\r\nimport { AuthModule } from './auth/auth.module';\r\nimport { DashboardController } from './dashboard/dashboard.controller';\r\nimport { DashboardService } from './dashboard/dashboard.service';\r\nimport { DashboardModule } from './dashboard/dashboard.module';\r\nimport { UsersController } from './users/users.controller';\r\nimport { AdminController } from './admin/admin.controller';\r\nimport { AdminModule } from './admin/admin.module';\r\nimport { AdminService } from './admin/admin.service';\r\nimport { CoachController } from './coach/coach.controller';\r\nimport { NutritionistController } from './nutritionist/nutritionist.controller';\r\nimport { CoachModule } from './coach/coach.module';\r\nimport { NutritionistModule } from './nutritionist/nutritionist.module';\r\nimport { CoachService } from './coach/coach.service';\r\nimport { NutritionistService } from './nutritionist/nutritionist.service';\r\nimport { UsersService } from './users/users.service';\r\nimport { UsersModule } from './users/users.module';\r\nimport { join } from 'path';\r\nimport { ServeStaticModule } from '@nestjs/serve-static';\r\nimport { AffiliatesModule } from './admin/affiliates.module';\r\nimport { AffiliatesController } from './admin/affiliates.controller';\r\nimport { AffiliatesService } from './admin/affiliates.service';\r\nimport { ClientsModule } from './clients/clients.module';\r\nimport { FriendsModule } from './friends/friends.module';\r\nimport { NotificationsModule } from './notifications/notifications.module';\r\nimport { FoodsModule } from './foods/foods.module';\r\nimport { ChallengesModule } from './challenges/challenges.module';\r\nimport { PointsModule } from './points/points.module';\r\nimport { RewardsModule } from './rewards/rewards.module';\r\nimport { CompatibilityModule } from './compatibility/compatibility.module';\r\nimport { WearablesModule } from './wearables/wearables.module';\r\nimport { AnalyticsModule } from './analytics/analytics.module';\r\nimport { SyncModule } from './sync/sync.module';\r\nimport { SettingsModule } from './settings/settings.module';\r\nimport { WalletModule } from './wallet/wallet.module';\r\n\r\ndotenv.config();\r\n\r\n/*\r\nTypeOrmModule.forRoot({\r\n    type: 'mysql',\r\n    host: process.env.DB_HOST,\r\n    // port: parseInt(process.env.DB_PORT || '3306'),\r\n    username: process.env.DB_USERNAME,\r\n    password: process.env.DB_PASSWORD,\r\n    database: process.env.DB_DATABASE,\r\n   ...\r\n*/\r\n\r\n@Module({\r\n  imports: [\r\n    /*\r\n  ServeStaticModule.forRoot(\r\n    {\r\n      rootPath: join(__dirname, '..', 'storage'),  // Caminho para o diretório de arquivos estáticos\r\n      serveRoot: '/storage/',                        // Prefixo da URL para acessar os arquivos\r\n    }),\r\n    ServeStaticModule.forRoot(\r\n    {\r\n      rootPath: join(__dirname, '..', '__temp'),  // Caminho para o diretório de arquivos estáticos\r\n      serveRoot: '/__temp/',                        // Prefixo da URL para acessar os arquivos\r\n    }), */\r\n  AuthModule,\r\n  DashboardModule,\r\n  AdminModule,\r\n  CoachModule,\r\n  NutritionistModule,\r\n  UsersModule,\r\n  AffiliatesModule,\r\n  ClientsModule,\r\n  FriendsModule,\r\n  NotificationsModule,\r\n  FoodsModule,\r\n  ChallengesModule,\r\n  PointsModule,\r\n  RewardsModule,\r\n  CompatibilityModule,\r\n  WearablesModule,\r\n  AnalyticsModule,\r\n  SyncModule,\r\n  SettingsModule,\r\n  WalletModule,\r\n],\r\n  controllers: [AppController, DashboardController, UsersController, AdminController, CoachController, NutritionistController, AffiliatesController],\r\n  providers: [AppService, DashboardService, AdminService, CoachService, NutritionistService, UsersService, AffiliatesService],\r\n})\r\nexport class AppModule {}\r\n"], "names": ["AppModule", "dotenv", "config", "imports", "AuthModule", "DashboardModule", "AdminModule", "CoachModule", "NutritionistModule", "UsersModule", "AffiliatesModule", "ClientsModule", "FriendsModule", "NotificationsModule", "FoodsModule", "ChallengesModule", "PointsModule", "RewardsModule", "CompatibilityModule", "WearablesModule", "AnalyticsModule", "SyncModule", "SettingsModule", "WalletModule", "controllers", "AppController", "DashboardController", "UsersController", "AdminController", "Coach<PERSON><PERSON><PERSON><PERSON>", "NutritionistController", "AffiliatesController", "providers", "AppService", "DashboardService", "AdminService", "CoachService", "NutritionistService", "UsersService", "AffiliatesService"], "mappings": ";;;;+BAyFaA;;;eAAAA;;;wBAzFU;+BA<PERSON>;4BACH;gEACH;4BACG;qCACS;kCACH;iCACD;iCACA;iCACA;6BACJ;8BACC;iCACG;wCACO;6BACX;oCACO;8BACN;qCACO;8BACP;6BACD;kCAGK;sCACI;mCACH;+BACJ;+BACA;qCACM;6BACR;kCACK;8BACJ;+BACC;qCACM;iCACJ;iCACA;4BACL;gCACI;8BACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7BC,QAAOC,MAAM;AAkDN,IAAA,AAAMF,YAAN,MAAMA;AAAW;;;QApCtBG,SAAS;YACP;;;;;;;;;;QAUI,GACNC,sBAAU;YACVC,gCAAe;YACfC,wBAAW;YACXC,wBAAW;YACXC,sCAAkB;YAClBC,wBAAW;YACXC,kCAAgB;YAChBC,4BAAa;YACbC,4BAAa;YACbC,wCAAmB;YACnBC,wBAAW;YACXC,kCAAgB;YAChBC,0BAAY;YACZC,4BAAa;YACbC,wCAAmB;YACnBC,gCAAe;YACfC,gCAAe;YACfC,sBAAU;YACVC,8BAAc;YACdC,0BAAY;SACb;QACCC,aAAa;YAACC,4BAAa;YAAEC,wCAAmB;YAAEC,gCAAe;YAAEC,gCAAe;YAAEC,gCAAe;YAAEC,8CAAsB;YAAEC,0CAAoB;SAAC;QAClJC,WAAW;YAACC,sBAAU;YAAEC,kCAAgB;YAAEC,0BAAY;YAAEC,0BAAY;YAAEC,wCAAmB;YAAEC,0BAAY;YAAEC,oCAAiB;SAAC"}