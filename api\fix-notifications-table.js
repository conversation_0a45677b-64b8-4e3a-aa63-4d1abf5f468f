const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixNotificationsTable() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });

    console.log('Connected to database successfully');

    // Check current structure of notifications table
    console.log('🔍 Checking current notifications table structure...');
    
    try {
      const [columns] = await connection.execute("DESCRIBE notifications");
      console.log('Current table structure:');
      columns.forEach(column => {
        console.log(`  - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(not null)'} ${column.Key ? `(${column.Key})` : ''}`);
      });
    } catch (error) {
      if (error.code === 'ER_NO_SUCH_TABLE') {
        console.log('❌ Table notifications does not exist');
      } else {
        console.log('❌ Error checking table structure:', error.message);
      }
    }

    // Drop and recreate the table with correct structure
    console.log('\n🔧 Dropping and recreating notifications table...');
    
    await connection.execute('DROP TABLE IF EXISTS notifications');
    console.log('✅ Dropped existing notifications table');

    await connection.execute(`
      CREATE TABLE notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        type VARCHAR(100) NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        data JSON,
        read_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_user_unread (user_id, read_at),
        INDEX idx_type (type),
        INDEX idx_created (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    
    console.log('✅ Created notifications table with correct structure');

    // Verify the new structure
    console.log('\n✅ Verifying new table structure...');
    const [newColumns] = await connection.execute("DESCRIBE notifications");
    console.log('New table structure:');
    newColumns.forEach(column => {
      console.log(`  - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(not null)'} ${column.Key ? `(${column.Key})` : ''}`);
    });

    // Insert some test notifications
    console.log('\n📝 Inserting test notifications...');
    
    // Get a user ID to use for test notifications
    const [users] = await connection.execute('SELECT id FROM users LIMIT 1');
    
    if (users.length > 0) {
      const userId = users[0].id;
      
      await connection.execute(`
        INSERT INTO notifications (user_id, type, title, message, data, created_at) VALUES
        (?, 'system', 'Welcome to SnapFit!', 'Your account has been created successfully.', '{"action": "welcome"}', NOW()),
        (?, 'protocol', 'New Protocol Available', 'A new workout protocol has been generated for you.', '{"protocol_id": 1}', NOW()),
        (?, 'achievement', 'Goal Achieved!', 'Congratulations! You have completed your daily water intake goal.', '{"goal": "water", "amount": 2000}', NOW())
      `, [userId, userId, userId]);
      
      console.log(`✅ Inserted 3 test notifications for user ID ${userId}`);
    } else {
      console.log('⚠️ No users found, skipping test notifications');
    }

    console.log('\n🎉 Notifications table fixed successfully!');

  } catch (error) {
    console.error('❌ Database error:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

fixNotificationsTable();
