"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AnalyticsController", {
    enumerable: true,
    get: function() {
        return AnalyticsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _analyticsservice = require("./analytics.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let AnalyticsController = class AnalyticsController {
    async getDashboardAnalytics(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getDashboardAnalytics(userId, query);
    }
    async getProgressSummary(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getProgressSummary(userId, query);
    }
    async getNutritionTrends(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getNutritionTrends(userId, query);
    }
    async getWorkoutTrends(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getWorkoutTrends(userId, query);
    }
    async getBodyCompositionAnalysis(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getBodyCompositionAnalysis(userId, query);
    }
    async getHabitsAnalysis(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getHabitsAnalysis(userId, query);
    }
    async getGoalsTracking(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getGoalsTracking(userId, query);
    }
    async getSocialStats(req) {
        const userId = req.user.userId;
        return this.analyticsService.getSocialStats(userId);
    }
    async getGamificationStats(req) {
        const userId = req.user.userId;
        return this.analyticsService.getGamificationStats(userId);
    }
    async getWeeklyReport(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getWeeklyReport(userId, query);
    }
    async getMonthlyReport(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getMonthlyReport(userId, query);
    }
    async getAIInsights(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getAIInsights(userId, query);
    }
    async getWeightPredictions(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getWeightPredictions(userId, query);
    }
    async getBenchmarkComparison(req, query) {
        const userId = req.user.userId;
        return this.analyticsService.getBenchmarkComparison(userId, query);
    }
    constructor(analyticsService){
        this.analyticsService = analyticsService;
    }
};
_ts_decorate([
    (0, _common.Get)('dashboard'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getDashboardAnalytics", null);
_ts_decorate([
    (0, _common.Get)('progress/summary'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getProgressSummary", null);
_ts_decorate([
    (0, _common.Get)('nutrition/trends'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getNutritionTrends", null);
_ts_decorate([
    (0, _common.Get)('workout/trends'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getWorkoutTrends", null);
_ts_decorate([
    (0, _common.Get)('body/composition'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getBodyCompositionAnalysis", null);
_ts_decorate([
    (0, _common.Get)('habits/analysis'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getHabitsAnalysis", null);
_ts_decorate([
    (0, _common.Get)('goals/tracking'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getGoalsTracking", null);
_ts_decorate([
    (0, _common.Get)('social/stats'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getSocialStats", null);
_ts_decorate([
    (0, _common.Get)('gamification/stats'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getGamificationStats", null);
_ts_decorate([
    (0, _common.Get)('reports/weekly'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getWeeklyReport", null);
_ts_decorate([
    (0, _common.Get)('reports/monthly'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getMonthlyReport", null);
_ts_decorate([
    (0, _common.Get)('insights/ai'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getAIInsights", null);
_ts_decorate([
    (0, _common.Get)('predictions/weight'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getWeightPredictions", null);
_ts_decorate([
    (0, _common.Get)('benchmarks/comparison'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getBenchmarkComparison", null);
AnalyticsController = _ts_decorate([
    (0, _common.Controller)('analytics'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _analyticsservice.AnalyticsService === "undefined" ? Object : _analyticsservice.AnalyticsService
    ])
], AnalyticsController);

//# sourceMappingURL=analytics.controller.js.map