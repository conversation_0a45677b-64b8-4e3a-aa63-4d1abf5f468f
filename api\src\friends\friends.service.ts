import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { db } from '../database';

@Injectable()
export class FriendsService {

  async searchUsers(query: string, currentUserId: number) {
    try {
      if (!query || query.length < 2) {
        return {
          status: 'success',
          data: []
        };
      }

      // Remove @ if present at the beginning
      const searchQuery = query.startsWith('@') ? query.slice(1) : query;

      const sql = `
        SELECT 
          u.id,
          u.name,
          u.username,
          u.photo,
          COALESCE(up.total_points, 0) as points,
          CASE 
            WHEN f.id IS NOT NULL THEN 'friends'
            WHEN fr_sent.id IS NOT NULL THEN 'pending_sent'
            WHEN fr_received.id IS NOT NULL THEN 'pending_received'
            ELSE 'none'
          END as friendshipStatus
        FROM users u
        LEFT JOIN user_points up ON u.id = up.user_id
        LEFT JOIN users_friends f ON (
          (f.user_id = ? AND f.friend_id = u.id) OR
          (f.friend_id = ? AND f.user_id = u.id)
        ) AND f.status = 1 AND f.deleted_at IS NULL
        LEFT JOIN friend_requests fr_sent ON fr_sent.from_user_id = ? AND fr_sent.to_user_id = u.id AND fr_sent.status = 'pending'
        LEFT JOIN friend_requests fr_received ON fr_received.from_user_id = u.id AND fr_received.to_user_id = ? AND fr_received.status = 'pending'
        WHERE u.id != ? 
        AND (u.name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)
        AND u.deleted_at IS NULL
        ORDER BY u.name ASC
        LIMIT 20
      `;

      const searchPattern = `%${searchQuery}%`;
      const [results] = await db.execute(sql, [
        currentUserId, currentUserId, currentUserId, currentUserId, currentUserId,
        searchPattern, searchPattern, searchPattern
      ]);

      return {
        status: 'success',
        data: results
      };
    } catch (error) {
      console.error('Error searching users:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to search users'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getFriends(userId: number) {
    try {
      const sql = `
        SELECT 
          u.id,
          u.name,
          u.username,
          u.photo,
          COALESCE(up.total_points, 0) as points,
          COALESCE(up.monthly_points, 0) as monthlyPoints,
          COALESCE(up.yearly_points, 0) as yearlyPoints,
          u.last_active as lastActive,
          f.created_at as friendshipDate
        FROM users_friends f
        INNER JOIN users u ON (
          CASE
            WHEN f.user_id = ? THEN u.id = f.friend_id
            ELSE u.id = f.user_id
          END
        )
        LEFT JOIN user_points up ON u.id = up.user_id
        WHERE (f.user_id = ? OR f.friend_id = ?)
        AND f.status = 1 AND f.deleted_at IS NULL
        ORDER BY up.total_points DESC, u.name ASC
      `;

      const [results] = await db.execute(sql, [userId, userId, userId]);

      // Check if results is valid
      if (!results || !Array.isArray(results)) {
        console.log('No results or invalid results format for getFriends:', results);
        return {
          status: 'success',
          data: []
        };
      }

      // Add ranking
      const friendsWithRank = results.map((friend, index) => ({
        ...friend,
        rank: index + 1
      }));

      return {
        status: 'success',
        data: friendsWithRank
      };
    } catch (error) {
      console.error('Error getting friends:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get friends'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getFriendRequests(userId: number) {
    try {
      const sql = `
        SELECT 
          fr.id,
          fr.from_user_id as fromUserId,
          fr.to_user_id as toUserId,
          fr.status,
          fr.created_at as createdAt,
          fr.updated_at as updatedAt,
          u_from.id as fromUserId,
          u_from.name as fromUserName,
          u_from.username as fromUserUsername,
          u_from.photo as fromUserPhoto,
          COALESCE(up_from.total_points, 0) as fromUserPoints,
          u_to.id as toUserId,
          u_to.name as toUserName,
          u_to.username as toUserUsername,
          u_to.photo as toUserPhoto,
          COALESCE(up_to.total_points, 0) as toUserPoints
        FROM friend_requests fr
        INNER JOIN users u_from ON fr.from_user_id = u_from.id
        INNER JOIN users u_to ON fr.to_user_id = u_to.id
        LEFT JOIN user_points up_from ON u_from.id = up_from.user_id
        LEFT JOIN user_points up_to ON u_to.id = up_to.user_id
        WHERE (fr.from_user_id = ? OR fr.to_user_id = ?) 
        AND fr.status = 'pending'
        ORDER BY fr.created_at DESC
      `;

      const [results] = await db.execute(sql, [userId, userId]);

      // Check if results is valid
      if (!results || !Array.isArray(results)) {
        console.log('No results or invalid results format for getFriendRequests:', results);
        return {
          status: 'success',
          data: []
        };
      }

      // Format the results
      const formattedResults = results.map((row: any) => ({
        id: row.id,
        fromUserId: row.fromUserId,
        toUserId: row.toUserId,
        fromUser: {
          id: row.fromUserId,
          name: row.fromUserName,
          username: row.fromUserUsername,
          photo: row.fromUserPhoto,
          points: row.fromUserPoints
        },
        toUser: {
          id: row.toUserId,
          name: row.toUserName,
          username: row.toUserUsername,
          photo: row.toUserPhoto,
          points: row.toUserPoints
        },
        status: row.status,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt
      }));

      return {
        status: 'success',
        data: formattedResults
      };
    } catch (error) {
      console.error('Error getting friend requests:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get friend requests'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async sendFriendRequest(fromUserId: number, toUserId: number) {
    try {
      if (fromUserId === toUserId) {
        throw new HttpException({
          status: 'error',
          message: 'Cannot send friend request to yourself'
        }, HttpStatus.BAD_REQUEST);
      }

      // Check if users are already friends
      const [existingFriendship] = await db.execute(
        `SELECT id FROM users_friends
         WHERE ((user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?))
         AND status = 1 AND deleted_at IS NULL`,
        [fromUserId, toUserId, toUserId, fromUserId]
      );

      if (existingFriendship[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Users are already friends'
        }, HttpStatus.CONFLICT);
      }

      // Check if there's already a pending request
      const [existingRequest] = await db.execute(
        `SELECT id FROM friend_requests 
         WHERE ((from_user_id = ? AND to_user_id = ?) OR (from_user_id = ? AND to_user_id = ?))
         AND status = 'pending'`,
        [fromUserId, toUserId, toUserId, fromUserId]
      );

      if (existingRequest[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Friend request already exists'
        }, HttpStatus.CONFLICT);
      }

      // Check if target user exists
      const [targetUser] = await db.execute(
        'SELECT id FROM users WHERE id = ?',
        [toUserId]
      );

      if (!targetUser[0]) {
        throw new HttpException({
          status: 'error',
          message: 'User not found'
        }, HttpStatus.NOT_FOUND);
      }

      // Create friend request
      await db.execute(
        `INSERT INTO friend_requests (from_user_id, to_user_id, status, created_at, updated_at)
         VALUES (?, ?, 'pending', NOW(), NOW())`,
        [fromUserId, toUserId]
      );

      return {
        status: 'success',
        message: 'Friend request sent successfully'
      };
    } catch (error) {
      console.error('Error sending friend request:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to send friend request'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async acceptFriendRequest(requestId: number, userId: number) {
    try {
      // Get the friend request
      const [request] = await db.execute(
        'SELECT from_user_id, to_user_id FROM friend_requests WHERE id = ? AND to_user_id = ? AND status = "pending"',
        [requestId, userId]
      );

      if (!request[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Friend request not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      const { from_user_id: fromUserId, to_user_id: toUserId } = request[0];

      // Start transaction
      await db.execute('START TRANSACTION');

      try {
        // Update friend request status
        await db.execute(
          'UPDATE friend_requests SET status = "accepted", updated_at = NOW() WHERE id = ?',
          [requestId]
        );

        // Create friendship
        await db.execute(
          `INSERT INTO users_friends (user_id, friend_id, status, created_at, updated_at)
           VALUES (?, ?, 1, NOW(), NOW())`,
          [fromUserId, toUserId]
        );

        await db.execute('COMMIT');

        return {
          status: 'success',
          message: 'Friend request accepted successfully'
        };
      } catch (error) {
        await db.execute('ROLLBACK');
        throw error;
      }
    } catch (error) {
      console.error('Error accepting friend request:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to accept friend request'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async rejectFriendRequest(requestId: number, userId: number) {
    try {
      // Verify the request belongs to the user
      const [request] = await db.execute(
        'SELECT id FROM friend_requests WHERE id = ? AND to_user_id = ? AND status = "pending"',
        [requestId, userId]
      );

      if (!request[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Friend request not found or not accessible'
        }, HttpStatus.NOT_FOUND);
      }

      // Update friend request status
      await db.execute(
        'UPDATE friend_requests SET status = "rejected", updated_at = NOW() WHERE id = ?',
        [requestId]
      );

      return {
        status: 'success',
        message: 'Friend request rejected successfully'
      };
    } catch (error) {
      console.error('Error rejecting friend request:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to reject friend request'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async removeFriend(userId: number, friendId: number) {
    try {
      // Check if friendship exists
      const [friendship] = await db.execute(
        `SELECT id FROM users_friends
         WHERE ((user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?))
         AND status = 1 AND deleted_at IS NULL`,
        [userId, friendId, friendId, userId]
      );

      if (!friendship[0]) {
        throw new HttpException({
          status: 'error',
          message: 'Friendship not found'
        }, HttpStatus.NOT_FOUND);
      }

      // Remove friendship (soft delete)
      await db.execute(
        `UPDATE users_friends SET deleted_at = NOW()
         WHERE ((user_id = ? AND friend_id = ?) OR (user_id = ? AND friend_id = ?))
         AND deleted_at IS NULL`,
        [userId, friendId, friendId, userId]
      );

      return {
        status: 'success',
        message: 'Friend removed successfully'
      };
    } catch (error) {
      console.error('Error removing friend:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to remove friend'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getFriendsRanking(userId: number, query: any) {
    try {
      const { period = 'all' } = query;
      
      let pointsColumn = 'up.total_points';
      if (period === 'monthly') pointsColumn = 'up.monthly_points';
      if (period === 'yearly') pointsColumn = 'up.yearly_points';

      const sql = `
        SELECT 
          u.id,
          u.name,
          u.username,
          u.photo,
          COALESCE(${pointsColumn}, 0) as points,
          u.last_active as lastActive
        FROM users_friends f
        INNER JOIN users u ON (
          CASE 
            WHEN f.user_id = ? THEN u.id = f.friend_id
            ELSE u.id = f.user_id
          END
        )
        LEFT JOIN user_points up ON u.id = up.user_id
        WHERE (f.user_id = ? OR f.friend_id = ?)
        AND f.status = 1 AND f.deleted_at IS NULL
        ORDER BY ${pointsColumn} DESC, u.name ASC
      `;

      const [results] = await db.execute(sql, [userId, userId, userId]);

      // Add ranking and include current user
      const [currentUser] = await db.execute(
        `SELECT u.id, u.name, u.username, u.photo, COALESCE(${pointsColumn}, 0) as points
         FROM users u
         LEFT JOIN user_points up ON u.id = up.user_id
         WHERE u.id = ?`,
        [userId]
      );

      const allUsers = [...results, ...currentUser];
      allUsers.sort((a, b) => b.points - a.points);

      const rankedUsers = allUsers.map((user, index) => ({
        ...user,
        rank: index + 1,
        isCurrentUser: user.id === userId
      }));

      return {
        status: 'success',
        data: rankedUsers
      };
    } catch (error) {
      console.error('Error getting friends ranking:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get friends ranking'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
