{"version": 3, "sources": ["../src/app.controller.ts"], "sourcesContent": ["import { Controller, Get, Post, Request, UseGuards } from '@nestjs/common';\r\nimport { AppService } from './app.service';\r\nimport { JwtAuthGuard } from './auth/jwt-auth.guard';\r\n\r\n@Controller()\r\nexport class AppController {\r\n  constructor(private readonly appService: AppService) {}\r\n\r\n  @Get()\r\n  getHello(): string {\r\n    return this.appService.getHello();\r\n  }\r\n\r\n  @Get('protected')\r\n  @UseGuards(JwtAuthGuard)\r\n  getProtected(@Request() request: any): string {\r\n    return 'Protected route. User ID: '+request.user.userId;\r\n  }\r\n}\r\n"], "names": ["AppController", "get<PERSON>ello", "appService", "getProtected", "request", "user", "userId", "constructor"], "mappings": ";;;;+BAKaA;;;eAAAA;;;wBAL6C;4BAC/B;8BACE;;;;;;;;;;;;;;;AAGtB,IAAA,AAAMA,gBAAN,MAAMA;IAIXC,WAAmB;QACjB,OAAO,IAAI,CAACC,UAAU,CAACD,QAAQ;IACjC;IAIAE,aAAa,AAAWC,OAAY,EAAU;QAC5C,OAAO,+BAA6BA,QAAQC,IAAI,CAACC,MAAM;IACzD;IAXAC,YAAY,AAAiBL,UAAsB,CAAE;aAAxBA,aAAAA;IAAyB;AAYxD"}