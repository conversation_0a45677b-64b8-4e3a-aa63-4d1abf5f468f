"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AppModule", {
    enumerable: true,
    get: function() {
        return AppModule;
    }
});
const _common = require("@nestjs/common");
const _appcontroller = require("./app.controller");
const _appservice = require("./app.service");
const _dotenv = /*#__PURE__*/ _interop_require_wildcard(require("dotenv"));
const _authmodule = require("./auth/auth.module");
const _dashboardcontroller = require("./dashboard/dashboard.controller");
const _dashboardservice = require("./dashboard/dashboard.service");
const _dashboardmodule = require("./dashboard/dashboard.module");
const _userscontroller = require("./users/users.controller");
const _admincontroller = require("./admin/admin.controller");
const _adminmodule = require("./admin/admin.module");
const _adminservice = require("./admin/admin.service");
const _coachcontroller = require("./coach/coach.controller");
const _nutritionistcontroller = require("./nutritionist/nutritionist.controller");
const _coachmodule = require("./coach/coach.module");
const _nutritionistmodule = require("./nutritionist/nutritionist.module");
const _coachservice = require("./coach/coach.service");
const _nutritionistservice = require("./nutritionist/nutritionist.service");
const _usersservice = require("./users/users.service");
const _usersmodule = require("./users/users.module");
const _affiliatesmodule = require("./admin/affiliates.module");
const _affiliatescontroller = require("./admin/affiliates.controller");
const _affiliatesservice = require("./admin/affiliates.service");
const _clientsmodule = require("./clients/clients.module");
const _friendsmodule = require("./friends/friends.module");
const _notificationsmodule = require("./notifications/notifications.module");
const _foodsmodule = require("./foods/foods.module");
const _challengesmodule = require("./challenges/challenges.module");
const _pointsmodule = require("./points/points.module");
const _rewardsmodule = require("./rewards/rewards.module");
const _compatibilitymodule = require("./compatibility/compatibility.module");
const _wearablesmodule = require("./wearables/wearables.module");
const _analyticsmodule = require("./analytics/analytics.module");
const _syncmodule = require("./sync/sync.module");
const _settingsmodule = require("./settings/settings.module");
const _walletmodule = require("./wallet/wallet.module");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
_dotenv.config();
let AppModule = class AppModule {
};
AppModule = _ts_decorate([
    (0, _common.Module)({
        imports: [
            /*
  ServeStaticModule.forRoot(
    {
      rootPath: join(__dirname, '..', 'storage'),  // Caminho para o diretório de arquivos estáticos
      serveRoot: '/storage/',                        // Prefixo da URL para acessar os arquivos
    }),
    ServeStaticModule.forRoot(
    {
      rootPath: join(__dirname, '..', '__temp'),  // Caminho para o diretório de arquivos estáticos
      serveRoot: '/__temp/',                        // Prefixo da URL para acessar os arquivos
    }), */ _authmodule.AuthModule,
            _dashboardmodule.DashboardModule,
            _adminmodule.AdminModule,
            _coachmodule.CoachModule,
            _nutritionistmodule.NutritionistModule,
            _usersmodule.UsersModule,
            _affiliatesmodule.AffiliatesModule,
            _clientsmodule.ClientsModule,
            _friendsmodule.FriendsModule,
            _notificationsmodule.NotificationsModule,
            _foodsmodule.FoodsModule,
            _challengesmodule.ChallengesModule,
            _pointsmodule.PointsModule,
            _rewardsmodule.RewardsModule,
            _compatibilitymodule.CompatibilityModule,
            _wearablesmodule.WearablesModule,
            _analyticsmodule.AnalyticsModule,
            _syncmodule.SyncModule,
            _settingsmodule.SettingsModule,
            _walletmodule.WalletModule
        ],
        controllers: [
            _appcontroller.AppController,
            _dashboardcontroller.DashboardController,
            _userscontroller.UsersController,
            _admincontroller.AdminController,
            _coachcontroller.CoachController,
            _nutritionistcontroller.NutritionistController,
            _affiliatescontroller.AffiliatesController
        ],
        providers: [
            _appservice.AppService,
            _dashboardservice.DashboardService,
            _adminservice.AdminService,
            _coachservice.CoachService,
            _nutritionistservice.NutritionistService,
            _usersservice.UsersService,
            _affiliatesservice.AffiliatesService
        ]
    })
], AppModule);

//# sourceMappingURL=app.module.js.map