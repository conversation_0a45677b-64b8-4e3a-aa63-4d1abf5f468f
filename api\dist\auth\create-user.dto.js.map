{"version": 3, "sources": ["../../src/auth/create-user.dto.ts"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';\r\n\r\nexport class CreateUserDto {\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @IsEmail()\r\n  email: string;\r\n\r\n  @IsNotEmpty()\r\n  @MinLength(6) // A senha deve ter pelo menos 6 caracteres\r\n  password: string;\r\n\r\n  @IsOptional()\r\n  invite?: string;\r\n}"], "names": ["CreateUserDto"], "mappings": ";;;;+BAEaA;;;eAAAA;;;gCAF8C;;;;;;;;;;AAEpD,IAAA,AAAMA,gBAAN,MAAMA;AAab"}