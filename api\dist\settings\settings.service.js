"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SettingsService", {
    enumerable: true,
    get: function() {
        return SettingsService;
    }
});
const _common = require("@nestjs/common");
const _dayjs = /*#__PURE__*/ _interop_require_wildcard(require("dayjs"));
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let SettingsService = class SettingsService {
    async getAllSettings(userId) {
        return {
            status: 'success',
            data: {
                profile: {},
                privacy: {},
                goals: {},
                units: {},
                integrations: {},
                reminders: {},
                themes: {},
                dataRetention: {}
            }
        };
    }
    async getProfileSettings(userId) {
        return {
            status: 'success',
            data: {
                displayName: '',
                bio: '',
                profileVisibility: 'public',
                showAchievements: true,
                showProgress: true
            }
        };
    }
    async updateProfileSettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async getPrivacySettings(userId) {
        return {
            status: 'success',
            data: {
                profileVisibility: 'public',
                dataSharing: false,
                analyticsOptOut: false,
                marketingOptOut: false
            }
        };
    }
    async updatePrivacySettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async getGoalSettings(userId) {
        return {
            status: 'success',
            data: {
                primaryGoal: '',
                targetWeight: 0,
                targetBodyFat: 0,
                weeklyGoal: '',
                reminderFrequency: 'daily'
            }
        };
    }
    async updateGoalSettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async getUnitSettings(userId) {
        return {
            status: 'success',
            data: {
                weightUnit: 'kg',
                heightUnit: 'cm',
                distanceUnit: 'km',
                temperatureUnit: 'celsius'
            }
        };
    }
    async updateUnitSettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async getIntegrationSettings(userId) {
        return {
            status: 'success',
            data: {
                googleFit: false,
                appleHealth: false,
                fitbit: false,
                strava: false,
                myFitnessPal: false
            }
        };
    }
    async updateIntegrationSettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async getReminderSettings(userId) {
        return {
            status: 'success',
            data: {
                workoutReminders: true,
                mealReminders: true,
                waterReminders: true,
                sleepReminders: true,
                reminderTimes: []
            }
        };
    }
    async updateReminderSettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async testReminder(userId, reminderData) {
        return {
            status: 'success',
            data: {
                sent: true,
                type: reminderData.type || 'test'
            }
        };
    }
    async getThemeSettings(userId) {
        return {
            status: 'success',
            data: {
                theme: 'light',
                accentColor: '#007bff',
                fontSize: 'medium',
                animations: true
            }
        };
    }
    async updateThemeSettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async getDataRetentionSettings(userId) {
        return {
            status: 'success',
            data: {
                retentionPeriod: '2years',
                autoDelete: false,
                backupBeforeDelete: true
            }
        };
    }
    async updateDataRetentionSettings(userId, settings) {
        return {
            status: 'success',
            data: {
                updated: true,
                settings
            }
        };
    }
    async resetSettings(userId, resetOptions) {
        return {
            status: 'success',
            data: {
                reset: true,
                categories: resetOptions.categories || []
            }
        };
    }
    async exportSettings(userId) {
        return {
            status: 'success',
            data: {
                exportId: 'settings_export_' + Date.now(),
                downloadUrl: null,
                expiresAt: _dayjs().add(24, 'hours').toISOString()
            }
        };
    }
    async importSettings(userId, settingsData) {
        return {
            status: 'success',
            data: {
                imported: true,
                categoriesImported: Object.keys(settingsData).length
            }
        };
    }
};
SettingsService = _ts_decorate([
    (0, _common.Injectable)()
], SettingsService);

//# sourceMappingURL=settings.service.js.map