{"version": 3, "sources": ["../../src/foods/foods.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { FoodsController } from './foods.controller';\nimport { FoodsService } from './foods.service';\n\n@Module({\n  controllers: [FoodsController],\n  providers: [FoodsService],\n  exports: [FoodsService],\n})\nexport class FoodsModule {}\n"], "names": ["FoodsModule", "controllers", "FoodsController", "providers", "FoodsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;iCACS;8BACH;;;;;;;AAOtB,IAAA,AAAMA,cAAN,MAAMA;AAAa;;;QAJxBC,aAAa;YAACC,gCAAe;SAAC;QAC9BC,WAAW;YAACC,0BAAY;SAAC;QACzBC,SAAS;YAACD,0BAAY;SAAC"}