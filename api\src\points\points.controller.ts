import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { PointsService } from './points.service';

@Controller('points')
@UseGuards(JwtAuthGuard)
export class PointsController {
  constructor(private readonly pointsService: PointsService) {}

  @Get('balance')
  async getPointsBalance(@Request() req: any) {
    const userId = req.user.userId;
    return this.pointsService.getPointsBalance(userId);
  }

  @Get('history')
  async getPointsHistory(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.pointsService.getPointsHistory(userId, query);
  }

  @Post('earn')
  async earnPoints(@Body() body: { points: number; reason: string }, @Request() req: any) {
    const userId = req.user.userId;
    return this.pointsService.earnPoints(userId, body.points, body.reason);
  }

  @Post('spend')
  async spendPoints(@Body() body: { points: number; reason: string }, @Request() req: any) {
    const userId = req.user.userId;
    return this.pointsService.spendPoints(userId, body.points, body.reason);
  }

  @Get('leaderboard')
  async getLeaderboard(@Query() query: any) {
    return this.pointsService.getLeaderboard(query);
  }
}
