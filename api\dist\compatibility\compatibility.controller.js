"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "CompatibilityController", {
    enumerable: true,
    get: function() {
        return CompatibilityController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let CompatibilityController = class CompatibilityController {
    // Redirect /user/data to /users/me
    getUserData() {
    // This will redirect to the new endpoint
    }
    // Redirect /subscriptions to /users/subscriptions
    getSubscriptions() {
    // This will redirect to the new endpoint
    }
    cancelSubscription() {
    // This will redirect to the new endpoint
    }
    // Redirect /protocols/coach to /coach/protocols
    getCoachProtocols() {
    // This will redirect to the new endpoint
    }
    // Redirect /protocols/nutritionist to /nutritionist/protocols
    getNutritionistProtocols() {
    // This will redirect to the new endpoint
    }
    // Profile friends endpoint (redirect to friends)
    getProfileFriends() {
    // This will redirect to the new endpoint
    }
};
_ts_decorate([
    (0, _common.Get)('user/data'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Redirect)('/users/me', 301),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], CompatibilityController.prototype, "getUserData", null);
_ts_decorate([
    (0, _common.Get)('subscriptions'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Redirect)('/users/subscriptions', 301),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], CompatibilityController.prototype, "getSubscriptions", null);
_ts_decorate([
    (0, _common.Post)('subscriptions/:id/cancel'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Redirect)('/users/subscriptions/:id/cancel', 301),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], CompatibilityController.prototype, "cancelSubscription", null);
_ts_decorate([
    (0, _common.Get)('protocols/coach'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Redirect)('/coach/protocols', 301),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], CompatibilityController.prototype, "getCoachProtocols", null);
_ts_decorate([
    (0, _common.Get)('protocols/nutritionist'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Redirect)('/nutritionist/protocols', 301),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], CompatibilityController.prototype, "getNutritionistProtocols", null);
_ts_decorate([
    (0, _common.Get)('profile/friends'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Redirect)('/friends', 301),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], CompatibilityController.prototype, "getProfileFriends", null);
CompatibilityController = _ts_decorate([
    (0, _common.Controller)()
], CompatibilityController);

//# sourceMappingURL=compatibility.controller.js.map