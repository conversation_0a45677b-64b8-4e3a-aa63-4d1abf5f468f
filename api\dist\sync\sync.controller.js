"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SyncController", {
    enumerable: true,
    get: function() {
        return SyncController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _syncservice = require("./sync.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let SyncController = class SyncController {
    async getSyncStatus(req) {
        const userId = req.user.userId;
        return this.syncService.getSyncStatus(userId);
    }
    async performFullSync(req, options) {
        const userId = req.user.userId;
        return this.syncService.performFullSync(userId, options);
    }
    async performIncrementalSync(req, options) {
        const userId = req.user.userId;
        return this.syncService.performIncrementalSync(userId, options);
    }
    async getSyncConflicts(req) {
        const userId = req.user.userId;
        return this.syncService.getSyncConflicts(userId);
    }
    async resolveSyncConflicts(req, resolutions) {
        const userId = req.user.userId;
        return this.syncService.resolveSyncConflicts(userId, resolutions);
    }
    async createBackup(req, options) {
        const userId = req.user.userId;
        return this.syncService.createBackup(userId, options);
    }
    async listBackups(req) {
        const userId = req.user.userId;
        return this.syncService.listBackups(userId);
    }
    async restoreBackup(req, restoreData) {
        const userId = req.user.userId;
        return this.syncService.restoreBackup(userId, restoreData);
    }
    async exportUserData(req, options) {
        const userId = req.user.userId;
        return this.syncService.exportUserData(userId, options);
    }
    async importUserData(req, importData) {
        const userId = req.user.userId;
        return this.syncService.importUserData(userId, importData);
    }
    async getSyncDevices(req) {
        const userId = req.user.userId;
        return this.syncService.getSyncDevices(userId);
    }
    async registerDevice(req, deviceInfo) {
        const userId = req.user.userId;
        return this.syncService.registerDevice(userId, deviceInfo);
    }
    async unregisterDevice(req, deviceId) {
        const userId = req.user.userId;
        return this.syncService.unregisterDevice(userId, deviceId);
    }
    constructor(syncService){
        this.syncService = syncService;
    }
};
_ts_decorate([
    (0, _common.Get)('status'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "getSyncStatus", null);
_ts_decorate([
    (0, _common.Post)('full'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "performFullSync", null);
_ts_decorate([
    (0, _common.Post)('incremental'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "performIncrementalSync", null);
_ts_decorate([
    (0, _common.Get)('conflicts'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "getSyncConflicts", null);
_ts_decorate([
    (0, _common.Post)('conflicts/resolve'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "resolveSyncConflicts", null);
_ts_decorate([
    (0, _common.Get)('backup/create'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "createBackup", null);
_ts_decorate([
    (0, _common.Get)('backup/list'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "listBackups", null);
_ts_decorate([
    (0, _common.Post)('backup/restore'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "restoreBackup", null);
_ts_decorate([
    (0, _common.Get)('export/data'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "exportUserData", null);
_ts_decorate([
    (0, _common.Post)('import/data'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "importUserData", null);
_ts_decorate([
    (0, _common.Get)('devices'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "getSyncDevices", null);
_ts_decorate([
    (0, _common.Post)('devices/register'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "registerDevice", null);
_ts_decorate([
    (0, _common.Post)('devices/:deviceId/unregister'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], SyncController.prototype, "unregisterDevice", null);
SyncController = _ts_decorate([
    (0, _common.Controller)('sync'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _syncservice.SyncService === "undefined" ? Object : _syncservice.SyncService
    ])
], SyncController);

//# sourceMappingURL=sync.controller.js.map