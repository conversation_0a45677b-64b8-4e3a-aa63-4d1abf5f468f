"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "NotificationsService", {
    enumerable: true,
    get: function() {
        return NotificationsService;
    }
});
const _common = require("@nestjs/common");
const _database = require("../database");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let NotificationsService = class NotificationsService {
    async getNotifications(userId, query) {
        try {
            const { page = 1, limit = 20, type, read } = query;
            const limitNum = parseInt(limit, 10) || 20;
            const pageNum = parseInt(page, 10) || 1;
            const offset = (pageNum - 1) * limitNum;
            let sql = `
        SELECT 
          id,
          type,
          title,
          message,
          data,
          read_at as readAt,
          created_at as createdAt
        FROM notifications 
        WHERE user_id = ?
      `;
            const params = [
                userId
            ];
            if (type) {
                sql += ` AND type = ?`;
                params.push(type);
            }
            if (read !== undefined) {
                if (read === 'true') {
                    sql += ` AND read_at IS NOT NULL`;
                } else {
                    sql += ` AND read_at IS NULL`;
                }
            }
            sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
            params.push(limitNum, offset);
            const [notifications] = await _database.db.execute(sql, params);
            // Check if notifications is valid
            if (!notifications || !Array.isArray(notifications)) {
                console.log('No notifications or invalid notifications format:', notifications);
                return {
                    status: 'success',
                    data: {
                        notifications: [],
                        pagination: {
                            page: pageNum,
                            limit: limitNum,
                            total: 0,
                            totalPages: 0
                        }
                    }
                };
            }
            // Get total count
            let countSql = `SELECT COUNT(*) as total FROM notifications WHERE user_id = ?`;
            const countParams = [
                userId
            ];
            if (type) {
                countSql += ` AND type = ?`;
                countParams.push(type);
            }
            if (read !== undefined) {
                if (read === 'true') {
                    countSql += ` AND read_at IS NOT NULL`;
                } else {
                    countSql += ` AND read_at IS NULL`;
                }
            }
            const [countResult] = await _database.db.execute(countSql, countParams);
            const total = countResult[0]?.total || 0;
            return {
                status: 'success',
                data: {
                    notifications: notifications.map((notification)=>({
                            ...notification,
                            data: notification.data ? JSON.parse(notification.data) : null,
                            isRead: !!notification.readAt
                        })),
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        totalPages: Math.ceil(total / limitNum)
                    }
                }
            };
        } catch (error) {
            console.error('Error getting notifications:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get notifications'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUnreadCount(userId) {
        try {
            const [result] = await _database.db.execute('SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND read_at IS NULL', [
                userId
            ]);
            return {
                status: 'success',
                data: {
                    unreadCount: result[0]?.count || 0
                }
            };
        } catch (error) {
            console.error('Error getting unread count:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get unread count'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async markAsRead(notificationIds, userId) {
        try {
            if (!notificationIds || notificationIds.length === 0) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'No notification IDs provided'
                }, _common.HttpStatus.BAD_REQUEST);
            }
            const placeholders = notificationIds.map(()=>'?').join(',');
            await _database.db.execute(`UPDATE notifications 
         SET read_at = NOW() 
         WHERE id IN (${placeholders}) AND user_id = ? AND read_at IS NULL`, [
                ...notificationIds,
                userId
            ]);
            return {
                status: 'success',
                message: 'Notifications marked as read'
            };
        } catch (error) {
            console.error('Error marking notifications as read:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to mark notifications as read'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async markAllAsRead(userId) {
        try {
            await _database.db.execute('UPDATE notifications SET read_at = NOW() WHERE user_id = ? AND read_at IS NULL', [
                userId
            ]);
            return {
                status: 'success',
                message: 'All notifications marked as read'
            };
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to mark all notifications as read'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getNotificationSettings(userId) {
        try {
            const [settings] = await _database.db.execute(`SELECT 
          push_enabled,
          email_enabled,
          workout_reminders,
          meal_reminders,
          progress_updates,
          social_notifications,
          marketing_emails
         FROM notification_settings 
         WHERE user_id = ?`, [
                userId
            ]);
            // Return default settings if none exist
            const defaultSettings = {
                pushEnabled: true,
                emailEnabled: true,
                workoutReminders: true,
                mealReminders: true,
                progressUpdates: true,
                socialNotifications: true,
                marketingEmails: false
            };
            const userSettings = settings[0] ? {
                pushEnabled: !!settings[0].push_enabled,
                emailEnabled: !!settings[0].email_enabled,
                workoutReminders: !!settings[0].workout_reminders,
                mealReminders: !!settings[0].meal_reminders,
                progressUpdates: !!settings[0].progress_updates,
                socialNotifications: !!settings[0].social_notifications,
                marketingEmails: !!settings[0].marketing_emails
            } : defaultSettings;
            return {
                status: 'success',
                data: userSettings
            };
        } catch (error) {
            console.error('Error getting notification settings:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get notification settings'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateNotificationSettings(userId, settings) {
        try {
            const { pushEnabled, emailEnabled, workoutReminders, mealReminders, progressUpdates, socialNotifications, marketingEmails } = settings;
            // Check if settings exist
            const [existing] = await _database.db.execute('SELECT id FROM notification_settings WHERE user_id = ?', [
                userId
            ]);
            if (existing[0]) {
                // Update existing settings
                await _database.db.execute(`UPDATE notification_settings SET
           push_enabled = ?,
           email_enabled = ?,
           workout_reminders = ?,
           meal_reminders = ?,
           progress_updates = ?,
           social_notifications = ?,
           marketing_emails = ?,
           updated_at = NOW()
           WHERE user_id = ?`, [
                    pushEnabled,
                    emailEnabled,
                    workoutReminders,
                    mealReminders,
                    progressUpdates,
                    socialNotifications,
                    marketingEmails,
                    userId
                ]);
            } else {
                // Create new settings
                await _database.db.execute(`INSERT INTO notification_settings (
           user_id, push_enabled, email_enabled, workout_reminders,
           meal_reminders, progress_updates, social_notifications,
           marketing_emails, created_at, updated_at
           ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`, [
                    userId,
                    pushEnabled,
                    emailEnabled,
                    workoutReminders,
                    mealReminders,
                    progressUpdates,
                    socialNotifications,
                    marketingEmails
                ]);
            }
            return {
                status: 'success',
                message: 'Notification settings updated successfully'
            };
        } catch (error) {
            console.error('Error updating notification settings:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to update notification settings'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async subscribeToPushNotifications(userId, subscription) {
        try {
            const { endpoint, keys } = subscription;
            // Check if subscription already exists
            const [existing] = await _database.db.execute('SELECT id FROM push_subscriptions WHERE user_id = ? AND endpoint = ?', [
                userId,
                endpoint
            ]);
            if (!existing[0]) {
                await _database.db.execute(`INSERT INTO push_subscriptions (user_id, endpoint, p256dh_key, auth_key, created_at, updated_at)
           VALUES (?, ?, ?, ?, NOW(), NOW())`, [
                    userId,
                    endpoint,
                    keys.p256dh,
                    keys.auth
                ]);
            }
            return {
                status: 'success',
                message: 'Successfully subscribed to push notifications'
            };
        } catch (error) {
            console.error('Error subscribing to push notifications:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to subscribe to push notifications'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async unsubscribeFromPushNotifications(userId, endpoint) {
        try {
            await _database.db.execute('DELETE FROM push_subscriptions WHERE user_id = ? AND endpoint = ?', [
                userId,
                endpoint
            ]);
            return {
                status: 'success',
                message: 'Successfully unsubscribed from push notifications'
            };
        } catch (error) {
            console.error('Error unsubscribing from push notifications:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to unsubscribe from push notifications'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async sendTestNotification(userId) {
        try {
            await this.createNotification(userId, {
                type: 'test',
                title: 'Notificação de Teste',
                message: 'Esta é uma notificação de teste para verificar se o sistema está funcionando corretamente.',
                data: {
                    testData: true,
                    timestamp: new Date().toISOString()
                }
            });
            return {
                status: 'success',
                message: 'Test notification sent successfully'
            };
        } catch (error) {
            console.error('Error sending test notification:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to send test notification'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // Helper method to create notifications
    async createNotification(userId, notification) {
        try {
            await _database.db.execute(`INSERT INTO notifications (user_id, type, title, message, data, created_at)
         VALUES (?, ?, ?, ?, ?, NOW())`, [
                userId,
                notification.type,
                notification.title,
                notification.message,
                notification.data ? JSON.stringify(notification.data) : null
            ]);
            // Here you would also trigger push notifications if enabled
            // await this.sendPushNotification(userId, notification);
            return true;
        } catch (error) {
            console.error('Error creating notification:', error);
            return false;
        }
    }
    // Helper method for sending push notifications (to be implemented)
    async sendPushNotification(userId, notification) {
        // Implementation for sending actual push notifications
        // This would use web-push library or similar
        console.log('Push notification would be sent:', {
            userId,
            notification
        });
    }
};
NotificationsService = _ts_decorate([
    (0, _common.Injectable)()
], NotificationsService);

//# sourceMappingURL=notifications.service.js.map