"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "CoachController", {
    enumerable: true,
    get: function() {
        return CoachController;
    }
});
const _common = require("@nestjs/common");
const _getcoachclientsquerydto = require("../admin/dto/get-coach-clients-query.dto");
const _coachservice = require("./coach.service");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _importcoachprotocoldto = require("../admin/dto/import-coach-protocol.dto");
const _createcoachprotocoldto = require("../admin/dto/create-coach-protocol.dto");
const _createcoachclientprotocoldto = require("./dto/create-coach-client-protocol.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let CoachController = class CoachController {
    getStats() {
        return {
            status: 'success',
            data: {
                total_clients: 99,
                active_protocols: 99,
                completion_rate: 99
            }
        };
    }
    getClients(query, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.getClients(query, userId);
    }
    getClient(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.getClient(id, userId);
    }
    getClientProtocols(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.getClientProtocols(id, userId);
    }
    importProtocol(id, importCoachProtocolDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.importProtocol(id, importCoachProtocolDto, userId);
    }
    createClientProtocol(id, createCoachClientProtocolDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.createClientProtocol(id, createCoachClientProtocolDto, userId);
    }
    getProtocols(req) {
        const userId = req.user?.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.getProtocols(userId);
    }
    getProtocol(id, req) {
        const userId = req.user?.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.getProtocol(id, userId);
    }
    createProtocol(createCoachProtocolDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.createProtocol(createCoachProtocolDto, userId);
    }
    updateProtocol(id, updateData, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.updateProtocol(id, updateData, userId);
    }
    deleteProtocol(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            return {
                status: 'error',
                message: 'User not found'
            };
        }
        return this.coachService.deleteProtocol(id, userId);
    }
    constructor(coachService){
        this.coachService = coachService;
    }
};
_ts_decorate([
    (0, _common.Get)('stats'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "getStats", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('clients'),
    _ts_param(0, (0, _common.Query)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _getcoachclientsquerydto.GetCoachClientsQueryDto === "undefined" ? Object : _getcoachclientsquerydto.GetCoachClientsQueryDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "getClients", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('clients/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "getClient", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('clients/:id/protocols'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "getClientProtocols", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('clients/:id/protocols/import'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        typeof _importcoachprotocoldto.ImportCoachProtocolDto === "undefined" ? Object : _importcoachprotocoldto.ImportCoachProtocolDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "importProtocol", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('clients/:id/protocols'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        typeof _createcoachclientprotocoldto.CreateCoachClientProtocolDto === "undefined" ? Object : _createcoachclientprotocoldto.CreateCoachClientProtocolDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "createClientProtocol", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "getProtocols", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "getProtocol", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createcoachprotocoldto.CreateCoachProtocolDto === "undefined" ? Object : _createcoachprotocoldto.CreateCoachProtocolDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "createProtocol", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Put)('protocols/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "updateProtocol", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Delete)('protocols/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], CoachController.prototype, "deleteProtocol", null);
CoachController = _ts_decorate([
    (0, _common.Controller)('coach'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _coachservice.CoachService === "undefined" ? Object : _coachservice.CoachService
    ])
], CoachController);

//# sourceMappingURL=coach.controller.js.map