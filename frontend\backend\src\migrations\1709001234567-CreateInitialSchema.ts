import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateInitialSchema1709001234567 implements MigrationInterface {
  name = 'CreateInitialSchema1709001234567';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Users table
    await queryRunner.query(`
      CREATE TABLE users (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('user', 'admin', 'coach', 'nutritionist') NOT NULL DEFAULT 'user',
        photo VARCHAR(255),
        code VARCHAR(255),
        height DECIMAL(5,2),
        activity_level VARCHAR(255),
        coach_id VARCHAR(36),
        nutritionist_id VARCHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON><PERSON><PERSON><PERSON><PERSON>EY (coach_id) REFERENCES users(id),
        <PERSON><PERSON><PERSON><PERSON><PERSON>EY (nutritionist_id) REFERENCES users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Assessments table
    await queryRunner.query(`
      CREATE TABLE assessments (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        weight DECIMAL(5,2) NOT NULL,
        body_fat DECIMAL(5,2),
        photos JSON,
        measurements JSON,
        date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Workouts table
    await queryRunner.query(`
      CREATE TABLE workouts (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(255) NOT NULL,
        completed BOOLEAN DEFAULT FALSE,
        duration INT,
        date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Exercises table
    await queryRunner.query(`
      CREATE TABLE exercises (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        workout_id VARCHAR(36) NOT NULL,
        name VARCHAR(255) NOT NULL,
        sets INT NOT NULL,
        reps INT NOT NULL,
        weight DECIMAL(5,2),
        rpe DECIMAL(3,1),
        rest_time INT NOT NULL,
        completed BOOLEAN DEFAULT FALSE,
        notes TEXT,
        gif_url VARCHAR(255),
        FOREIGN KEY (workout_id) REFERENCES workouts(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Meals table
    await queryRunner.query(`
      CREATE TABLE meals (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        name VARCHAR(255) NOT NULL,
        time VARCHAR(5) NOT NULL,
        completed BOOLEAN DEFAULT FALSE,
        date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Foods table
    await queryRunner.query(`
      CREATE TABLE foods (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        meal_id VARCHAR(36) NOT NULL,
        name VARCHAR(255) NOT NULL,
        quantity DECIMAL(10,2) NOT NULL,
        unit VARCHAR(10) NOT NULL,
        calories DECIMAL(10,2) NOT NULL,
        protein DECIMAL(10,2) NOT NULL,
        carbs DECIMAL(10,2) NOT NULL,
        fat DECIMAL(10,2) NOT NULL,
        image VARCHAR(255),
        category VARCHAR(255),
        FOREIGN KEY (meal_id) REFERENCES meals(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('DROP TABLE IF EXISTS foods');
    await queryRunner.query('DROP TABLE IF EXISTS meals');
    await queryRunner.query('DROP TABLE IF EXISTS exercises');
    await queryRunner.query('DROP TABLE IF EXISTS workouts');
    await queryRunner.query('DROP TABLE IF EXISTS assessments');
    await queryRunner.query('DROP TABLE IF EXISTS users');
  }
}