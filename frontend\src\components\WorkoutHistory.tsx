import { <PERSON>, CheckCircle2, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>fresh<PERSON><PERSON>, Edit } from 'lucide-react';
import { formatDate } from '../utils/date';
import React, { useState } from 'react';
import { apiService } from '../services/api';
import { EnhancedProtocolHistory } from './EnhancedProtocolHistoryNew';
import { ProtocolDetailsModal } from './ProtocolDetailsModal';
import { ProtocolHistoryItem } from '../services/protocolHistory';
import { useWorkoutHistory } from '../hooks/useWorkout';
import { runCompleteWorkoutHistoryDiagnostic, debugComponentDataFlow } from '../utils/debugWorkoutHistory';

interface WorkoutHistoryProps {
  date_start?: string;
  date_end?: string;
  onReuseProtocol?: (protocol: any) => void;
  showProtocolHistory?: boolean;
}

export function WorkoutHistory({ date_start, date_end, onReuseProtocol, showProtocolHistory = false }: WorkoutHistoryProps) {
  const [selectedProtocol, setSelectedProtocol] = useState<string | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Use the workout history hook to fetch real data
  const { data: workoutHistoryData, isLoading, error, refetch } = useWorkoutHistory('month');

  const workouts = workoutHistoryData?.workouts || [];

  // Debug: Log the workout data structure
  React.useEffect(() => {
    if (workoutHistoryData) {
      console.log('🔍 WorkoutHistory: Received workout history data:', workoutHistoryData);
      console.log('🔍 WorkoutHistory: Workouts array:', workouts);
      console.log('🔍 WorkoutHistory: Workouts count:', workouts.length);
      debugComponentDataFlow('WorkoutHistory', workoutHistoryData);
      if (workouts.length > 0) {
        console.log('🔍 WorkoutHistory: First workout structure:', workouts[0]);
        console.log('🔍 WorkoutHistory: Completed workouts:', workouts.filter(w => w.completed));
        console.log('🔍 WorkoutHistory: In-progress workouts:', workouts.filter(w => !w.completed));
      } else {
        console.log('⚠️ WorkoutHistory: No workouts found in response');
        console.log('🔍 WorkoutHistory: Raw API response structure:', JSON.stringify(workoutHistoryData, null, 2));
      }
    }
  }, [workoutHistoryData, workouts]);

  // Debug function to run complete diagnostic
  const runDiagnostic = async () => {
    console.log('🚀 WorkoutHistory: Running complete diagnostic...');
    await runCompleteWorkoutHistoryDiagnostic();
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
        <div className="flex items-center justify-center py-8">
          <div className="text-white">Carregando histórico de treinos...</div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
        <div className="flex items-center justify-center py-8">
          <div className="text-red-400">Erro ao carregar histórico de treinos</div>
        </div>
      </div>
    );
  }

  // function to return "e" na palavra final se for mais de uma. Recebe itens separados por vírgula
  const getFormatMuscleGroup = (muscleGroups: any) => {
    if (!muscleGroups || typeof muscleGroups !== 'string') {
      return '';
    }
    const muscleGroupsArray = muscleGroups.split(', ');
    if (muscleGroupsArray.length < 2) {
      return muscleGroups;
    } else {
      const lastMuscleGroup = muscleGroupsArray.pop();
      return muscleGroupsArray.join(', ') + ' e ' + lastMuscleGroup;
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}min` : `${mins}min`;
  };

  const minOrHours = (time: string) => {
    const [hours, minutes, seconds] = time.split(':').map(Number);
    // two digits
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}h`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}min`;
    }
  };

  const handleViewProtocolDetails = (protocol: ProtocolHistoryItem) => {
    setSelectedProtocol(protocol.id);
    setShowDetailsModal(true);
  };

  const handleReuseProtocol = (protocol: ProtocolHistoryItem, shouldEdit?: boolean) => {
    if (onReuseProtocol) {
      onReuseProtocol({ ...protocol, edit: shouldEdit });
    }
  };

  // Show empty state if no workouts
  if (!workouts || workouts.length === 0) {
    return (
      <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
              <Calendar className="w-5 h-5 text-snapfit-green" />
            </div>
            <h2 className="text-xl font-bold text-white">
              Histórico de Treinos
            </h2>
          </div>
          <button
            onClick={runDiagnostic}
            className="px-3 py-1 bg-red-500/20 text-red-400 rounded text-sm border border-red-500/30 hover:bg-red-500/30 transition-colors"
            title="Executar diagnóstico completo"
          >
            🔍 DEBUG
          </button>
        </div>
        <div className="flex flex-col items-center justify-center py-12">
          <div className="w-16 h-16 bg-gray-600/20 rounded-full flex items-center justify-center mb-4">
            <Dumbbell className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-white mb-2">Nenhum treino encontrado</h3>
          <p className="text-gray-400 text-center max-w-md">
            {error ?
              'Erro ao carregar histórico. Verifique sua conexão e tente novamente.' :
              'Você ainda não completou nenhum treino. Comece um treino agora para ver seu histórico aqui!'
            }
          </p>
          {error && (
            <button
              onClick={() => refetch()}
              className="mt-4 px-4 py-2 bg-snapfit-green text-white rounded-lg hover:bg-snapfit-green/80 transition-colors"
            >
              Tentar novamente
            </button>
          )}
        </div>
      </div>
    );
  }

  if (showProtocolHistory) {
    return (
      <>
        <EnhancedProtocolHistory
          type="workout"
          onReuseProtocol={handleReuseProtocol}
          onViewDetails={handleViewProtocolDetails}
        />

        {selectedProtocol && (
          <ProtocolDetailsModal
            protocolId={selectedProtocol}
            type="workout"
            isOpen={showDetailsModal}
            onClose={() => {
              setShowDetailsModal(false);
              setSelectedProtocol(null);
            }}
            onReuseProtocol={handleReuseProtocol}
          />
        )}
      </>
    );
  }

  return (
    <>
    {workouts && (
    <div className="bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
            <Calendar className="w-5 h-5 text-snapfit-green" />
          </div>
          <h2 className="text-xl font-bold text-white">
            Histórico de Treinos
          </h2>
        </div>
      </div>

      <div className="space-y-2 sm:space-y-4">
        {workouts?.map((workout: any, index: number) => (
          <div
            key={workout.session_id || index}
            className="p-3 sm:p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/10"
          >
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-3 sm:mb-4">
              <div className="flex items-center gap-3 flex-1">
                <div className={`p-2 rounded-full ${
                  workout.completed ? 'bg-snapfit-green/20 border border-snapfit-green/30' : 'bg-orange-400/10 border border-orange-400/30'
                }`}>
                  <Dumbbell className={`w-5 h-5 ${workout.completed ? 'text-snapfit-green' : 'text-orange-400'}`} />
                </div>
                <div>
                  <h3 className="font-semibold text-white text-sm sm:text-base">
                    {workout.workout_name || 'Treino'}
                  </h3>
                  <p className="text-gray-400 text-xs sm:text-sm">
                    {formatDate(workout.date)}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm">
                <div className="flex items-center gap-1 text-gray-300">
                  <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{formatDuration(workout.duration || 0)}</span>
                </div>
                <div className="flex items-center gap-1 text-gray-300">
                  <Flame className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{workout.total_calories || 0} kcal</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 text-xs sm:text-sm">
              <div className="text-center p-2 bg-snapfit-gray rounded">
                <div className="text-gray-400">Volume</div>
                <div className="font-semibold text-white">{workout.total_weight || 0}kg</div>
              </div>
              <div className="text-center p-2 bg-snapfit-gray rounded">
                <div className="text-gray-400">Duração</div>
                <div className="font-semibold text-white">{formatDuration(workout.duration || 0)}</div>
              </div>
              <div className="text-center p-2 bg-snapfit-gray rounded">
                <div className="text-gray-400">Calorias</div>
                <div className="font-semibold text-white">{workout.total_calories || 0}</div>
              </div>
              <div className="text-center p-2 bg-snapfit-gray rounded">
                <div className="text-gray-400">Status</div>
                <div className={`font-semibold ${workout.completed ? 'text-snapfit-green' : 'text-orange-400'}`}>
                  {workout.completed ? 'Concluído' : 'Em andamento'}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
    )}
    </>
  );
}