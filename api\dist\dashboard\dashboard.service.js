"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "DashboardService", {
    enumerable: true,
    get: function() {
        return DashboardService;
    }
});
const _common = require("@nestjs/common");
const _database = require("../database");
const _kysely = require("kysely");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let DashboardService = class DashboardService {
    /**
     * Get user's weight progress data
     */ async getWeightProgress(userId, period = 'month') {
        try {
            const { startDate, endDate } = this.getPeriodDates(period);
            // Get weight data from evaluations table
            const weightData = await _database.db.selectFrom('evaluations').select([
                'weight',
                'bf',
                'created_at as date'
            ]).where('user_id', '=', userId).where('created_at', '>=', startDate).where('created_at', '<=', endDate).orderBy('created_at', 'asc').execute();
            // Get current and goal weight
            const user = await _database.db.selectFrom('users').select([
                'weight',
                'goal_id'
            ]).where('id', '=', userId).executeTakeFirst();
            // Calculate trend and change with proper type checking
            const currentRaw = weightData.length > 0 ? weightData[weightData.length - 1].weight : user?.weight || 0;
            const previousRaw = weightData.length > 1 ? weightData[0].weight : currentRaw;
            // Ensure values are numbers (database might return strings for DECIMAL columns)
            const current = typeof currentRaw === 'string' ? parseFloat(currentRaw) : Number(currentRaw) || 0;
            const previous = typeof previousRaw === 'string' ? parseFloat(previousRaw) : Number(previousRaw) || 0;
            const change = current - previous;
            const trend = change > 0.5 ? 'increasing' : change < -0.5 ? 'decreasing' : 'stable';
            return {
                status: 'success',
                data: {
                    current: parseFloat(current.toFixed(1)),
                    goal: 70,
                    change: parseFloat(change.toFixed(1)),
                    trend,
                    history: weightData.map((item)=>{
                        // Ensure weight and bf are numbers before calling toFixed
                        const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;
                        const bf = item.bf ? typeof item.bf === 'string' ? parseFloat(item.bf) : Number(item.bf) : null;
                        return {
                            date: item.date,
                            weight: parseFloat(weight.toFixed(1)),
                            bodyFat: bf ? parseFloat(bf.toFixed(1)) : null
                        };
                    })
                }
            };
        } catch (error) {
            console.error('Error getting weight progress:', error);
            throw error;
        }
    }
    /**
     * Get user's analytics overview
     */ async getAnalyticsOverview(userId, period = 'month') {
        try {
            const { startDate, endDate } = this.getPeriodDates(period);
            // Get nutrition adherence
            const nutritionData = await _database.db.selectFrom('daily_meals_goal').select([
                'meals',
                'meals_completed',
                'goal_met'
            ]).where('user_id', '=', userId).where('goal_date', '>=', startDate.toISOString().split('T')[0]).where('goal_date', '<=', endDate.toISOString().split('T')[0]).execute();
            // Get workout consistency
            const workoutData = await _database.db.selectFrom('daily_workouts_activities').select([
                'id',
                'calories'
            ]).where('user_id', '=', userId).where('daily_at', '>=', startDate).where('daily_at', '<=', endDate).execute();
            // Get strength gains (from coach protocol series)
            const strengthData = await _database.db.selectFrom('daily_coach_protocol_series').innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id').select([
                'daily_coach_protocol_series.weight',
                'daily_coach_protocol_series.reps'
            ]).where('daily_coach_protocol.user_id', '=', userId).where('daily_coach_protocol.daily_at', '>=', startDate).where('daily_coach_protocol.daily_at', '<=', endDate).execute();
            // Calculate metrics
            const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            const nutritionAdherence = nutritionData.length > 0 ? nutritionData.reduce((sum, day)=>sum + (day.goal_met ? 1 : 0), 0) / nutritionData.length * 100 : 0;
            const workoutConsistency = workoutData.length / totalDays * 100;
            const strengthGains = strengthData.length > 0 ? (strengthData[strengthData.length - 1]?.weight || 0) - (strengthData[0]?.weight || 0) : 0;
            return {
                status: 'success',
                data: {
                    weightTrend: 'stable',
                    calorieAdherence: Math.round(nutritionAdherence),
                    workoutConsistency: Math.round(workoutConsistency),
                    strengthGains: Math.round(strengthGains * 100) / 100,
                    bodyFatPercentage: 18.2,
                    recommendations: [
                        'Manter consistência nos treinos',
                        'Aumentar ingestão de proteínas',
                        'Focar em exercícios compostos'
                    ]
                }
            };
        } catch (error) {
            console.error('Error getting analytics overview:', error);
            throw error;
        }
    }
    /**
     * Get workout analytics data
     */ async getWorkoutAnalytics(userId, period = 'week') {
        try {
            const { startDate, endDate } = this.getPeriodDates(period);
            // Get workout data from daily_workouts_activities
            const workouts = await _database.db.selectFrom('daily_workouts_activities').select([
                'daily_at',
                'calories'
            ]).where('user_id', '=', userId).where('daily_at', '>=', startDate).where('daily_at', '<=', endDate).orderBy('daily_at', 'asc').execute();
            // Get training volume from coach protocol series
            const volumeData = await _database.db.selectFrom('daily_coach_protocol_series').innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id').select([
                'daily_coach_protocol.daily_at',
                'daily_coach_protocol_series.weight',
                'daily_coach_protocol_series.reps'
            ]).where('daily_coach_protocol.user_id', '=', userId).where('daily_coach_protocol.daily_at', '>=', startDate).where('daily_coach_protocol.daily_at', '<=', endDate).execute();
            // Calculate training volume by date with proper type checking
            const volumeByDate = volumeData.reduce((acc, item)=>{
                const date = item.daily_at ? item.daily_at.toISOString().split('T')[0] : '';
                // Ensure weight and reps are numbers
                const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;
                const reps = typeof item.reps === 'string' ? parseFloat(item.reps) : Number(item.reps) || 0;
                const volume = weight * reps * 3; // Assume 3 sets
                if (date) {
                    acc[date] = (acc[date] || 0) + volume;
                }
                return acc;
            }, {});
            // Format chart data based on period
            const chartData = this.formatChartData(period, startDate, endDate, volumeByDate);
            // Calculate totals
            const totalWorkouts = workouts.length;
            const totalMinutes = totalWorkouts * 45; // Estimate 45 minutes per workout
            const totalCalories = workouts.reduce((sum, w)=>sum + (w.calories || 0), 0);
            return {
                status: 'success',
                data: {
                    chart: chartData,
                    workouts: totalWorkouts,
                    minutes: totalMinutes,
                    calories: totalCalories,
                    period: period
                }
            };
        } catch (error) {
            console.error('Error getting workout analytics:', error);
            throw error;
        }
    }
    /**
     * Get nutrition analytics data
     */ async getNutritionAnalytics(userId, period = 'week') {
        try {
            const { startDate, endDate } = this.getPeriodDates(period);
            // Get nutrition data from daily_meals table instead
            const nutritionData = await _database.db.selectFrom('daily_meals').select([
                'calories',
                'protein',
                'carbs',
                'fat'
            ]).where('user_id', '=', userId).where('daily_at', '>=', startDate).where('daily_at', '<=', endDate).execute();
            // Calculate averages
            const totalDays = nutritionData.length || 1;
            const avgCalories = nutritionData.reduce((sum, day)=>sum + (day.calories || 0), 0) / totalDays;
            const avgProtein = nutritionData.reduce((sum, day)=>sum + (day.protein || 0), 0) / totalDays;
            const avgCarbs = nutritionData.reduce((sum, day)=>sum + (day.carbs || 0), 0) / totalDays;
            const avgFat = nutritionData.reduce((sum, day)=>sum + (day.fat || 0), 0) / totalDays;
            // Calculate percentages (assuming 2000 cal target)
            const targetCalories = 2000;
            const caloriesPercent = Math.round(avgCalories / targetCalories * 100);
            return {
                status: 'success',
                data: {
                    calories: Math.round(avgCalories),
                    calories_percent: caloriesPercent,
                    carbs: Math.round(avgCarbs),
                    carbs_percent: Math.round(avgCarbs * 4 / avgCalories * 100),
                    protein: Math.round(avgProtein),
                    protein_percent: Math.round(avgProtein * 4 / avgCalories * 100),
                    fat: Math.round(avgFat),
                    fat_percent: Math.round(avgFat * 9 / avgCalories * 100)
                }
            };
        } catch (error) {
            console.error('Error getting nutrition analytics:', error);
            throw error;
        }
    }
    /**
     * Get caloric balance data
     */ async getCaloricBalance(userId, period = 'week') {
        try {
            const { startDate, endDate } = this.getPeriodDates(period);
            // Get daily nutrition and workout data
            const nutritionData = await _database.db.selectFrom('daily_meals').select([
                'daily_at',
                'calories'
            ]).where('user_id', '=', userId).where('daily_at', '>=', startDate).where('daily_at', '<=', endDate).execute();
            const workoutData = await _database.db.selectFrom('daily_workouts_activities').select([
                'daily_at',
                'calories'
            ]).where('user_id', '=', userId).where('daily_at', '>=', startDate).where('daily_at', '<=', endDate).execute();
            // Combine data by date
            const dataByDate = this.combineCaloricData(nutritionData, workoutData, startDate, endDate);
            return {
                status: 'success',
                data: dataByDate
            };
        } catch (error) {
            console.error('Error getting caloric balance:', error);
            throw error;
        }
    }
    /**
     * Get strength progress data
     */ async getStrengthProgress(userId, period = 'month') {
        try {
            const { startDate, endDate } = this.getPeriodDates(period);
            // Get strength data from coach protocol series
            const strengthData = await _database.db.selectFrom('daily_coach_protocol_series').innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id').select([
                'daily_coach_protocol_series.weight',
                'daily_coach_protocol_series.reps',
                'daily_coach_protocol.daily_at'
            ]).where('daily_coach_protocol.user_id', '=', userId).where('daily_coach_protocol.daily_at', '>=', startDate).where('daily_coach_protocol.daily_at', '<=', endDate).orderBy('daily_coach_protocol.daily_at', 'asc').execute();
            // Group by exercise and calculate progress
            const exerciseProgress = this.calculateExerciseProgress(strengthData);
            const totalVolume = strengthData.reduce((sum, item)=>{
                // Ensure weight and reps are numbers
                const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;
                const reps = typeof item.reps === 'string' ? parseFloat(item.reps) : Number(item.reps) || 0;
                return sum + weight * reps;
            }, 0);
            return {
                status: 'success',
                data: {
                    exercises: exerciseProgress,
                    total_volume: totalVolume,
                    volume_change: 150,
                    personal_records: [],
                    period: period
                }
            };
        } catch (error) {
            console.error('Error getting strength progress:', error);
            throw error;
        }
    }
    /**
     * Get body composition data
     */ async getBodyComposition(userId, period = 'month') {
        try {
            const { startDate, endDate } = this.getPeriodDates(period);
            // Get body composition data from evaluations
            const evaluations = await _database.db.selectFrom('evaluations').select([
                'weight',
                'bf',
                'created_at as date'
            ]).where('user_id', '=', userId).where('created_at', '>=', startDate).where('created_at', '<=', endDate).orderBy('created_at', 'asc').execute();
            // Get measurements data
            const measurements = await _database.db.selectFrom('evaluations_measurements').selectAll().where('user_id', '=', userId).where('created_at', '>=', startDate).where('created_at', '<=', endDate).orderBy('created_at', 'asc').execute();
            // Format data for charts with proper type checking
            const weightData = evaluations.map((item)=>{
                // Ensure weight and bf are numbers before calling toFixed
                const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;
                const bf = item.bf ? typeof item.bf === 'string' ? parseFloat(item.bf) : Number(item.bf) : null;
                return {
                    date: item.date,
                    weight: parseFloat(weight.toFixed(1)),
                    bodyFat: bf ? parseFloat(bf.toFixed(1)) : null
                };
            });
            const measurementData = measurements.map((item)=>({
                    date: item.created_at,
                    chest: item.chest,
                    waist: item.waist,
                    hips: item.hips,
                    biceps: (item.biceps_right + item.biceps_left) / 2,
                    thigh: (item.thigh_right + item.thigh_left) / 2
                }));
            return {
                status: 'success',
                data: {
                    weight_progress: weightData,
                    measurements_progress: measurementData,
                    period: period
                }
            };
        } catch (error) {
            console.error('Error getting body composition:', error);
            throw error;
        }
    }
    /**
     * Helper method to get period dates
     */ getPeriodDates(period) {
        const endDate = new Date();
        const startDate = new Date();
        switch(period){
            case 'week':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case 'month':
                startDate.setMonth(endDate.getMonth() - 1);
                break;
            case 'semester':
                startDate.setMonth(endDate.getMonth() - 6);
                break;
            case 'year':
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
            case 'all':
                startDate.setFullYear(endDate.getFullYear() - 2);
                break;
            default:
                startDate.setMonth(endDate.getMonth() - 1);
        }
        return {
            startDate,
            endDate
        };
    }
    /**
     * Helper method to format chart data based on period
     */ formatChartData(period, startDate, endDate, volumeByDate) {
        const chartData = [];
        switch(period){
            case 'week':
                const days = [
                    'sáb',
                    'dom',
                    'seg',
                    'ter',
                    'qua',
                    'qui',
                    'sex'
                ];
                for(let i = 0; i < 7; i++){
                    const date = new Date(startDate);
                    date.setDate(startDate.getDate() + i);
                    const dateStr = date.toISOString().split('T')[0];
                    chartData.push({
                        label: days[i],
                        training_volume: volumeByDate[dateStr] || 0
                    });
                }
                break;
            default:
                // For other periods, use actual dates
                const currentDate = new Date(startDate);
                while(currentDate <= endDate){
                    const dateStr = currentDate.toISOString().split('T')[0];
                    chartData.push({
                        label: dateStr,
                        training_volume: volumeByDate[dateStr] || 0
                    });
                    currentDate.setDate(currentDate.getDate() + 1);
                }
        }
        return chartData;
    }
    /**
     * Helper method to combine caloric data
     */ combineCaloricData(nutritionData, workoutData, startDate, endDate) {
        const dataByDate = [];
        const currentDate = new Date(startDate);
        while(currentDate <= endDate){
            const dateStr = currentDate.toISOString().split('T')[0];
            const nutrition = nutritionData.find((n)=>n.date === dateStr);
            const workout = workoutData.find((w)=>w.date === dateStr);
            dataByDate.push({
                date: dateStr,
                consumed: nutrition?.goal_calories || 0,
                burned: workout?.calories_burned || 0,
                balance: (nutrition?.goal_calories || 0) - (workout?.calories_burned || 0)
            });
            currentDate.setDate(currentDate.getDate() + 1);
        }
        return dataByDate;
    }
    /**
     * Helper method to calculate exercise progress
     */ calculateExerciseProgress(strengthData) {
        const exerciseMap = new Map();
        strengthData.forEach((item)=>{
            const exerciseName = `Exercise ${item.daily_at}`;
            if (!exerciseMap.has(exerciseName)) {
                exerciseMap.set(exerciseName, []);
            }
            // Ensure weight and reps are numbers
            const weight = typeof item.weight === 'string' ? parseFloat(item.weight) : Number(item.weight) || 0;
            const reps = typeof item.reps === 'string' ? parseFloat(item.reps) : Number(item.reps) || 0;
            exerciseMap.get(exerciseName).push({
                weight: weight,
                reps: reps,
                date: item.daily_at
            });
        });
        const exerciseProgress = [];
        exerciseMap.forEach((data, exerciseName)=>{
            const sortedData = data.sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime());
            // Ensure weight values are numbers
            const currentRaw = sortedData[sortedData.length - 1]?.weight || 0;
            const previousRaw = sortedData[0]?.weight || 0;
            const current = typeof currentRaw === 'string' ? parseFloat(currentRaw) : Number(currentRaw) || 0;
            const previous = typeof previousRaw === 'string' ? parseFloat(previousRaw) : Number(previousRaw) || 0;
            exerciseProgress.push({
                name: exerciseName,
                current: current,
                previous: previous,
                change: current - previous,
                history: sortedData
            });
        });
        return exerciseProgress;
    }
    /**
     * Get workout dashboard data
     */ async getWorkoutDashboardData(userId) {
        try {
            console.log('🏋️ getWorkoutDashboardData: Getting workout dashboard data for user:', userId);
            // Check if user has active workout protocol
            const activeProtocol = await _database.db.selectFrom('coach_protocols').selectAll().where('client_id', '=', userId).where('started_at', '<=', new Date()).where((eb)=>eb.or([
                    eb('ended_at', 'is', null),
                    eb('ended_at', '>', new Date())
                ])).executeTakeFirst();
            const hasProtocol = !!activeProtocol;
            const protocolFrequency = activeProtocol?.frequency || 0;
            // Get workout statistics for the last 30 days
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const workoutStats = await _database.db.selectFrom('daily_coach_protocol').select([
                _database.db.fn.count('id').as('total_workouts'),
                _database.db.fn.sum('workout_time').as('total_minutes'),
                _database.db.fn.sum('total_calories').as('total_calories')
            ]).where('user_id', '=', userId).where('daily_at', '>=', thirtyDaysAgo).executeTakeFirst();
            // Get weekly frequency (last 7 days)
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            const weeklyWorkouts = await _database.db.selectFrom('daily_coach_protocol').select(_database.db.fn.count('id').as('weekly_workouts')).where('user_id', '=', userId).where('daily_at', '>=', sevenDaysAgo).executeTakeFirst();
            // Get last workout
            const lastWorkout = await _database.db.selectFrom('daily_coach_protocol as dcp').innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id').select([
                'dcp.daily_at',
                'cpw.name as workout_name',
                'dcp.workout_time',
                'dcp.total_calories'
            ]).where('dcp.user_id', '=', userId).orderBy('dcp.daily_at', 'desc').executeTakeFirst();
            // Calculate current streak (consecutive days with workouts)
            const allWorkoutDays = await _database.db.selectFrom('daily_coach_protocol').select([
                'daily_at'
            ]).where('user_id', '=', userId).orderBy('daily_at', 'desc').execute();
            let currentStreak = 0;
            let bestStreak = 0;
            let tempStreak = 0;
            if (allWorkoutDays.length > 0) {
                const workoutDates = allWorkoutDays.map((w)=>new Date(w.daily_at || new Date()).toDateString());
                const uniqueDates = [
                    ...new Set(workoutDates)
                ].sort((a, b)=>new Date(b).getTime() - new Date(a).getTime());
                // Calculate current streak
                const today = new Date().toDateString();
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                const yesterdayStr = yesterday.toDateString();
                if (uniqueDates.includes(today) || uniqueDates.includes(yesterdayStr)) {
                    let checkDate = new Date();
                    for(let i = 0; i < uniqueDates.length; i++){
                        const dateStr = checkDate.toDateString();
                        if (uniqueDates.includes(dateStr)) {
                            currentStreak++;
                            checkDate.setDate(checkDate.getDate() - 1);
                        } else {
                            break;
                        }
                    }
                }
                // Calculate best streak
                tempStreak = 1;
                for(let i = 1; i < uniqueDates.length; i++){
                    const currentDate = new Date(uniqueDates[i]);
                    const previousDate = new Date(uniqueDates[i - 1]);
                    const dayDiff = Math.abs(previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);
                    if (dayDiff === 1) {
                        tempStreak++;
                    } else {
                        bestStreak = Math.max(bestStreak, tempStreak);
                        tempStreak = 1;
                    }
                }
                bestStreak = Math.max(bestStreak, tempStreak);
            }
            // Calculate progress percentages
            const weeklyGoal = protocolFrequency || 3; // Use protocol frequency or default to 3
            const weeklyFrequency = Number(weeklyWorkouts?.weekly_workouts || 0);
            const weeklyProgressPercentage = Math.min(100, weeklyFrequency / weeklyGoal * 100);
            // Calculate overall protocol completion percentage
            let protocolCompletionPercentage = 0;
            if (activeProtocol && protocolFrequency > 0) {
                const protocolStartDate = new Date(activeProtocol.started_at);
                const currentDate = new Date();
                const daysSinceStart = Math.floor((currentDate.getTime() - protocolStartDate.getTime()) / (1000 * 60 * 60 * 24));
                const weeksSinceStart = Math.max(1, Math.ceil(daysSinceStart / 7)); // At least 1 week
                const expectedTotalWorkouts = weeksSinceStart * protocolFrequency;
                const actualTotalWorkouts = Number(workoutStats?.total_workouts || 0);
                protocolCompletionPercentage = expectedTotalWorkouts > 0 ? Math.min(100, actualTotalWorkouts / expectedTotalWorkouts * 100) : 0;
            }
            return {
                status: 'success',
                data: {
                    has_protocol: hasProtocol,
                    protocol_frequency: protocolFrequency,
                    total_workouts: Number(workoutStats?.total_workouts || 0),
                    total_minutes: Number(workoutStats?.total_minutes || 0),
                    total_calories: Number(workoutStats?.total_calories || 0),
                    weekly_frequency: weeklyFrequency,
                    current_streak: currentStreak,
                    best_streak: bestStreak,
                    last_workout: lastWorkout ? {
                        date: lastWorkout.daily_at,
                        name: lastWorkout.workout_name,
                        duration: lastWorkout.workout_time,
                        calories: lastWorkout.total_calories
                    } : null,
                    next_workout: hasProtocol ? 'Próximo treino disponível' : null,
                    weekly_progress_percentage: Math.round(weeklyProgressPercentage),
                    protocol_completion_percentage: Math.round(protocolCompletionPercentage)
                }
            };
        } catch (error) {
            console.error('❌ Error getting workout dashboard data:', error);
            // Return default data on error
            return {
                status: 'success',
                data: {
                    has_protocol: false,
                    protocol_frequency: 0,
                    total_workouts: 0,
                    total_minutes: 0,
                    total_calories: 0,
                    weekly_frequency: 0,
                    current_streak: 0,
                    best_streak: 0,
                    last_workout: null,
                    next_workout: null,
                    weekly_progress_percentage: 0,
                    protocol_completion_percentage: 0
                }
            };
        }
    }
    async getNutritionalSummary(userId, query) {
        const today = query.date ? new Date(query.date) : new Date();
        const dateStr = today.toISOString().split('T')[0];
        console.log('🔄 getNutritionalSummary: Buscando resumo nutricional para usuário', userId, 'data:', dateStr);
        try {
            // 1. Buscar protocolo ativo para obter metas
            const protocol = await _database.db.selectFrom('nutritionist_protocols as p').select([
                'p.goal_calories as calories',
                'p.goal_protein as protein',
                'p.goal_carbs as carbs',
                'p.goal_fat as fat',
                'p.goal_water as water'
            ]).where('p.client_id', '=', userId).where('p.started_at', '<=', today).where('p.ended_at', 'is', null).orderBy('p.started_at', 'desc').executeTakeFirst();
            // 2. Buscar refeições consumidas no dia
            const startOfDay = new Date(today);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(today);
            endOfDay.setHours(23, 59, 59, 999);
            const consumedMeals = await _database.db.selectFrom('daily_meals as dm').leftJoin('daily_meals_foods as dmf', 'dmf.meal_id', 'dm.id').select([
                (0, _kysely.sql)`SUM(COALESCE(dmf.calories, dm.calories))`.as('total_calories'),
                (0, _kysely.sql)`SUM(COALESCE(dmf.protein, dm.protein))`.as('total_protein'),
                (0, _kysely.sql)`SUM(COALESCE(dmf.carbs, dm.carbs))`.as('total_carbs'),
                (0, _kysely.sql)`SUM(COALESCE(dmf.fat, dm.fat))`.as('total_fat')
            ]).where('dm.user_id', '=', userId).where('dm.daily_at', '>=', startOfDay).where('dm.daily_at', '<=', endOfDay).executeTakeFirst();
            // 3. Buscar calorias queimadas (treinos do dia)
            const caloriesBurned = await _database.db.selectFrom('daily_workouts_activities').select([
                (0, _kysely.sql)`SUM(calories)`.as('total_burned')
            ]).where('user_id', '=', userId).where('daily_at', '>=', startOfDay).where('daily_at', '<=', endOfDay).executeTakeFirst();
            // 4. Calcular valores
            const targets = {
                calories: Number(protocol?.calories) || 2000,
                protein: Number(protocol?.protein) || 150,
                carbs: Number(protocol?.carbs) || 220,
                fat: Number(protocol?.fat) || 70
            };
            const consumed = {
                calories: Number(consumedMeals?.total_calories) || 0,
                protein: Number(consumedMeals?.total_protein) || 0,
                carbs: Number(consumedMeals?.total_carbs) || 0,
                fat: Number(consumedMeals?.total_fat) || 0
            };
            const burned = Number(caloriesBurned?.total_burned) || 0;
            const remaining = {
                calories: Math.max(0, targets.calories - consumed.calories),
                protein: Math.max(0, targets.protein - consumed.protein),
                carbs: Math.max(0, targets.carbs - consumed.carbs),
                fat: Math.max(0, targets.fat - consumed.fat)
            };
            console.log('✅ getNutritionalSummary: Resumo calculado:', {
                targets,
                consumed,
                burned,
                remaining
            });
            return {
                status: 'success',
                data: {
                    calories: {
                        current: consumed.calories,
                        target: targets.calories,
                        remaining: remaining.calories,
                        burned: burned
                    },
                    protein: {
                        current: consumed.protein,
                        target: targets.protein
                    },
                    carbs: {
                        current: consumed.carbs,
                        target: targets.carbs
                    },
                    fat: {
                        current: consumed.fat,
                        target: targets.fat
                    },
                    date: dateStr
                }
            };
        } catch (error) {
            console.error('❌ Erro ao buscar resumo nutricional:', error);
            // Fallback para dados padrão em caso de erro
            return {
                status: 'success',
                data: {
                    calories: {
                        current: 0,
                        target: 2000,
                        remaining: 2000,
                        burned: 0
                    },
                    protein: {
                        current: 0,
                        target: 150
                    },
                    carbs: {
                        current: 0,
                        target: 220
                    },
                    fat: {
                        current: 0,
                        target: 70
                    },
                    date: dateStr
                }
            };
        }
    }
};
DashboardService = _ts_decorate([
    (0, _common.Injectable)()
], DashboardService);

//# sourceMappingURL=dashboard.service.js.map