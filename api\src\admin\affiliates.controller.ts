import { Body, Controller, Delete, Get, NotFoundException, Param, Post, Put, Query, Request, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { AffiliatesService } from './affiliates.service';

@Controller('affiliates')
export class AffiliatesController {
    constructor(private readonly affiliatesService: AffiliatesService) {}

    @UseGuards(JwtAuthGuard)
    @Get('me')
    async getMe(@Request() req: any) {
      const userId = req.user.userId;
      return this.affiliatesService.getAffiliateById(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('onboarding')
    async checkPaymentEligibility(@Request() req: any) {
      const affiliateId = req.user.userId;
      return this.affiliatesService.checkPaymentEligibilityByAffiliateId(affiliateId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('onboarding')
    async startOnboarding(@Request() req: any) {
      const affiliateId = req.user.userId;
      return this.affiliatesService.startOnboarding(affiliateId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('links')
    async getLinks(@Request() req: any) {
      const userId = req.user.userId;
      return this.affiliatesService.getLinks(userId);
    }

    // hit this endpoint when a user clicks on a link
    @Get('links/visit/:id')
    async registerLinkVisit(@Param('id') id: string) {
      return this.affiliatesService.registerLinkVisit(id);
    }

    @UseGuards(JwtAuthGuard)
    @Post('links')
    async createLink(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;
      return this.affiliatesService.createLink(userId, body);
    }

    @UseGuards(JwtAuthGuard)
    @Get('dashboard')
    async getDashboardStats(@Request() req: any) {
      const userId = req.user.userId;
      return this.affiliatesService.getDashboardStats(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('stats')
    async getDetailedStats(@Request() req: any, @Query('start_date') startDate?: string, @Query('end_date') endDate?: string) {
      const userId = req.user.userId;
      return this.affiliatesService.getDetailedStats(userId, startDate, endDate);
    }

    @UseGuards(JwtAuthGuard)
    @Get('payments')
    async getPaymentHistory(@Request() req: any, @Query('page') page?: string, @Query('limit') limit?: string) {
      const userId = req.user.userId;
      const pageNumber = page ? parseInt(page, 10) : 1;
      const limitNumber = limit ? parseInt(limit, 10) : 10;
      return this.affiliatesService.getPaymentHistory(userId, pageNumber, limitNumber);
    }

    @UseGuards(JwtAuthGuard)
    @Get('transactions')
    async getRecentTransactions(@Request() req: any, @Query('page') page?: string, @Query('limit') limit?: string) {
      const userId = req.user.userId;
      const pageNumber = page ? parseInt(page, 10) : 1;
      const limitNumber = limit ? parseInt(limit, 10) : 10;
      return this.affiliatesService.getRecentTransactions(userId, pageNumber, limitNumber);
    }
}