{"version": 3, "sources": ["../../src/rewards/rewards.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { RewardsController } from './rewards.controller';\nimport { RewardsService } from './rewards.service';\n\n@Module({\n  controllers: [RewardsController],\n  providers: [RewardsService],\n  exports: [RewardsService],\n})\nexport class RewardsModule {}\n"], "names": ["RewardsModule", "controllers", "RewardsController", "providers", "RewardsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;mCACW;gCACH;;;;;;;AAOxB,IAAA,AAAMA,gBAAN,MAAMA;AAAe;;;QAJ1BC,aAAa;YAACC,oCAAiB;SAAC;QAChCC,WAAW;YAACC,8BAAc;SAAC;QAC3BC,SAAS;YAACD,8BAAc;SAAC"}