import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON>rray, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePlanDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @IsString()
  type: 'app' | 'coach' | 'nutritionist';

  @ApiProperty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsString()
  interval: 'monthly' | 'quarterly' | 'semiannual' | 'annual';

  @ApiProperty()
  @IsArray()
  features: string[];

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  active?: boolean;
}