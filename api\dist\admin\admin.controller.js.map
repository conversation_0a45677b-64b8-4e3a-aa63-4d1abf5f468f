{"version": 3, "sources": ["../../src/admin/admin.controller.ts"], "sourcesContent": ["import { Body, Controller, Delete, Get, NotFoundException, Param, Post, Put, Query, Request, UseGuards } from '@nestjs/common';\r\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\r\nimport { AdminService } from './admin.service';\r\nimport { GetAllUsersQueryDto } from './get-users-query.dto';\r\nimport { CreateUserDto } from './dto/create-user.dto';\r\nimport { UpdateUserDto } from './dto/update-user.dto';\r\nimport { GetAllFoodsQueryDto } from './dto/get-all-foods-query.dto';\r\nimport { CreateFoodDto } from './dto/create-food.dto';\r\nimport { UpdateFoodDto } from './dto/update-food.dto';\r\nimport { CreateExerciseDto } from './dto/create-exercise.dto';\r\nimport { GetAllExercisesQueryDto } from './dto/get-all-exercises-query.dto';\r\nimport { CreatePlanDto } from './dto/plans/create-plan.dto';\r\nimport { UpdatePlanDto } from './dto/plans/update-plan.dto';\r\nimport { ConfigPlanDto } from './dto/plans/config-plan.dto';\r\n\r\n@Controller('admin')\r\nexport class AdminController {\r\n    constructor(private readonly adminService: AdminService) {}\r\n\r\n    // Stats\r\n    @Get('stats')\r\n    // @UseGuards(JwtAuthGuard)\r\n    getStats() {\r\n        return this.adminService.getStats();\r\n    }\r\n\r\n    // Users\r\n    @Get('users')\r\n    // @UseGuards(JwtAuthGuard)\r\n    getAllUsers(@Query() query: GetAllUsersQueryDto) {\r\n        return this.adminService.getAllUsers(query);\r\n    }\r\n\r\n    @Post('users')\r\n    @UseGuards(JwtAuthGuard)\r\n    createUser(@Body() createUserDto: CreateUserDto) {\r\n        return this.adminService.createUser(createUserDto);\r\n    }\r\n\r\n    @Put('users/:id')\r\n    @UseGuards(JwtAuthGuard)\r\n    async updateUser(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {\r\n      return this.adminService.updateUser(Number(id), updateUserDto);\r\n    }\r\n\r\n    @Delete('users/:id')\r\n    async deleteUser(@Param('id') id: string) {\r\n      return this.adminService.deleteUser(Number(id));\r\n    }\r\n\r\n    // Foods\r\n    @Get('/select_options/:type')\r\n    @UseGuards(JwtAuthGuard)\r\n    // @UseGuards(JwtAuthGuard)\r\n    async getSelectOptions(@Param('type') type: string) {\r\n        /*\r\n      const types_allowed = ['foods_categories'];\r\n\r\n      if (!types_allowed.includes(type)) {\r\n        throw new NotFoundException('Type not found');\r\n      }\r\n        */\r\n\r\n      return this.adminService.getSelectOptions(type);\r\n    }\r\n\r\n    @Get('foods')\r\n    async getAllFoods(@Query() query: GetAllFoodsQueryDto) {\r\n      return this.adminService.getAllFoods(query);\r\n    }\r\n\r\n    @Get('foods/search')\r\n    async searchFoods(@Query() query: { q: string; limit?: number }) {\r\n      return this.adminService.searchFoods(query);\r\n    }\r\n\r\n    @Post('foods')\r\n    async createFood(@Body() createFoodDto: CreateFoodDto) {\r\n      return this.adminService.createFood(createFoodDto);\r\n    }\r\n\r\n    @Put('foods/:id')\r\n    async updateFood(@Param('id') id: string, @Body() updateFoodDto: UpdateFoodDto) {\r\n      return this.adminService.updateFood(Number(id), updateFoodDto);\r\n    }\r\n\r\n    @Delete('foods/:id')\r\n    async deleteFood(@Param('id') id: string) {\r\n      return this.adminService.deleteFood(Number(id));\r\n    }\r\n\r\n    // Exercícios\r\n    @Get('exercises')\r\n    async getAllExercises(@Query() query: GetAllExercisesQueryDto) {\r\n      return this.adminService.getAllExercises(query);\r\n    }\r\n\r\n    @Post('exercises')\r\n    async createExercise(@Body() createExercise: CreateExerciseDto) {\r\n      return this.adminService.createExercise(createExercise);\r\n    }\r\n\r\n    @Delete('exercises/:id')\r\n    async deleteExercise(@Param('id') id: string) {\r\n      return this.adminService.deleteExercise(Number(id));\r\n    }\r\n\r\n    // Plans management\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('plans')\r\n    async getAllPlans() {\r\n      return this.adminService.getAllPlans();\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('plans')\r\n    async createPlan(@Request() req: any, @Body() createPlanDto: CreatePlanDto) {\r\n      const userId = req.user.userId;\r\n      return this.adminService.createPlan(createPlanDto, userId);\r\n    }\r\n\r\n    @Put('plans/:id')\r\n    async updatePlan(@Param('id') id: string, @Body() updatePlanDto: UpdatePlanDto) {\r\n      return this.adminService.updatePlan(Number(id), updatePlanDto);\r\n    }\r\n\r\n    @Post('plans/:id/config')\r\n    async configPlan(@Param('id') id: string, @Body() configPlanDto: ConfigPlanDto) {\r\n      return this.adminService.configPlan(Number(id), configPlanDto);\r\n    }\r\n\r\n    @Delete('plans/:id')\r\n    async deletePlan(@Param('id') id: string) {\r\n      return this.adminService.deletePlan(Number(id));\r\n    }\r\n\r\n    @Delete('plans/config/:id')\r\n    async deletePlanConfig(@Param('id') id: string) {\r\n      return this.adminService.deletePlanConfig(Number(id));\r\n    }\r\n\r\n    // Subscriptions with pagination\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('subscriptions')\r\n    async getAllSubscriptions(@Query() query: any) {\r\n      return this.adminService.getAllSubscriptions(query);\r\n    }\r\n\r\n    // Transactions\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('transactions')\r\n    async getAllTransactions(@Query() query: any) {\r\n      return this.adminService.getAllTransactions(query);\r\n    }\r\n\r\n    // Affiliates\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('affiliates')\r\n    async getAffiliates(@Query() query: any) {\r\n      return this.adminService.getAffiliates(query);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Put('affiliates/:id')\r\n    async updateAffiliate(@Param('id') id: string, @Body() updateAffiliateDto: any) {\r\n      return this.adminService.updateAffiliate(Number(id), updateAffiliateDto);\r\n    }\r\n\r\n    // Subscriptions\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('users/:id/subscriptions')\r\n    async getUserSubscriptions(@Param('id') id: string) {\r\n      return this.adminService.getUserSubscriptions(Number(id));\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('users/:id/transactions')\r\n    async getUserTransactions(@Param('id') id: string) {\r\n      return this.adminService.getUserTransactions(Number(id));\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('users/:userId/transactions/:id')\r\n    async getTransactionDetails(@Param('userId') userId: string, @Param('id') id: string) {\r\n      return this.adminService.getTransactionDetails(Number(id), Number(userId));\r\n    }\r\n\r\n    // Webhook Stripe\r\n    @Post('webhook')\r\n    async webhook(@Body() body: any, @Request() req: any) {\r\n      // Em produção, você deve verificar a assinatura do webhook\r\n      // const signature = req.headers['stripe-signature'];\r\n      return this.adminService.webhook(body);\r\n    }\r\n\r\n    // Registrar comissões manualmente\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('subscriptions/:id/register-commissions')\r\n    async registerCommissionsForSubscription(@Param('id') id: string) {\r\n      return this.adminService.registerCommissionsForSubscription(Number(id));\r\n    }\r\n}"], "names": ["AdminController", "getStats", "adminService", "getAllUsers", "query", "createUser", "createUserDto", "updateUser", "id", "updateUserDto", "Number", "deleteUser", "getSelectOptions", "type", "getAllFoods", "searchFoods", "createFood", "createFoodDto", "updateFood", "updateFoodDto", "deleteFood", "getAllExercises", "createExercise", "deleteExercise", "getAllPlans", "createPlan", "req", "createPlanDto", "userId", "user", "updatePlan", "updatePlanDto", "configPlan", "configPlanDto", "deletePlan", "deletePlanConfig", "getAllSubscriptions", "getAllTransactions", "getAffiliates", "updateAffiliate", "updateAffiliateDto", "getUserSubscriptions", "getUserTransactions", "getTransactionDetails", "webhook", "body", "registerCommissionsForSubscription", "constructor"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;wBAhBiG;8BACjF;8BACA;kCACO;+BACN;+BACA;qCACM;+BACN;+BACA;mCACI;yCACM;+BACV;+BACA;+BACA;;;;;;;;;;;;;;;AAGvB,IAAA,AAAMA,kBAAN,MAAMA;IAGT,QAAQ;IAER,2BAA2B;IAC3BC,WAAW;QACP,OAAO,IAAI,CAACC,YAAY,CAACD,QAAQ;IACrC;IAEA,QAAQ;IAER,2BAA2B;IAC3BE,YAAY,AAASC,KAA0B,EAAE;QAC7C,OAAO,IAAI,CAACF,YAAY,CAACC,WAAW,CAACC;IACzC;IAIAC,WAAW,AAAQC,aAA4B,EAAE;QAC7C,OAAO,IAAI,CAACJ,YAAY,CAACG,UAAU,CAACC;IACxC;IAEA,MAEMC,WAAW,AAAaC,EAAU,EAAE,AAAQC,aAA4B,EAAE;QAC9E,OAAO,IAAI,CAACP,YAAY,CAACK,UAAU,CAACG,OAAOF,KAAKC;IAClD;IAEA,MACME,WAAW,AAAaH,EAAU,EAAE;QACxC,OAAO,IAAI,CAACN,YAAY,CAACS,UAAU,CAACD,OAAOF;IAC7C;IAEA,QAAQ;IACR,MAGMI,iBAAiB,AAAeC,IAAY,EAAE;QAChD;;;;;;QAMA,GAEF,OAAO,IAAI,CAACX,YAAY,CAACU,gBAAgB,CAACC;IAC5C;IAEA,MACMC,YAAY,AAASV,KAA0B,EAAE;QACrD,OAAO,IAAI,CAACF,YAAY,CAACY,WAAW,CAACV;IACvC;IAEA,MACMW,YAAY,AAASX,KAAoC,EAAE;QAC/D,OAAO,IAAI,CAACF,YAAY,CAACa,WAAW,CAACX;IACvC;IAEA,MACMY,WAAW,AAAQC,aAA4B,EAAE;QACrD,OAAO,IAAI,CAACf,YAAY,CAACc,UAAU,CAACC;IACtC;IAEA,MACMC,WAAW,AAAaV,EAAU,EAAE,AAAQW,aAA4B,EAAE;QAC9E,OAAO,IAAI,CAACjB,YAAY,CAACgB,UAAU,CAACR,OAAOF,KAAKW;IAClD;IAEA,MACMC,WAAW,AAAaZ,EAAU,EAAE;QACxC,OAAO,IAAI,CAACN,YAAY,CAACkB,UAAU,CAACV,OAAOF;IAC7C;IAEA,aAAa;IACb,MACMa,gBAAgB,AAASjB,KAA8B,EAAE;QAC7D,OAAO,IAAI,CAACF,YAAY,CAACmB,eAAe,CAACjB;IAC3C;IAEA,MACMkB,eAAe,AAAQA,cAAiC,EAAE;QAC9D,OAAO,IAAI,CAACpB,YAAY,CAACoB,cAAc,CAACA;IAC1C;IAEA,MACMC,eAAe,AAAaf,EAAU,EAAE;QAC5C,OAAO,IAAI,CAACN,YAAY,CAACqB,cAAc,CAACb,OAAOF;IACjD;IAEA,mBAAmB;IACnB,MAEMgB,cAAc;QAClB,OAAO,IAAI,CAACtB,YAAY,CAACsB,WAAW;IACtC;IAEA,MAEMC,WAAW,AAAWC,GAAQ,EAAE,AAAQC,aAA4B,EAAE;QAC1E,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAAC1B,YAAY,CAACuB,UAAU,CAACE,eAAeC;IACrD;IAEA,MACME,WAAW,AAAatB,EAAU,EAAE,AAAQuB,aAA4B,EAAE;QAC9E,OAAO,IAAI,CAAC7B,YAAY,CAAC4B,UAAU,CAACpB,OAAOF,KAAKuB;IAClD;IAEA,MACMC,WAAW,AAAaxB,EAAU,EAAE,AAAQyB,aAA4B,EAAE;QAC9E,OAAO,IAAI,CAAC/B,YAAY,CAAC8B,UAAU,CAACtB,OAAOF,KAAKyB;IAClD;IAEA,MACMC,WAAW,AAAa1B,EAAU,EAAE;QACxC,OAAO,IAAI,CAACN,YAAY,CAACgC,UAAU,CAACxB,OAAOF;IAC7C;IAEA,MACM2B,iBAAiB,AAAa3B,EAAU,EAAE;QAC9C,OAAO,IAAI,CAACN,YAAY,CAACiC,gBAAgB,CAACzB,OAAOF;IACnD;IAEA,gCAAgC;IAChC,MAEM4B,oBAAoB,AAAShC,KAAU,EAAE;QAC7C,OAAO,IAAI,CAACF,YAAY,CAACkC,mBAAmB,CAAChC;IAC/C;IAEA,eAAe;IACf,MAEMiC,mBAAmB,AAASjC,KAAU,EAAE;QAC5C,OAAO,IAAI,CAACF,YAAY,CAACmC,kBAAkB,CAACjC;IAC9C;IAEA,aAAa;IACb,MAEMkC,cAAc,AAASlC,KAAU,EAAE;QACvC,OAAO,IAAI,CAACF,YAAY,CAACoC,aAAa,CAAClC;IACzC;IAEA,MAEMmC,gBAAgB,AAAa/B,EAAU,EAAE,AAAQgC,kBAAuB,EAAE;QAC9E,OAAO,IAAI,CAACtC,YAAY,CAACqC,eAAe,CAAC7B,OAAOF,KAAKgC;IACvD;IAEA,gBAAgB;IAChB,MAEMC,qBAAqB,AAAajC,EAAU,EAAE;QAClD,OAAO,IAAI,CAACN,YAAY,CAACuC,oBAAoB,CAAC/B,OAAOF;IACvD;IAEA,MAEMkC,oBAAoB,AAAalC,EAAU,EAAE;QACjD,OAAO,IAAI,CAACN,YAAY,CAACwC,mBAAmB,CAAChC,OAAOF;IACtD;IAEA,MAEMmC,sBAAsB,AAAiBf,MAAc,EAAE,AAAapB,EAAU,EAAE;QACpF,OAAO,IAAI,CAACN,YAAY,CAACyC,qBAAqB,CAACjC,OAAOF,KAAKE,OAAOkB;IACpE;IAEA,iBAAiB;IACjB,MACMgB,QAAQ,AAAQC,IAAS,EAAE,AAAWnB,GAAQ,EAAE;QACpD,2DAA2D;QAC3D,qDAAqD;QACrD,OAAO,IAAI,CAACxB,YAAY,CAAC0C,OAAO,CAACC;IACnC;IAEA,kCAAkC;IAClC,MAEMC,mCAAmC,AAAatC,EAAU,EAAE;QAChE,OAAO,IAAI,CAACN,YAAY,CAAC4C,kCAAkC,CAACpC,OAAOF;IACrE;IAvLAuC,YAAY,AAAiB7C,YAA0B,CAAE;aAA5BA,eAAAA;IAA6B;AAwL9D"}