// ============================================================================
// UNIFIED PROTOCOL HISTORY COMPONENT - HISTÓRICO UNIFICADO DE PROTOCOLOS
// ============================================================================

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  History,
  Calendar,
  Filter,
  ChevronLeft,
  ChevronRight,
  Eye,
  Copy,
  CheckCircle,
  Clock,
  Target,
  TrendingUp,
  BarChart3,
  StopCircle,
  Edit,
  RefreshCw
} from 'lucide-react';
import { apiService } from '../services/api';
import { LoadingError } from './UnifiedErrorDisplay';
import { ConfirmationDialog } from './ConfirmationDialog';
import { useFinalizeProtocol, useReuseProtocol, useCheckActiveProtocol, useReuseProtocolWithCheck } from '../hooks/useProtocolManagement';
import { toast } from 'react-toastify';

// ============================================================================
// TYPES
// ============================================================================

interface Protocol {
  id: number;
  name: string;
  objective: string;
  started_at: string;
  ended_at: string | null;
  created_at: string;
  type: string;
  status: 'active' | 'finished' | 'scheduled';
  duration_days: number;
  goal_calories?: number;
  goal_protein?: number;
  goal_carbs?: number;
  goal_fat?: number;
  goal_water?: number;
}

interface HistoryData {
  protocols: Protocol[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  stats: {
    total: number;
    active: number;
    finished: number;
    avgDuration: number;
    topTypes: Array<{ type: string; count: number }>;
  };
  filters: {
    status: string;
    startDate?: string;
    endDate?: string;
    type?: string;
  };
}

interface UnifiedProtocolHistoryProps {
  protocolType: 'diet' | 'workout';
  onProtocolSelect?: (protocol: Protocol) => void;
  onProtocolDuplicate?: (protocol: Protocol) => void;
  onProtocolFinish?: (protocol: Protocol) => void;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function UnifiedProtocolHistory({
  protocolType,
  onProtocolSelect,
  onProtocolDuplicate,
  onProtocolFinish
}: UnifiedProtocolHistoryProps) {
  const navigate = useNavigate();
  const [data, setData] = useState<HistoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    status: 'all',
    startDate: '',
    endDate: '',
    type: ''
  });

  // Confirmation dialog states
  const [showFinalizeConfirmation, setShowFinalizeConfirmation] = useState(false);
  const [showReuseConfirmation, setShowReuseConfirmation] = useState(false);
  const [showActiveProtocolReuseConfirmation, setShowActiveProtocolReuseConfirmation] = useState(false);
  const [selectedProtocol, setSelectedProtocol] = useState<Protocol | null>(null);

  // Protocol management hooks
  const finalizeProtocol = useFinalizeProtocol();
  const reuseProtocol = useReuseProtocol();
  const checkActiveProtocol = useCheckActiveProtocol();
  const reuseProtocolWithCheck = useReuseProtocolWithCheck();

  // ============================================================================
  // DATA FETCHING
  // ============================================================================

  const fetchHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      // Verificar se há token de autenticação
      const accessToken = localStorage.getItem("accessToken");
      console.log(`🔑 Token disponível:`, !!accessToken);

      if (!accessToken) {
        throw new Error('Token de autenticação não encontrado. Faça login novamente.');
      }

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });

      const endpoint = protocolType === 'diet'
        ? `users/protocols/diet/history?${params}`
        : `users/protocols/workout/history?${params}`;

      console.log(`🎯 Tipo de protocolo: ${protocolType}`);

      console.log(`📚 Buscando histórico: ${endpoint}`);
      console.log(`📋 Filtros aplicados:`, filters);
      console.log(`🌐 URL base da API:`, import.meta.env.VITE_API_URL);
      console.log(`🌐 URL completa seria:`, `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/${endpoint}`);

      const response = await apiService.get(endpoint);

      console.log(`🔍 Resposta completa:`, response);
      console.log(`🔍 Status da resposta:`, response?.status);
      console.log(`🔍 Data da resposta:`, response?.data);

      // Debug: Log protocol statuses
      if (response?.data?.protocols) {
        console.log(`🔍 Protocolos encontrados:`, response.data.protocols.length);
        console.log(`🔍 Status dos protocolos:`, response.data.protocols.map(p => ({
          id: p.id,
          name: p.name,
          status: p.status,
          ended_at: p.ended_at
        })));
        console.log(`🔍 Protocolos ativos:`, response.data.protocols.filter(p => p.status === 'active').length);
        console.log(`🔍 Protocolos finalizados:`, response.data.protocols.filter(p => p.status === 'finished').length);
      }

      // Handle different response formats
      if (response?.status === 'success' && response?.data) {
        setData(response.data);
        console.log(`✅ Histórico carregado: ${response.data.protocols?.length || 0} protocolos`);
      } else if (response?.data && Array.isArray(response.data)) {
        // Handle direct array response
        setData({ protocols: response.data, pagination: { page: 1, totalPages: 1 }, stats: {} });
        console.log(`✅ Histórico carregado (formato direto): ${response.data.length} protocolos`);
      } else if (response && typeof response === 'object') {
        // Handle direct object response
        setData(response);
        console.log(`✅ Histórico carregado (formato objeto): ${response.protocols?.length || 0} protocolos`);
      } else {
        console.error(`❌ Resposta inválida:`, response);
        throw new Error('Formato de resposta inválido da API');
      }
    } catch (err: any) {
      console.error('❌ Erro ao carregar histórico:', err);
      console.error('❌ Detalhes do erro:', {
        message: err?.message,
        response: err?.response,
        status: err?.response?.status,
        data: err?.response?.data
      });
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔄 UnifiedProtocolHistory: useEffect triggered', { filters, protocolType });
    fetchHistory();
  }, [filters, protocolType]);

  // Force refetch on component mount
  useEffect(() => {
    console.log('🔄 UnifiedProtocolHistory: Component mounted, forcing initial fetch');
    fetchHistory();
  }, []);

  // ============================================================================
  // TEST FUNCTIONS
  // ============================================================================

  const testCreateFinishedProtocol = async () => {
    try {
      console.log('🧪 Criando protocolo de teste finalizado...');

      // First create a protocol
      const createResponse = await apiService.post('users/protocols/workout', {
        name: 'Protocolo Teste Finalizado',
        type: 'hipertrofia',
        objective: 'Teste de protocolo finalizado',
        split: 'ABC',
        frequency: 3,
        workouts: []
      });

      console.log('✅ Protocolo criado:', createResponse);

      // Then immediately finish it
      const protocolId = createResponse.data?.id;
      if (protocolId) {
        const finishResponse = await apiService.post(`users/protocols/workout/${protocolId}/finish`);
        console.log('✅ Protocolo finalizado:', finishResponse);

        // Refresh the history
        await fetchHistory();
        alert('Protocolo de teste criado e finalizado com sucesso!');
      }
    } catch (error) {
      console.error('❌ Erro ao criar protocolo de teste:', error);
      alert('Erro ao criar protocolo de teste: ' + error.message);
    }
  };

  const testApiConnection = async () => {
    try {
      console.log('🧪 Testando conexão com API...');
      console.log('🔗 URL base configurada:', import.meta.env.VITE_API_URL);

      // Teste direto com fetch no endpoint de histórico
      const accessToken = localStorage.getItem("accessToken");
      console.log('🔑 Token:', accessToken ? 'Presente' : 'Ausente');

      const historyUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/users/protocols/diet/history?page=1&limit=10&status=all`;
      console.log('🎯 URL de teste (histórico):', historyUrl);

      const fetchResponse = await fetch(historyUrl, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Resposta fetch:', fetchResponse.status, fetchResponse.statusText);
      const fetchData = await fetchResponse.json();
      console.log('📦 Dados fetch:', fetchData);

      if (fetchData.status === 'success') {
        alert(`✅ Endpoint de histórico funcionando! ${fetchData.data.protocols.length} protocolos encontrados.`);
      } else {
        alert('❌ Endpoint retornou erro: ' + (fetchData.message || 'Erro desconhecido'));
      }

      // Test with apiService to compare
      console.log('🧪 Testando com apiService...');
      const apiResponse: any = await apiService.get('users/protocols/diet/history?page=1&limit=10&status=all');
      console.log('📦 Resposta apiService:', apiResponse);

      if (apiResponse?.status === 'success') {
        console.log('✅ apiService também funcionando!');
      } else {
        console.log('❌ apiService com problema:', apiResponse);
      }
    } catch (err) {
      console.error('❌ Erro na API:', err);
      alert('Erro na API! Verifique o console para mais detalhes.');
    }
  };

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset page when other filters change
    }));
  };

  const handlePageChange = (newPage: number) => {
    handleFilterChange('page', newPage);
  };

  const handleDuplicate = async (protocol: Protocol) => {
    console.log('🔍 DEBUG: handleDuplicate called with protocol:', protocol);
    setSelectedProtocol(protocol);

    try {
      // Check if there's an active protocol
      const activeProtocol = await checkActiveProtocol.mutateAsync(protocolType);

      if (activeProtocol && activeProtocol.id) {
        // There's an active protocol, show confirmation dialog
        console.log('🔍 Active protocol found, showing confirmation dialog');
        setShowActiveProtocolReuseConfirmation(true);
      } else {
        // No active protocol, proceed directly with reuse
        console.log('🔍 No active protocol found, proceeding with reuse');
        setShowReuseConfirmation(true);
      }
    } catch (error) {
      console.error('Error checking active protocol:', error);
      // If there's an error checking, proceed with normal reuse
      setShowReuseConfirmation(true);
    }
  };

  const handleConfirmReuse = async () => {
    if (!selectedProtocol) return;

    try {
      await reuseProtocol.mutateAsync({
        protocol: selectedProtocol,
        protocolType
      });

      // Refresh the list
      fetchHistory();

      // Call the callback if provided
      if (onProtocolDuplicate) {
        onProtocolDuplicate(selectedProtocol);
      }
    } catch (error) {
      console.error('Error reusing protocol:', error);
    } finally {
      setShowReuseConfirmation(false);
      setSelectedProtocol(null);
    }
  };

  const handleConfirmReuseWithActiveProtocol = async () => {
    if (!selectedProtocol) return;

    try {
      await reuseProtocolWithCheck.mutateAsync({
        protocol: selectedProtocol,
        protocolType,
        shouldFinalizeActive: true
      });

      // Refresh the list
      fetchHistory();

      // Call the callback if provided
      if (onProtocolDuplicate) {
        onProtocolDuplicate(selectedProtocol);
      }
    } catch (error) {
      console.error('Error reusing protocol with active check:', error);
    } finally {
      setShowActiveProtocolReuseConfirmation(false);
      setSelectedProtocol(null);
    }
  };

  const handleFinish = (protocol: Protocol) => {
    setSelectedProtocol(protocol);
    setShowFinalizeConfirmation(true);
  };

  const handleConfirmFinalize = async () => {
    if (!selectedProtocol) return;

    try {
      await finalizeProtocol.mutateAsync({
        protocolId: selectedProtocol.id.toString(),
        protocolType
      });

      // Refresh the list
      fetchHistory();

      // Call the callback if provided
      if (onProtocolFinish) {
        onProtocolFinish(selectedProtocol);
      }
    } catch (error) {
      console.error('Error finalizing protocol:', error);
    } finally {
      setShowFinalizeConfirmation(false);
      setSelectedProtocol(null);
    }
  };

  const handleEdit = (protocol: Protocol) => {
    console.log('🔍 DEBUG: handleEdit called with protocol:', protocol);
    console.log('🔍 DEBUG: protocolType:', protocolType);

    // Check if protocol has valid ID
    if (!protocol?.id) {
      console.error('❌ DEBUG: Protocol ID is missing for edit!', protocol);
      alert('Erro: ID do protocolo não encontrado para edição.');
      return;
    }

    try {
      console.log(`✏️ Editando protocolo ${protocol.id}`);

      // Determine the correct edit URL based on protocol type
      let url: string;
      if (protocolType === 'workout') {
        url = `/dashboard/workout/edit-protocol/${protocol.id}`;
      } else if (protocolType === 'diet') {
        url = `/dashboard/diet/edit-protocol/${protocol.id}`;
      } else {
        console.error('❌ DEBUG: Unknown protocol type:', protocolType);
        alert('Erro: Tipo de protocolo desconhecido.');
        return;
      }

      console.log('🔍 DEBUG: Navigating to edit URL:', url);
      console.log('🔍 DEBUG: Current location:', window.location.href);

      // Show loading feedback
      const originalText = 'Editar';
      const button = document.querySelector(`[title="Editar protocolo"]`) as HTMLButtonElement;
      if (button) {
        button.textContent = 'Carregando...';
        button.disabled = true;
      }

      // Check if protocol exists in edit pages
      const checkProtocolExists = async () => {
        try {
          const endpoint = `users/protocols/${protocolType}/${protocol.id}`;
          const response = await apiService.get(endpoint);
          return !!response;
        } catch (error) {
          console.warn('⚠️ Protocol not found in backend, using fallback navigation');
          return false;
        }
      };

      // Try React Router navigation first
      try {
        console.log('🔍 DEBUG: Attempting React Router navigation...');

        // Check if we're in a React Router context
        if (navigate) {
          navigate(url);
          console.log('✅ DEBUG: React Router navigation successful');
        } else {
          throw new Error('Navigate function not available');
        }
      } catch (navError) {
        console.warn('⚠️ DEBUG: React Router navigation failed, using window.location:', navError);

        // Show loading feedback before navigation
        setTimeout(() => {
          console.log('🔍 DEBUG: Executing window.location navigation...');
          window.location.href = url;
        }, 300);
      }

    } catch (err: any) {
      console.error('❌ Erro ao navegar para edição:', err);
      alert('Erro ao abrir editor de protocolo: ' + err.message);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Clock className="w-4 h-4 text-green-400" />;
      case 'finished':
        return <CheckCircle className="w-4 h-4 text-blue-400" />;
      default:
        return <Calendar className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Ativo';
      case 'finished':
        return 'Finalizado';
      default:
        return 'Agendado';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/10 text-green-400 border-green-500/30';
      case 'finished':
        return 'bg-blue-500/10 text-blue-400 border-blue-500/30';
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/30';
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"></div>
      </div>
    );
  }

  if (error) {
    return (
      <LoadingError
        error={error}
        onRetry={fetchHistory}
        context="carregar histórico de protocolos"
      />
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-gray-400">
        <History className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>Nenhum dado de histórico disponível</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Stats */}
      <div className="bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <History className="w-6 h-6 text-snapfit-green" />
            <h2 className="text-xl font-semibold text-white">
              Histórico de Protocolos de {protocolType === 'diet' ? 'Dieta' : 'Treino'}
            </h2>
          </div>
          <button
            onClick={() => {
              console.log('🔄 Manual refresh triggered');
              fetchHistory();
            }}
            disabled={loading}
            className={`flex items-center gap-2 px-3 py-1 rounded text-sm border transition-colors ${
              loading
                ? 'bg-gray-500/20 text-gray-400 border-gray-500/30 cursor-not-allowed'
                : 'bg-snapfit-green/20 text-snapfit-green border-snapfit-green/30 hover:bg-snapfit-green/30'
            }`}
            title="Atualizar dados"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Carregando...' : 'Atualizar'}
          </button>
          <div className="flex gap-2">
            <button
              onClick={testApiConnection}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors hidden"
            >
              Testar API
            </button>
            <button
              onClick={fetchHistory}
              className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors hidden"
            >
              Recarregar
            </button>
            <button
              onClick={testCreateFinishedProtocol}
              className="px-3 py-1 bg-purple-500/20 text-purple-400 rounded text-sm border border-purple-500/30 hover:bg-purple-500/30 transition-colors"
              title="Criar protocolo de teste finalizado"
            >
              🧪 Criar Teste
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-snapfit-green">{data.stats.total}</div>
            <div className="text-sm text-gray-400">Total</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{data.stats.active}</div>
            <div className="text-sm text-gray-400">Ativos</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{data.stats.finished}</div>
            <div className="text-sm text-gray-400">Finalizados</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">{data.stats.avgDuration}</div>
            <div className="text-sm text-gray-400">Dias (média)</div>
          </div>
        </div>

        {/* Top Types */}
        {data.stats.topTypes.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-300 mb-2">Tipos mais usados:</h4>
            <div className="flex flex-wrap gap-2">
              {data.stats.topTypes.map((typeData, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-snapfit-dark-gray text-xs rounded-full text-gray-300"
                >
                  {typeData.type} ({typeData.count})
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Debug Info - Simplified */}
        <div className="mt-4 p-3 bg-gray-800/50 rounded border border-gray-600/30">
          <div className="text-xs text-gray-400 space-y-1">
            <div>🔍 Protocolos carregados: {data.protocols?.length || 0}</div>
            <div>🔍 Tipo: {protocolType}</div>
            <div>🔍 Status: {loading ? 'Carregando...' : 'Pronto'}</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20">
        <div className="flex items-center gap-3 mb-3">
          <Filter className="w-5 h-5 text-snapfit-green" />
          <h3 className="font-medium text-white">Filtros</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          {/* Status Filter */}
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="px-3 py-2 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white focus:border-snapfit-green focus:outline-none"
          >
            <option value="all">Todos os status</option>
            <option value="active">Ativos</option>
            <option value="finished">Finalizados</option>
          </select>

          {/* Start Date */}
          <input
            type="date"
            value={filters.startDate}
            onChange={(e) => handleFilterChange('startDate', e.target.value)}
            className="px-3 py-2 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white focus:border-snapfit-green focus:outline-none"
            placeholder="Data início"
          />

          {/* End Date */}
          <input
            type="date"
            value={filters.endDate}
            onChange={(e) => handleFilterChange('endDate', e.target.value)}
            className="px-3 py-2 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white focus:border-snapfit-green focus:outline-none"
            placeholder="Data fim"
          />

          {/* Type Filter */}
          <input
            type="text"
            value={filters.type}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="px-3 py-2 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white focus:border-snapfit-green focus:outline-none"
            placeholder="Filtrar por tipo..."
          />
        </div>
      </div>

      {/* Protocol List */}
      <div className="space-y-3">
        {data.protocols.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <History className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum protocolo encontrado com os filtros aplicados</p>
          </div>
        ) : (
          data.protocols.map((protocol) => (
            <div
              key={protocol.id}
              className="bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20 hover:border-snapfit-green/40 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-medium text-white">{protocol.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(protocol.status)}`}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(protocol.status)}
                        {getStatusLabel(protocol.status)}
                      </div>
                    </span>
                    {protocol.type && (
                      <span className="px-2 py-1 bg-snapfit-dark-gray text-xs rounded-full text-gray-300">
                        {protocol.type}
                      </span>
                    )}
                  </div>

                  <p className="text-gray-400 text-sm mb-3">{protocol.objective}</p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Início:</span>
                      <div className="text-white">
                        {new Date(protocol.started_at).toLocaleDateString('pt-BR')}
                      </div>
                    </div>
                    {protocol.ended_at && (
                      <div>
                        <span className="text-gray-400">Fim:</span>
                        <div className="text-white">
                          {new Date(protocol.ended_at).toLocaleDateString('pt-BR')}
                        </div>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-400">Duração:</span>
                      <div className="text-white">{protocol.duration_days} dias</div>
                    </div>
                    {protocolType === 'diet' && protocol.goal_calories && (
                      <div>
                        <span className="text-gray-400">Calorias:</span>
                        <div className="text-white">{protocol.goal_calories}</div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex gap-2 ml-4">
                  <button
                    onClick={() => {
                      console.log('🔍 DEBUG: Ver button clicked for protocol:', protocol);
                      console.log('🔍 DEBUG: onProtocolSelect function:', onProtocolSelect);
                      onProtocolSelect?.(protocol);
                    }}
                    className="flex items-center gap-1 px-2 py-1 bg-gray-500/20 text-gray-300 rounded text-xs border border-gray-500/30 hover:bg-gray-500/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Visualizar detalhes"
                    disabled={loading}
                  >
                    <Eye className="w-3 h-3" />
                    Ver
                  </button>
                  <button
                    onClick={() => {
                      console.log('🔍 DEBUG: Reutilizar button clicked for protocol:', protocol);
                      handleDuplicate(protocol);
                    }}
                    className="flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs border border-blue-500/30 hover:bg-blue-500/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Reutilizar protocolo"
                    disabled={loading || reuseProtocol.isPending}
                  >
                    {reuseProtocol.isPending ? (
                      <div className="w-3 h-3 border border-blue-400/30 border-t-blue-400 rounded-full animate-spin" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                    Reutilizar
                  </button>
                  {protocol.status === 'active' && (
                    <button
                      onClick={() => handleFinish(protocol)}
                      className="flex items-center gap-1 px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs border border-red-500/30 hover:bg-red-500/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Finalizar protocolo"
                      disabled={loading || finalizeProtocol.isPending}
                    >
                      {finalizeProtocol.isPending ? (
                        <div className="w-3 h-3 border border-red-400/30 border-t-red-400 rounded-full animate-spin" />
                      ) : (
                        <StopCircle className="w-3 h-3" />
                      )}
                      Finalizar
                    </button>
                  )}
                  {protocol.status !== 'active' && (
                    <button
                      onClick={() => {
                        console.log('🔍 DEBUG: Editar button clicked for protocol:', protocol);
                        handleEdit(protocol);
                      }}
                      className="flex items-center gap-1 px-2 py-1 bg-amber-500/20 text-amber-400 rounded text-xs border border-amber-500/30 hover:bg-amber-500/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Editar protocolo"
                      disabled={loading}
                    >
                      <Edit className="w-3 h-3" />
                      Editar
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {data.pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Página {data.pagination.page} de {data.pagination.totalPages} 
            ({data.pagination.total} protocolos)
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={() => handlePageChange(data.pagination.page - 1)}
              disabled={!data.pagination.hasPrev}
              className="p-2 text-gray-400 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            
            <span className="px-3 py-2 bg-snapfit-dark-gray rounded text-white text-sm">
              {data.pagination.page}
            </span>
            
            <button
              onClick={() => handlePageChange(data.pagination.page + 1)}
              disabled={!data.pagination.hasNext}
              className="p-2 text-gray-400 hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Confirmation Dialogs */}
      <ConfirmationDialog
        isOpen={showFinalizeConfirmation}
        onClose={() => {
          setShowFinalizeConfirmation(false);
          setSelectedProtocol(null);
        }}
        onConfirm={handleConfirmFinalize}
        title="Finalizar Protocolo"
        message={`Tem certeza que deseja finalizar o protocolo "${selectedProtocol?.name}"? Esta ação não pode ser desfeita.`}
        confirmText="Sim, Finalizar"
        cancelText="Cancelar"
        type="warning"
        isLoading={finalizeProtocol.isPending}
      />

      <ConfirmationDialog
        isOpen={showReuseConfirmation}
        onClose={() => {
          setShowReuseConfirmation(false);
          setSelectedProtocol(null);
        }}
        onConfirm={handleConfirmReuse}
        title="Reutilizar Protocolo"
        message={`Deseja criar uma cópia do protocolo "${selectedProtocol?.name}" como um novo protocolo ativo?`}
        confirmText="Sim, Reutilizar"
        cancelText="Cancelar"
        type="info"
        isLoading={reuseProtocol.isPending}
      />

      <ConfirmationDialog
        isOpen={showActiveProtocolReuseConfirmation}
        onClose={() => {
          setShowActiveProtocolReuseConfirmation(false);
          setSelectedProtocol(null);
        }}
        onConfirm={handleConfirmReuseWithActiveProtocol}
        title="Protocolo Ativo Encontrado"
        message={`Você já possui um protocolo ativo. Ao reutilizar "${selectedProtocol?.name}", o protocolo atual será finalizado. Deseja continuar?`}
        confirmText="Sim, Finalizar e Reutilizar"
        cancelText="Cancelar"
        type="warning"
        isLoading={reuseProtocolWithCheck.isPending || checkActiveProtocol.isPending}
      />
    </div>
  );
}
