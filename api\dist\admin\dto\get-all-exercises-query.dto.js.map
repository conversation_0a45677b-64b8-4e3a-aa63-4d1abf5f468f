{"version": 3, "sources": ["../../../src/admin/dto/get-all-exercises-query.dto.ts"], "sourcesContent": ["// src/admin/dto/get-all-exercises-query.dto.ts\r\nimport { IsOptional, IsString, IsInt } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class GetAllExercisesQueryDto {\r\n  @IsOptional()\r\n  @IsString()\r\n  name?: string; // Filtro por nome do exercício\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  muscle_group_id?: number; // Filtro por grupo muscular\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  page?: number; // Página atual (padrão: 1)\r\n\r\n  @IsOptional()\r\n  @Type(() => Number)\r\n  @IsInt()\r\n  limit?: number; // Limite de itens por página (padrão: 10)\r\n}"], "names": ["GetAllExercisesQueryDto", "Number"], "mappings": "AAAA,+CAA+C;;;;;+BAIlCA;;;eAAAA;;;gCAH+B;kCACvB;;;;;;;;;;AAEd,IAAA,AAAMA,0BAAN,MAAMA;AAmBb;;;;;;;;oCAbcC;;;;;;oCAKAA;;;;;;oCAKAA"}