import { Injectable } from '@nestjs/common';
import { GetCoachClientsQueryDto } from '../admin/dto/get-coach-clients-query.dto';
import { db } from '../database';
import * as dayjs from 'dayjs';
import { ImportCoachProtocolDto } from 'src/admin/dto/import-coach-protocol.dto';
import { CreateCoachProtocolDto } from 'src/admin/dto/create-coach-protocol.dto';
import { CreateCoachClientProtocolDto } from './dto/create-coach-client-protocol.dto';

@Injectable()
export class CoachService {
    formatDatetime(datetime: any): string {
        return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
    }

    calculateAge(birthDate: Date): number | null {
        if (!birthDate) {
          return null;
        }

        const today = new Date();
        const birthDateObj = new Date(birthDate);
    
        let age = today.getFullYear() - birthDateObj.getFullYear();
        const monthDifference = today.getMonth() - birthDateObj.getMonth();
    
        if (
          monthDifference < 0 ||
          (monthDifference === 0 && today.getDate() < birthDateObj.getDate())
        ) {
          age--;
        }
    
        return age;
      }

    async getClients(query: GetCoachClientsQueryDto, userId: number) {
        const { q } = query;
        let { page = 1, limit = 100 } = query;
        const offset = (page - 1) * limit;
            
        let queryBuilder = db
            .selectFrom('users')
            .innerJoin('users_roles', 'users.id', 'users_roles.user_id')
            .innerJoin('clients', 'users.id', 'clients.client_id')
            .leftJoin('coach_protocols', 'coach_protocols.user_id', 'users.id')
            .select([
            'users.id',
            'users.name',
            'users.email',
            'users.photo',
            'users.created_at',
            'coach_protocols.name as protocol'
            // db.fn<string>('group_concat', ['roles.name']).as('roles'),
            ])
            .where('clients.role_id', '=', 2)
            .where('clients.user_id', '=', userId)
            .groupBy('users.id')
            .orderBy('users.created_at', 'desc');
    
        if (q) {
            queryBuilder = queryBuilder.where((eb) =>
            eb.or([
                eb('users.name', 'like', `%${q}%`),
                eb('users.email', 'like', `%${q}%`),
            ])
            );
        }
    
        const [data, total] = await Promise.all([
            queryBuilder.limit(limit).offset(offset).execute(),
            db.selectFrom('users').select(db.fn.countAll().as('total')).executeTakeFirst(),
        ]);
    
        return {
            status: 'success',
            data: data.map((row) => ({
            id: row.id,
            name: row.name,
            email: row.email,
            photo: row.photo,
            protocol: row.protocol,
            plan: '',
            plan_status: '',
            attendance: 99,
            date: this.formatDatetime(row.created_at),            
            })),
            pagination: {
            page,
            limit,
            total: Number(total?.total),
            },
        };
    }

    async getClient(id: number, userId: number) {
        const client = await db
            .selectFrom('users')
            .innerJoin('clients', 'users.id', 'clients.client_id')
            .select([
                'clients.client_id as id',
                'users.name',
                'users.email',
                'users.photo',
                'users.height',
                'users.weight',
                'clients.created_at as client_date',
                'users.date_of_birth'
            ])
            .where('users.id', '=', id)
            .where('clients.client_id', '=', id)
            .where('clients.user_id', '=', userId)
            .where('clients.role_id', '=', 2)
            .groupBy('users.id')
            .executeTakeFirst();

            if (!client) {
                return {
                    status: 'error',
                    message: 'Client not found',
                };
            }

            const attendance = {
                week: 999,
                month: 999,
                sequence: 999,
                record: 999
            };

            /*
            const lastProtocol = await db
                .selectFrom('coach_protocols as p')
                .where('p.user_id', '=', userId)
                .where('p.client_id', '=', id)
                .where('p.ended_at', 'is', null)
                .leftJoin('select_options as s', 's.id', 'p.type_id')
                .select([
                    'p.id',
                    'p.name',
                    's.value_option as type',
                    'p.split',
                    'p.frequency',
                    'p.notes',
                    'p.started_at',
                    'p.objective',
                ])
                .orderBy('p.id', 'desc')
                .limit(1)
                .executeTakeFirst();

            let protocol: any = null;

            if(lastProtocol) {
            protocol = {
                id: lastProtocol.id,
                name: lastProtocol.name,
                type: lastProtocol.type,
                split: lastProtocol.split,
                frequency: lastProtocol.frequency,
                objective: lastProtocol.objective,
                notes: lastProtocol.notes,
                started_at: this.formatDatetime(lastProtocol.started_at),
            }
            }

            const clientData = {
                id: client.id,
                name: client.name,
                email: client.email,
                photo: client.photo,                
                height: client.height,
                weight: client.weight,
                age: client.date_of_birth ? this.calculateAge(client.date_of_birth) : null,
                client_date: this.formatDatetime(client.client_date),
                // last_protocol_date: this.formatDatetime(client.last_protocol_date),
                attendance,
                protocol
            };
            
        return {
            status: 'success',
            data: clientData,
        };
        */
    }

    async getClientProtocols(id: number, userId: number) {
        /*
        const protocols = await db
            .selectFrom('coach_protocols as p')
            .where('p.user_id', '=', userId)
            .where('p.client_id', '=', id)
            .leftJoin('select_options as s', 's.id', 'p.type_id')
            .select([
                'p.id',
                'p.name',
                's.value_option as type',
                'p.split',
                'p.frequency',
                'p.objective',
                'p.notes',
                'p.started_at',
                'p.ended_at',
                'p.created_at',
                'p.updated_at',
            ])
            .orderBy('p.created_at', 'desc')
            .execute();

        return {
            status: 'success',
            data: protocols,
        };
        */
    }

    async createClientProtocol(id: number, createCoachClientProtocolDto: CreateCoachClientProtocolDto, userId: number) {
        const { name, type_id, split, frequency, objective, notes, workouts } = createCoachClientProtocolDto;

        const new_protocol = await db
            .insertInto('coach_protocols')
            .values({
                client_id: id,
                user_id: userId,
                name: name,
                type_id: type_id,
                split: split,
                frequency: frequency,
                objective: objective,
                // general_notes: general_notes,
                started_at: new Date(),
                ended_at: null,
                created_at: new Date(),
                updated_at: new Date(),
            })
            .executeTakeFirst();

        const new_protocol_id = Number(new_protocol.insertId);
        
        for (const [index, workout] of workouts.entries()) {
            for (const exercise of workout.exercises) {
                /*
                await db.insertInto('coach_protocols_workouts')
                    .values({
                        protocol_id: new_protocol_id,
                        exercise_id: exercise.exercise_id,
                        split_group: index + 1,
                        sets: exercise.sets,
                        reps: exercise.reps,
                        rpe: exercise.rpe,
                        rest_seconds: exercise.rest_seconds,
                        notes: exercise.notes,
                    })
                    .execute();
                    */
            }
        }

        return {
            status: 'success',
            data: [],
        };
    }

    async importProtocol(id: number, importCoachProtocolDto: ImportCoachProtocolDto, userId: number) {
        const { protocol_id } = importCoachProtocolDto;

        const protocol_template = await db
            .selectFrom('coach_protocols_templates')
            .where('id', '=', protocol_id)
            .where('user_id', '=', userId)
            .select(['id', 'name', 'type_id', 'split', 'frequency', 'objective', 'notes'])
            .executeTakeFirst();

        if (!protocol_template) {
            return {
                status: 'error',
                message: 'Protocol not found',
            };
        }

        const protocol_template_workouts = await db
            .selectFrom('coach_protocols_templates_workouts')
            .where('protocol_id', '=', protocol_id)
            .select(['id', 'exercise_id', 'split_group', 'sets', 'reps', 'rpe', 'rest_seconds', 'notes'])
            .execute();

        const new_protocol = await db
            .insertInto('coach_protocols')
            .values({
                client_id: id,
                user_id: userId,
                name: protocol_template.name,
                type_id: protocol_template.type_id,
                split: protocol_template.split,
                frequency: protocol_template.frequency,
                objective: protocol_template.objective,
                general_notes: protocol_template.notes,
                started_at: new Date(),
                ended_at: null,
                created_at: new Date(),
                updated_at: new Date(),
            })
            .executeTakeFirst();

            const new_protocol_id = Number(new_protocol.insertId);

            /*
            await db
                .insertInto('coach_protocols_workouts')
                .values(protocol_template_workouts.map((workout) => ({
                    protocol_id: new_protocol_id,
                    exercise_id: workout.exercise_id,
                    split_group: workout.split_group,
                    sets: workout.sets,
                    reps: workout.reps,
                    rpe: workout.rpe,
                    rest_seconds: workout.rest_seconds,
                    notes: workout.notes,
                })))
                .execute();
                */

        return {
            status: 'success',
            data: [],
        };
    }

    async getProtocols(userId: number) {
        const protocols = await db
        .selectFrom('coach_protocols_templates as p')
        .leftJoin('select_options as s', 's.id', 'p.type_id')
        .where('p.user_id', '=', userId)
        .select(['p.id', 'p.name', 's.value_option as type', 'p.split', 'p.frequency', 'p.objective', 'p.notes', 'p.created_at', 'p.updated_at'])
        .orderBy('p.id', 'desc')
        .execute();

        return {
            status: 'success',
            data: protocols,
        };
    }

    async getProtocol(protocolId: number, userId: number) {
        // Buscar o protocolo específico
        const protocol = await db
            .selectFrom('coach_protocols_templates as p')
            .leftJoin('select_options as s', 's.id', 'p.type_id')
            .where('p.user_id', '=', userId)
            .where('p.id', '=', protocolId)
            .select([
                'p.id',
                'p.name',
                's.value_option as type',
                'p.split',
                'p.frequency',
                'p.objective',
                'p.notes',
                'p.created_at',
                'p.updated_at'
            ])
            .executeTakeFirst();

        if (!protocol) {
            return {
                status: 'error',
                message: 'Protocol not found',
            };
        }

        // Para templates, buscar exercícios diretamente da tabela de templates
        const templateExercises = await db
            .selectFrom('coach_protocols_templates_workouts as tw')
            .leftJoin('exercises as e', 'e.id', 'tw.exercise_id')
            .where('tw.protocol_id', '=', protocolId)
            .selectAll()
            .execute();

        // Agrupar exercícios por split_group para formar os workouts
        const workoutGroups: { [key: number]: any[] } = {};
        templateExercises.forEach(exercise => {
            const splitGroup = exercise.split_group;
            if (!workoutGroups[splitGroup]) {
                workoutGroups[splitGroup] = [];
            }
            workoutGroups[splitGroup].push({
                id: exercise.id,
                exerciseId: exercise.exercise_id,
                name: exercise.name,
                muscle_group: exercise.muscle_group_id,
                equipment: exercise.equipment_id,
                media_url: exercise.media_url,
                sets: exercise.sets,
                reps: exercise.reps,
                rpe: exercise.rpe,
                restTime: exercise.rest_seconds,
                notes: exercise.notes,
                orderIndex: workoutGroups[splitGroup].length + 1
            });
        });

        // Converter grupos em workouts
        const workoutsWithExercises = Object.keys(workoutGroups).map(splitGroup => {
            const splitLetter = String.fromCharCode(64 + parseInt(splitGroup)); // A, B, C, etc.
            return {
                id: splitGroup,
                name: `Treino ${splitLetter}`,
                exercises: workoutGroups[parseInt(splitGroup)]
            };
        });

        return {
            status: 'success',
            data: {
                ...protocol,
                workouts: workoutsWithExercises
            },
        };
    }

    async createProtocol(createCoachProtocolDto: CreateCoachProtocolDto, userId: number) {
        const { name, type_id, split, frequency, objective, notes, workouts } = createCoachProtocolDto;

        const new_protocol = await db
            .insertInto('coach_protocols_templates')
            .values({
                name: name,
                type_id: type_id,
                split: split,
                frequency: frequency,
                objective: objective,
                notes: notes,
                user_id: userId
            })
            .executeTakeFirst();

            const new_protocol_id = Number(new_protocol.insertId);

            for (const [index, workout] of workouts.entries()) {
                await Promise.all(
                    workout.exercises.map(async (exercise) => {
                        await db
                            .insertInto('coach_protocols_templates_workouts')
                            .values({
                                protocol_id: new_protocol_id,
                                exercise_id: exercise.exercise_id,
                                split_group: index + 1, 
                                sets: exercise.sets,
                                reps: exercise.reps,
                                rpe: exercise.rpe,
                                rest_seconds: exercise.rest_seconds,
                                notes: exercise?.notes,
                            })
                            .execute();
                    })
                );
            }
            

        return {
            status: 'success',
            data: [],
        };
    }

    async updateProtocol(protocolId: number, updateData: any, userId: number) {
        // Verificar se o protocolo existe e pertence ao usuário
        const existingProtocol = await db
            .selectFrom('coach_protocols')
            .select(['id', 'client_id', 'ended_at', 'started_at'])
            .where('id', '=', protocolId)
            .where('client_id', '=', userId)
            .executeTakeFirst();

        if (!existingProtocol) {
            return {
                status: 'error',
                message: 'Protocolo não encontrado ou você não tem permissão para editá-lo.',
            };
        }

        // Verificar se o protocolo ainda está ativo (não foi finalizado)
        if (existingProtocol.ended_at) {
            return {
                status: 'error',
                message: 'Não é possível editar um protocolo que já foi finalizado.',
            };
        }

        try {
            // Atualizar dados básicos do protocolo
            await db
                .updateTable('coach_protocols')
                .set({
                    name: updateData.name,
                    type_id: updateData.type,
                    split: updateData.split,
                    frequency: updateData.frequency,
                    objective: updateData.objective,
                    general_notes: updateData.notes,
                    updated_at: new Date(),
                })
                .where('id', '=', protocolId)
                .execute();

            // Se há workouts para atualizar
            if (updateData.workouts && Array.isArray(updateData.workouts)) {
                // Remover workouts e exercícios existentes
                const existingWorkouts = await db
                    .selectFrom('coach_protocols_workouts')
                    .select(['id'])
                    .where('protocol_id', '=', protocolId)
                    .execute();

                for (const workout of existingWorkouts) {
                    await db.deleteFrom('coach_protocols_workouts_exercises').where('workout_id', '=', workout.id).execute();
                }
                await db.deleteFrom('coach_protocols_workouts').where('protocol_id', '=', protocolId).execute();

                // Adicionar novos workouts e exercícios
                const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                for (const [index, workout] of updateData.workouts.entries()) {
                    const workoutName = workout.name || `Treino ${letters[index]}`;

                    const newWorkout = await db
                        .insertInto('coach_protocols_workouts')
                        .values({
                            protocol_id: protocolId,
                            name: workoutName,
                            created_at: new Date(),
                            updated_at: new Date(),
                        })
                        .executeTakeFirst();

                    const workoutId = Number(newWorkout.insertId);

                    if (workout.exercises && Array.isArray(workout.exercises)) {
                        for (const exercise of workout.exercises) {
                            // Verificar se é um exercício personalizado (ID começa com 'custom_')
                            const isCustomExercise = typeof exercise.exercise_id === 'string' &&
                                exercise.exercise_id.toString().startsWith('custom_');

                            await db
                                .insertInto('coach_protocols_workouts_exercises')
                                .values({
                                    workout_id: workoutId,
                                    // Para exercícios personalizados, definir exercise_id como null
                                    exercise_id: isCustomExercise ? null : (exercise.exercise?.id || exercise.exercise_id),
                                    name: exercise.name || exercise.exercise_name || exercise.exercise?.name,
                                    sets: exercise.sets,
                                    reps: exercise.reps,
                                    rpe: exercise.rpe,
                                    rest_seconds: exercise.restTime || exercise.rest_seconds,
                                    notes: exercise.notes,
                                })
                                .execute();
                        }
                    }
                }
            }

            return {
                status: 'success',
                message: 'Protocolo atualizado com sucesso',
                data: { id: protocolId }
            };
        } catch (error) {
            console.error('Erro ao atualizar protocolo:', error);
            return {
                status: 'error',
                message: 'Erro interno do servidor ao atualizar protocolo',
            };
        }
    }

    async deleteProtocol(id: number, userId: number) {
        await db
            .deleteFrom('coach_protocols_templates')
            .where('id', '=', id)
            .execute();

        await db
            .deleteFrom('coach_protocols_templates_workouts')
            .where('protocol_id', '=', id)
            .execute();

        return {
            status: 'success',
            data: [],
        };
    }

}
