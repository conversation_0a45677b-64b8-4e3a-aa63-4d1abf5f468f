import{c as qt,am as Nt,bR as Wt,bm as Ht,b as ge,R as H,j as e,bx as zt,bG as Mt,bS as Ut,ab as Qt,aj as xe,C as Yt,bT as ut,bU as Kt,aT as Oe,bV as Ve,a8 as St,L as Pe,P as Ct,a as kt,a7 as Vt,y as ke,t as Ze,bW as Zt,r as G,au as gt,bX as Gt,aG as _e,bY as Jt,bZ as Xt,b_ as es,b$ as Dt,bo as ts,F as Ge,c0 as ss,b4 as as,b3 as rs,H as qe,a6 as ns,Z as is,s as Je,D as We,G as os,b5 as cs,l as $t,f as Tt,as as ls,n as ds,bD as us,aq as gs,I as He,m as ms,u as It,at as mt,h as hs,c1 as ht,c2 as Re,c3 as Xe,c4 as et,c5 as fs,ai as xs,p as ps,ad as bs}from"./index-D0i1bWZj.js";import{B as vs}from"./BadgetComingSoon-C7GNETkz.js";import{b as ys,c as js,d as ws,e as Ns,u as Ms,a as Ss,f as Cs,g as ks}from"./useDiary-DhoFSyaB.js";import{a as Ds,M as $s,A as Ts}from"./MealCheckModal-D7QsNDmH.js";import{f as Is}from"./date-DOfZ7AVQ.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const As=[["path",{d:"M10 8h.01",key:"1r9ogq"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M14 8h.01",key:"1primd"}],["path",{d:"M16 12h.01",key:"1l6xoz"}],["path",{d:"M18 8h.01",key:"emo2bl"}],["path",{d:"M6 8h.01",key:"x9i8wu"}],["path",{d:"M7 16h10",key:"wp8him"}],["path",{d:"M8 12h.01",key:"czm47f"}],["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}]],Es=qt("keyboard",As),Os={diet:[{action:"meal_completed",points:15,description:"Refeição realizada no horário"},{action:"daily_diet_completed",points:50,description:"Todas as refeições do dia realizadas"},{action:"weekly_diet_completed",points:200,description:"Meta semanal de dieta atingida"}],workout:[{action:"workout_completed",points:50,description:"Treino realizado"},{action:"exercise_completed",points:5,description:"Exercício concluído"},{action:"weekly_workout_completed",points:150,description:"Meta semanal de treinos atingida"}],activity:[{action:"steps_goal",points:20,description:"Meta de passos diários atingida"},{action:"activity_logged",points:15,description:"Atividade física registrada"}],goals:[{action:"weight_goal",points:100,description:"Meta de peso atingida"},{action:"measurement_goal",points:100,description:"Meta de medidas atingida"},{action:"streak_week",points:100,description:"Sequência de 7 dias mantida"}]};let Te=[];function Ps(a){return Te.push(a),()=>{Te=Te.filter(n=>n!==a)}}async function ft(a,n){var h;if(!Nt().isAuthenticated)return 0;const t=(h=Os[a])==null?void 0:h.find(f=>f.action===n);if(!t)return 0;Te.forEach(f=>f(t.points,t.description));try{await Wt(t.points,t.description,"protocol_completion")}catch(f){console.error("Error awarding SnapCoins:",f)}try{if(Ht.isMockModeEnabled())return console.log("Mock mode: Awarding points",{category:a,action:n,points:t.points}),t.points;const f=await ge.post("points/award",{category:a,action:n,points:t.points,timestamp:new Date().toISOString()});return f!=null&&f.data?(Te.forEach(c=>c(f.data.points,t.description)),f.data.points):t.points}catch(f){return console.error("Error awarding points:",f),t.points}}var tt={};(function a(n,o,t,h){var f=!!(n.Worker&&n.Blob&&n.Promise&&n.OffscreenCanvas&&n.OffscreenCanvasRenderingContext2D&&n.HTMLCanvasElement&&n.HTMLCanvasElement.prototype.transferControlToOffscreen&&n.URL&&n.URL.createObjectURL),c=typeof Path2D=="function"&&typeof DOMMatrix=="function",b=function(){if(!n.OffscreenCanvas)return!1;var r=new OffscreenCanvas(1,1),s=r.getContext("2d");s.fillRect(0,0,1,1);var w=r.transferToImageBitmap();try{s.createPattern(w,"no-repeat")}catch{return!1}return!0}();function x(){}function M(r){var s=o.exports.Promise,w=s!==void 0?s:n.Promise;return typeof w=="function"?new w(r):(r(x,x),null)}var N=function(r,s){return{transform:function(w){if(r)return w;if(s.has(w))return s.get(w);var C=new OffscreenCanvas(w.width,w.height),E=C.getContext("2d");return E.drawImage(w,0,0),s.set(w,C),C},clear:function(){s.clear()}}}(b,new Map),D=function(){var r=Math.floor(16.666666666666668),s,w,C={},E=0;return typeof requestAnimationFrame=="function"&&typeof cancelAnimationFrame=="function"?(s=function(P){var B=Math.random();return C[B]=requestAnimationFrame(function I(q){E===q||E+r-1<q?(E=q,delete C[B],P()):C[B]=requestAnimationFrame(I)}),B},w=function(P){C[P]&&cancelAnimationFrame(C[P])}):(s=function(P){return setTimeout(P,r)},w=function(P){return clearTimeout(P)}),{frame:s,cancel:w}}(),p=function(){var r,s,w={};function C(E){function P(B,I){E.postMessage({options:B||{},callback:I})}E.init=function(I){var q=I.transferControlToOffscreen();E.postMessage({canvas:q},[q])},E.fire=function(I,q,Q){if(s)return P(I,null),s;var z=Math.random().toString(36).slice(2);return s=M(function(Y){function V(X){X.data.callback===z&&(delete w[z],E.removeEventListener("message",V),s=null,N.clear(),Q(),Y())}E.addEventListener("message",V),P(I,z),w[z]=V.bind(null,{data:{callback:z}})}),s},E.reset=function(){E.postMessage({reset:!0});for(var I in w)w[I](),delete w[I]}}return function(){if(r)return r;if(!t&&f){var E=["var CONFETTI, SIZE = {}, module = {};","("+a.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join(`
`);try{r=new Worker(URL.createObjectURL(new Blob([E])))}catch(P){return typeof console!==void 0&&typeof console.warn=="function"&&console.warn("🎊 Could not load worker",P),null}C(r)}return r}}(),S={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function l(r,s){return s?s(r):r}function i(r){return r!=null}function m(r,s,w){return l(r&&i(r[s])?r[s]:S[s],w)}function F(r){return r<0?0:Math.floor(r)}function g(r,s){return Math.floor(Math.random()*(s-r))+r}function T(r){return parseInt(r,16)}function Z(r){return r.map(U)}function U(r){var s=String(r).replace(/[^0-9a-f]/gi,"");return s.length<6&&(s=s[0]+s[0]+s[1]+s[1]+s[2]+s[2]),{r:T(s.substring(0,2)),g:T(s.substring(2,4)),b:T(s.substring(4,6))}}function O(r){var s=m(r,"origin",Object);return s.x=m(s,"x",Number),s.y=m(s,"y",Number),s}function J(r){r.width=document.documentElement.clientWidth,r.height=document.documentElement.clientHeight}function u(r){var s=r.getBoundingClientRect();r.width=s.width,r.height=s.height}function W(r){var s=document.createElement("canvas");return s.style.position="fixed",s.style.top="0px",s.style.left="0px",s.style.pointerEvents="none",s.style.zIndex=r,s}function $(r,s,w,C,E,P,B,I,q){r.save(),r.translate(s,w),r.rotate(P),r.scale(C,E),r.arc(0,0,1,B,I,q),r.restore()}function L(r){var s=r.angle*(Math.PI/180),w=r.spread*(Math.PI/180);return{x:r.x,y:r.y,wobble:Math.random()*10,wobbleSpeed:Math.min(.11,Math.random()*.1+.05),velocity:r.startVelocity*.5+Math.random()*r.startVelocity,angle2D:-s+(.5*w-Math.random()*w),tiltAngle:(Math.random()*(.75-.25)+.25)*Math.PI,color:r.color,shape:r.shape,tick:0,totalTicks:r.ticks,decay:r.decay,drift:r.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:r.gravity*3,ovalScalar:.6,scalar:r.scalar,flat:r.flat}}function te(r,s){s.x+=Math.cos(s.angle2D)*s.velocity+s.drift,s.y+=Math.sin(s.angle2D)*s.velocity+s.gravity,s.velocity*=s.decay,s.flat?(s.wobble=0,s.wobbleX=s.x+10*s.scalar,s.wobbleY=s.y+10*s.scalar,s.tiltSin=0,s.tiltCos=0,s.random=1):(s.wobble+=s.wobbleSpeed,s.wobbleX=s.x+10*s.scalar*Math.cos(s.wobble),s.wobbleY=s.y+10*s.scalar*Math.sin(s.wobble),s.tiltAngle+=.1,s.tiltSin=Math.sin(s.tiltAngle),s.tiltCos=Math.cos(s.tiltAngle),s.random=Math.random()+2);var w=s.tick++/s.totalTicks,C=s.x+s.random*s.tiltCos,E=s.y+s.random*s.tiltSin,P=s.wobbleX+s.random*s.tiltCos,B=s.wobbleY+s.random*s.tiltSin;if(r.fillStyle="rgba("+s.color.r+", "+s.color.g+", "+s.color.b+", "+(1-w)+")",r.beginPath(),c&&s.shape.type==="path"&&typeof s.shape.path=="string"&&Array.isArray(s.shape.matrix))r.fill(A(s.shape.path,s.shape.matrix,s.x,s.y,Math.abs(P-C)*.1,Math.abs(B-E)*.1,Math.PI/10*s.wobble));else if(s.shape.type==="bitmap"){var I=Math.PI/10*s.wobble,q=Math.abs(P-C)*.1,Q=Math.abs(B-E)*.1,z=s.shape.bitmap.width*s.scalar,Y=s.shape.bitmap.height*s.scalar,V=new DOMMatrix([Math.cos(I)*q,Math.sin(I)*q,-Math.sin(I)*Q,Math.cos(I)*Q,s.x,s.y]);V.multiplySelf(new DOMMatrix(s.shape.matrix));var X=r.createPattern(N.transform(s.shape.bitmap),"no-repeat");X.setTransform(V),r.globalAlpha=1-w,r.fillStyle=X,r.fillRect(s.x-z/2,s.y-Y/2,z,Y),r.globalAlpha=1}else if(s.shape==="circle")r.ellipse?r.ellipse(s.x,s.y,Math.abs(P-C)*s.ovalScalar,Math.abs(B-E)*s.ovalScalar,Math.PI/10*s.wobble,0,2*Math.PI):$(r,s.x,s.y,Math.abs(P-C)*s.ovalScalar,Math.abs(B-E)*s.ovalScalar,Math.PI/10*s.wobble,0,2*Math.PI);else if(s.shape==="star")for(var K=Math.PI/2*3,ae=4*s.scalar,se=8*s.scalar,ne=s.x,ce=s.y,le=5,ie=Math.PI/le;le--;)ne=s.x+Math.cos(K)*se,ce=s.y+Math.sin(K)*se,r.lineTo(ne,ce),K+=ie,ne=s.x+Math.cos(K)*ae,ce=s.y+Math.sin(K)*ae,r.lineTo(ne,ce),K+=ie;else r.moveTo(Math.floor(s.x),Math.floor(s.y)),r.lineTo(Math.floor(s.wobbleX),Math.floor(E)),r.lineTo(Math.floor(P),Math.floor(B)),r.lineTo(Math.floor(C),Math.floor(s.wobbleY));return r.closePath(),r.fill(),s.tick<s.totalTicks}function R(r,s,w,C,E){var P=s.slice(),B=r.getContext("2d"),I,q,Q=M(function(z){function Y(){I=q=null,B.clearRect(0,0,C.width,C.height),N.clear(),E(),z()}function V(){t&&!(C.width===h.width&&C.height===h.height)&&(C.width=r.width=h.width,C.height=r.height=h.height),!C.width&&!C.height&&(w(r),C.width=r.width,C.height=r.height),B.clearRect(0,0,C.width,C.height),P=P.filter(function(X){return te(B,X)}),P.length?I=D.frame(V):Y()}I=D.frame(V),q=Y});return{addFettis:function(z){return P=P.concat(z),Q},canvas:r,promise:Q,reset:function(){I&&D.cancel(I),q&&q()}}}function y(r,s){var w=!r,C=!!m(s||{},"resize"),E=!1,P=m(s,"disableForReducedMotion",Boolean),B=f&&!!m(s||{},"useWorker"),I=B?p():null,q=w?J:u,Q=r&&I?!!r.__confetti_initialized:!1,z=typeof matchMedia=="function"&&matchMedia("(prefers-reduced-motion)").matches,Y;function V(K,ae,se){for(var ne=m(K,"particleCount",F),ce=m(K,"angle",Number),le=m(K,"spread",Number),ie=m(K,"startVelocity",Number),Me=m(K,"decay",Number),Se=m(K,"gravity",Number),ee=m(K,"drift",Number),pe=m(K,"colors",Z),ye=m(K,"ticks",Number),be=m(K,"shapes"),je=m(K,"scalar"),we=!!m(K,"flat"),oe=O(K),De=ne,Ce=[],Ae=r.width*oe.x,Ee=r.height*oe.y;De--;)Ce.push(L({x:Ae,y:Ee,angle:ce,spread:le,startVelocity:ie,color:pe[De%pe.length],shape:be[g(0,be.length)],ticks:ye,decay:Me,gravity:Se,drift:ee,scalar:je,flat:we}));return Y?Y.addFettis(Ce):(Y=R(r,Ce,q,ae,se),Y.promise)}function X(K){var ae=P||m(K,"disableForReducedMotion",Boolean),se=m(K,"zIndex",Number);if(ae&&z)return M(function(ie){ie()});w&&Y?r=Y.canvas:w&&!r&&(r=W(se),document.body.appendChild(r)),C&&!Q&&q(r);var ne={width:r.width,height:r.height};I&&!Q&&I.init(r),Q=!0,I&&(r.__confetti_initialized=!0);function ce(){if(I){var ie={getBoundingClientRect:function(){if(!w)return r.getBoundingClientRect()}};q(ie),I.postMessage({resize:{width:ie.width,height:ie.height}});return}ne.width=ne.height=null}function le(){Y=null,C&&(E=!1,n.removeEventListener("resize",ce)),w&&r&&(document.body.contains(r)&&document.body.removeChild(r),r=null,Q=!1)}return C&&!E&&(E=!0,n.addEventListener("resize",ce,!1)),I?I.fire(K,ne,le):V(K,ne,le)}return X.reset=function(){I&&I.reset(),Y&&Y.reset()},X}var j;function v(){return j||(j=y(null,{useWorker:!0,resize:!0})),j}function A(r,s,w,C,E,P,B){var I=new Path2D(r),q=new Path2D;q.addPath(I,new DOMMatrix(s));var Q=new Path2D;return Q.addPath(q,new DOMMatrix([Math.cos(B)*E,Math.sin(B)*E,-Math.sin(B)*P,Math.cos(B)*P,w,C])),Q}function d(r){if(!c)throw new Error("path confetti are not supported in this browser");var s,w;typeof r=="string"?s=r:(s=r.path,w=r.matrix);var C=new Path2D(s),E=document.createElement("canvas"),P=E.getContext("2d");if(!w){for(var B=1e3,I=B,q=B,Q=0,z=0,Y,V,X=0;X<B;X+=2)for(var K=0;K<B;K+=2)P.isPointInPath(C,X,K,"nonzero")&&(I=Math.min(I,X),q=Math.min(q,K),Q=Math.max(Q,X),z=Math.max(z,K));Y=Q-I,V=z-q;var ae=10,se=Math.min(ae/Y,ae/V);w=[se,0,0,se,-Math.round(Y/2+I)*se,-Math.round(V/2+q)*se]}return{type:"path",path:s,matrix:w}}function _(r){var s,w=1,C="#000000",E='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';typeof r=="string"?s=r:(s=r.text,w="scalar"in r?r.scalar:w,E="fontFamily"in r?r.fontFamily:E,C="color"in r?r.color:C);var P=10*w,B=""+P+"px "+E,I=new OffscreenCanvas(P,P),q=I.getContext("2d");q.font=B;var Q=q.measureText(s),z=Math.ceil(Q.actualBoundingBoxRight+Q.actualBoundingBoxLeft),Y=Math.ceil(Q.actualBoundingBoxAscent+Q.actualBoundingBoxDescent),V=2,X=Q.actualBoundingBoxLeft+V,K=Q.actualBoundingBoxAscent+V;z+=V+V,Y+=V+V,I=new OffscreenCanvas(z,Y),q=I.getContext("2d"),q.font=B,q.fillStyle=C,q.fillText(s,X,K);var ae=1/w;return{type:"bitmap",bitmap:I.transferToImageBitmap(),matrix:[ae,0,0,ae,-z*ae/2,-Y*ae/2]}}o.exports=function(){return v().apply(this,arguments)},o.exports.reset=function(){v().reset()},o.exports.create=y,o.exports.shapeFromPath=d,o.exports.shapeFromText=_})(function(){return typeof window<"u"?window:typeof self<"u"?self:this||{}}(),tt,!1);const xt=tt.exports;tt.exports.create;function Ls({points:a,message:n,onClose:o}){return H.useEffect(()=>{const h=Date.now()+2e3,f=setInterval(()=>{if(h-Date.now()<=0){clearInterval(f);return}xt({particleCount:3,angle:60,spread:55,origin:{x:0},colors:["#B9FF43","#66B100","#1A3201"]}),xt({particleCount:3,angle:120,spread:55,origin:{x:1},colors:["#B9FF43","#66B100","#1A3201"]})},150),c=setTimeout(o,3e3);return()=>{clearInterval(f),clearTimeout(c)}},[]),e.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-[9999] pointer-events-none",children:e.jsx("div",{className:"bg-snapfit-gray/90 backdrop-blur-sm rounded-2xl shadow-xl p-6 animate-bounce border border-snapfit-green/30",children:e.jsxs("div",{className:"flex flex-col items-center text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-snapfit-green to-snapfit-green/50 rounded-full flex items-center justify-center mb-4 shadow-lg shadow-snapfit-green/30",children:e.jsx(zt,{className:"w-8 h-8 text-black"})}),e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green mb-1",children:["+",a," snapcoins"]}),e.jsx("div",{className:"text-sm text-white",children:n})]})})})}function Rs({suggestions:a,mealId:n,onSelect:o}){return e.jsxs("div",{className:"mt-4 pt-4 border-t border-snapfit-green/20",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Sugestões de Substituição"}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:a.map(t=>e.jsxs("div",{onClick:()=>o(t),className:"flex items-center gap-3 p-3 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors cursor-pointer border border-snapfit-green/10",children:[t.image&&e.jsx("img",{src:t.image,alt:t.name,className:"w-12 h-12 rounded-md object-cover"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-white",children:t.name}),e.jsxs("div",{className:"text-xs text-gray-400",children:[t.calories," kcal | ",t.protein,"g proteína"]})]}),e.jsx(Mt,{className:"w-4 h-4 text-snapfit-green"})]},t.id))})]})}function Bs({meals:a,onComplete:n,onReplaceMeal:o,onEdit:t,onCancel:h,onRefreshSuggestions:f}){const{rewardMealCompleted:c}=Ut(),[b,x]=H.useState(null),[M,N]=H.useState(null),[D,p]=H.useState(!1),[S,l]=H.useState(!1),[i,m]=H.useState(null);H.useEffect(()=>Ps((J,u)=>{m({points:J,message:u})}),[]);const F=O=>{x(O),N(null)},g=async O=>{p(!1),console.log("Photo captured, size:",O.size)},T=O=>{N(O),O==="photo"&&p(!0)},Z=()=>{b&&(t(b),x(null),N(null))},U=O=>{const J=a.find(W=>W.id===O);if(!J)return;J.foods.reduce((W,$)=>({calories:W.calories+$.calories*($.quantity||1),protein:W.protein+$.protein*($.quantity||1),carbs:W.carbs+$.carbs*($.quantity||1),fat:W.fat+$.fat*($.quantity||1)}),{calories:0,protein:0,carbs:0,fat:0}),n(O),l(!0),setTimeout(()=>l(!1),3e3),c(J.name),ft("diet","meal_completed").catch(console.error),a.every(W=>W.completed||W.id===O)&&ft("diet","daily_diet_completed").catch(console.error)};return S?e.jsx("div",{className:"fixed inset-0 flex items-center justify-center bg-black/70 backdrop-blur-sm z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-6 text-center max-w-sm mx-4 border border-snapfit-green/30 shadow-lg",children:[e.jsx("div",{className:"w-16 h-16 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-snapfit-green/30",children:e.jsx(Qt,{className:"w-8 h-8 text-snapfit-green"})}),e.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:"Refeição Registrada!"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Sua refeição foi registrada com sucesso."})]})}):e.jsxs("div",{className:"space-y-4",children:[a.map(O=>e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-3 sm:p-4 border border-snapfit-green/20 ${O.completed?"border-l-4 border-snapfit-green":""}`,children:[b===O.id?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-sm sm:text-base font-bold text-white",children:O.name}),e.jsx("button",{onClick:()=>x(null),className:"text-gray-400 hover:text-snapfit-green",children:e.jsx(xe,{className:"w-4 h-4 sm:w-5 sm:h-5"})})]}),M?e.jsxs("div",{className:"space-y-4",children:[M==="text"&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("input",{type:"text",placeholder:"Digite o que você comeu...",className:"w-full p-2 sm:p-3 text-sm border border-snapfit-green/30 rounded-lg bg-snapfit-dark-gray text-white focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green"}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:Z,className:"px-3 py-1.5 sm:px-4 sm:py-2 text-sm bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors font-bold shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:"Salvar"})})]}),M==="photo"&&e.jsx("div",{className:"space-y-4",children:D&&e.jsx(Kt,{onCapture:g,onClose:()=>p(!1)})}),M==="audio"&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"p-6 bg-snapfit-dark-gray rounded-lg flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ut,{className:"w-8 h-8 text-snapfit-green"})}),e.jsx("button",{onClick:()=>{},className:"w-full py-2 bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors font-bold shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:"Gravar Áudio"})]})]}):e.jsxs("div",{className:"grid grid-cols-3 gap-2 sm:gap-3",children:[e.jsxs("button",{onClick:()=>T("text"),className:"flex flex-col items-center gap-1 sm:gap-2 p-2 sm:p-4 rounded-lg border-2 border-dashed border-snapfit-green/30 bg-snapfit-dark-gray hover:border-snapfit-green hover:bg-snapfit-green/10 transition-colors",children:[e.jsx(Es,{className:"w-4 h-4 sm:w-6 sm:h-6 text-snapfit-green"}),e.jsx("span",{className:"text-xs sm:text-sm font-medium text-white",children:"Texto"})]}),e.jsxs("button",{onClick:()=>T("photo"),className:"flex flex-col items-center gap-1 sm:gap-2 p-2 sm:p-4 rounded-lg border-2 border-dashed border-snapfit-green/30 bg-snapfit-dark-gray hover:border-snapfit-green hover:bg-snapfit-green/10 transition-colors",children:[e.jsx(Yt,{className:"w-4 h-4 sm:w-6 sm:h-6 text-snapfit-green"}),e.jsx("span",{className:"text-xs sm:text-sm font-medium text-white",children:"Foto"})]}),e.jsxs("button",{onClick:()=>T("audio"),className:"flex flex-col items-center gap-1 sm:gap-2 p-2 sm:p-4 rounded-lg border-2 border-dashed border-snapfit-green/30 bg-snapfit-dark-gray hover:border-snapfit-green hover:bg-snapfit-green/10 transition-colors",children:[e.jsx(ut,{className:"w-4 h-4 sm:w-6 sm:h-6 text-snapfit-green"}),e.jsx("span",{className:"text-xs sm:text-sm font-medium text-white",children:"Áudio"})]})]})]}):e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center gap-2 sm:gap-3",children:e.jsx("h3",{className:"text-base sm:text-lg font-bold text-white",children:O.name})}),e.jsx("div",{className:"mt-1 text-[10px] sm:text-sm text-gray-400",children:O.foods.map((J,u)=>e.jsxs("span",{className:"mr-2",children:[J.name," (",J.quantity,J.unit,")",u<O.foods.length-1&&e.jsx("span",{style:{margin:"0 8px"},children:"•"})]},J.id))})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 ml-2 sm:ml-4",children:[e.jsx("span",{className:"text-xs sm:text-sm text-snapfit-green",children:O.time}),!O.suggestions&&e.jsx("button",{onClick:()=>f(O.id),className:"p-1.5 sm:p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-lg transition-colors",title:"Gerar sugestões de substituição",children:e.jsx(Oe,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>F(O.id),className:"p-1.5 sm:p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-lg transition-colors",children:e.jsx(Ve,{className:"w-4 h-4 sm:w-5 sm:h-5"})}),e.jsx("button",{onClick:()=>U(O.id),className:`p-1.5 sm:p-2 rounded-lg transition-colors ${O.completed?"text-snapfit-green bg-snapfit-green/20":"text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10"}`,children:e.jsx(St,{className:"w-4 h-4 sm:w-5 sm:h-5"})}),e.jsx("button",{onClick:()=>h(O.id),className:"p-1.5 sm:p-2 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-colors",children:e.jsx(xe,{className:"w-4 h-4 sm:w-5 sm:h-5"})})]})]}),O.suggestions&&O.suggestions.length>0&&!b&&e.jsx(Rs,{suggestions:O.suggestions,mealId:O.id,onSelect:J=>o(O.id,J)})]},O.id)),i&&e.jsx(Ls,{points:i.points,message:i.message,onClose:()=>m(null)})]})}const Fs=[{id:"walking",name:"Caminhada",met:3.5,description:"Caminhada em ritmo moderado",category:"cardio"},{id:"running",name:"Corrida",met:8,description:"Corrida em ritmo moderado",category:"cardio"},{id:"cycling",name:"Ciclismo",met:7,description:"Ciclismo em ritmo moderado",category:"cardio"},{id:"swimming",name:"Natação",met:6,description:"Natação em ritmo moderado",category:"cardio"},{id:"basketball",name:"Basquete",met:6.5,description:"Jogo recreativo",category:"sports"},{id:"soccer",name:"Futebol",met:7,description:"Jogo recreativo",category:"sports"},{id:"tennis",name:"Tênis",met:7,description:"Jogo recreativo",category:"sports"},{id:"volleyball",name:"Vôlei",met:4,description:"Jogo recreativo",category:"sports"},{id:"weight_training",name:"Musculação",met:3.5,description:"Treino com pesos",category:"strength"},{id:"yoga",name:"Yoga",met:2.5,description:"Prática moderada",category:"lifestyle"},{id:"pilates",name:"Pilates",met:3,description:"Prática moderada",category:"lifestyle"},{id:"dancing",name:"Dança",met:4.5,description:"Dança aeróbica",category:"lifestyle"}];function _s({activities:a,onAddActivity:n,onRemoveActivity:o}){const[t,h]=H.useState(!1),[f,c]=H.useState(!1),[b,x]=H.useState({name:"",duration:"",activityType:""}),[M,N]=H.useState([]),[D,p]=H.useState(!1);H.useEffect(()=>{(async()=>{try{const i=await ge.get("users/daily/list/workouts_activities");N(i.data)}catch(i){console.error("Error fetching workouts activities:",i)}})()},[]);const S=async l=>{l.preventDefault();try{p(!0);const i={id:b.activityType,duration:Number(b.duration)},m=await ge.post("users/daily/workouts_activities",i);ke.success("Atividade registrada com sucesso!",{position:"bottom-right"}),n({name:m.data.name,duration:Number(b.duration),caloriesBurned:m.data.calories})}catch(i){console.error("Error adding activity:",i),ke.error("Ocorreu um erro ao adicionar a atividade.",{position:"bottom-right"})}finally{h(!1),p(!1)}};return e.jsxs(e.Fragment,{children:[D&&e.jsx(Pe,{}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-xs font-medium text-white",children:"Outras Atividades"}),e.jsxs("button",{onClick:()=>h(!0),className:"flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-black bg-snapfit-green rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:[e.jsx(Ct,{className:"w-3.5 h-3.5"}),"Adicionar"]})]}),t&&e.jsxs("form",{onSubmit:S,className:"bg-snapfit-dark-gray rounded-lg p-4 space-y-3 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-xs font-medium text-white",children:"Registrar Atividade"}),e.jsx("button",{type:"button",onClick:()=>h(!1),className:"text-gray-400 hover:text-snapfit-green",children:e.jsx(xe,{className:"w-4 h-4"})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-400 mb-1",children:"Tipo de Atividade"}),e.jsxs("select",{value:b.activityType,onChange:l=>x({...b,activityType:l.target.value}),className:"w-full p-2 border border-snapfit-green/30 rounded-lg text-xs bg-snapfit-dark-gray text-white focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green",required:!0,children:[e.jsx("option",{value:"",children:"Selecione..."}),M==null?void 0:M.map(l=>e.jsx("option",{value:l.id,children:l.name},l.id))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-400 mb-1",children:"Duração (min)"}),e.jsx("input",{type:"number",value:b.duration||"",onChange:l=>x({...b,duration:Number(l.target.value)}),className:"w-full p-2 border border-snapfit-green/30 rounded-lg text-xs bg-snapfit-dark-gray text-white focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green",min:"1",required:!0})]})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{type:"button",onClick:()=>h(!1),className:"px-3 py-1.5 text-xs font-medium text-gray-400 hover:bg-snapfit-dark-gray/80 rounded-full transition-colors border border-snapfit-green/20",children:"Cancelar"}),e.jsx("button",{type:"submit",disabled:f,className:"px-3 py-1.5 text-xs bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95 font-medium",children:f?e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(kt,{className:"w-3 h-3 animate-spin"}),e.jsx("span",{children:"Calculando..."})]}):"Adicionar"})]})]}),e.jsx("div",{className:"space-y-2",children:a.map(l=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10 text-xs",title:new Date(l.date).toLocaleString(),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/10 rounded-full flex items-center justify-center border border-snapfit-green/20",children:e.jsx(Vt,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:l.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[l.duration," min • ",l.caloriesBurned," kcal"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>{var i;x({name:l.name,duration:l.duration.toString(),activityType:((i=Fs.find(m=>m.name===l.name))==null?void 0:i.id)||""}),h(!0)},className:"p-1.5 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-lg transition-colors",children:e.jsx(Ve,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>o(l.id),className:"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-colors",children:e.jsx(xe,{className:"w-4 h-4"})})]})]},l.id))})]})]})}function qs({stats:a,dailyData:n}){var S,l,i,m,F,g,T,Z,U,O,J,u,W,$,L,te,R,y,j,v,A,d,_,r,s,w,C,E,P,B,I,q,Q,z,Y,V,X,K,ae,se,ne,ce,le,ie,Me,Se,ee,pe,ye,be,je,we;const o=Ze(),[t,h]=H.useState(null),{connectedDevices:f,getStats:c}=Zt(),[b,x]=G.useState(null),[M,N]=G.useState(!1);G.useEffect(()=>{h(n)},[n]),G.useEffect(()=>{f.length>0&&D()},[f]);const D=async()=>{if(f.length!==0)try{N(!0);const oe=await c(f[0].id,"day");x(oe)}catch(oe){console.error("Erro ao carregar estatísticas do wearable:",oe)}finally{N(!1)}},p=oe=>oe>=1e6?(oe/1e6).toFixed(1)+"M":oe>=1e3?(oe/1e3).toFixed(1)+"K":oe.toString();return e.jsxs("div",{className:`\r
    bg-snapfit-gray backdrop-blur-sm rounded-xl sm:rounded-3xl shadow-lg p-3 sm:p-6 border border-snapfit-green/20`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(gt,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("h2",{className:"text-lg font-bold text-white",children:["Atividade Diária ",e.jsx(vs,{})]})]}),((S=t==null?void 0:t.activity)==null?void 0:S.device_source)&&e.jsxs("div",{className:"text-sm text-gray-400 bg-snapfit-dark-gray px-3 py-1 rounded-full border border-snapfit-green/20",children:["via ",(l=t==null?void 0:t.activity)==null?void 0:l.device_source]})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg sm:rounded-xl p-3 sm:p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-400/10 rounded-full flex items-center justify-center",children:e.jsx(Gt,{className:"w-3.5 h-3.5 text-blue-400"})}),e.jsxs("span",{className:"text-[10px] sm:text-xs text-gray-400",children:["Meta: ",(m=(i=t==null?void 0:t.activity)==null?void 0:i.steps)!=null&&m.goal?(g=(F=t==null?void 0:t.activity)==null?void 0:F.steps)==null?void 0:g.goal:"--"]})]}),e.jsx("div",{className:"text-lg sm:text-2xl font-bold text-white",children:(Z=(T=t==null?void 0:t.activity)==null?void 0:T.steps)!=null&&Z.steps?a.steps.current.toLocaleString():"0"}),e.jsx("div",{className:"text-xs sm:text-sm text-gray-400",children:"Passos"}),e.jsx("div",{className:"mt-2 w-full bg-snapfit-gray rounded-full h-1.5",children:e.jsx("div",{className:"bg-blue-400 h-1.5 rounded-full transition-all duration-300 filter drop-shadow(0 0 2px rgba(59, 130, 246, 0.5))",style:{width:`${(O=(U=t==null?void 0:t.activity)==null?void 0:U.steps)!=null&&O.steps?((u=(J=t==null?void 0:t.activity)==null?void 0:J.steps)==null?void 0:u.steps)/(($=(W=t==null?void 0:t.activity)==null?void 0:W.steps)==null?void 0:$.goal)*100:0}%`}})})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg sm:rounded-xl p-3 sm:p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-6 h-6 bg-snapfit-green/10 rounded-full flex items-center justify-center",children:e.jsx(_e,{className:"w-3.5 h-3.5 text-snapfit-green"})}),e.jsxs("span",{className:"text-[10px] sm:text-xs text-gray-400",children:["Meta: ",(te=(L=t==null?void 0:t.activity)==null?void 0:L.active)!=null&&te.goal?(y=(R=t==null?void 0:t.activity)==null?void 0:R.active)==null?void 0:y.minutes:"--","min"]})]}),e.jsx("div",{className:"text-lg sm:text-2xl font-bold text-white",children:(v=(j=t==null?void 0:t.activity)==null?void 0:j.active)!=null&&v.minutes?(d=(A=t==null?void 0:t.activity)==null?void 0:A.active)==null?void 0:d.minutes:"0"}),e.jsx("div",{className:"text-xs sm:text-sm text-gray-400",children:"Min. Ativos"}),e.jsx("div",{className:"mt-2 w-full bg-snapfit-gray rounded-full h-1.5",children:e.jsx("div",{className:"bg-snapfit-green h-1.5 rounded-full transition-all duration-300 filter drop-shadow(0 0 2px rgba(185, 255, 67, 0.5))",style:{width:`${(r=(_=t==null?void 0:t.activity)==null?void 0:_.active)!=null&&r.minutes?((w=(s=t==null?void 0:t.activity)==null?void 0:s.active)==null?void 0:w.minutes)/((E=(C=t==null?void 0:t.activity)==null?void 0:C.active)==null?void 0:E.goal)*100:0}%`}})})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg sm:rounded-xl p-3 sm:p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-6 h-6 bg-red-400/10 rounded-full flex items-center justify-center",children:e.jsx(gt,{className:"w-3.5 h-3.5 text-red-400"})}),e.jsxs("span",{className:"text-[10px] sm:text-xs text-gray-400",children:[(B=(P=t==null?void 0:t.activity)==null?void 0:P.heart)!=null&&B.default_min?(q=(I=t==null?void 0:t.activity)==null?void 0:I.heart)==null?void 0:q.default_min:"--","-",(z=(Q=t==null?void 0:t.activity)==null?void 0:Q.heart)!=null&&z.default_max?(V=(Y=t==null?void 0:t.activity)==null?void 0:Y.heart)==null?void 0:V.default_max:"--"," bpm"]})]}),e.jsx("div",{className:"text-lg sm:text-2xl font-bold text-white",children:(K=(X=t==null?void 0:t.activity)==null?void 0:X.heart)!=null&&K.heart_rate?(se=(ae=t==null?void 0:t.activity)==null?void 0:ae.heart)==null?void 0:se.heart_rate:"--"}),e.jsx("div",{className:"text-xs sm:text-sm text-gray-400",children:"Freq. Cardíaca"}),e.jsx("div",{className:"mt-2 w-full bg-snapfit-gray rounded-full h-1.5",children:e.jsx("div",{className:"bg-red-400 h-1.5 rounded-full transition-all duration-300 filter drop-shadow(0 0 2px rgba(255, 71, 87, 0.5))",style:{width:"0%"}})})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg sm:rounded-xl p-3 sm:p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-6 h-6 bg-purple-400/10 rounded-full flex items-center justify-center",children:e.jsx(_e,{className:"w-3.5 h-3.5 text-purple-400"})}),e.jsx("span",{className:"text-[10px] sm:text-xs text-gray-400",children:"Meta: 8h"})]}),e.jsx("div",{className:"text-lg sm:text-2xl font-bold text-white",children:(ce=(ne=t==null?void 0:t.activity)==null?void 0:ne.sleep)!=null&&ce.hours?`${(ie=(le=t==null?void 0:t.activity)==null?void 0:le.sleep)==null?void 0:ie.hours}h ${((Se=(Me=t==null?void 0:t.activity)==null?void 0:Me.sleep)==null?void 0:Se.minutes)||"0"}m`:"7h 30m"}),e.jsx("div",{className:"text-xs sm:text-sm text-gray-400",children:"Sono"}),e.jsx("div",{className:"mt-2 w-full bg-snapfit-gray rounded-full h-1.5",children:e.jsx("div",{className:"bg-purple-400 h-1.5 rounded-full transition-all duration-300 filter drop-shadow(0 0 2px rgba(168, 85, 247, 0.5))",style:{width:`${(pe=(ee=t==null?void 0:t.activity)==null?void 0:ee.sleep)!=null&&pe.hours?(((be=(ye=t==null?void 0:t.activity)==null?void 0:ye.sleep)==null?void 0:be.hours)*60+(((we=(je=t==null?void 0:t.activity)==null?void 0:je.sleep)==null?void 0:we.minutes)||0))/480*100:93}%`}})})]})]}),f.length>0&&e.jsxs("div",{className:"mt-4 pt-4 border-t border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Jt,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Wearable Conectado"}),e.jsx("div",{className:"flex items-center gap-1",children:f[0].connectionStatus==="connected"?e.jsx(Xt,{className:"w-3 h-3 text-green-400"}):e.jsx(es,{className:"w-3 h-3 text-red-400"})})]}),e.jsx("button",{onClick:()=>o("/wearables"),className:"text-xs text-snapfit-green hover:text-snapfit-green/80 transition-colors",children:"Gerenciar"})]}),M?e.jsxs("div",{className:"text-center py-3",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-snapfit-green mx-auto"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Carregando dados..."})]}):b?e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-2 border border-snapfit-green/5",children:[e.jsx("div",{className:"text-sm font-bold text-white",children:p(b.totalSteps)}),e.jsx("div",{className:"text-xs text-gray-400",children:"Passos"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-2 border border-snapfit-green/5",children:[e.jsx("div",{className:"text-sm font-bold text-white",children:p(b.totalCalories)}),e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"})]})]}):e.jsx("div",{className:"text-center py-3",children:e.jsx("p",{className:"text-xs text-gray-400",children:"Nenhum dado disponível hoje"})})]})]})}function Ws({onAdd:a,remainingMacros:n}){const o=Ze();return e.jsxs("button",{onClick:()=>o("/diary/add-meal"),className:`w-full flex items-center justify-center gap-3 p-4 sm:p-5\r
                border-2 border-dashed border-snapfit-green/30 rounded-xl\r
                hover:border-snapfit-green hover:bg-snapfit-green/5\r
                transition-all duration-300 animate-fade-in\r
                group backdrop-blur-sm relative overflow-hidden`,children:[e.jsx("div",{className:"absolute inset-0 bg-snapfit-green/5 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-700 skew-x-12"}),e.jsx("div",{className:"p-3 bg-snapfit-green/10 rounded-xl group-hover:bg-snapfit-green/20 transition-all duration-300 relative z-10 flex items-center justify-center",children:e.jsx(Ct,{className:"w-5 h-5 sm:w-6 sm:h-6 text-snapfit-green"})}),e.jsx("span",{className:"text-sm sm:text-base font-bold text-gray-700 dark:text-gray-200 group-hover:text-snapfit-green relative z-10",children:"Adicionar Refeição"}),n&&e.jsxs("div",{className:"absolute bottom-1 right-2 text-xs text-gray-500 dark:text-gray-400",children:["Restante: ",n.calories," kcal"]})]})}const ze=(a,n)=>n.some(o=>a instanceof o);let pt,bt;function Hs(){return pt||(pt=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function zs(){return bt||(bt=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}const Ue=new WeakMap,Be=new WeakMap,Le=new WeakMap;function Us(a){const n=new Promise((o,t)=>{const h=()=>{a.removeEventListener("success",f),a.removeEventListener("error",c)},f=()=>{o(Ne(a.result)),h()},c=()=>{t(a.error),h()};a.addEventListener("success",f),a.addEventListener("error",c)});return Le.set(n,a),n}function Qs(a){if(Ue.has(a))return;const n=new Promise((o,t)=>{const h=()=>{a.removeEventListener("complete",f),a.removeEventListener("error",c),a.removeEventListener("abort",c)},f=()=>{o(),h()},c=()=>{t(a.error||new DOMException("AbortError","AbortError")),h()};a.addEventListener("complete",f),a.addEventListener("error",c),a.addEventListener("abort",c)});Ue.set(a,n)}let Qe={get(a,n,o){if(a instanceof IDBTransaction){if(n==="done")return Ue.get(a);if(n==="store")return o.objectStoreNames[1]?void 0:o.objectStore(o.objectStoreNames[0])}return Ne(a[n])},set(a,n,o){return a[n]=o,!0},has(a,n){return a instanceof IDBTransaction&&(n==="done"||n==="store")?!0:n in a}};function At(a){Qe=a(Qe)}function Ys(a){return zs().includes(a)?function(...n){return a.apply(Ye(this),n),Ne(this.request)}:function(...n){return Ne(a.apply(Ye(this),n))}}function Ks(a){return typeof a=="function"?Ys(a):(a instanceof IDBTransaction&&Qs(a),ze(a,Hs())?new Proxy(a,Qe):a)}function Ne(a){if(a instanceof IDBRequest)return Us(a);if(Be.has(a))return Be.get(a);const n=Ks(a);return n!==a&&(Be.set(a,n),Le.set(n,a)),n}const Ye=a=>Le.get(a);function Vs(a,n,{blocked:o,upgrade:t,blocking:h,terminated:f}={}){const c=indexedDB.open(a,n),b=Ne(c);return t&&c.addEventListener("upgradeneeded",x=>{t(Ne(c.result),x.oldVersion,x.newVersion,Ne(c.transaction),x)}),o&&c.addEventListener("blocked",x=>o(x.oldVersion,x.newVersion,x)),b.then(x=>{f&&x.addEventListener("close",()=>f()),h&&x.addEventListener("versionchange",M=>h(M.oldVersion,M.newVersion,M))}).catch(()=>{}),b}const Zs=["get","getKey","getAll","getAllKeys","count"],Gs=["put","add","delete","clear"],Fe=new Map;function vt(a,n){if(!(a instanceof IDBDatabase&&!(n in a)&&typeof n=="string"))return;if(Fe.get(n))return Fe.get(n);const o=n.replace(/FromIndex$/,""),t=n!==o,h=Gs.includes(o);if(!(o in(t?IDBIndex:IDBObjectStore).prototype)||!(h||Zs.includes(o)))return;const f=async function(c,...b){const x=this.transaction(c,h?"readwrite":"readonly");let M=x.store;return t&&(M=M.index(b.shift())),(await Promise.all([M[o](...b),h&&x.done]))[0]};return Fe.set(n,f),f}At(a=>({...a,get:(n,o,t)=>vt(n,o)||a.get(n,o,t),has:(n,o)=>!!vt(n,o)||a.has(n,o)}));const Js=["continue","continuePrimaryKey","advance"],yt={},Ke=new WeakMap,Et=new WeakMap,Xs={get(a,n){if(!Js.includes(n))return a[n];let o=yt[n];return o||(o=yt[n]=function(...t){Ke.set(this,Et.get(this)[n](...t))}),o}};async function*ea(...a){let n=this;if(n instanceof IDBCursor||(n=await n.openCursor(...a)),!n)return;n=n;const o=new Proxy(n,Xs);for(Et.set(o,n),Le.set(o,Ye(n));n;)yield o,n=await(Ke.get(o)||n.continue()),Ke.delete(o)}function jt(a,n){return n===Symbol.asyncIterator&&ze(a,[IDBIndex,IDBObjectStore,IDBCursor])||n==="iterate"&&ze(a,[IDBIndex,IDBObjectStore])}At(a=>({...a,get(n,o,t){return jt(n,o)?ea:a.get(n,o,t)},has(n,o){return jt(n,o)||a.has(n,o)}}));let Ie;async function ta(){return Ie||(Ie=await Vs("fitness-app",1,{upgrade(a){a.objectStoreNames.contains("mealSuggestions")||a.createObjectStore("mealSuggestions",{keyPath:"mealId"})}})),Ie}async function Ot(){return Ie||await ta(),Ie}async function sa(a){try{const o=await(await Ot()).get("mealSuggestions",a);return(o==null?void 0:o.suggestions)||null}catch(n){return console.error("Error getting meal suggestions:",n),null}}async function aa(a,n){try{await(await Ot()).put("mealSuggestions",{mealId:a,suggestions:n,timestamp:Date.now()})}catch(o){throw console.error("Error saving meal suggestions:",o),o}}async function ra(a,n=.7){throw Nt().isAuthenticated?new Error("OpenAI API token not configured"):new Error("User must be authenticated to use AI features")}async function na(a){const n=a.foods.reduce((c,b)=>c+b.calories,0),o=a.foods.reduce((c,b)=>c+b.protein,0),t=a.foods.reduce((c,b)=>c+b.carbs,0),h=a.foods.reduce((c,b)=>c+b.fat,0),f=`Como nutricionista, sugira 3 alternativas para a seguinte refeição:

Refeição atual: ${a.name}
Horário: ${a.time}

Alimentos atuais:
${a.foods.map(c=>`- ${c.name} (${c.quantity}${c.unit})`).join(`
`)}

Total de macros a serem mantidos:
- Calorias: ${n}kcal
- Proteína: ${o}g
- Carboidratos: ${t}g
- Gorduras: ${h}g

Forneça alternativas práticas e saudáveis que mantenham os macronutrientes similares.
Retorne apenas os nomes dos alimentos e suas quantidades.`;return ra(f,.7)}const fe=()=>Math.random().toString(36).substr(2,9),Ea={breakfast:[{id:fe(),name:"Omelete com Queijo Cottage",quantity:1,unit:"unit",calories:300,protein:25,carbs:5,fat:20,image:"https://images.unsplash.com/photo-1510693206972-df098062cb71?w=120&h=120&fit=crop"},{id:fe(),name:"Iogurte Grego com Granola",quantity:200,unit:"g",calories:280,protein:20,carbs:30,fat:10,image:"https://images.unsplash.com/photo-1557749823-c1c981c88e85?w=120&h=120&fit=crop"},{id:fe(),name:"Panqueca de Aveia com Whey",quantity:150,unit:"g",calories:320,protein:28,carbs:35,fat:8,image:"https://images.unsplash.com/photo-1528207776546-365bb710ee93?w=120&h=120&fit=crop"}],lunch:[{id:fe(),name:"Salmão Grelhado com Quinoa",quantity:200,unit:"g",calories:450,protein:35,carbs:40,fat:15,image:"https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=120&h=120&fit=crop"},{id:fe(),name:"Peito de Frango com Batata Doce",quantity:250,unit:"g",calories:420,protein:40,carbs:45,fat:8,image:"https://images.unsplash.com/photo-1598515214211-89d3c73ae83b?w=120&h=120&fit=crop"},{id:fe(),name:"Carne Magra com Arroz Integral",quantity:200,unit:"g",calories:440,protein:38,carbs:42,fat:12,image:"https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=120&h=120&fit=crop"}],snack:[{id:fe(),name:"Mix de Castanhas",quantity:30,unit:"g",calories:180,protein:6,carbs:6,fat:15,image:"https://images.unsplash.com/photo-1599599810769-bcde5a160d32?w=120&h=120&fit=crop"},{id:fe(),name:"Shake de Whey com Banana",quantity:300,unit:"ml",calories:200,protein:25,carbs:25,fat:2,image:"https://images.unsplash.com/photo-1553530979-7ee52a2670c4?w=120&h=120&fit=crop"},{id:fe(),name:"Pão Integral com Ovo",quantity:100,unit:"g",calories:220,protein:15,carbs:20,fat:8,image:"https://images.unsplash.com/photo-1525351484163-7529414344d8?w=120&h=120&fit=crop"}]},ia=7*24*60*60*1e3;async function oa(a){try{const n=await sa(a.id);if(n&&Date.now()-n.timestamp<ia)return n;let o;return o=await na({remainingMacros:{calories:a.foods.reduce((t,h)=>t+h.calories*(h.quantity||1),0),protein:a.foods.reduce((t,h)=>t+h.protein*(h.quantity||1),0),carbs:a.foods.reduce((t,h)=>t+h.carbs*(h.quantity||1),0),fat:a.foods.reduce((t,h)=>t+h.fat*(h.quantity||1),0)}}),await aa(a.id,o),o}catch(n){return console.error("Error getting meal suggestions:",n),[]}}function ca({onClose:a,remainingMacros:n}){const[o,t]=H.useState([{role:"assistant",content:"O que você quer comer agora?"}]),[h,f]=H.useState(null),[c,b]=H.useState(""),[x,M]=H.useState(!1),N=H.useRef(null),[D,p]=H.useState([]),[S,l]=H.useState(!1),i="users/ai/foods-suggestions";G.useEffect(()=>(document.body.classList.add("overflow-hidden"),()=>{document.body.classList.remove("overflow-hidden")}),[]);const m=()=>{var T;(T=N.current)==null||T.scrollIntoView({behavior:"smooth"})};H.useEffect(()=>{m()},[o]);const F=async T=>{l(!0);try{const Z={type:"text",meal_type:"new",content:T},O=(await ge.post(i,Z,{timeout:15e3})).data;p(O)}catch(Z){return console.error("Error getting AI response:",Z),f("Não foi possível processar sua solicitação. Tente novamente."),!1}finally{return l(!1),!0}},g=async T=>{if(T.preventDefault(),!c.trim()||x)return;f(null);const Z=c.trim();b(""),t(U=>[...U,{role:"user",content:Z}]),await F(c.trim())};return e.jsx("div",{className:"fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-[9999] backdrop-blur-sm",style:{margin:"0"},children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl w-full max-w-lg max-h-[80vh] flex flex-col border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Oe,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h3",{className:"font-bold text-white",children:"Sugestão de Refeição"})]}),e.jsx("button",{onClick:a,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(xe,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[o.map((T,Z)=>e.jsx("div",{className:`flex ${T.role==="user"?"justify-end":"justify-start"}`,children:e.jsx("div",{className:`max-w-[80%] p-3 rounded-lg ${T.role==="user"?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white border border-snapfit-green/10"}`,children:e.jsx("p",{className:"whitespace-pre-wrap text-sm",children:T.content})})},Z)),(S||D.length>0)&&e.jsx(Dt,{isLoading:S,suggestions:D,cols:1}),e.jsx("div",{ref:N})]}),h&&e.jsx("div",{className:"px-4 py-2 bg-red-400/10 text-red-400 text-sm border border-red-400/30 rounded-lg",children:h}),e.jsx("form",{onSubmit:g,className:"p-4 border-t border-snapfit-green/20",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx("input",{type:"text",value:c,onChange:T=>b(T.target.value),onKeyPress:T=>T.key==="Enter"&&g(T),placeholder:"Digite sua mensagem...",className:"flex-1 p-2 text-sm bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:border-snapfit-green/50 focus:ring-snapfit-green/20",disabled:x}),e.jsx("button",{type:"submit",disabled:!c.trim()||x,"aria-label":x?"Enviando...":"Enviar mensagem",className:"px-3 py-2 bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors disabled:opacity-50 shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:x?e.jsx(kt,{className:"w-5 h-5 animate-spin"}):e.jsx(ts,{className:"w-5 h-5"})})]})})]})})}const la={"bench-press":{baseCalories:3.5,intensityMultiplier:1.2,weightMultiplier:.1},squat:{baseCalories:5,intensityMultiplier:1.3,weightMultiplier:.12},deadlift:{baseCalories:6,intensityMultiplier:1.4,weightMultiplier:.15},"pull-up":{baseCalories:4,intensityMultiplier:1.2,weightMultiplier:.08}},da={baseCalories:3,intensityMultiplier:1,weightMultiplier:.08};function ua(a,n,o){const t=la[a.id]||da,h=a.rpe?a.rpe/10:.8,f=a.weight?a.weight*t.weightMultiplier:1,b=t.baseCalories*t.intensityMultiplier*h*f*a.sets*a.reps,x=n/75;return Math.round(b*x)}function ga(a,n,o=!0){const t=o?(a.duration||0)*(n*.9)/60:0,h=a.exercises.map(c=>({exerciseId:c.id,calories:ua(c,n,a.duration||60)})),f=h.reduce((c,{calories:b})=>c+b,0)+t;return{total:Math.round(f),byExercise:h,basalRate:Math.round(t)}}function ma({workout:a,onClose:n,onConfirm:o}){const[t,h]=H.useState(!0),f=75,c=H.useMemo(()=>ga(a,f,t),[a,f,t]);return e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-xl max-w-md w-full p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ge,{className:"w-5 h-5 text-orange-500"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Calorias do Treino"}),e.jsx(ss,{context:"cálculos de gasto calórico"})]}),e.jsx("button",{onClick:n,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:e.jsx(xe,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 bg-orange-50 rounded-lg",children:[e.jsx("div",{className:"text-sm text-gray-600 mb-1",children:"Total Estimado"}),e.jsxs("div",{className:"text-3xl font-bold text-orange-600",children:[c.total,e.jsx("span",{className:"text-base font-normal ml-1",children:"kcal"})]})]}),e.jsx("div",{className:"space-y-2",children:c.byExercise.map(({exerciseId:b,calories:x})=>{const M=a.exercises.find(N=>N.id===b);return M?e.jsxs("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded-lg",children:[e.jsx("span",{className:"text-sm text-gray-700",children:M.name}),e.jsxs("span",{className:"text-sm font-medium text-gray-600",children:[x," kcal"]})]},b):null})}),c.basalRate>0&&e.jsxs("div",{className:"flex justify-between items-center p-2 bg-blue-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-700",children:"Gasto Basal"}),e.jsxs("div",{className:"group relative",children:[e.jsx(as,{className:"w-4 h-4 text-blue-500 cursor-help"}),e.jsx("div",{className:"absolute left-0 bottom-full mb-2 w-48 p-2 bg-white rounded-lg shadow-lg text-xs text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity",children:"Calorias que você queimaria naturalmente durante este período"})]})]}),e.jsxs("span",{className:"text-sm font-medium text-blue-600",children:[c.basalRate," kcal"]})]}),e.jsxs("label",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:t,onChange:b=>h(b.target.checked),className:"rounded text-indigo-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-800",children:"Incluir Gasto Basal"}),e.jsx("div",{className:"text-xs text-gray-600",children:"Desmarque se você usa um smartwatch para evitar contagem dupla"})]})]})]}),e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsx(rs,{})}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6",children:[e.jsx("button",{onClick:()=>o(!1),className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Não Incluir"}),e.jsx("button",{onClick:()=>o(t),className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors",children:"Confirmar"})]})]})})}function ha({caloriesConsumed:a=0,caloriesGoal:n=2e3,caloriesRemaining:o=0,caloriesBurned:t=0,protein:h={current:0,goal:150},carbs:f={current:0,goal:220},fat:c={current:0,goal:70}}){const b=Ze(),x=[{current:h.current,target:h.goal,percentage:h.current/h.goal*100,emoji:"🥩",color:"#FF4757",name:"Proteína",shortName:"P"},{current:c.current,target:c.goal,percentage:c.current/c.goal*100,emoji:"🥑",color:"#F59E0B",name:"Gordura",shortName:"G"},{current:f.current,target:f.goal,percentage:f.current/f.goal*100,emoji:"🍚",color:"#3B82F6",name:"Carboidrato",shortName:"C"}];return e.jsxs("div",{className:"bg-snapfit-gray backdrop-blur-sm rounded-xl shadow-lg p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(qe,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-sm font-bold text-white",children:"Resumo Nutricional"}),e.jsx(ns,{className:"ml-auto"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-snapfit-green/10 flex flex-col items-center justify-center",children:[e.jsx("div",{className:"w-7 h-7 bg-snapfit-green/10 rounded-full flex items-center justify-center mb-1",children:e.jsx(qe,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("div",{className:"text-lg font-bold text-snapfit-green",children:a}),e.jsx("div",{className:"text-[10px] text-gray-400",children:"Consumidas"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-snapfit-green/10 flex flex-col items-center justify-center",children:[e.jsx("div",{className:"w-7 h-7 bg-red-400/10 rounded-full flex items-center justify-center mb-1",children:e.jsx(Ge,{className:"w-4 h-4 text-red-400"})}),e.jsx("div",{className:"text-lg font-bold text-red-400",children:t}),e.jsx("div",{className:"text-[10px] text-gray-400",children:"Gastas"})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-3 border border-snapfit-green/10 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-6 h-6 bg-snapfit-green/10 rounded-full flex items-center justify-center",children:e.jsx(is,{className:"w-3.5 h-3.5 text-snapfit-green"})}),e.jsx("span",{className:"text-xs font-medium text-white",children:"Balanço Calórico"})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Meta"}),e.jsxs("div",{className:"text-sm font-medium text-white",children:[n," kcal"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Restante"}),e.jsxs("div",{className:"text-sm font-bold text-snapfit-green",children:[o," kcal"]})]}),e.jsx("div",{className:"mt-2 w-full bg-snapfit-gray rounded-full h-2",children:e.jsx("div",{className:"h-2 rounded-full transition-all duration-300 bg-snapfit-green",style:{width:`${Math.min(100,a/n*100)}%`}})})]}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:x.map((M,N)=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-2 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"text-base",children:M.emoji}),e.jsxs("span",{className:"text-[10px] text-gray-400",children:[Math.round(M.percentage),"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-sm font-bold text-white",children:Math.round(M.current)}),e.jsxs("div",{className:"text-[10px] text-gray-400",children:["/",M.target,"g"]})]}),e.jsx("div",{className:"mt-1 w-full bg-snapfit-gray rounded-full h-1",children:e.jsx("div",{className:"h-1 rounded-full transition-all duration-300",style:{width:`${Math.min(100,M.percentage)}%`,backgroundColor:M.color}})})]},N))}),e.jsx("div",{className:"mt-3 text-center",children:e.jsx("button",{onClick:()=>b("/dashboard/scientific-sources"),className:"text-xs text-gray-500 hover:text-blue-400 transition-colors underline decoration-dotted",children:"*baseado em diretrizes nutricionais"})})]})}function fa({initialCurrent:a=0,goal:n}){const[o,t]=G.useState(a);return{current:o,goal:n,addWater:c=>{t(b=>b+c)},resetWater:()=>{t(0)}}}function xa({days:a=7}){const{getHydrationHistory:n,dailyHydration:o}=Je(),t=n(a),h=N=>{const D=new Date(N),p=new Date,S=new Date(p);return S.setDate(S.getDate()-1),N===p.toISOString().split("T")[0]?"Hoje":N===S.toISOString().split("T")[0]?"Ontem":D.toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"})},f=(N,D)=>Math.min(100,Math.round(N/D*100)),c=N=>N>=100?"text-snapfit-green":N>=75?"text-blue-400":N>=50?"text-yellow-400":"text-red-400",b=N=>N>=100?"bg-snapfit-green/20 border-snapfit-green/30":N>=75?"bg-blue-500/20 border-blue-500/30":N>=50?"bg-yellow-500/20 border-yellow-500/30":"bg-red-500/20 border-red-500/30",M=(()=>{if(t.length<2)return null;const N=t.find(l=>l.date===o.date),D=t.find(l=>{const i=new Date;return i.setDate(i.getDate()-1),l.date===i.toISOString().split("T")[0]});if(!N||!D)return null;const p=f(N.current,N.goal),S=f(D.current,D.goal);return p>S?{type:"up",value:p-S}:p<S?{type:"down",value:S-p}:{type:"same",value:0}})();return e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(We,{className:"w-5 h-5 text-blue-400"})}),e.jsx("h2",{className:"text-lg font-bold text-white",children:"Histórico de Hidratação"})]}),M&&e.jsxs("div",{className:`flex items-center gap-1 text-sm ${M.type==="up"?"text-snapfit-green":M.type==="down"?"text-red-400":"text-gray-400"}`,children:[M.type==="up"&&e.jsx(os,{className:"w-4 h-4"}),M.type==="down"&&e.jsx(cs,{className:"w-4 h-4"}),M.value>0&&`${M.value}%`]})]}),e.jsx("div",{className:"space-y-3",children:t.map(N=>{const D=f(N.current,N.goal),p=N.date===o.date;return e.jsxs("div",{className:`p-3 rounded-lg border transition-all ${p?"bg-snapfit-green/10 border-snapfit-green/30":b(D)}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($t,{className:"w-4 h-4 text-gray-400"}),e.jsxs("span",{className:"text-sm font-medium text-white",children:[h(N.date),p&&e.jsx("span",{className:"ml-2 text-xs text-snapfit-green",children:"(hoje)"})]})]}),e.jsxs("div",{className:`text-sm font-bold ${c(D)}`,children:[D,"%"]})]}),e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"text-sm text-gray-400",children:[N.current,"ml / ",N.goal,"ml"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[N.entries.length," ",N.entries.length===1?"registro":"registros"]})]}),e.jsx("div",{className:"w-full bg-snapfit-dark-gray rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${D>=100?"bg-snapfit-green":D>=75?"bg-blue-400":D>=50?"bg-yellow-400":"bg-red-400"}`,style:{width:`${Math.min(100,D)}%`}})}),p&&N.entries.length>0&&e.jsxs("div",{className:"mt-3 pt-3 border-t border-snapfit-green/20",children:[e.jsxs("div",{className:"text-xs text-gray-400 mb-2 flex items-center gap-1",children:[e.jsx(Tt,{className:"w-3 h-3"}),"Registros de hoje:"]}),e.jsxs("div",{className:"flex flex-wrap gap-1",children:[N.entries.slice(-5).map(S=>e.jsxs("div",{className:"px-2 py-1 bg-blue-500/20 rounded text-xs text-blue-300 border border-blue-500/30",children:["+",S.amount,"ml",e.jsx("span",{className:"ml-1 text-gray-500",children:S.timestamp.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]},S.id)),N.entries.length>5&&e.jsxs("div",{className:"px-2 py-1 bg-gray-500/20 rounded text-xs text-gray-400",children:["+",N.entries.length-5," mais"]})]})]})]},N.date)})}),t.length===0&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(We,{className:"w-12 h-12 text-gray-600 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Nenhum histórico de hidratação encontrado"}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Comece adicionando água hoje!"})]})]})}const pa={enabled:!1,interval:120,startTime:"09:00",endTime:"20:00",weekdaysOnly:!1};function Pt(){const{dailyHydration:a,lastAddedAmount:n}=Je(),[o,t]=G.useState(()=>{const l=localStorage.getItem("hydration_reminder_settings");return l?JSON.parse(l):pa}),[h,f]=G.useState(0);G.useEffect(()=>{localStorage.setItem("hydration_reminder_settings",JSON.stringify(o))},[o]);const c=()=>{const l=new Date,i=l.getHours()*60+l.getMinutes(),[m,F]=o.startTime.split(":").map(Number),[g,T]=o.endTime.split(":").map(Number),Z=m*60+F,U=g*60+T;return i>=Z&&i<=U},b=()=>{const l=new Date().getDay();return l>=1&&l<=5},x=()=>{if(!o.enabled||!c()||o.weekdaysOnly&&!b())return!1;const i=Date.now()-h,m=o.interval*60*1e3;return i>=m},M=()=>{const l=Math.round(a.current/a.goal*100),i=a.goal-a.current,m={low:["Hora de beber água","Lembrete de hidratação","Que tal um copo d'água?"],medium:["Continue se hidratando","Mais um pouco de água","Hidratação em dia"],high:["Quase na meta!","Faltam poucos ml","Você está indo bem"],complete:["Meta atingida!","Hidratação completa","Parabéns!"]};let F;l>=100?F="complete":l>=75?F="high":l>=40?F="medium":F="low";const g=m[F],T=g[Math.floor(Math.random()*g.length)];return F!=="complete"&&i>0?`${T} (${i}ml restantes)`:T},N=()=>{const l=M();Math.round(a.current/a.goal*100)>=100||(ke.info(l,{toastId:"hydration-reminder",position:"bottom-right",autoClose:3e3,hideProgressBar:!0,closeOnClick:!0,pauseOnHover:!1,draggable:!1,style:{fontSize:"14px",padding:"12px",borderRadius:"8px",backgroundColor:"#1E1E1E",color:"#ffffff",border:"1px solid rgba(185, 255, 67, 0.2)"},onClick:()=>{console.log("Reminder clicked - could open water modal")}}),f(Date.now()))};G.useEffect(()=>{if(!o.enabled)return;const l=()=>{x()&&N()};l();const i=setInterval(l,6e5);return()=>clearInterval(i)},[o,a,h]),G.useEffect(()=>{n&&f(Date.now())},[n]);const D=l=>{t(i=>({...i,...l}))},p=()=>{N()},S=()=>o.enabled?new Date(h+o.interval*60*1e3):null;return{settings:o,updateSettings:D,triggerReminder:p,getNextReminderTime:S,isWithinReminderHours:c(),timeUntilNextReminder:S()?Math.max(0,(S().getTime()-Date.now())/1e3/60):0}}function ba({isOpen:a,onClose:n}){const{dailyHydration:o,setDailyGoal:t}=Je(),{settings:h,updateSettings:f}=Pt(),[c,b]=G.useState(o.goal),[x,M]=G.useState(h),[N,D]=G.useState(!1),p=async()=>{D(!0);try{c!==o.goal&&t(c),f(x),await new Promise(i=>setTimeout(i,500)),n()}catch(i){console.error("Error saving settings:",i)}finally{D(!1)}},S=[1500,2e3,2500,3e3,3500,4e3],l=[30,60,90,120,180];return a?e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-2xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20 max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(ls,{className:"w-5 h-5 text-blue-400"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Configurações de Hidratação"})]}),e.jsx("button",{onClick:n,className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(xe,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm font-medium text-white mb-3",children:[e.jsx(ds,{className:"w-4 h-4 text-snapfit-green"}),"Meta Diária de Água"]}),e.jsx("div",{className:"grid grid-cols-3 gap-2 mb-3",children:S.map(i=>e.jsxs("button",{onClick:()=>b(i),className:`p-2 rounded-lg border text-sm transition-all ${c===i?"bg-blue-500/20 border-blue-500/50 text-blue-400":"bg-snapfit-dark-gray border-blue-400/20 text-white hover:bg-blue-500/10"}`,children:[i,"ml"]},i))}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"number",value:c,onChange:i=>b(parseInt(i.target.value)||0),className:"flex-1 px-3 py-2 bg-snapfit-dark-gray border border-blue-400/20 rounded-lg text-white placeholder-gray-400 focus:border-blue-400 focus:outline-none transition-colors",placeholder:"Meta personalizada",min:"500",max:"10000",step:"100"}),e.jsx("span",{className:"text-sm text-gray-400",children:"ml"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm font-medium text-white mb-3",children:[e.jsx(us,{className:"w-4 h-4 text-snapfit-green"}),"Lembretes de Hidratação"]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white",children:"Ativar lembretes"}),e.jsxs("label",{className:"toggle-switch",children:[e.jsx("input",{type:"checkbox",checked:x.enabled,onChange:i=>M(m=>({...m,enabled:i.target.checked}))}),e.jsx("span",{className:"toggle-slider"})]})]}),x.enabled&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-gray-400 mb-2",children:"Intervalo entre lembretes"}),e.jsx("select",{value:x.interval,onChange:i=>M(m=>({...m,interval:parseInt(i.target.value)})),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-blue-400/20 rounded-lg text-white focus:border-blue-400 focus:outline-none transition-colors",children:l.map(i=>e.jsx("option",{value:i,children:i<60?`${i} minutos`:`${i/60} hora${i>60?"s":""}`},i))})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-gray-400 mb-2",children:"Início"}),e.jsx("input",{type:"time",value:x.startTime,onChange:i=>M(m=>({...m,startTime:i.target.value})),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-blue-400/20 rounded-lg text-white focus:border-blue-400 focus:outline-none transition-colors"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm text-gray-400 mb-2",children:"Fim"}),e.jsx("input",{type:"time",value:x.endTime,onChange:i=>M(m=>({...m,endTime:i.target.value})),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-blue-400/20 rounded-lg text-white focus:border-blue-400 focus:outline-none transition-colors"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-white",children:"Apenas dias úteis"}),e.jsxs("label",{className:"toggle-switch",children:[e.jsx("input",{type:"checkbox",checked:x.weekdaysOnly,onChange:i=>M(m=>({...m,weekdaysOnly:i.target.checked}))}),e.jsx("span",{className:"toggle-slider"})]})]})]})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-blue-400/20",children:[e.jsxs("h3",{className:"text-sm font-medium text-white mb-2 flex items-center gap-2",children:[e.jsx(We,{className:"w-4 h-4 text-blue-400"}),"Status Atual"]}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Consumido hoje:"}),e.jsxs("span",{className:"text-white",children:[o.current,"ml"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Meta atual:"}),e.jsxs("span",{className:"text-white",children:[o.goal,"ml"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Progresso:"}),e.jsxs("span",{className:`font-medium ${o.current>=o.goal?"text-snapfit-green":"text-blue-400"}`,children:[Math.round(o.current/o.goal*100),"%"]})]})]})]})]}),e.jsxs("div",{className:"flex gap-3 pt-6 mt-6 border-t border-snapfit-green/20",children:[e.jsx("button",{onClick:n,className:"flex-1 px-4 py-2 text-sm font-medium text-gray-400 bg-snapfit-dark-gray hover:bg-snapfit-dark-gray/80 rounded-lg transition-colors border border-snapfit-green/10",children:"Cancelar"}),e.jsxs("button",{onClick:p,disabled:N,className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-bold text-black bg-blue-400 hover:bg-blue-500 rounded-lg transition-colors disabled:opacity-50",children:[N?e.jsx("div",{className:"w-4 h-4 border-2 border-black/20 border-t-black rounded-full animate-spin"}):e.jsx(gs,{className:"w-4 h-4"}),N?"Salvando...":"Salvar"]})]})]})}):null}function va({date_start:a,date_end:n}){let o={page:1};a&&n&&(o={page:1,date_start:a,date_end:n});const[t,h]=G.useState([]),[f,c]=G.useState(o),[b,x]=G.useState(!1),[M,N]=G.useState(0),D=i=>{if(!i||typeof i!="string")return"";const m=i.split(", ");if(m.length<2)return i;{const F=m.pop();return m.join(", ")+" e "+F}},p=i=>{if(!i||typeof i!="string")return"0min";const[m,F,g]=i.split(":").map(Number);return m>0?`${m.toString().padStart(2,"0")}:${F.toString().padStart(2,"0")}:${g.toString().padStart(2,"0")}h`:`${F.toString().padStart(2,"0")}:${g.toString().padStart(2,"0")}min`},S=async(i=!1)=>{var m,F;try{c(i?U=>({...U,page:U.page+1}):U=>({...U,page:1}));const g=await ge.get("users/workouts/history",{searchParams:f}),T=((m=g.data)==null?void 0:m.workouts)||[];h(i?[...t,...T]:T);const Z=(F=g.data)==null?void 0:F.pagination;Z&&Z.page<Z.totalPages?x(!0):x(!1)}catch(g){console.error("Error fetching workouts:",g)}};G.useEffect(()=>{S()},[]),G.useEffect(()=>{c(i=>({...i,date_start:a,date_end:n})),N(Date.now())},[a,n]),G.useEffect(()=>{M>0&&S()},[M]);const l=()=>{S(!0)};return e.jsx(e.Fragment,{children:t&&e.jsxs("div",{className:`\r
    `,children:[e.jsx("div",{className:"space-y-2 sm:space-y-4",children:t==null?void 0:t.map((i,m)=>e.jsxs("div",{className:"p-3 sm:p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-3 sm:mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[e.jsx("div",{className:`p-2 rounded-lg ${i.completed?"bg-snapfit-green/20 border border-snapfit-green/30":"bg-orange-500/20 border border-orange-500/30"}`,children:e.jsx(He,{className:`w-5 h-5 ${i.completed?"text-snapfit-green":"text-orange-400"}`})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm sm:text-base font-medium text-white",children:[(i==null?void 0:i.workout_name)||"Treino"," - ",D(i==null?void 0:i.muscle_groups)]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-1 sm:gap-2 text-[10px] sm:text-xs text-gray-400",children:[e.jsx("span",{children:Is(i==null?void 0:i.date)}),e.jsx("span",{children:"•"}),(i==null?void 0:i.exercise_count)||0," exercícios"]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:(i==null?void 0:i.completed)&&e.jsxs("span",{className:"flex items-center gap-1 px-2 py-1 bg-snapfit-green/20 text-snapfit-green rounded-full text-xs border border-snapfit-green/30",children:[e.jsx(ms,{className:"w-4 h-4"}),e.jsx("span",{children:"Concluído"})]})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 sm:gap-3 text-[10px] sm:text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1 min-w-[80px]",children:[e.jsx(Tt,{className:"w-4 h-4"}),e.jsx("span",{children:p(i==null?void 0:i.workout_time)})]}),e.jsxs("div",{className:"flex items-center gap-1 min-w-[80px]",children:[e.jsx(Ge,{className:"w-4 h-4 text-orange-500"}),e.jsxs("span",{children:[(i==null?void 0:i.total_kcal)||0,"kcal"]})]}),e.jsxs("div",{className:"min-w-[100px]",children:[(i==null?void 0:i.exercise_count)||0," exercícios"]}),e.jsx("div",{className:"text-xs px-2 py-1 rounded-full bg-snapfit-dark-gray text-gray-300 border border-snapfit-green/20 whitespace-nowrap",children:(i==null?void 0:i.type)||"Treino"})]})]},m))}),e.jsx("div",{className:"flex justify-center mt-4",children:b&&e.jsx("button",{className:"text-sm cursor-pointer px-4 pb-1 border border-snapfit-green/30 text-gray-400 rounded-lg hover:border-snapfit-green hover:text-snapfit-green transition-colors",onClick:l,disabled:!b,children:"Carregar mais"})})]})})}const ya=()=>{const[a,n]=G.useState(null),[o,t]=G.useState(!1),[h,f]=G.useState({weight:"",height:""}),[c,b]=G.useState(!1),x=G.useCallback(async()=>{try{b(!0);const D=await ge.get("users/me"),p=D.data||D;if(p){n(p),localStorage.setItem("userData",JSON.stringify(p));const S=!p.weight||!p.height||p.weight===""||p.height==="";t(S)}}catch(D){console.error("Error fetching user data:",D)}finally{b(!1)}},[]);G.useEffect(()=>{const D=localStorage.getItem("userData");if(D){const p=JSON.parse(D);n(p);const S=!p.weight||!p.height||p.weight===""||p.height==="";t(S),S&&x()}else x()},[x]);const M=async D=>{D.preventDefault();try{b(!0);const p={weight:Number(h.weight),height:Number(h.height)},l=(await ge.put("users/me",p)).data;n(l),localStorage.setItem("userData",JSON.stringify(l)),f({weight:"",height:""}),t(!1)}catch(p){console.error("Error saving user data:",p)}finally{b(!1)}},N=D=>{const{name:p,value:S}=D.target;f(l=>({...l,[p]:S}))};return e.jsxs(e.Fragment,{children:[c&&e.jsx(Pe,{}),o&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-40",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20",children:[e.jsx("div",{className:"text-center mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-white mb-2",children:"Complete seu perfil"})}),e.jsx("form",{onSubmit:M,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Peso (kg)"}),e.jsx("input",{type:"number",name:"weight",className:"w-full p-2 border border-snapfit-green/30 rounded-lg bg-snapfit-dark-gray text-white focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green",placeholder:"Ex: 75.5",required:!0,value:h.weight,onChange:N,step:"0.1",min:"0"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Altura (cm)"}),e.jsx("input",{type:"number",name:"height",className:"w-full p-2 border border-snapfit-green/30 rounded-lg bg-snapfit-dark-gray text-white focus:ring-2 focus:ring-snapfit-green focus:border-snapfit-green",placeholder:"Ex: 175",required:!0,value:h.height,onChange:N,min:"0"})]}),e.jsx("button",{type:"submit",disabled:c,className:"w-full px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:bg-snapfit-green/50 font-medium",children:c?"Salvando...":"Salvar"})]})})]})})]})},ja=({day:a})=>{const n=It(),[o,t]=G.useState(!1),[h,f]=G.useState(null),[c,b]=G.useState(!1),[x,M]=G.useState(!1),[N,D]=G.useState(null),[p,S]=G.useState(!1),l=()=>{S(!1)},{mutate:i,isPending:m}=mt({mutationFn:async u=>(S(!1),await ge.post("users/protocols/diet/uncheck",u)),onSuccess:(u,W)=>{const $=a;n.invalidateQueries({queryKey:["meals",$]}),n.invalidateQueries({queryKey:["dailyDataNutritionalSummary",$]}),ke.success("Refeição desmarcada",{position:"bottom-right"})},onError:u=>{S(!0),console.error("Erro ao marcar refeição como consumida:",u),ke.error("Erro ao desmarcar refeição",{position:"bottom-right"})}}),F=async()=>{N&&i({id:N})},{data:g,isLoading:T,error:Z}=hs({queryKey:["meals",a],queryFn:async()=>{var u,W,$,L,te,R;console.log("🔄 MealsOfDayWeek: Fetching meals for day:",a),console.log("📅 MealsOfDayWeek: Date being sent:",`${a} 00:00:00`),console.log("🔑 MealsOfDayWeek: Access token:",localStorage.getItem("accessToken")?"Present":"Missing");try{const y=await ge.get("users/protocols/diet/meals/active",{searchParams:{date:`${a} 00:00:00`}});return console.log("🍽️ MealsOfDayWeek: Full response:",y),console.log("📋 MealsOfDayWeek: Has protocol:",(u=y==null?void 0:y.data)==null?void 0:u.has_protocol),console.log("🥘 MealsOfDayWeek: Meals count:",(($=(W=y==null?void 0:y.data)==null?void 0:W.meals)==null?void 0:$.length)||0),(L=y==null?void 0:y.data)!=null&&L.has_protocol||console.warn("⚠️ MealsOfDayWeek: User does not have an active diet protocol"),(te=y==null?void 0:y.data)!=null&&te.has_protocol?(R=y==null?void 0:y.data)==null?void 0:R.meals:[]}catch(y){return console.error("❌ MealsOfDayWeek: API error:",y),[]}}}),{mutate:U,isPending:O}=mt({mutationFn:async u=>await ge.post("users/protocols/diet/check",u),onSuccess:async(u,W)=>{const $=a;await Promise.all([n.invalidateQueries({queryKey:["dashboard"]}),n.invalidateQueries({queryKey:["dashboard","nutrition"]}),n.invalidateQueries({queryKey:["dashboard","nutrition","meals",$]}),n.invalidateQueries({queryKey:["dashboard","nutrition","summary",$]}),n.invalidateQueries({queryKey:["meals",$]}),n.invalidateQueries({queryKey:["dailyDataNutritionalSummary",$]}),n.invalidateQueries({queryKey:["daily-meals"]}),n.invalidateQueries({queryKey:["diary"]}),n.invalidateQueries({queryKey:["diary","meals",$]}),n.invalidateQueries({queryKey:["diet-protocol"]}),n.invalidateQueries({queryKey:["mealofdayweek"]})]),f(null),console.log("✅ Refeição marcada como consumida e cache invalidado")},onError:u=>{console.error("Erro ao marcar refeição como consumida:",u)}});G.useEffect(()=>{h&&U({meal_id:h})},[h]),G.useEffect(()=>{const u=new Date().toISOString().split("T")[0];M(a===u)},[a]);const J=async u=>{n.setQueryData(["meals",a],$=>$.map(L=>L.id===u?{...L,suggestionsAiLoading:!0}:L));const W="users/ai/foods-suggestions";try{const $={meal_id:u,meal_type:"replacement"},L={type:"text",content:null},te=await ge.post(W,L,{searchParams:$,timeout:15e3});console.log("🔄 Raw API response object:",te),console.log("🔄 Response.data:",te.data),console.log("🔄 Response.data type:",typeof te.data),console.log("🔄 Response.data keys:",Object.keys(te.data||{}));let R=te.data;if(R){if(typeof R=="string")try{R=JSON.parse(R),console.log("🔄 Parsed string data:",R)}catch(y){console.error("Error parsing JSON string:",y),R=[]}if(R.data&&Array.isArray(R.data))console.log("🔄 Found data array:",R.data),R=R.data;else if(R.recommendations&&Array.isArray(R.recommendations))console.log("🔄 Found recommendations array:",R.recommendations),R=R.recommendations;else if(Array.isArray(R))console.log("🔄 Data is already array:",R);else{console.warn("🔄 Unexpected data structure:",R);const y=Object.keys(R);for(const j of y)if(Array.isArray(R[j])&&R[j].length>0){console.log(`🔄 Found array in key "${j}":`,R[j]),R=R[j];break}Array.isArray(R)||(console.warn("🔄 Could not find array data, using empty array"),R=[])}}else console.warn("🔄 No data in response"),R=[];console.log("🔄 Final processed suggestions:",R),console.log("🔄 Final suggestions length:",R.length),n.setQueryData(["meals",a],y=>{const j=y.map(v=>v.id===u?{...v,suggestionsAi:R,suggestionsAiLoading:!1}:v);return console.log("🔄 Updated meal with suggestions:",j.find(v=>v.id===u)),j})}catch($){console.error("Error getting meal suggestions:",$)}finally{n.setQueryData(["meals",a],$=>$.map(L=>L.id===u?{...L,suggestionsAiLoading:!1,suggestionsAi:[]}:L)),ke.error("Erro ao obter sugestões da IA. Tente novamente.")}};return G.useEffect(()=>{},[c]),e.jsxs(e.Fragment,{children:[(T||O||m)&&e.jsx(ht,{backdrop:!0,vertical:!0,className:"z-[99999]"}),o&&e.jsx(Pe,{}),e.jsx("div",{className:"space-y-4",children:g==null?void 0:g.map(u=>{var W,$,L,te;return e.jsxs("div",{className:`bg-snapfit-gray rounded-lg shadow-sm p-3 sm:p-4 border ${u.completed?"border-l-4 border-snapfit-green border-snapfit-green/30":"border-l-4 border-snapfit-green/10 border-snapfit-green/10"}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col items-left md:flex-row md:items-center gap-3",children:[e.jsx("h3",{className:"text-base sm:text-lg font-medium text-white",children:u==null?void 0:u.name}),e.jsxs("span",{className:"text-xs text-gray-400",children:[(W=u==null?void 0:u.nutrients)==null?void 0:W.calories," kcal • ",($=u==null?void 0:u.nutrients)==null?void 0:$.protein,"g P • ",(L=u==null?void 0:u.nutrients)==null?void 0:L.carbs,"g C • ",(te=u==null?void 0:u.nutrients)==null?void 0:te.fat,"g G"]}),!u.completed&&e.jsxs("button",{onClick:()=>{J(u.id)},className:"flex items-center gap-1.5 text-xs text-gray-400 hover:text-snapfit-green transition-colors",children:[e.jsx(Oe,{className:"w-3.5 h-3.5"}),e.jsx("span",{className:"font-light",children:"gerar tabela de substituição"})]})]}),e.jsx("div",{className:"mt-1 text-xs sm:text-sm text-gray-400",children:u==null?void 0:u.foods.map((R,y)=>e.jsxs("span",{className:"mr-2",children:[R.name," ",e.jsxs("small",{children:["(",R.quantity," ",R.unit,")"]})," ",y<u.foods.length-1&&e.jsx("span",{style:{margin:"0 4px"},children:"•"})]},y))})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 ml-4",children:[e.jsx("span",{className:"text-xs sm:text-sm text-snapfit-green",children:u==null?void 0:u.meal_time.split(":").slice(0,2).join(":")}),e.jsxs("div",{children:[e.jsx("button",{className:`\r
                  hidden\r
                  p-1.5 sm:p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors`,children:e.jsx(Ve,{className:"w-4 h-4 sm:w-5 sm:h-5"})}),e.jsxs(e.Fragment,{children:[u.completed&&e.jsx("button",{onClick:()=>{if(u.completed){D(u.id),S(!0);return}f(u.id)},className:`p-1.5 sm:p-2 rounded-full transition-colors
                    ${u.completed?"bg-snapfit-green/20 text-snapfit-green hover:text-red-400 border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 border border-snapfit-green/10"}`,children:e.jsx(St,{className:"w-4 h-4 sm:w-5 sm:h-5"})}),!u.completed&&e.jsx(Ds,{id:u.id,dateInit:a,timeInit:u==null?void 0:u.meal_time,onConfirm:(R,y)=>{console.log("Confirmado:",R,y),n.invalidateQueries({queryKey:["meals",a]}),n.invalidateQueries({queryKey:["dailyDataNutritionalSummary",a]})},onCancel:()=>console.log("Cancelado")})]}),e.jsx("button",{className:`\r
                  hidden\r
                  p-1.5 sm:p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors`,children:e.jsx(xe,{className:"w-4 h-4 sm:w-5 sm:h-5"})})]})]})]}),((u==null?void 0:u.suggestionsAiLoading)||(u==null?void 0:u.suggestionsAi))&&e.jsx(Dt,{mealId:u.id,dateInit:a,timeInit:u==null?void 0:u.meal_time,isLoading:u==null?void 0:u.suggestionsAiLoading,suggestions:u==null?void 0:u.suggestionsAi})]},u.id)})}),e.jsxs(Re,{backdrop:"static",role:"alertdialog",open:p,onClose:l,size:"xs",children:[(m||T)&&e.jsx(ht,{backdrop:!0,vertical:!0,className:"z-[99999]"}),e.jsx(Re.Body,{className:"bg-snapfit-gray text-white border border-snapfit-green/20 rounded-lg",children:"Deseja desmarcar esta refeição?"}),e.jsx(Re.Footer,{className:"bg-snapfit-gray border-t border-snapfit-green/20",children:e.jsxs("div",{className:"flex gap-2 justify-center",children:[e.jsx("button",{className:"bg-snapfit-green text-black px-4 py-2 rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",onClick:F,children:"Sim"}),e.jsx("button",{className:"text-gray-400 hover:text-snapfit-green px-4 py-2",onClick:l,children:"Cancelar"})]})})]})]})};var Lt={exports:{}};(function(a,n){(function(o,t){a.exports=t()})(Xe,function(){var o=1e3,t=6e4,h=36e5,f="millisecond",c="second",b="minute",x="hour",M="day",N="week",D="month",p="quarter",S="year",l="date",i="Invalid Date",m=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,F=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,g={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(y){var j=["th","st","nd","rd"],v=y%100;return"["+y+(j[(v-20)%10]||j[v]||j[0])+"]"}},T=function(y,j,v){var A=String(y);return!A||A.length>=j?y:""+Array(j+1-A.length).join(v)+y},Z={s:T,z:function(y){var j=-y.utcOffset(),v=Math.abs(j),A=Math.floor(v/60),d=v%60;return(j<=0?"+":"-")+T(A,2,"0")+":"+T(d,2,"0")},m:function y(j,v){if(j.date()<v.date())return-y(v,j);var A=12*(v.year()-j.year())+(v.month()-j.month()),d=j.clone().add(A,D),_=v-d<0,r=j.clone().add(A+(_?-1:1),D);return+(-(A+(v-d)/(_?d-r:r-d))||0)},a:function(y){return y<0?Math.ceil(y)||0:Math.floor(y)},p:function(y){return{M:D,y:S,w:N,d:M,D:l,h:x,m:b,s:c,ms:f,Q:p}[y]||String(y||"").toLowerCase().replace(/s$/,"")},u:function(y){return y===void 0}},U="en",O={};O[U]=g;var J="$isDayjsObject",u=function(y){return y instanceof te||!(!y||!y[J])},W=function y(j,v,A){var d;if(!j)return U;if(typeof j=="string"){var _=j.toLowerCase();O[_]&&(d=_),v&&(O[_]=v,d=_);var r=j.split("-");if(!d&&r.length>1)return y(r[0])}else{var s=j.name;O[s]=j,d=s}return!A&&d&&(U=d),d||!A&&U},$=function(y,j){if(u(y))return y.clone();var v=typeof j=="object"?j:{};return v.date=y,v.args=arguments,new te(v)},L=Z;L.l=W,L.i=u,L.w=function(y,j){return $(y,{locale:j.$L,utc:j.$u,x:j.$x,$offset:j.$offset})};var te=function(){function y(v){this.$L=W(v.locale,null,!0),this.parse(v),this.$x=this.$x||v.x||{},this[J]=!0}var j=y.prototype;return j.parse=function(v){this.$d=function(A){var d=A.date,_=A.utc;if(d===null)return new Date(NaN);if(L.u(d))return new Date;if(d instanceof Date)return new Date(d);if(typeof d=="string"&&!/Z$/i.test(d)){var r=d.match(m);if(r){var s=r[2]-1||0,w=(r[7]||"0").substring(0,3);return _?new Date(Date.UTC(r[1],s,r[3]||1,r[4]||0,r[5]||0,r[6]||0,w)):new Date(r[1],s,r[3]||1,r[4]||0,r[5]||0,r[6]||0,w)}}return new Date(d)}(v),this.init()},j.init=function(){var v=this.$d;this.$y=v.getFullYear(),this.$M=v.getMonth(),this.$D=v.getDate(),this.$W=v.getDay(),this.$H=v.getHours(),this.$m=v.getMinutes(),this.$s=v.getSeconds(),this.$ms=v.getMilliseconds()},j.$utils=function(){return L},j.isValid=function(){return this.$d.toString()!==i},j.isSame=function(v,A){var d=$(v);return this.startOf(A)<=d&&d<=this.endOf(A)},j.isAfter=function(v,A){return $(v)<this.startOf(A)},j.isBefore=function(v,A){return this.endOf(A)<$(v)},j.$g=function(v,A,d){return L.u(v)?this[A]:this.set(d,v)},j.unix=function(){return Math.floor(this.valueOf()/1e3)},j.valueOf=function(){return this.$d.getTime()},j.startOf=function(v,A){var d=this,_=!!L.u(A)||A,r=L.p(v),s=function(Q,z){var Y=L.w(d.$u?Date.UTC(d.$y,z,Q):new Date(d.$y,z,Q),d);return _?Y:Y.endOf(M)},w=function(Q,z){return L.w(d.toDate()[Q].apply(d.toDate("s"),(_?[0,0,0,0]:[23,59,59,999]).slice(z)),d)},C=this.$W,E=this.$M,P=this.$D,B="set"+(this.$u?"UTC":"");switch(r){case S:return _?s(1,0):s(31,11);case D:return _?s(1,E):s(0,E+1);case N:var I=this.$locale().weekStart||0,q=(C<I?C+7:C)-I;return s(_?P-q:P+(6-q),E);case M:case l:return w(B+"Hours",0);case x:return w(B+"Minutes",1);case b:return w(B+"Seconds",2);case c:return w(B+"Milliseconds",3);default:return this.clone()}},j.endOf=function(v){return this.startOf(v,!1)},j.$set=function(v,A){var d,_=L.p(v),r="set"+(this.$u?"UTC":""),s=(d={},d[M]=r+"Date",d[l]=r+"Date",d[D]=r+"Month",d[S]=r+"FullYear",d[x]=r+"Hours",d[b]=r+"Minutes",d[c]=r+"Seconds",d[f]=r+"Milliseconds",d)[_],w=_===M?this.$D+(A-this.$W):A;if(_===D||_===S){var C=this.clone().set(l,1);C.$d[s](w),C.init(),this.$d=C.set(l,Math.min(this.$D,C.daysInMonth())).$d}else s&&this.$d[s](w);return this.init(),this},j.set=function(v,A){return this.clone().$set(v,A)},j.get=function(v){return this[L.p(v)]()},j.add=function(v,A){var d,_=this;v=Number(v);var r=L.p(A),s=function(E){var P=$(_);return L.w(P.date(P.date()+Math.round(E*v)),_)};if(r===D)return this.set(D,this.$M+v);if(r===S)return this.set(S,this.$y+v);if(r===M)return s(1);if(r===N)return s(7);var w=(d={},d[b]=t,d[x]=h,d[c]=o,d)[r]||1,C=this.$d.getTime()+v*w;return L.w(C,this)},j.subtract=function(v,A){return this.add(-1*v,A)},j.format=function(v){var A=this,d=this.$locale();if(!this.isValid())return d.invalidDate||i;var _=v||"YYYY-MM-DDTHH:mm:ssZ",r=L.z(this),s=this.$H,w=this.$m,C=this.$M,E=d.weekdays,P=d.months,B=d.meridiem,I=function(z,Y,V,X){return z&&(z[Y]||z(A,_))||V[Y].slice(0,X)},q=function(z){return L.s(s%12||12,z,"0")},Q=B||function(z,Y,V){var X=z<12?"AM":"PM";return V?X.toLowerCase():X};return _.replace(F,function(z,Y){return Y||function(V){switch(V){case"YY":return String(A.$y).slice(-2);case"YYYY":return L.s(A.$y,4,"0");case"M":return C+1;case"MM":return L.s(C+1,2,"0");case"MMM":return I(d.monthsShort,C,P,3);case"MMMM":return I(P,C);case"D":return A.$D;case"DD":return L.s(A.$D,2,"0");case"d":return String(A.$W);case"dd":return I(d.weekdaysMin,A.$W,E,2);case"ddd":return I(d.weekdaysShort,A.$W,E,3);case"dddd":return E[A.$W];case"H":return String(s);case"HH":return L.s(s,2,"0");case"h":return q(1);case"hh":return q(2);case"a":return Q(s,w,!0);case"A":return Q(s,w,!1);case"m":return String(w);case"mm":return L.s(w,2,"0");case"s":return String(A.$s);case"ss":return L.s(A.$s,2,"0");case"SSS":return L.s(A.$ms,3,"0");case"Z":return r}return null}(z)||r.replace(":","")})},j.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},j.diff=function(v,A,d){var _,r=this,s=L.p(A),w=$(v),C=(w.utcOffset()-this.utcOffset())*t,E=this-w,P=function(){return L.m(r,w)};switch(s){case S:_=P()/12;break;case D:_=P();break;case p:_=P()/3;break;case N:_=(E-C)/6048e5;break;case M:_=(E-C)/864e5;break;case x:_=E/h;break;case b:_=E/t;break;case c:_=E/o;break;default:_=E}return d?_:L.a(_)},j.daysInMonth=function(){return this.endOf(D).$D},j.$locale=function(){return O[this.$L]},j.locale=function(v,A){if(!v)return this.$L;var d=this.clone(),_=W(v,A,!0);return _&&(d.$L=_),d},j.clone=function(){return L.w(this.$d,this)},j.toDate=function(){return new Date(this.valueOf())},j.toJSON=function(){return this.isValid()?this.toISOString():null},j.toISOString=function(){return this.$d.toISOString()},j.toString=function(){return this.$d.toUTCString()},y}(),R=te.prototype;return $.prototype=R,[["$ms",f],["$s",c],["$m",b],["$H",x],["$W",M],["$M",D],["$y",S],["$D",l]].forEach(function(y){R[y[1]]=function(j){return this.$g(j,y[0],y[1])}}),$.extend=function(y,j){return y.$i||(y(j,te,$),y.$i=!0),$},$.locale=W,$.isDayjs=u,$.unix=function(y){return $(1e3*y)},$.en=O[U],$.Ls=O,$.p={},$})})(Lt);var wa=Lt.exports;const Rt=et(wa);var Bt={exports:{}};(function(a,n){(function(o,t){a.exports=t()})(Xe,function(){var o="minute",t=/[+-]\d\d(?::?\d\d)?/g,h=/([+-]|\d\d)/g;return function(f,c,b){var x=c.prototype;b.utc=function(i){var m={date:i,utc:!0,args:arguments};return new c(m)},x.utc=function(i){var m=b(this.toDate(),{locale:this.$L,utc:!0});return i?m.add(this.utcOffset(),o):m},x.local=function(){return b(this.toDate(),{locale:this.$L,utc:!1})};var M=x.parse;x.parse=function(i){i.utc&&(this.$u=!0),this.$utils().u(i.$offset)||(this.$offset=i.$offset),M.call(this,i)};var N=x.init;x.init=function(){if(this.$u){var i=this.$d;this.$y=i.getUTCFullYear(),this.$M=i.getUTCMonth(),this.$D=i.getUTCDate(),this.$W=i.getUTCDay(),this.$H=i.getUTCHours(),this.$m=i.getUTCMinutes(),this.$s=i.getUTCSeconds(),this.$ms=i.getUTCMilliseconds()}else N.call(this)};var D=x.utcOffset;x.utcOffset=function(i,m){var F=this.$utils().u;if(F(i))return this.$u?0:F(this.$offset)?D.call(this):this.$offset;if(typeof i=="string"&&(i=function(U){U===void 0&&(U="");var O=U.match(t);if(!O)return null;var J=(""+O[0]).match(h)||["-",0,0],u=J[0],W=60*+J[1]+ +J[2];return W===0?0:u==="+"?W:-W}(i),i===null))return this;var g=Math.abs(i)<=16?60*i:i,T=this;if(m)return T.$offset=g,T.$u=i===0,T;if(i!==0){var Z=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(T=this.local().add(g+Z,o)).$offset=g,T.$x.$localOffset=Z}else T=this.utc();return T};var p=x.format;x.format=function(i){var m=i||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return p.call(this,m)},x.valueOf=function(){var i=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*i},x.isUTC=function(){return!!this.$u},x.toISOString=function(){return this.toDate().toISOString()},x.toString=function(){return this.toDate().toUTCString()};var S=x.toDate;x.toDate=function(i){return i==="s"&&this.$offset?b(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():S.call(this)};var l=x.diff;x.diff=function(i,m,F){if(i&&this.$u===i.$u)return l.call(this,i,m,F);var g=this.local(),T=b(i).local();return l.call(g,T,m,F)}}})})(Bt);var Na=Bt.exports;const Ma=et(Na);var Ft={exports:{}};(function(a,n){(function(o,t){a.exports=t()})(Xe,function(){var o={year:0,month:1,day:2,hour:3,minute:4,second:5},t={};return function(h,f,c){var b,x=function(p,S,l){l===void 0&&(l={});var i=new Date(p),m=function(F,g){g===void 0&&(g={});var T=g.timeZoneName||"short",Z=F+"|"+T,U=t[Z];return U||(U=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:F,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:T}),t[Z]=U),U}(S,l);return m.formatToParts(i)},M=function(p,S){for(var l=x(p,S),i=[],m=0;m<l.length;m+=1){var F=l[m],g=F.type,T=F.value,Z=o[g];Z>=0&&(i[Z]=parseInt(T,10))}var U=i[3],O=U===24?0:U,J=i[0]+"-"+i[1]+"-"+i[2]+" "+O+":"+i[4]+":"+i[5]+":000",u=+p;return(c.utc(J).valueOf()-(u-=u%1e3))/6e4},N=f.prototype;N.tz=function(p,S){p===void 0&&(p=b);var l,i=this.utcOffset(),m=this.toDate(),F=m.toLocaleString("en-US",{timeZone:p}),g=Math.round((m-new Date(F))/1e3/60),T=15*-Math.round(m.getTimezoneOffset()/15)-g;if(!Number(T))l=this.utcOffset(0,S);else if(l=c(F,{locale:this.$L}).$set("millisecond",this.$ms).utcOffset(T,!0),S){var Z=l.utcOffset();l=l.add(i-Z,"minute")}return l.$x.$timezone=p,l},N.offsetName=function(p){var S=this.$x.$timezone||c.tz.guess(),l=x(this.valueOf(),S,{timeZoneName:p}).find(function(i){return i.type.toLowerCase()==="timezonename"});return l&&l.value};var D=N.startOf;N.startOf=function(p,S){if(!this.$x||!this.$x.$timezone)return D.call(this,p,S);var l=c(this.format("YYYY-MM-DD HH:mm:ss:SSS"),{locale:this.$L});return D.call(l,p,S).tz(this.$x.$timezone,!0)},c.tz=function(p,S,l){var i=l&&S,m=l||S||b,F=M(+c(),m);if(typeof p!="string")return c(p).tz(m);var g=function(O,J,u){var W=O-60*J*1e3,$=M(W,u);if(J===$)return[W,J];var L=M(W-=60*($-J)*1e3,u);return $===L?[W,$]:[O-60*Math.min($,L)*1e3,Math.max($,L)]}(c.utc(p,i).valueOf(),F,m),T=g[0],Z=g[1],U=c(T).utcOffset(Z);return U.$x.$timezone=m,U},c.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},c.tz.setDefault=function(p){b=p}}})})(Ft);var Sa=Ft.exports;const Ca=et(Sa);Rt.extend(Ma);Rt.extend(Ca);const wt=a=>a.toISOString().split("T")[0],ka=a=>{console.log("getInitialDailyProgress called with protocol:",a);const n={calories:2500,protein:180,carbs:300,fat:70,water:2500},o=(a==null?void 0:a.goals)||n;console.log("Extracted goals:",o);const t=o.calories||n.calories,h=o.protein||n.protein,f=o.carbs||n.carbs,c=o.fat||n.fat;return{calories:{remaining:t,consumed:0,target:t,unit:"kcal"},protein:{current:0,target:h,unit:"g"},carbs:{current:0,target:f,unit:"g"},fat:{current:0,target:c,unit:"g"},fiber:{current:0,target:32,unit:"g"},burned:0}};function Oa(){var pe,ye,be,je,we,oe,De,Ce,Ae,Ee,st,at,rt,nt,it,ot,ct,lt,dt;const a=It();fs();const[n,o]=H.useState(()=>{const k=new Date,re={timeZone:"America/Sao_Paulo",year:"numeric",month:"numeric",day:"numeric"},de=new Intl.DateTimeFormat("pt-BR",re).format(k),[ue,me,he]=de.split("/");return new Date(he,me-1,ue)}),t=wt(n),[h,f]=H.useState(!0),{data:c,isLoading:b,error:x}=ys(t),{data:M,isLoading:N,error:D}=js(t),{data:p,isLoading:S,error:l}=ws(t),{data:i,isLoading:m,error:F}=Ns(t),{data:g,isLoading:T,error:Z}=Ms(t,t),U=Ss(),O=Cs(),J=ks(),u=async()=>{if(f(!0),window.location.hash){const k=window.location.hash.substring(1),re=document.getElementById(k);if(k==="start-page"&&(I(!1),W()),k!=="start-page"&&(k==="meals-section"&&(I(!1),setRefreshNutrionalSummary(new Date().toISOString())),await new Promise(de=>setTimeout(de,500))),re){const ue=re.getBoundingClientRect().top+window.pageYOffset+-65;window.scrollTo({top:ue,behavior:"smooth"})}window.history.replaceState(null,"",window.location.pathname+window.location.search)}f(!1)},W=async()=>{try{const k=await ge.get("users/daily/workouts_activities",{searchParams:{date_start:t,date_end:t}});X(k.data||[])}catch(k){console.error("Error loading daily workouts and activities:",k),X([])}},$=b||N||S||m||T,L=k=>{U.mutate({...k,date:t})},te=k=>{O.mutate({...k,date:t})},R=k=>{J.mutate({activityId:k,date:t})};H.useEffect(()=>{f($)},[$]);const y=()=>{const k=new Date(n);k.setDate(n.getDate()-1),o(k)},j=()=>{const k=new Date(n);k.setDate(n.getDate()+1),k<=new Date&&o(k)},A=t===wt((()=>{const k=new Date,re={timeZone:"America/Sao_Paulo",year:"numeric",month:"numeric",day:"numeric"},de=new Intl.DateTimeFormat("pt-BR",re).format(k),[ue,me,he]=de.split("/");return new Date(he,me-1,ue)})()),[d,_]=H.useState(null),[r,s]=H.useState(!0),[w,C]=H.useState(!1),[E,P]=H.useState(!1),[B,I]=H.useState(!1),[q,Q]=H.useState(!1),[z,Y]=H.useState(!1),[V,X]=H.useState([]),[K,ae]=H.useState(""),[se,ne]=H.useState(()=>ka(d==null?void 0:d.protocol)),[ce,le]=H.useState(0);fa({initialCurrent:((pe=d==null?void 0:d.waterIntake)==null?void 0:pe.current)||0,goal:((ye=d==null?void 0:d.waterIntake)==null?void 0:ye.goal)||2500}),Pt();const ie=k=>{ee&&(console.log("Workout calories handling:",{includeCalories:k,workout:ee}),P(!1))},Me=(k,re)=>{console.log("Replace meal:",k,re)},Se=async k=>{try{const re=M==null?void 0:M.find(ue=>ue.id===k);if(!re)return;const de=await oa(re);console.log("Refresh suggestions for meal:",k,de)}catch(re){console.error("Error getting meal suggestions:",re)}},ee=H.useMemo(()=>((d==null?void 0:d.workouts)||[]).find(re=>re.completed&&new Date(re.date).toDateString()===new Date().toDateString()),[d==null?void 0:d.workouts]);return H.useEffect(()=>{var he,ve;const k=ee!=null&&ee.completed?(ee.duration||0)*8:0,de=((d==null?void 0:d.activities)||[]).reduce(($e,_t)=>$e+(_t.caloriesBurned||0),0),ue=((ve=(he=d==null?void 0:d.healthStats)==null?void 0:he.caloriesBurned)==null?void 0:ve.current)||0,me=k+de+ue;le(me),ne($e=>({...$e,burned:me}))},[]),H.useEffect(()=>{var me,he;if(!p)return;const k=p.reduce((ve,$e)=>ve+($e.caloriesBurned||0),0),re=ee!=null&&ee.duration?ee.duration*8:0,de=((he=(me=d==null?void 0:d.healthStats)==null?void 0:me.caloriesBurned)==null?void 0:he.current)||0,ue=k+re+de;le(ue),ne(ve=>({...ve,burned:ue}))},[p,ee,d]),G.useEffect(()=>{u()},[window.location.hash]),H.useEffect(()=>{W()},[t]),H.useEffect(()=>{K&&W()},[K]),e.jsxs(e.Fragment,{children:[h&&e.jsx(Pe,{}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{id:"start-page",className:"flex items-center justify-between px-2",children:[e.jsx("button",{onClick:y,className:"p-2 hover:bg-snapfit-green/20 rounded-lg transition-colors",children:e.jsx(xs,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-snapfit-dark-gray backdrop-blur-sm rounded-full border border-snapfit-green/30 shadow-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx($t,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-white",children:A?"Hoje":new Date(t+"T00:00:00").toLocaleDateString("pt-BR",{weekday:"long",day:"numeric",month:"long"})}),e.jsx("span",{className:"text-xs sm:text-sm text-gray-400",children:new Date(t+"T00:00:00").toLocaleDateString("pt-BR",{weekday:"short",day:"2-digit",month:"2-digit"}).replace(".","")})]})]}),e.jsx("button",{onClick:j,disabled:A,className:"p-2 hover:bg-snapfit-green/20 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:e.jsx(ps,{className:"w-6 h-6 text-snapfit-green"})})]}),e.jsx(ha,{caloriesConsumed:((be=g==null?void 0:g.calories)==null?void 0:be.consumed)||0,caloriesGoal:((je=g==null?void 0:g.calories)==null?void 0:je.target)||2e3,caloriesRemaining:((we=g==null?void 0:g.calories)==null?void 0:we.remaining)||0,caloriesBurned:((oe=g==null?void 0:g.calories)==null?void 0:oe.burned)||0,protein:{current:((De=g==null?void 0:g.protein)==null?void 0:De.current)||0,goal:((Ce=g==null?void 0:g.protein)==null?void 0:Ce.target)||150},carbs:{current:((Ae=g==null?void 0:g.carbs)==null?void 0:Ae.current)||0,goal:((Ee=g==null?void 0:g.carbs)==null?void 0:Ee.target)||220},fat:{current:((st=g==null?void 0:g.fat)==null?void 0:st.current)||0,goal:((at=g==null?void 0:g.fat)==null?void 0:at.target)||70}}),e.jsx($s,{day:t,goal:(i==null?void 0:i.goal)||2500,current:(i==null?void 0:i.current)||0,onAdd:()=>{},loadDailyData:()=>{},onShowHistory:()=>Q(!0),onShowSettings:()=>Y(!0)}),e.jsxs("div",{id:"meals-section",className:"bg-snapfit-gray backdrop-blur-sm rounded-xl sm:rounded-3xl shadow-lg p-3 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(qe,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-sm sm:text-base font-bold text-white",children:"Refeições do Dia"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>{I(!0)},className:"flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-black bg-snapfit-green rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:[e.jsx(Oe,{className:"w-3.5 h-3.5"}),"Pedir Sugestão"]}),e.jsxs("button",{onClick:()=>{const k=new CustomEvent("showSubstitutions");document.dispatchEvent(k)},className:"flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-white bg-snapfit-dark-gray rounded-full hover:bg-snapfit-green/20 hover:text-snapfit-green transition-colors",children:[e.jsx(Mt,{className:"w-3.5 h-3.5"}),"Ver Substituições"]})]})]}),e.jsx(ja,{day:t,reloadMeals:()=>{a.invalidateQueries({queryKey:["diary","meals",t]}),a.invalidateQueries({queryKey:["diary","nutrition",t]}),a.invalidateQueries({queryKey:["diary","entry",t]})}}),e.jsx("div",{className:"mt-4 mb-4",children:e.jsx(Ws,{onAdd:L})}),e.jsx(Bs,{meals:M||[],onComplete:k=>{console.log("Complete meal:",k)},onEdit:k=>{console.log("Edit meal:",k)},onCancel:k=>{console.log("Cancel meal:",k)},onReplaceMeal:Me,onRefreshSuggestions:Se})]}),e.jsx("div",{className:"space-y-4",children:e.jsx(Ts,{caloriesRemaining:((rt=g==null?void 0:g.calories)==null?void 0:rt.remaining)||0,proteinRemaining:Math.max(0,(((nt=g==null?void 0:g.protein)==null?void 0:nt.target)||0)-(((it=g==null?void 0:g.protein)==null?void 0:it.current)||0)),carbsRemaining:Math.max(0,(((ot=g==null?void 0:g.carbs)==null?void 0:ot.target)||0)-(((ct=g==null?void 0:g.carbs)==null?void 0:ct.current)||0)),fatRemaining:Math.max(0,(((lt=g==null?void 0:g.fat)==null?void 0:lt.target)||0)-(((dt=g==null?void 0:g.fat)==null?void 0:dt.current)||0)),mealsEaten:(M==null?void 0:M.map(k=>({name:k.name,time:k.meal_time})))||[],currentTime:new Date().toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",hour12:!1}),sleepQuality:"good",plannedMeals:[{name:"Jantar",time:"20:00",foods:[{name:"Frango grelhado",quantity:"150g",calories:250,protein:30,carbs:0,fat:8},{name:"Arroz integral",quantity:"100g",calories:130,protein:3,carbs:28,fat:1},{name:"Salada verde",quantity:"100g",calories:25,protein:2,carbs:5,fat:0}]}],plannedActivities:[{name:"Corrida",duration:"30min",caloriesBurned:300,time:"18:00"}],onActivityAdded:te,onMealAdded:L})}),B&&e.jsx(ca,{onClose:()=>I(!1),remainingMacros:{calories:Math.max(0,se.calories.remaining),protein:Math.max(0,se.protein.target-se.protein.current),carbs:Math.max(0,se.carbs.target-se.carbs.current),fat:Math.max(0,se.fat.target-se.fat.current)}}),e.jsx(qs,{stats:(d==null?void 0:d.healthStats)||{steps:{current:0,target:1e4},activeMinutes:{current:0,target:60},caloriesBurned:{current:0,target:500},heartRate:{current:72,min:60,max:120},source:"No Device"},dailyData:p||{}}),e.jsxs("div",{id:"workout-section",className:"bg-snapfit-gray backdrop-blur-sm rounded-xl shadow-lg p-3 sm:p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(He,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-sm sm:text-base font-bold text-white",children:"Treino e Atividades"})]}),e.jsx(va,{date_start:t,date_end:t}),ee&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("button",{onClick:()=>C(!w),className:"w-full flex items-center justify-between p-3 rounded-lg bg-snapfit-dark-gray hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 rounded-full bg-snapfit-green/20 border border-snapfit-green/30",children:e.jsx(He,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xs font-medium text-white",children:ee.name}),e.jsxs("div",{className:"text-[10px] text-gray-400",children:[ee.exercises.length," exercícios • ",ee.duration," min • ",ee.duration*8," kcal"]})]})]}),e.jsx(bs,{className:`w-4 h-4 text-snapfit-green transition-transform ${w?"rotate-180":""}`})]}),w&&e.jsx("div",{className:"mt-3 space-y-2",children:ee.exercises.map(k=>e.jsx("div",{className:`p-3 rounded-lg ${k.completed?"bg-snapfit-green/10 border border-snapfit-green/30":"bg-snapfit-dark-gray border border-snapfit-green/10"}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs font-medium text-white",children:k.name}),e.jsxs("span",{className:"text-xs text-gray-400",children:[k.sets,"x",k.reps," • ",k.weight,"kg"]})]})},k.id))})]}),e.jsxs("div",{className:"border-t pt-6",children:[e.jsx(_s,{activities:p||[],onAddActivity:k=>{ae(Date.now().toString())},onRemoveActivity:R}),(V==null?void 0:V.length)>0&&e.jsx("div",{className:"space-y-2 mt-3",children:V==null?void 0:V.map(k=>e.jsxs("div",{className:"flex justify-between items-center p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/10 rounded-full flex items-center justify-center border border-snapfit-green/20",children:e.jsx(_e,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:k==null?void 0:k.name}),e.jsxs("div",{className:"text-xs text-gray-400",children:[k==null?void 0:k.activity_time," min"]})]})]}),e.jsxs("div",{className:"text-snapfit-green font-medium",children:[k==null?void 0:k.kcal," kcal"]})]},k==null?void 0:k.id))})]})]}),E&&ee&&e.jsx(ma,{workout:ee,onClose:()=>P(!1),onConfirm:ie}),q&&e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Hidratação"}),e.jsx("button",{onClick:()=>Q(!1),className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(xe,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:e.jsx(xa,{days:14})})]})}),e.jsx(ba,{isOpen:z,onClose:()=>Y(!1)})]}),e.jsx(ya,{})]})}export{Oa as DiaryPage};
