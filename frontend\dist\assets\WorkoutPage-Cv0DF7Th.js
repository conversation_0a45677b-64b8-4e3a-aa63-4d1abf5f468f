import{c as De,R as L,j as e,I as Q,f as se,aa as oe,ab as ae,r as b,l as re,ac as ce,m as Ee,ad as Te,b as K,t as ve,ae as xe,af as He,ag as qe,a7 as we,n as de,ah as Fe,ai as Me,p as me,aj as $e,ak as Ve,B as X,al as We,am as Oe,a as Ge,C as be,an as Ue,ao as Se,G as ee,ap as Y,a8 as he,aq as Qe,ar as Ke,as as Je,h as ge,u as _e,at as Ze,g as ye,Q as Ne,au as te,av as ke,Z as Xe,aw as Ye,ax as es,F as Re,ay as Ae,az as ss,aA as ts,aB as as,aC as rs,y as ns,aD as is,L as ls,aE as Ce,aF as os,P as cs,aG as ds,aH as ms,aI as xs,aJ as gs}from"./index-D0i1bWZj.js";import{f as hs}from"./date-DOfZ7AVQ.js";import{P as us}from"./ProtocolDetailsModal-DS3WbAej.js";import{D as je}from"./download-Aq5FG7bp.js";import{S as Z}from"./StatCard-BTQ2dzW7.js";import{C as ue}from"./CircularProgress-BEoiBbG4.js";import{u as ps}from"./useWorkoutProtocol-DKr0uMl-.js";import"./workoutService-Cfz7oK3X.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Le=De("house",fs);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bs=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],pe=De("lightbulb",bs);function js({protocolName:t,protocolStatus:s,isCurrentProtocol:r,showProtocolName:u=!0,size:m="md",className:x=""}){const i=()=>{if(r)return{color:"bg-snapfit-green/20 text-snapfit-green border-snapfit-green/30",icon:e.jsx(Q,{className:"w-3 h-3"}),label:"Protocolo Atual",textColor:"text-snapfit-green"};switch(s){case"completed":return{color:"bg-blue-500/20 text-blue-400 border-blue-500/30",icon:e.jsx(ae,{className:"w-3 h-3"}),label:"Protocolo Finalizado",textColor:"text-blue-400"};case"archived":return{color:"bg-gray-500/20 text-gray-400 border-gray-500/30",icon:e.jsx(oe,{className:"w-3 h-3"}),label:"Protocolo Arquivado",textColor:"text-gray-400"};default:return{color:"bg-orange-500/20 text-orange-400 border-orange-500/30",icon:e.jsx(se,{className:"w-3 h-3"}),label:"Protocolo Anterior",textColor:"text-orange-400"}}},v=()=>{switch(m){case"sm":return"px-2 py-1 text-xs";case"lg":return"px-4 py-2 text-sm";default:return"px-3 py-1.5 text-xs"}},y=i();return e.jsxs("div",{className:`inline-flex items-center gap-1.5 rounded-full border ${y.color} ${v()} ${x}`,children:[y.icon,e.jsx("span",{className:"font-medium",children:u?t:y.label})]})}function vs({protocolName:t,protocolStatus:s,protocolObjective:r,protocolSplit:u,protocolFrequency:m,startedAt:x,endedAt:i,children:v}){const[y,l]=L.useState(!1),w=C=>C?new Date(C).toLocaleDateString("pt-BR"):"";return e.jsxs("div",{className:"relative inline-block",onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),children:[v,y&&e.jsxs("div",{className:"absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 p-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg shadow-lg",children:[e.jsxs("div",{className:"text-white text-sm space-y-2",children:[e.jsx("div",{className:"font-semibold text-snapfit-green",children:t}),r&&e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-400",children:"Objetivo:"}),e.jsx("div",{className:"text-xs text-gray-300",children:r})]}),e.jsxs("div",{className:"flex gap-4 text-xs",children:[u&&e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-400",children:"Divisão:"}),e.jsx("div",{className:"text-white",children:u})]}),m&&e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-400",children:"Frequência:"}),e.jsxs("div",{className:"text-white",children:[m,"x/semana"]})]})]}),e.jsxs("div",{className:"text-xs border-t border-gray-600 pt-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Início:"}),e.jsx("span",{className:"text-white",children:w(x)})]}),i&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Fim:"}),e.jsx("span",{className:"text-white",children:w(i)})]})]})]}),e.jsx("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-snapfit-dark-gray"})]})]})}function ys({hasActiveProtocol:t=!1,onReuseProtocol:s}){const[r,u]=b.useState([]),[m,x]=b.useState(!0),[i,v]=b.useState(!1),[y,l]=b.useState(null),[w,C]=b.useState({page:1,limit:10,total:0,totalPages:0}),[F,p]=b.useState(!1),A=o=>{if(o<60)return`${o}min`;const a=Math.floor(o/60),c=o%60;return`${a}h${c>0?` ${c}min`:""}`},E=async(o=1,a=!1)=>{try{o===1?(x(!0),l(null)):v(!0);const d=(await K.get("users/workouts/history",{searchParams:{period:"month",page:o.toString(),limit:w.limit.toString()}})).data,g=(d==null?void 0:d.workouts)||[];u(a?T=>[...T,...g]:g),d!=null&&d.pagination&&(C(d.pagination),p(d.pagination.page<d.pagination.totalPages))}catch(c){console.error("Error fetching workout history:",c),l("Erro ao carregar histórico de treinos")}finally{x(!1),v(!1)}},S=()=>{!i&&F&&E(w.page+1,!0)};return b.useEffect(()=>{E()},[]),m&&r.length===0?e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Carregando histórico..."})]})}):y?e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 text-center",children:[e.jsx("p",{className:"text-red-600 text-sm",children:y}),e.jsx("button",{onClick:()=>E(),className:"mt-2 text-red-700 underline text-xs",children:"Tentar novamente"})]})}):r.length===0?e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Q,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhum treino encontrado"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Seus treinos realizados aparecerão aqui"})]})}):e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(re,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[w.total," treino",w.total!==1?"s":""," encontrado",w.total!==1?"s":""]})]})]}),e.jsxs("button",{onClick:()=>E(),disabled:m,className:"flex items-center gap-2 px-3 py-2 bg-snapfit-green/20 text-snapfit-green rounded-lg border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors disabled:opacity-50",children:[e.jsx(ce,{className:`w-4 h-4 ${m?"animate-spin":""}`}),e.jsx("span",{className:"text-sm",children:"Atualizar"})]})]}),e.jsx("div",{className:"space-y-3",children:r.map(o=>e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-start justify-between gap-3 mb-3",children:[e.jsxs("div",{className:"flex items-start gap-3 flex-1",children:[e.jsx("div",{className:`p-2 rounded-full ${o.completed?"bg-snapfit-green/20 border border-snapfit-green/30":"bg-orange-400/10 border border-orange-400/30"}`,children:e.jsx(Q,{className:`w-5 h-5 ${o.completed?"text-snapfit-green":"text-orange-400"}`})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-white text-sm sm:text-base",children:o.workout_name||"Treino"}),e.jsx("p",{className:"text-gray-400 text-xs sm:text-sm",children:hs(o.date)}),(!t||!o.is_current_protocol)&&e.jsx("div",{className:"mt-2",children:e.jsx(vs,{protocolName:o.protocol_name,protocolStatus:o.protocol_status,protocolObjective:o.objective,protocolSplit:o.split,protocolFrequency:o.protocol_frequency,startedAt:o.protocol_started_at,endedAt:o.protocol_ended_at,children:e.jsx(js,{protocolName:o.protocol_name,protocolStatus:o.protocol_status,isCurrentProtocol:o.is_current_protocol,showProtocolName:!t,size:"sm"})})})]})]}),o.completed&&e.jsxs("span",{className:"flex items-center gap-1 px-2 py-1 bg-snapfit-green/20 text-snapfit-green rounded-full text-xs border border-snapfit-green/30 whitespace-nowrap",children:[e.jsx(Ee,{className:"w-3 h-3"}),e.jsx("span",{children:"Concluído"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-3 text-xs sm:text-sm",children:[e.jsxs("div",{className:"text-center p-3 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Volume"}),e.jsxs("div",{className:"font-semibold text-white",children:[o.total_weight||0,"kg"]})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Duração"}),e.jsx("div",{className:"font-semibold text-white",children:A(o.duration||0)})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Calorias"}),e.jsx("div",{className:"font-semibold text-white",children:o.total_calories||0})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Status"}),e.jsx("div",{className:`font-semibold ${o.completed?"text-snapfit-green":"text-orange-400"}`,children:o.completed?"Concluído":"Pendente"})]})]})]},o.session_id))}),F&&e.jsx("div",{className:"mt-6 text-center",children:e.jsx("button",{onClick:S,disabled:i,className:"flex items-center gap-2 mx-auto px-6 py-3 bg-snapfit-green/20 text-snapfit-green rounded-lg border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors disabled:opacity-50",children:i?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-snapfit-green"}),e.jsx("span",{children:"Carregando..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(Te,{className:"w-4 h-4"}),e.jsx("span",{children:"Carregar mais treinos"})]})})}),w.totalPages>1&&e.jsxs("div",{className:"mt-4 text-center text-xs text-gray-400",children:["Página ",w.page," de ",w.totalPages," • ",w.total," treinos no total"]})]})}function Ns({type:t,onReuseProtocol:s,onViewDetails:r}){ve();const u=i=>{if(console.log("🔍 EnhancedProtocolHistory: Protocolo selecionado",i),console.log("🔍 EnhancedProtocolHistory: Protocol ID:",i==null?void 0:i.id),console.log("🔍 EnhancedProtocolHistory: Protocol structure:",Object.keys(i||{})),console.log("🔍 EnhancedProtocolHistory: onViewDetails function:",r),r){const v={id:(i==null?void 0:i.id)||(i==null?void 0:i.mock_id)||"unknown",name:(i==null?void 0:i.name)||"Protocolo sem nome",type:(i==null?void 0:i.objective)||"workout",objective:(i==null?void 0:i.objective)||"Não especificado",startDate:(i==null?void 0:i.started_at)||new Date().toISOString(),endDate:(i==null?void 0:i.ended_at)||null,status:(i==null?void 0:i.status)||"unknown",...i};console.log("🔍 EnhancedProtocolHistory: Transformed protocol:",v),r(v)}else console.error("❌ EnhancedProtocolHistory: onViewDetails function not available")},m=i=>{if(console.log("📋 EnhancedProtocolHistory: Protocolo duplicado",i),s){const v={id:(i==null?void 0:i.id)||(i==null?void 0:i.mock_id)||"unknown",name:(i==null?void 0:i.name)||"Protocolo sem nome",type:(i==null?void 0:i.objective)||"workout",objective:(i==null?void 0:i.objective)||"Não especificado",startDate:(i==null?void 0:i.started_at)||new Date().toISOString(),endDate:(i==null?void 0:i.ended_at)||null,status:(i==null?void 0:i.status)||"unknown",...i};s(v,!1)}},x=i=>{console.log("🏁 EnhancedProtocolHistory: Protocolo finalizado",i)};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(xe,{className:"w-6 h-6 text-snapfit-green"}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-white",children:["Histórico de Protocolos de ",t==="diet"?"Dieta":"Treino"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Visualize, duplique e gerencie seus protocolos anteriores com dados reais do banco de dados"})]})]})}),e.jsx(He,{protocolType:t,onProtocolSelect:u,onProtocolDuplicate:m,onProtocolFinish:x})]})}function ws({onReuseProtocol:t,defaultTab:s="sessions",hasActiveProtocol:r=!1}){const u=s,[m,x]=b.useState(u),[i,v]=b.useState(null),[y,l]=b.useState(!1),w=p=>{console.log("🔍 WorkoutHistoryTabs: Protocol selected for viewing:",p),v(p.id),l(!0)},C=(p,A)=>{t&&t({...p,edit:A})},F=[{id:"sessions",label:"Treinos Realizados",icon:Q,description:r?"Histórico das suas sessões de treino concluídas":"Histórico de todos os seus treinos realizados"},{id:"protocols",label:r?"Protocolos Anteriores":"Histórico de Protocolos",icon:xe,description:r?"Visualize, reutilize e gerencie seus protocolos anteriores":"Visualize, reutilize e gerencie todos os seus protocolos"}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"p-4 sm:p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(re,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"})]}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-2",children:F.map(p=>{const A=p.icon,E=m===p.id;return e.jsxs("button",{onClick:()=>x(p.id),className:`flex items-center gap-3 p-3 sm:p-4 rounded-lg transition-all duration-200 text-left flex-1 ${E?"bg-snapfit-green/20 border border-snapfit-green/30 text-white":"bg-snapfit-dark-gray border border-snapfit-green/10 text-gray-300 hover:bg-snapfit-green/10 hover:text-white"}`,children:[e.jsx("div",{className:`p-2 rounded-lg ${E?"bg-snapfit-green/30":"bg-snapfit-green/10"}`,children:e.jsx(A,{className:`w-5 h-5 ${E?"text-snapfit-green":"text-gray-400"}`})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:p.label}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:p.description})]})]},p.id)})})]})}),e.jsxs("div",{className:"min-h-[400px]",children:[m==="sessions"&&e.jsx("div",{className:"animate-fade-in",children:e.jsx(ys,{hasActiveProtocol:r,onReuseProtocol:t})}),m==="protocols"&&e.jsx("div",{className:"animate-fade-in",children:e.jsx(Ns,{type:"workout",onReuseProtocol:C,onViewDetails:w})})]}),i&&e.jsx(us,{protocolId:i,type:"workout",isOpen:y,onClose:()=>{console.log("🔍 WorkoutHistoryTabs: Modal close triggered"),l(!1),v(null)},onReuseProtocol:C})]})}const Ss=`
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
`;if(typeof document<"u"){const t=document.createElement("style");t.textContent=Ss,document.head.appendChild(t)}const ks=({exercise:t})=>{const[s,r]=b.useState(!0);return e.jsx(e.Fragment,{children:e.jsx("div",{className:"pb-5",children:e.jsxs("div",{className:"p-3 sm:p-4 rounded-lg bg-snapfit-dark-gray border border-snapfit-gray/30",children:[e.jsxs("button",{onClick:()=>r(!s),className:"flex items-center justify-between w-full gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[e.jsx("div",{className:"w-6 h-6 rounded-full flex items-center justify-center bg-snapfit-dark-gray text-gray-400 border border-snapfit-gray/30",children:e.jsx(Te,{className:`w-3 h-3 transition-transform ${s?"":"rotate-180"}`})}),e.jsx("h3",{className:"text-sm font-medium text-white truncate max-w-[180px]",children:t.name})]}),e.jsxs("div",{className:"text-xs text-gray-400 flex-shrink-0 bg-snapfit-dark-gray px-2 py-1 rounded-full border border-snapfit-gray/30",children:[t.sets,"x",t.reps," • ",t.rest_seconds,"s"]})]}),!s&&e.jsxs("div",{className:"mt-4 space-y-4 animate-fade-in",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-2 text-center bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Séries"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.sets})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Reps"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.reps})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"RPE"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.rpe})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Descanso"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[t.rest_seconds,"s"]})]})]}),t.notes&&e.jsxs("div",{className:"flex items-start gap-2 p-2 sm:p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsx(qe,{className:"w-4 h-4 text-snapfit-green mt-0.5"}),e.jsx("p",{className:"text-xs text-gray-300",children:t.notes})]}),t.gif_url&&e.jsx("div",{className:"rounded-lg overflow-hidden border border-snapfit-green/20",children:e.jsx("img",{src:t.gif_url,alt:t.name,className:"w-full h-48 object-cover"})})]})]})})})};function As({protocolId:t,protocolName:s,startDate:r,splitInfo:u,frequency:m,objective:x,completedWorkouts:i,notes:v,workouts:y,selectedWorkout:l,onSelectWorkout:w,onGenerateNewProtocol:C,onEditNotes:F,workoutsDb:p,onDeleteProtocol:A}){var d;ve();const[E,S]=L.useState(!1),o=g=>{switch(g){case"hypertrophy":return"Hipertrofia";case"weight-loss":return"Emagrecimento";case"maintenance":return"Manutenção";case"strength":return"Força";default:return g}},[a,c]=L.useState(0);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"card p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-snapfit-dark-gray flex items-center justify-center text-snapfit-green border border-snapfit-green/30",children:e.jsx(re,{className:"w-5 h-5"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h2",{className:"text-lg font-semibold text-white",children:s||"Protocolo de Treino"}),e.jsx("div",{className:"inline-block px-3 py-1 bg-snapfit-green text-black rounded-full text-xs font-bold",children:"Protocolo Atual"})]}),e.jsxs("p",{className:"text-sm text-gray-400 mt-1",children:["Início: ",new Date(r).toLocaleDateString()]})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(we,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Divisão"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:u})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(re,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Frequência"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:m})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(de,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Objetivo"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:o(x)})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(we,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Treinos"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:i})]})]}),v&&e.jsxs("div",{className:"border-t border-snapfit-gray/30 pt-4 sm:pt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-snapfit-dark-gray flex items-center justify-center text-snapfit-green border border-snapfit-green/30",children:e.jsx(Fe,{className:"w-4 h-4"})}),e.jsx("h3",{className:"text-sm sm:text-base font-medium text-white",children:"Observações do Coach"})]}),e.jsx("button",{onClick:F,className:"text-sm text-snapfit-green hover:text-snapfit-green/80 hidden",children:"Editar"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-400 whitespace-pre-wrap bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-gray/30",children:v})]}),e.jsxs("div",{className:"border-t border-snapfit-gray/30 pt-4 sm:pt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-base font-medium text-white",children:"Treinos"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{const g=p.length;a==0?c(g-1):c(a-1)},className:"p-2 text-gray-400 hover:text-snapfit-green bg-snapfit-dark-gray hover:bg-snapfit-dark-gray rounded-full transition-colors hidden sm:block border border-snapfit-gray/30",children:e.jsx(Me,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>{const g=p.length;a==g-1?c(0):c(a+1)},className:"p-2 text-gray-400 hover:text-snapfit-green bg-snapfit-dark-gray hover:bg-snapfit-dark-gray rounded-full transition-colors hidden sm:block border border-snapfit-gray/30",children:e.jsx(me,{className:"w-4 h-4"})})]})]}),e.jsx("div",{className:"flex gap-2 overflow-x-auto pb-4 scrollbar-hide",children:p.map((g,T)=>e.jsx("button",{onClick:()=>{c(T)},className:`flex-none px-3 py-1.5 text-xs sm:text-sm rounded-full transition-colors whitespace-nowrap ${a===T?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-gray/30"}`,children:g==null?void 0:g.name},T))})]})]}),e.jsxs("div",{className:"card p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("div",{className:"inline-block px-3 py-1 bg-snapfit-green text-black rounded-full text-xs font-bold mb-2",children:"Treino"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:(d=p[a])==null?void 0:d.name}),e.jsxs("span",{className:"text-sm text-gray-400",children:[p[a].exercises.length," ",p[a].exercises.length>1?"exercícios":"exercício"]})]})}),e.jsx("div",{className:"space-y-4",children:p[a].exercises.map((g,T)=>e.jsx(ks,{exercise:g},g.id))})]}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs("button",{className:"flex justify-between items-center gap-1 text-sm text-gray-400 hover:text-snapfit-green transition-colors p-2 rounded-full bg-snapfit-dark-gray border border-snapfit-gray/30",onClick:A,children:[e.jsx($e,{className:"w-4 h-4"})," ",e.jsx("span",{children:"Remover Protocolo"})]})}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsxs("button",{onClick:()=>{console.log("🎯 UnifiedWorkoutCard: Iniciar Treino button clicked"),console.log("🔍 UnifiedWorkoutCard: Opening WorkoutSelectionModal"),console.log("🔍 UnifiedWorkoutCard: Using CONSISTENT workflow with Add (+) button"),S(!0)},className:"flex items-center justify-center gap-2 px-6 py-3 w-full sm:w-auto bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95 font-bold","data-testid":"unified-workout-card-start-button",title:"Iniciar Treino - Abre seleção de treinos",children:[e.jsx("span",{children:"Iniciar Treino"}),e.jsx(me,{className:"w-5 h-5"})]})}),E&&e.jsx(Ve,{onClose:()=>{console.log("🔍 UnifiedWorkoutCard: Closing WorkoutSelectionModal"),S(!1)}})]})}function Cs({onGenerateAI:t,onCreateManual:s,onImportFromCoach:r,onReadProtocol:u,onClose:m}){return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Novo Protocolo de Treino"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("button",{onClick:t,className:`\r
            w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10`,children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(X,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Gerar com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Gerar protocolo personalizado baseado no seu perfil"})]})]}),e.jsxs("button",{onClick:s,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Fe,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Criar Manualmente"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Monte seu próprio protocolo selecionando exercícios"})]})]}),u&&e.jsxs("button",{onClick:u,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(We,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Ler seu protocolo atual com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Faça upload de um PDF ou imagem do seu protocolo atual"})]})]}),e.jsxs("button",{onClick:r,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(je,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Importar do Coach"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Importar protocolo preparado pelo seu coach"})]})]})]}),e.jsx("button",{onClick:m,className:"w-full mt-6 px-4 py-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors border border-snapfit-green/20",children:"Cancelar"})]})})}async function Ps(){const t=Oe();if(!t.isAuthenticated)throw new Error("User must be authenticated to fetch protocols");try{const s=await fetch("https://api.mysnapfit.com.br/protocols/coach",{headers:{Authorization:`Bearer ${t.token}`}});if(!s.ok)throw new Error("Failed to fetch coach protocols");return s.json()}catch(s){throw console.error("Error fetching coach protocols:",s),s}}function Ds({onImport:t,onCancel:s}){const[r,u]=L.useState([]),[m,x]=L.useState(!0),[i,v]=L.useState(null);return L.useEffect(()=>{async function y(){try{const l=await Ps();u(l)}catch(l){v(l instanceof Error?l.message:"Erro ao carregar protocolos")}finally{x(!1)}}y()},[]),m?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsx(Ge,{className:"w-8 h-8 text-indigo-600 animate-spin"})}):i?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx("div",{className:"text-red-600 mb-4",children:i}),e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Voltar"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(je,{className:"w-6 h-6 text-indigo-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Importar do Coach"})]}),r.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-gray-600 mb-4",children:"Nenhum protocolo disponível para importação."}),e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Voltar"})]}):e.jsxs("div",{className:"space-y-4",children:[r.map(y=>{var l,w,C;return e.jsxs("button",{onClick:()=>t(y),className:"w-full flex items-center justify-between p-4 bg-white rounded-lg hover:bg-gray-50 transition-colors text-left",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-800",children:y.name}),e.jsxs("div",{className:"flex items-center gap-4 mt-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{src:(l=y.coach)==null?void 0:l.photo,alt:(w=y.coach)==null?void 0:w.name,className:"w-6 h-6 rounded-full"}),e.jsx("span",{className:"text-sm text-gray-600",children:(C=y.coach)==null?void 0:C.name})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsx(re,{className:"w-4 h-4"}),e.jsx("span",{children:new Date(y.createdAt).toLocaleDateString()})]})]})]}),e.jsx(je,{className:"w-5 h-5 text-gray-400"})]},y.id)}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"})})]})]})}function fe({data:t,title:s,subtitle:r,height:u=200,showLegend:m=!0,type:x="bar",className:i="",animate:v=!0}){if(!t||!Array.isArray(t)||t.length===0)return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 border border-snapfit-green/20 ${i}`,children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:s}),e.jsx("div",{className:"flex items-center justify-center h-32 text-gray-400",children:"Nenhum dado disponível"})]});const y=t.map(d=>d.value).filter(d=>typeof d=="number"&&!isNaN(d)&&isFinite(d));if(y.length===0)return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 border border-snapfit-green/20 ${i}`,children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:s}),e.jsx("div",{className:"flex items-center justify-center h-32 text-gray-400",children:"Dados inválidos"})]});const l=Math.max(...y),w=Math.min(...y),C=y.reduce((d,g)=>d+g,0)/y.length,F=l-w;let p,A;if(F===0)l===0?(A=0,p=1):(A=Math.max(0,l*.8),p=l*1.2);else if(x==="bar")if(F<l*.2){const d=w*.8;if(A=Math.max(0,d),p=l*1.1,p-A<l*.3){const g=(l+w)/2,T=Math.max(l*.15,F*2);A=Math.max(0,g-T),p=g+T}}else{const d=Math.max(F*.05,l*.05);p=l+d,A=Math.max(0,w-d)}else{const d=F>0?F*.1:l*.1;p=l+d,A=Math.max(0,w-d)}const E=["#B9FF43","#66B100","#1A3201","#4CAF50","#8BC34A"],S=b.useRef(t.map(()=>0)),o=b.useRef(0);b.useEffect(()=>{S.current=t.map(()=>0)},[t]),b.useEffect(()=>{if(v&&x==="bar"){const d=()=>{let g=!1;const T=S.current.map((f,h)=>{if(!t[h]||typeof t[h].value!="number"||!isFinite(t[h].value))return f;const n=p-A,$=(n>0?(t[h].value-A)/n:0)*100;if(!isFinite($))return f;const R=$-f;return Math.abs(R)>.5?(g=!0,f+R*.1):$});S.current=T,g&&(o.current=requestAnimationFrame(d))};return o.current=requestAnimationFrame(d),()=>{cancelAnimationFrame(o.current)}}},[t,p,A,v,x]);const a=()=>e.jsx("div",{className:"flex items-end h-full gap-1 sm:gap-2 px-1",children:t.map((d,g)=>{const T=typeof d.value=="number"&&!isNaN(d.value)&&isFinite(d.value)?d.value:0,f=p-A,h=f>0?(T-A)/f:0,n=v?S.current[g]??h*100:h*100,P=d.color||E[g%E.length];return e.jsxs("div",{className:"flex flex-col items-center flex-1",children:[e.jsx("div",{className:"relative w-full h-full flex flex-col justify-end",children:e.jsxs("div",{className:"w-full rounded-lg relative group overflow-hidden",style:{height:`${n}%`,minHeight:"4px",boxShadow:`0 0 10px ${P}40`},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t",style:{backgroundImage:`linear-gradient(to top, ${P}, ${P}80)`}}),e.jsx("div",{className:"absolute -top-8 left-1/2 -translate-x-1/2 bg-snapfit-dark-gray text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap border border-snapfit-green/30",children:d.value})]})}),e.jsx("span",{className:"text-xs mt-2 text-gray-400 truncate max-w-full text-center",children:d.label})]},g)})}),c=()=>{const d=t.map((f,h)=>{const n=typeof f.value=="number"&&!isNaN(f.value)&&isFinite(f.value)?f.value:0,P=t.length>1?h/(t.length-1)*100:50,$=p-A,j=100-($>0?(n-A)/$:0)*100;return{x:isFinite(P)?P:50,y:isFinite(j)?j:50,...f,value:n}});let g="",T="";return d.forEach((f,h)=>{if(h===0)g+=`M${f.x},${f.y}`,T+=`M${f.x},100 L${f.x},${f.y}`;else{const n=d[h-1],P=n.x+(f.x-n.x)/2,$=n.x+(f.x-n.x)/2;g+=` C${P},${n.y} ${$},${f.y} ${f.x},${f.y}`,T+=` L${f.x},${f.y}`}}),x==="area"&&(T+=` L${d[d.length-1].x},100 L${d[0].x},100 Z`),e.jsxs("div",{className:"h-full w-full px-2 pt-4 pb-6 relative",children:[e.jsxs("svg",{width:"100%",height:"100%",viewBox:"0 0 100 100",preserveAspectRatio:"none",children:[e.jsx("line",{x1:"0",y1:"0",x2:"100",y2:"0",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"25",x2:"100",y2:"25",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"50",x2:"100",y2:"50",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"75",x2:"100",y2:"75",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"100",x2:"100",y2:"100",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5"}),x==="area"&&e.jsx("path",{d:T,fill:"url(#areaGradient)",opacity:"0.2"}),e.jsx("path",{d:g,fill:"none",stroke:"#B9FF43",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),d.map((f,h)=>e.jsxs("g",{className:"group",children:[e.jsx("circle",{cx:f.x,cy:f.y,r:"4",fill:"transparent",stroke:"#B9FF4330",strokeWidth:"4",className:"transition-all duration-300 group-hover:stroke-opacity-50 group-hover:r-6"}),e.jsx("circle",{cx:f.x,cy:f.y,r:"3",fill:"#B9FF43",stroke:"#000",strokeWidth:"1",className:"transition-all duration-300 group-hover:r-4"}),e.jsx("circle",{cx:f.x,cy:f.y,r:"10",fill:"transparent",className:"cursor-pointer"}),e.jsxs("g",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:[e.jsx("rect",{x:f.x-25,y:f.y-30,width:"50",height:"22",rx:"11",fill:"#1E1E1E",stroke:"#B9FF4330",strokeWidth:"1"}),e.jsx("text",{x:f.x,y:f.y-16,textAnchor:"middle",fill:"#B9FF43",fontSize:"10",fontWeight:"bold",children:f.value})]})]},h)),e.jsx("defs",{children:e.jsxs("linearGradient",{id:"areaGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:"#B9FF43",stopOpacity:"0.8"}),e.jsx("stop",{offset:"100%",stopColor:"#B9FF43",stopOpacity:"0.1"})]})})]}),e.jsx("div",{className:"flex justify-between absolute bottom-0 left-2 right-2",children:t.map((f,h)=>e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate max-w-[40px] text-center",children:f.label},h))})]})};return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl p-4 sm:p-5 ${i}`,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:s}),r&&e.jsx("p",{className:"text-gray-400 text-sm mt-1",children:r})]}),e.jsx("div",{style:{height:`${u}px`},className:"mt-2",children:x==="bar"?a():c()}),m&&e.jsx("div",{className:"mt-3 pt-3 border-t border-snapfit-gray/30",children:x==="line"||x==="bar"&&t.length>3?e.jsxs("div",{className:"flex flex-wrap gap-4 justify-center text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-red-400"}),e.jsxs("span",{className:"text-gray-300",children:["Máx: ",l.toFixed(0)]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-400"}),e.jsxs("span",{className:"text-gray-300",children:["Mín: ",w.toFixed(0)]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-snapfit-green"}),e.jsxs("span",{className:"text-gray-300",children:["Média: ",C.toFixed(0)]})]}),x==="bar"&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-yellow-400"}),e.jsxs("span",{className:"text-gray-300",children:["Total: ",t.reduce((d,g)=>d+g.value,0).toFixed(0)]})]})]}):e.jsx("div",{className:"flex flex-wrap gap-3",children:t.map((d,g)=>{const T=d.color||E[g%E.length];return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:T}}),e.jsxs("span",{className:"text-xs text-gray-400",children:[d.label,": ",d.value]})]},g)})})})]})}const Ie=()=>{const[t,s]=b.useState([]),[r,u]=b.useState([]),[m,x]=b.useState(!1),[i,v]=b.useState(!1),[y,l]=b.useState(null),w={id:"analysis_1",videoId:"video_1",exerciseName:"Agachamento",overallScore:78,analysisDate:new Date().toISOString(),postureScore:85,rangeOfMotionScore:72,stabilityScore:80,speedScore:75,improvementPoints:[{id:"imp_1",category:"posture",severity:"medium",description:"Joelhos tendendo a cair para dentro durante a descida",bodyPart:"Joelhos",timeInVideo:15},{id:"imp_2",category:"range_of_motion",severity:"low",description:"Pode descer um pouco mais para ativar melhor os glúteos",bodyPart:"Quadris",timeInVideo:18}],injuryRisks:[{id:"risk_1",riskLevel:"medium",bodyPart:"Joelhos",description:"Valgo dinâmico pode causar sobrecarga nos ligamentos",preventionTips:["Fortalecer glúteo médio","Melhorar mobilidade de tornozelo","Praticar agachamento com elástico"],relatedMovements:["Afundo","Step-up","Agachamento búlgaro"]}],correctionSuggestions:[{id:"corr_1",step:1,title:"Posicionamento dos pés",description:"Mantenha os pés ligeiramente mais afastados que a largura dos ombros",focusArea:"Base de apoio",difficulty:"easy"},{id:"corr_2",step:2,title:"Ativação do core",description:"Contraia o abdômen antes de iniciar o movimento",focusArea:"Estabilização",difficulty:"medium"}],preparatoryExercises:[{id:"prep_1",name:"Agachamento na parede",description:"Agachamento com apoio das costas na parede",sets:3,reps:"10-15",purpose:"Aprender o padrão de movimento correto"},{id:"prep_2",name:"Ponte de glúteo",description:"Fortalecimento dos glúteos em decúbito dorsal",sets:3,reps:"15-20",purpose:"Fortalecer glúteos para melhor estabilização"}]},C=async(S,o,a)=>{try{x(!0),l(null),await new Promise(d=>setTimeout(d,2e3));const c={id:`video_${Date.now()}`,exerciseName:o,angle:a,videoFile:S,uploadDate:new Date().toISOString(),duration:30,analysisStatus:"pending"};return s(d=>[c,...d]),await F(c.id),c}catch(c){throw l(c instanceof Error?c.message:"Erro no upload"),c}finally{x(!1)}},F=async S=>{try{v(!0),l(null),s(a=>a.map(c=>c.id===S?{...c,analysisStatus:"analyzing"}:c)),await new Promise(a=>setTimeout(a,5e3));const o={...w,id:`analysis_${Date.now()}`,videoId:S,analysisDate:new Date().toISOString()};return u(a=>[o,...a]),s(a=>a.map(c=>c.id===S?{...c,analysisStatus:"completed"}:c)),o}catch(o){throw l(o instanceof Error?o.message:"Erro na análise"),s(a=>a.map(c=>c.id===S?{...c,analysisStatus:"error"}:c)),o}finally{v(!1)}};return{videos:t,analyses:r,isUploading:m,isAnalyzing:i,error:y,uploadVideo:C,analyzeVideo:F,getEvolutionData:S=>({exerciseName:S,timeline:[{date:"2024-01-01",overallScore:65,postureScore:70,rangeOfMotionScore:60,stabilityScore:65,speedScore:65},{date:"2024-01-15",overallScore:72,postureScore:75,rangeOfMotionScore:68,stabilityScore:72,speedScore:73},{date:"2024-02-01",overallScore:78,postureScore:85,rangeOfMotionScore:72,stabilityScore:80,speedScore:75}],trend:"improving",targetReached:!1,nextGoal:"Atingir 85+ em todos os aspectos técnicos"}),getEnvironmentAnalysis:()=>({id:"env_1",spaceType:"home",availableSpace:"medium",equipment:["Halteres","Colchonete","Elásticos"],limitations:["Teto baixo","Vizinhos embaixo"],suggestedExercises:[{id:"ex_1",name:"Agachamento com halteres",category:"strength",difficulty:"intermediate",equipment:["Halteres"],spaceRequired:"minimal",duration:15,description:"Agachamento segurando halteres para aumentar a resistência",benefits:["Fortalece pernas","Melhora equilíbrio","Baixo impacto"]},{id:"ex_2",name:"Prancha com variações",category:"strength",difficulty:"beginner",equipment:["Colchonete"],spaceRequired:"minimal",duration:10,description:"Exercício isométrico para core",benefits:["Fortalece core","Melhora postura","Silencioso"]}],adaptations:["Use tênis com amortecimento para reduzir ruído","Prefira exercícios isométricos após 22h","Coloque colchonete extra para absorver impacto"]}),getRecoveryAnalysis:()=>({id:"rec_1",date:new Date().toISOString(),fatigueLevel:35,muscleGroups:[{name:"Pernas",fatigueLevel:60,recoveryTime:24,status:"recovering"},{name:"Peito",fatigueLevel:20,recoveryTime:8,status:"recovered"},{name:"Costas",fatigueLevel:40,recoveryTime:16,status:"recovering"}],sleepQuality:75,stressLevel:30,recommendations:[{id:"rec_rec_1",type:"active_recovery",priority:"medium",title:"Caminhada leve",description:"Faça uma caminhada de 20-30 minutos para acelerar a recuperação",duration:25},{id:"rec_rec_2",type:"stretching",priority:"high",title:"Alongamento de pernas",description:"Foque em quadríceps, isquiotibiais e panturrilhas",duration:15,instructions:["Alongue cada músculo por 30 segundos","Respire profundamente durante o alongamento","Não force além do confortável"]}]})}};function Es(){const{videos:t,analyses:s,isUploading:r,error:u,uploadVideo:m}=Ie(),[x,i]=b.useState(""),[v,y]=b.useState("frontal"),[l,w]=b.useState(!1),[C,F]=b.useState(null),p=async a=>{if(!x){alert("Por favor, selecione um exercício primeiro");return}if(!a.type.includes("video")){alert("Por favor, envie apenas arquivos de vídeo");return}try{await m(a,x,v)}catch(c){console.error("Erro no upload:",c)}},A=a=>{a.preventDefault(),w(!1);const c=Array.from(a.dataTransfer.files);c.length>0&&p(c[0])},E=a=>{const c=a.target.files;c&&c.length>0&&p(c[0])},S=a=>{switch(a){case"pending":return e.jsx(se,{className:"w-4 h-4 text-yellow-400"});case"analyzing":return e.jsx("div",{className:"w-4 h-4 border-2 border-snapfit-green border-t-transparent rounded-full animate-spin"});case"completed":return e.jsx(ae,{className:"w-4 h-4 text-green-400"});case"error":return e.jsx(Y,{className:"w-4 h-4 text-red-400"});default:return null}},o=a=>a>=80?"text-green-400":a>=60?"text-yellow-400":"text-red-400";return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(be,{className:"w-5 h-5 text-snapfit-green"}),"Análise de Movimento"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Exercício"}),e.jsxs("select",{value:x,onChange:a=>i(a.target.value),className:"w-full px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"",children:"Selecione um exercício"}),Ue.map(a=>e.jsx("option",{value:a,children:a},a))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Ângulo da Filmagem"}),e.jsxs("select",{value:v,onChange:a=>y(a.target.value),className:"w-full px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"frontal",children:"Frontal"}),e.jsx("option",{value:"lateral",children:"Lateral"}),e.jsx("option",{value:"posterior",children:"Posterior"})]})]})]}),e.jsx("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${l?"border-snapfit-green bg-snapfit-green/5":"border-gray-600 hover:border-snapfit-green/50"}`,onDrop:A,onDragOver:a=>a.preventDefault(),onDragEnter:()=>w(!0),onDragLeave:()=>w(!1),children:r?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto animate-pulse",children:e.jsx(Se,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Fazendo upload..."}),e.jsx("div",{className:"text-sm text-gray-400",children:"Aguarde um momento"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(be,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Arraste seu vídeo aqui ou clique para selecionar"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Suportamos MP4, MOV, AVI (máx. 100MB)"})]}),e.jsx("input",{type:"file",accept:"video/*",onChange:E,className:"hidden",id:"video-upload"}),e.jsxs("label",{htmlFor:"video-upload",className:"inline-flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors cursor-pointer",children:[e.jsx(Se,{className:"w-4 h-4"}),"Selecionar Vídeo"]})]})}),e.jsxs("div",{className:"mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx("h4",{className:"text-blue-400 font-medium mb-2",children:"💡 Dicas para melhor análise:"}),e.jsxs("ul",{className:"text-sm text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Filme em boa iluminação"}),e.jsx("li",{children:"• Mantenha o corpo inteiro no quadro"}),e.jsx("li",{children:"• Grave pelo menos 3-5 repetições"}),e.jsx("li",{children:"• Use roupas que permitam ver o movimento"})]})]})]}),s.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"}),"Análises Recentes"]}),e.jsx("div",{className:"space-y-4",children:s.slice(0,3).map(a=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:a.exerciseName}),e.jsx("div",{className:"text-sm text-gray-400",children:new Date(a.analysisDate).toLocaleDateString("pt-BR")})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`text-lg font-bold ${o(a.overallScore)}`,children:[a.overallScore,"/100"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Score Geral"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mb-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(a.postureScore)}`,children:a.postureScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Postura"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(a.rangeOfMotionScore)}`,children:a.rangeOfMotionScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Amplitude"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(a.stabilityScore)}`,children:a.stabilityScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Estabilidade"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(a.speedScore)}`,children:a.speedScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Velocidade"})]})]}),e.jsx("button",{onClick:()=>F(C===a.id?null:a.id),className:"w-full px-4 py-2 text-sm text-snapfit-green border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/10 transition-colors",children:C===a.id?"Ocultar Detalhes":"Ver Detalhes"}),C===a.id&&e.jsxs("div",{className:"mt-4 space-y-4 border-t border-gray-600 pt-4",children:[a.improvementPoints.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium mb-2",children:"Pontos de Melhoria:"}),e.jsx("div",{className:"space-y-2",children:a.improvementPoints.map(c=>e.jsxs("div",{className:"flex items-start gap-2 text-sm",children:[e.jsx("span",{className:`mt-1 ${c.severity==="high"?"text-red-400":c.severity==="medium"?"text-orange-400":"text-yellow-400"}`,children:"•"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-300",children:c.description}),e.jsxs("span",{className:"text-gray-500 ml-2",children:["(",c.bodyPart,")"]})]})]},c.id))})]}),a.correctionSuggestions.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium mb-2",children:"Sugestões de Correção:"}),e.jsx("div",{className:"space-y-2",children:a.correctionSuggestions.map(c=>e.jsxs("div",{className:"text-sm",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:[c.step,". ",c.title]}),e.jsx("div",{className:"text-gray-300 ml-4",children:c.description})]},c.id))})]})]})]},a.id))})]}),t.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Vídeos em Processamento"}),e.jsx("div",{className:"space-y-3",children:t.filter(a=>a.analysisStatus!=="completed").map(a=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[S(a.analysisStatus),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:a.exerciseName}),e.jsxs("div",{className:"text-sm text-gray-400",children:["Ângulo: ",a.angle," • ",new Date(a.uploadDate).toLocaleTimeString("pt-BR")]})]})]}),e.jsx("div",{className:"text-sm text-gray-400",children:a.analysisStatus==="analyzing"?"Analisando...":a.analysisStatus==="pending"?"Na fila":"Erro"})]},a.id))})]}),u&&e.jsx("div",{className:"p-4 bg-red-500/10 border border-red-500/20 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2 text-red-400",children:[e.jsx(Y,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"Erro:"}),e.jsx("span",{children:u})]})})]})}function Ts({isOpen:t,onClose:s,workout:r}){var q,B;const[u,m]=b.useState(0),[x,i]=b.useState(!1),[v,y]=b.useState(60),[l,w]=b.useState(0),[C,F]=b.useState(!1),[p,A]=b.useState(null),[E,S]=b.useState(0),[o,a]=b.useState(!1),[c,d]=b.useState(!1),[g,T]=b.useState(0),[f,h]=b.useState({}),n=r==null?void 0:r.exercises[u];b.useEffect(()=>{if(r!=null&&r.exercises&&Object.keys(f).length===0){const D={};r.exercises.forEach(k=>{D[k.id]={sets:Array.from({length:k.sets||4},()=>({weight:0,reps:0,completed:!1}))}}),h(D)}},[r,f]),b.useEffect(()=>{t&&!p&&A(new Date)},[t]),b.useEffect(()=>{let D;return C&&l>0&&(D=setInterval(()=>{w(k=>k<=1?(F(!1),i(!1),0):k-1)},1e3)),()=>clearInterval(D)},[C,l]);const P=(D,k,M,_)=>{h(H=>({...H,[D]:{...H[D],sets:H[D].sets.map((V,J)=>J===k?{...V,[M]:typeof _=="string"?parseFloat(_)||0:_}:V)}}))},$=(D,k)=>{h(M=>({...M,[D]:{...M[D],sets:M[D].sets.map((_,H)=>H===k?{..._,completed:!0}:_)}})),S(M=>M+5),T(v),a(!0),d(!0)},R=()=>n?f[n.id]:null,j=D=>{const k=Math.floor(D/60),M=D%60;return`${k}:${M.toString().padStart(2,"0")}`},z=()=>{if(!p)return"00:00";const k=Math.floor((new Date().getTime()-p.getTime())/1e3);return j(k)},U=()=>u>=(r==null?void 0:r.exercises.length),G=()=>r!=null&&r.exercises.length?Math.round(u/r.exercises.length*100):0;return!t||!r?null:e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:r.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-400 mt-1",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(se,{className:"w-4 h-4"}),z()]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(de,{className:"w-4 h-4"}),E," kcal"]})]})]}),e.jsx("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:e.jsx($e,{className:"w-6 h-6"})})]}),e.jsxs("div",{className:"p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Progresso do Treino"}),e.jsxs("span",{className:"text-sm text-snapfit-green font-medium",children:[G(),"%"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-snapfit-green h-2 rounded-full transition-all duration-300",style:{width:`${G()}%`}})})]}),U()?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(he,{className:"w-8 h-8 text-snapfit-green"})}),e.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Treino Concluído!"}),e.jsx("p",{className:"text-gray-400 mb-4",children:"Parabéns! Você completou seu treino adaptado."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-lg font-bold text-snapfit-green",children:z()}),e.jsx("div",{className:"text-sm text-gray-400",children:"Duração"})]}),e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-lg font-bold text-orange-400",children:E}),e.jsx("div",{className:"text-sm text-gray-400",children:"Kcal Queimadas"})]})]}),e.jsx("button",{onClick:s,className:"w-full px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:"Finalizar Treino"})]}):o?e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:e.jsx(se,{className:"text-white text-lg"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-blue-300",children:"Descanso entre séries"}),e.jsxs("div",{className:"text-xl font-bold text-blue-400",children:[Math.floor(g/60).toString().padStart(2,"0"),":",(g%60).toString().padStart(2,"0")]})]})]}),e.jsx("button",{onClick:()=>{a(!1),d(!1),T(0)},className:"px-4 py-2 bg-blue-500 text-white rounded-xl text-sm font-medium hover:bg-blue-600 transition-all hover:scale-105",children:"Pular"})]})})}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-xl p-4 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("button",{onClick:()=>m(D=>Math.max(0,D-1)),disabled:u===0,className:"p-2 rounded-lg bg-gray-700 text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed hover:bg-gray-600 hover:text-white transition-all",children:e.jsx(Me,{className:"w-5 h-5"})}),e.jsxs("div",{className:"text-center flex-1",children:[e.jsxs("div",{className:"text-xs text-gray-400 mb-1",children:["Exercício ",u+1," de ",((q=r==null?void 0:r.exercises)==null?void 0:q.length)||0]}),e.jsx("h2",{className:"text-lg font-bold text-white",children:n==null?void 0:n.name})]}),e.jsx("button",{onClick:()=>m(D=>{var k;return Math.min((((k=r==null?void 0:r.exercises)==null?void 0:k.length)||1)-1,D+1)}),disabled:u>=(((B=r==null?void 0:r.exercises)==null?void 0:B.length)||1)-1,className:"p-2 rounded-lg bg-gray-700 text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed hover:bg-gray-600 hover:text-white transition-all",children:e.jsx(me,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2 overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-snapfit-green to-green-400 transition-all duration-500 ease-out rounded-full shadow-lg shadow-snapfit-green/30",style:{width:`${(()=>{var M;const D=Object.values(f).filter(_=>_.sets.some(H=>H.completed)).length,k=((M=r==null?void 0:r.exercises)==null?void 0:M.length)||1;return D/k*100})()}%`}})})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-400 bg-gray-800/50 rounded-lg px-4 py-2",children:[e.jsxs("span",{children:[(n==null?void 0:n.sets)||4," séries"]}),e.jsxs("span",{children:[(n==null?void 0:n.reps)||"8"," reps"]}),e.jsx("span",{className:"text-snapfit-green",children:n==null?void 0:n.muscle}),e.jsxs("span",{children:[v,"s descanso"]})]}),r&&r.exercises&&r.exercises[u]&&(()=>{const D=R();return D?e.jsx("div",{className:"space-y-3",children:D.sets.map((k,M)=>e.jsxs("div",{className:`rounded-xl p-4 border transition-all duration-300 ${k.completed?"bg-snapfit-green/10 border-snapfit-green/30 shadow-lg shadow-snapfit-green/10":"bg-gray-800/50 border-gray-700 hover:border-gray-600"}`,children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${k.completed?"bg-snapfit-green text-black":"bg-gray-700 text-gray-300"}`,children:M+1}),k.completed&&e.jsxs("div",{className:"flex items-center gap-1 text-snapfit-green",children:[e.jsx(he,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm font-medium",children:"Concluída"})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 items-center",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1 text-center",children:"Reps"}),e.jsx("input",{type:"number",inputMode:"numeric",pattern:"[0-9]*",value:k.reps||"",onChange:_=>P(n.id,M,"reps",_.target.value),disabled:k.completed,className:"w-full text-center text-xl font-bold bg-transparent border-none outline-none text-white disabled:opacity-50 focus:text-snapfit-green transition-colors",placeholder:(n==null?void 0:n.reps)||"0",min:"0"})]}),!k.completed&&e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx("button",{onClick:()=>P(n.id,M,"reps",(k.reps||0)+1),className:"w-6 h-6 bg-gray-600 hover:bg-snapfit-green hover:text-black rounded text-xs font-bold transition-all",children:"+"}),e.jsx("button",{onClick:()=>P(n.id,M,"reps",Math.max(0,(k.reps||0)-1)),className:"w-6 h-6 bg-gray-600 hover:bg-red-500 rounded text-xs font-bold transition-all",children:"-"})]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>$(n.id,M),disabled:k.completed||!k.reps,className:`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${k.completed?"bg-snapfit-green text-black shadow-lg shadow-snapfit-green/30":k.reps?"bg-gray-700 text-gray-300 hover:bg-snapfit-green hover:text-black hover:shadow-lg hover:shadow-snapfit-green/30 hover:scale-105":"bg-gray-700 text-gray-500 cursor-not-allowed"}`,children:e.jsx(he,{className:"w-5 h-5"})})})]})]},M))}):null})(),(()=>{const D=(r==null?void 0:r.exercises.filter(k=>{const M=f[k.id];return!(M!=null&&M.sets.some(_=>_.completed))}))||[];if(D.length>0){const M=(()=>{for(let _=u+1;_<(r==null?void 0:r.exercises.length);_++){const H=r.exercises[_],V=f[H.id];if(!(V!=null&&V.sets.some(J=>J.completed)))return _}for(let _=0;_<u;_++){const H=r.exercises[_],V=f[H.id];if(!(V!=null&&V.sets.some(J=>J.completed)))return _}return null})();if(M!==null)return e.jsxs("button",{onClick:()=>m(M),className:"w-full py-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl font-medium transition-all hover:scale-[1.02] flex items-center justify-center gap-2",children:[e.jsx("span",{children:"Próximo Exercício"}),e.jsx(me,{className:"w-5 h-5"})]})}return D.length===0?e.jsxs("button",{onClick:s,className:"w-full py-4 bg-snapfit-green hover:bg-snapfit-green/90 text-black rounded-xl font-bold transition-all hover:scale-[1.02] flex items-center justify-center gap-2",children:[e.jsx(Qe,{className:"w-5 h-5"}),e.jsx("span",{children:"Finalizar Treino"})]}):null})()]})]})})}function Fs(){const{getEnvironmentAnalysis:t}=Ie(),[s,r]=b.useState("home"),[u,m]=b.useState("medium"),[x,i]=b.useState(["Nenhum (Peso Corporal)"]),[v,y]=b.useState([]),[l,w]=b.useState(""),[C,F]=b.useState(30),[p,A]=b.useState(!1),[E,S]=b.useState(!1),[o,a]=b.useState(null),c=t(),d=[{id:"workout_1",name:"Push A - Peito e Tríceps",muscleGroups:["Peito","Tríceps","Ombros"],exercises:[{name:"Supino Reto",sets:4,reps:"8-10",muscle:"Peito"},{name:"Supino Inclinado",sets:3,reps:"10-12",muscle:"Peito"},{name:"Desenvolvimento",sets:4,reps:"8-10",muscle:"Ombros"},{name:"Elevação Lateral",sets:3,reps:"12-15",muscle:"Ombros"},{name:"Tríceps Pulley",sets:4,reps:"10-12",muscle:"Tríceps"},{name:"Tríceps Francês",sets:3,reps:"10-12",muscle:"Tríceps"}]},{id:"workout_2",name:"Pull A - Costas e Bíceps",muscleGroups:["Costas","Bíceps"],exercises:[{name:"Puxada Frontal",sets:4,reps:"8-10",muscle:"Costas"},{name:"Remada Curvada",sets:4,reps:"8-10",muscle:"Costas"},{name:"Remada Unilateral",sets:3,reps:"10-12",muscle:"Costas"},{name:"Pullover",sets:3,reps:"12-15",muscle:"Costas"},{name:"Rosca Direta",sets:4,reps:"10-12",muscle:"Bíceps"},{name:"Rosca Martelo",sets:3,reps:"10-12",muscle:"Bíceps"}]},{id:"workout_3",name:"Legs A - Quadríceps e Glúteos",muscleGroups:["Quadríceps","Glúteos","Panturrilhas"],exercises:[{name:"Agachamento Livre",sets:4,reps:"8-10",muscle:"Quadríceps"},{name:"Leg Press 45°",sets:4,reps:"12-15",muscle:"Quadríceps"},{name:"Agachamento Búlgaro",sets:3,reps:"10-12",muscle:"Glúteos"},{name:"Extensão de Pernas",sets:3,reps:"12-15",muscle:"Quadríceps"},{name:"Afundo",sets:3,reps:"10-12",muscle:"Glúteos"},{name:"Panturrilha em Pé",sets:4,reps:"15-20",muscle:"Panturrilhas"}]},{id:"workout_4",name:"Push B - Ombros e Tríceps",muscleGroups:["Ombros","Tríceps"],exercises:[{name:"Desenvolvimento com Halteres",sets:4,reps:"8-10",muscle:"Ombros"},{name:"Elevação Lateral",sets:4,reps:"12-15",muscle:"Ombros"},{name:"Elevação Posterior",sets:3,reps:"12-15",muscle:"Ombros"},{name:"Desenvolvimento Arnold",sets:3,reps:"10-12",muscle:"Ombros"},{name:"Tríceps Testa",sets:4,reps:"10-12",muscle:"Tríceps"},{name:"Mergulho",sets:3,reps:"8-12",muscle:"Tríceps"}]}],g=n=>{i(P=>{if(n==="Nenhum (Peso Corporal)")return["Nenhum (Peso Corporal)"];const $=P.filter(R=>R!=="Nenhum (Peso Corporal)");if($.includes(n)){const R=$.filter(j=>j!==n);return R.length===0?["Nenhum (Peso Corporal)"]:R}else return[...$,n]})},T=n=>{y(P=>P.includes(n)?P.filter($=>$!==n):[...P,n])},f=()=>{const n=d.find(R=>R.id===l);if(!n)return;const P=n.exercises.map(R=>{let j={...R};return!x.includes("Barra")&&R.name.includes("Supino")?(j.name="Flexão de Braço",j.reps="8-15"):!x.includes("Halteres")&&R.name.includes("Halteres")?(j.name=R.name.replace("com Halteres","com Peso Corporal"),j.reps="10-15"):!x.includes("Barra Fixa")&&R.name.includes("Puxada")&&(j.name="Remada Invertida",j.reps="6-12"),j}),$={id:`adapted_${Date.now()}`,name:`${n.name} (Adaptado)`,originalWorkout:n.name,muscleGroups:n.muscleGroups,environment:s,availableTime:C,estimatedCalories:Math.round(C*4.5),exercises:P,adaptations:["Exercícios adaptados para equipamentos disponíveis","Tempo ajustado para duração desejada","Intensidade mantida para mesmos grupos musculares"]};a($),A(!0)},h=["Teto baixo","Vizinhos embaixo","Ruído limitado","Piso escorregadio"];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Le,{className:"w-5 h-5 text-snapfit-green"}),"Configuração do Ambiente"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Tipo de Espaço"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{value:"home",label:"Casa",icon:"🏠"},{value:"gym",label:"Academia",icon:"🏋️"},{value:"outdoor",label:"Ar Livre",icon:"🌳"},{value:"office",label:"Escritório",icon:"🏢"},{value:"hotel",label:"Hotel",icon:"🏨"}].map(n=>e.jsxs("button",{onClick:()=>r(n.value),className:`p-3 rounded-lg border transition-all duration-200 ${s===n.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-lg mb-1",children:n.icon}),e.jsx("div",{className:"text-xs",children:n.label})]},n.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Espaço Disponível"}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:[{value:"small",label:"Pequeno",desc:"< 2m²"},{value:"medium",label:"Médio",desc:"2-6m²"},{value:"large",label:"Grande",desc:"> 6m²"}].map(n=>e.jsxs("button",{onClick:()=>m(n.value),className:`p-3 rounded-lg border transition-all duration-200 text-center ${u===n.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-sm font-medium",children:n.label}),e.jsx("div",{className:"text-xs opacity-75",children:n.desc})]},n.value))})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Equipamentos Disponíveis"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:Ke.map(n=>e.jsx("button",{onClick:()=>g(n),className:`p-2 rounded-lg border transition-all duration-200 text-left ${x.includes(n)?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:e.jsx("div",{className:"text-sm",children:n})},n))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Limitações do Ambiente"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:h.map(n=>e.jsx("button",{onClick:()=>T(n),className:`p-2 rounded-lg border transition-all duration-200 text-left ${v.includes(n)?"bg-orange-500/20 border-orange-500 text-orange-400":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-orange-500/50"}`,children:e.jsx("div",{className:"text-sm",children:n})},n))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Treino do Protocolo para Adaptar"}),e.jsx("div",{className:"space-y-3",children:d.map(n=>e.jsxs("button",{onClick:()=>w(n.id),className:`w-full p-4 rounded-lg border transition-all duration-200 text-left ${l===n.id?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium",children:n.name}),e.jsxs("div",{className:"text-sm opacity-75",children:[n.exercises.length," exercícios"]})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:n.muscleGroups.map((P,$)=>e.jsx("span",{className:`px-2 py-1 text-xs rounded ${l===n.id?"bg-snapfit-green/30 text-snapfit-green":"bg-gray-700 text-gray-400"}`,children:P},$))})]},n.id))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["Tempo Disponível: ",C," minutos"]}),e.jsx("input",{type:"range",min:"10",max:"120",step:"5",value:C,onChange:n=>F(Number(n.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[e.jsx("span",{children:"10 min"}),e.jsx("span",{children:"60 min"}),e.jsx("span",{children:"120 min"})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("button",{onClick:f,disabled:!l,className:"w-full flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(pe,{className:"w-5 h-5"}),"Adaptar Treino para Ambiente"]}),!l&&e.jsx("p",{className:"text-xs text-gray-400 text-center mt-2",children:"Selecione um treino do protocolo para adaptar"})]})]}),p&&o&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(pe,{className:"w-5 h-5 text-snapfit-green"}),"Treino Adaptado Gerado"]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Calorias Estimadas"}),e.jsxs("div",{className:"text-lg font-bold text-orange-400",children:[o.estimatedCalories," kcal"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:o.name}),e.jsx("div",{className:"text-xs text-gray-400",children:"Treino Adaptado"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:[o.availableTime," min"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Duração"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:o.exercises.length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Exercícios"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:s==="home"?"Casa":s==="gym"?"Academia":s==="outdoor"?"Ar Livre":s==="office"?"Escritório":"Hotel"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Ambiente"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Grupos Musculares:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:o.muscleGroups.map((n,P)=>e.jsx("span",{className:"px-3 py-1 bg-snapfit-green/20 text-snapfit-green text-sm rounded-full border border-snapfit-green/30",children:n},P))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Exercícios Adaptados:"}),e.jsx("div",{className:"space-y-3",children:o.exercises.map((n,P)=>e.jsx("div",{className:"p-3 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center text-snapfit-green font-medium text-sm",children:P+1}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium",children:n.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[n.sets," séries × ",n.reps," reps"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-snapfit-green font-medium",children:n.muscle}),e.jsx("div",{className:"text-xs text-gray-400",children:"Músculo Alvo"})]})]})},P))})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:()=>S(!0),className:"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:[e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",clipRule:"evenodd"})}),"Iniciar Treino Adaptado"]}),e.jsx("button",{onClick:()=>{A(!1),a(null)},className:"px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cancelar"})]})]}),p&&c.adaptations.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Je,{className:"w-5 h-5 text-snapfit-green"}),"Adaptações Recomendadas"]}),e.jsx("div",{className:"space-y-3",children:c.adaptations.map((n,P)=>e.jsxs("div",{className:"flex items-start gap-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx(pe,{className:"w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0"}),e.jsx("div",{className:"text-sm text-gray-300",children:n})]},P))})]}),e.jsx(Ts,{isOpen:E,onClose:()=>S(!1),workout:o})]})}const O={all:["ai","analysis"],recovery:t=>[...O.all,"recovery",t],movement:()=>[...O.all,"movement"],environment:()=>[...O.all,"environment"],history:t=>[...O.all,"history",t],insights:t=>[...O.all,"insights",t]};function Ms(t="week"){return ge({queryKey:O.recovery(t),queryFn:async()=>{var s,r,u,m,x,i,v,y,l,w,C,F,p,A,E;console.log("🔄 useRecoveryAnalysis: Fetching recovery data for period:",t);try{const[S,o]=await Promise.all([K.get("wearables/data",{searchParams:{period:t,type:"recovery"}}),K.get("analytics/insights/ai",{searchParams:{category:"recovery",period:t}})]);console.log("📊 useRecoveryAnalysis: Wearables data:",S),console.log("🤖 useRecoveryAnalysis: AI insights:",o);const a=(S==null?void 0:S.data)||{},c=(o==null?void 0:o.data)||{},d={sleepData:{averageHours:((s=a.sleep)==null?void 0:s.average_duration)||0,quality:((r=a.sleep)==null?void 0:r.quality_score)||0,deepSleepPercentage:((u=a.sleep)==null?void 0:u.deep_sleep_percentage)||0,remSleepPercentage:((m=a.sleep)==null?void 0:m.rem_sleep_percentage)||0,sleepEfficiency:((x=a.sleep)==null?void 0:x.efficiency)||0},hrvData:{average:((i=a.hrv)==null?void 0:i.average)||0,trend:((v=a.hrv)==null?void 0:v.trend)||"stable",score:((y=a.hrv)==null?void 0:y.recovery_score)||0},stressData:{level:((l=a.stress)==null?void 0:l.average_level)||0,trend:((w=a.stress)==null?void 0:w.trend)||"stable"},energyData:{level:((C=a.energy)==null?void 0:C.average_level)||0,trend:((F=a.energy)==null?void 0:F.trend)||"stable"},heartRateData:{resting:((p=a.heart_rate)==null?void 0:p.resting_average)||0,max:((A=a.heart_rate)==null?void 0:A.max_recorded)||0,zones:((E=a.heart_rate)==null?void 0:E.zones)||{}},aiAnalysis:{recoveryStatus:c.recovery_status||"unknown",fatigueLevel:c.fatigue_level||0,recommendations:c.recommendations||[],insights:c.insights||[],confidence:c.confidence||0},overallScore:c.overall_recovery_score||0,rawWearables:a,rawInsights:c};return console.log("✅ useRecoveryAnalysis: Processed data:",d),d}catch(S){return console.warn("⚠️ useRecoveryAnalysis: Error fetching data, using fallback:",S),{sleepData:{averageHours:0,quality:0,deepSleepPercentage:0,remSleepPercentage:0,sleepEfficiency:0},hrvData:{average:0,trend:"stable",score:0},stressData:{level:0,trend:"stable"},energyData:{level:0,trend:"stable"},heartRateData:{resting:0,max:0,zones:{}},aiAnalysis:{recoveryStatus:"unknown",fatigueLevel:0,recommendations:[],insights:[],confidence:0},overallScore:0,rawWearables:{},rawInsights:{}}}},staleTime:1e3*60*10,refetchInterval:1e3*60*30,refetchIntervalInBackground:!0,refetchOnWindowFocus:!0,retry:2})}function $s(){return ge({queryKey:O.movement(),queryFn:async()=>{console.log("🔄 useMovementAnalysisHistory: Fetching movement analysis data");try{const t=await K.get("analytics/movement/history");console.log("📊 useMovementAnalysisHistory: Response:",t);const s=(t==null?void 0:t.data)||{};return{analyses:s.analyses||[],videos:s.videos||[],totalAnalyses:s.total_analyses||0,averageScore:s.average_score||0,improvementTrend:s.improvement_trend||"stable",rawData:s}}catch(t){return console.warn("⚠️ useMovementAnalysisHistory: Error fetching data, using fallback:",t),{analyses:[],videos:[],totalAnalyses:0,averageScore:0,improvementTrend:"stable",rawData:{}}}},staleTime:1e3*60*5,refetchOnWindowFocus:!0,retry:2})}function _s(){return ge({queryKey:O.environment(),queryFn:async()=>{console.log("🔄 useEnvironmentAdaptation: Fetching environment data");try{const[t,s]=await Promise.all([K.get("users/protocols/workout/active"),K.get("analytics/environment/analysis")]);console.log("📊 useEnvironmentAdaptation: Protocol data:",t),console.log("🌍 useEnvironmentAdaptation: Environment data:",s);const r=(t==null?void 0:t.data)||{},u=(s==null?void 0:s.data)||{};return{activeProtocol:r,environmentFactors:u.factors||{},adaptationSuggestions:u.suggestions||[],availableEquipment:u.equipment||[],spaceAnalysis:u.space_analysis||{},weatherData:u.weather||{},rawProtocol:r,rawEnvironment:u}}catch(t){return console.warn("⚠️ useEnvironmentAdaptation: Error fetching data, using fallback:",t),{activeProtocol:{},environmentFactors:{},adaptationSuggestions:[],availableEquipment:[],spaceAnalysis:{},weatherData:{},rawProtocol:{},rawEnvironment:{}}}},staleTime:1e3*60*15,refetchOnWindowFocus:!0,retry:2})}function Rs(t="month"){return ge({queryKey:O.history(t),queryFn:async()=>{console.log("🔄 useAIAnalysisHistory: Fetching AI analysis history for period:",t);try{const s=await K.get("analytics/insights/ai/history",{searchParams:{period:t}});console.log("📊 useAIAnalysisHistory: Response:",s);const r=(s==null?void 0:s.data)||{};return{workoutAnalyses:r.workout_analyses||[],recoveryAnalyses:r.recovery_analyses||[],movementAnalyses:r.movement_analyses||[],environmentAdaptations:r.environment_adaptations||[],totalInsights:r.total_insights||0,trendsData:r.trends||{},rawData:r}}catch(s){return console.warn("⚠️ useAIAnalysisHistory: Error fetching data, using fallback:",s),{workoutAnalyses:[],recoveryAnalyses:[],movementAnalyses:[],environmentAdaptations:[],totalInsights:0,trendsData:{},rawData:{}}}},staleTime:1e3*60*10,refetchOnWindowFocus:!0,retry:2})}function Ls(){const t=_e();return Ze({mutationFn:async({file:s,exerciseName:r,angle:u})=>{console.log("🔄 useUploadMovementVideo: Uploading video:",{exerciseName:r,angle:u});const m=new FormData;m.append("video",s),m.append("exerciseName",r),m.append("angle",u);const x=await K.post("analytics/movement/upload",m,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ useUploadMovementVideo: Upload response:",x),x.data},onSuccess:()=>{t.invalidateQueries({queryKey:O.movement()})},onError:s=>{console.error("❌ useUploadMovementVideo: Upload failed:",s)}})}function ze(t="week"){const s=Ms(t),r=$s(),u=_s(),m=Rs(t),x=Ls();return{recovery:s.data,isLoadingRecovery:s.isLoading,recoveryError:s.error,movement:r.data,isLoadingMovement:r.isLoading,movementError:r.error,environment:u.data,isLoadingEnvironment:u.isLoading,environmentError:u.error,history:m.data,isLoadingHistory:m.isLoading,historyError:m.error,uploadVideo:x.mutate,isUploading:x.isPending,uploadError:x.error,isLoading:s.isLoading||r.isLoading||u.isLoading||m.isLoading,refetchAll:()=>{s.refetch(),r.refetch(),u.refetch(),m.refetch()}}}function Is(){const t=ye("week",["recovery-analysis"]),{recovery:s,isLoadingRecovery:r,recoveryError:u,refetchAll:m}=ze(t.period),[x,i]=b.useState(7),[v,y]=b.useState(3),[l,w]=b.useState(7),[C,F]=b.useState(45),[p,A]=b.useState(65),[E,S]=b.useState(!1),[o,a]=b.useState(!1),[c,d]=b.useState(!1),g=s||{sleepData:{quality:0},stressData:{level:v*20},aiAnalysis:{recoveryStatus:"unknown",fatigueLevel:0,recommendations:[]},overallScore:0},f=(()=>{g.aiAnalysis.recoveryStatus;const j=g.aiAnalysis.fatigueLevel;return[{name:"Peito",group:"upper"},{name:"Costas",group:"upper"},{name:"Ombros",group:"upper"},{name:"Bíceps",group:"upper"},{name:"Tríceps",group:"upper"},{name:"Quadríceps",group:"lower"},{name:"Glúteos",group:"lower"},{name:"Panturrilha",group:"lower"}].map((U,G)=>{const q=G%3*10;let B=j+q;B=Math.max(0,Math.min(100,B));let D="recovered",k=0;return B>70?(D="overworked",k=48):B>50?(D="fatigued",k=24):B>30?(D="recovering",k=12):(D="recovered",k=0),{name:U.name,status:D,fatigueLevel:B,recoveryTime:k}})})(),h=()=>{d(!0),m()},n=j=>j<=30?"text-green-400":j<=60?"text-yellow-400":"text-red-400",P=j=>j<=30?"Baixa":j<=60?"Moderada":"Alta",$=j=>{switch(j){case"recovered":return"text-green-400";case"recovering":return"text-yellow-400";case"fatigued":return"text-orange-400";case"overworked":return"text-red-400";default:return"text-gray-400"}},R=j=>{switch(j){case"recovered":return e.jsx(ae,{className:"w-4 h-4 text-green-400"});case"recovering":return e.jsx(se,{className:"w-4 h-4 text-yellow-400"});case"fatigued":return e.jsx(Y,{className:"w-4 h-4 text-orange-400"});case"overworked":return e.jsx(Y,{className:"w-4 h-4 text-red-400"});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Recuperação Inteligente"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Análise de fadiga e sugestões de descanso baseadas em IA"})]}),e.jsx(Ne,{period:t.period,onPeriodChange:t.setPeriod,onCustomDateChange:t.setCustomDates,className:"w-full sm:w-auto"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(te,{className:"w-5 h-5 text-snapfit-green"}),"Dados de Recuperação"]}),r&&e.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[e.jsx(ce,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{className:"text-sm",children:"Carregando..."})]})]}),e.jsxs("div",{className:"mb-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${o?"bg-green-400":"bg-gray-400"}`}),e.jsx("span",{className:"text-white font-medium",children:o?"Wearable Conectado":"Wearable Desconectado"})]}),e.jsx("button",{onClick:()=>a(!o),className:`px-3 py-1 rounded text-sm transition-colors ${o?"bg-red-500/20 text-red-400 border border-red-500/30 hover:bg-red-500/30":"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30 hover:bg-snapfit-green/30"}`,children:o?"Desconectar":"Conectar"})]}),o?e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-blue-400 font-medium",children:[p," bpm"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"FC Repouso"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-green-400 font-medium",children:[C," ms"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"HRV"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-purple-400 font-medium",children:[x,"h 23m"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Sono Total"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-orange-400 font-medium",children:"85%"}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Qualidade"})]})]}):e.jsx("div",{className:"text-center text-gray-400 text-sm",children:"Conecte seu wearable para dados automáticos de sono, frequência cardíaca e HRV"})]}),e.jsx("h4",{className:"text-white font-medium mb-4",children:o?"Dados Complementares":"Dados Manuais"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["Horas de Sono (última noite)",o&&e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"4",max:"12",value:x,onChange:j=>i(Number(j.target.value)),disabled:o,className:`w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider ${o?"opacity-50 cursor-not-allowed":""}`}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(ke,{className:"w-4 h-4 text-blue-400"}),e.jsxs("span",{className:"text-white font-medium",children:[x,"h"]}),e.jsx("span",{className:`text-sm ${x>=7?"text-green-400":x>=6?"text-yellow-400":"text-red-400"}`,children:x>=7?"Bom":x>=6?"Regular":"Insuficiente"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Nível de Estresse (1-10)"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"1",max:"10",value:v,onChange:j=>y(Number(j.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(Y,{className:"w-4 h-4 text-orange-400"}),e.jsxs("span",{className:"text-white font-medium",children:[v,"/10"]}),e.jsx("span",{className:`text-sm ${v<=3?"text-green-400":v<=6?"text-yellow-400":"text-red-400"}`,children:v<=3?"Baixo":v<=6?"Moderado":"Alto"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Nível de Energia (1-10)"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"1",max:"10",value:l,onChange:j=>w(Number(j.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(Xe,{className:"w-4 h-4 text-yellow-400"}),e.jsxs("span",{className:"text-white font-medium",children:[l,"/10"]}),e.jsx("span",{className:`text-sm ${l>=7?"text-green-400":l>=4?"text-yellow-400":"text-red-400"}`,children:l>=7?"Alto":l>=4?"Moderado":"Baixo"})]})]})]})]}),o&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["HRV (Variabilidade da FC)",e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2 p-3 bg-snapfit-gray rounded-lg",children:[e.jsx(te,{className:"w-4 h-4 text-green-400"}),e.jsxs("span",{className:"text-white font-medium",children:[C," ms"]}),e.jsx("span",{className:`text-sm ${C>=50?"text-green-400":C>=30?"text-yellow-400":"text-red-400"}`,children:C>=50?"Excelente":C>=30?"Bom":"Baixo"})]})})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["FC de Repouso",e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2 p-3 bg-snapfit-gray rounded-lg",children:[e.jsx(te,{className:"w-4 h-4 text-blue-400"}),e.jsxs("span",{className:"text-white font-medium",children:[p," bpm"]}),e.jsx("span",{className:`text-sm ${p<=60?"text-green-400":p<=80?"text-yellow-400":"text-red-400"}`,children:p<=60?"Atlético":p<=80?"Normal":"Elevado"})]})})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("button",{onClick:h,className:"w-full flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:[e.jsx(ee,{className:"w-5 h-5"}),o?"Analisar com Dados Completos":"Analisar Recuperação"]}),o&&e.jsx("p",{className:"text-xs text-green-400 text-center mt-2",children:"✓ Análise aprimorada com dados do wearable"})]})]}),(c||s)&&e.jsxs(e.Fragment,{children:[u&&e.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 text-red-400",children:[e.jsx(Y,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"Erro ao carregar dados de recuperação"})]}),e.jsx("p",{className:"text-sm text-gray-400 mt-1",children:"Usando dados manuais como fallback"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(Ye,{className:"w-5 h-5 text-snapfit-green"}),"Status Geral de Recuperação"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[R(g.aiAnalysis.recoveryStatus),e.jsx("span",{className:`text-sm font-medium ${$(g.aiAnalysis.recoveryStatus)}`,children:g.aiAnalysis.recoveryStatus==="recovered"?"Recuperado":g.aiAnalysis.recoveryStatus==="recovering"?"Recuperando":g.aiAnalysis.recoveryStatus==="fatigued"?"Fatigado":g.aiAnalysis.recoveryStatus==="overworked"?"Sobrecarregado":"Desconhecido"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:`text-2xl font-bold ${n(g.aiAnalysis.fatigueLevel)}`,children:[Math.round(g.aiAnalysis.fatigueLevel),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Fadiga Geral"}),e.jsx("div",{className:`text-xs ${n(g.aiAnalysis.fatigueLevel)}`,children:P(g.aiAnalysis.fatigueLevel)})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-400",children:[Math.round(g.sleepData.quality),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Qualidade do Sono"}),e.jsx("div",{className:"text-xs text-blue-400",children:g.sleepData.quality>=80?"Excelente":g.sleepData.quality>=60?"Boa":"Regular"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-orange-400",children:[Math.round(g.stressData.level),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Nível de Estresse"}),e.jsx("div",{className:"text-xs text-orange-400",children:g.stressData.level<=30?"Baixo":g.stressData.level<=60?"Moderado":"Alto"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green",children:[Math.round(g.overallScore),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Score Geral"}),e.jsx("div",{className:"text-xs text-snapfit-green",children:g.overallScore>=80?"Excelente":g.overallScore>=60?"Bom":"Regular"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Status dos Grupos Musculares:"}),e.jsx("div",{className:"space-y-3",children:f.map((j,z)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[R(j.status),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:j.name}),e.jsx("div",{className:"text-sm text-gray-400",children:j.recoveryTime>0?`Recuperação estimada: ${j.recoveryTime}h`:"Totalmente recuperado"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`font-medium ${n(j.fatigueLevel)}`,children:[Math.round(j.fatigueLevel),"%"]}),e.jsx("div",{className:`text-xs ${$(j.status)}`,children:j.status==="recovered"?"Recuperado":j.status==="recovering"?"Recuperando":j.status==="fatigued"?"Fatigado":"Sobrecarregado"})]})]},z))})]})]}),g.aiAnalysis.insights.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"}),"Insights da IA"]}),e.jsx("div",{className:"space-y-4",children:g.aiAnalysis.insights.map((j,z)=>e.jsx("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center",children:j.type==="sleep"?e.jsx(ke,{className:"w-4 h-4 text-blue-400"}):j.type==="hrv"?e.jsx(te,{className:"w-4 h-4 text-green-400"}):e.jsx(ee,{className:"w-4 h-4 text-purple-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-white font-medium mb-1",children:j.title}),e.jsx("p",{className:"text-gray-300 text-sm",children:j.description}),e.jsxs("div",{className:"mt-2 text-xs text-gray-400",children:["Confiança: ",Math.round(j.confidence*100),"%"]})]})]})},z))})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ae,{className:"w-5 h-5 text-snapfit-green"}),"Recomendações Personalizadas"]}),e.jsx("div",{className:"space-y-4",children:g.aiAnalysis.recommendations.length>0?g.aiAnalysis.recommendations.map((j,z)=>e.jsx("div",{className:"p-4 rounded-lg border border-snapfit-green/20 bg-snapfit-green/5",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("span",{className:"text-2xl",children:"💡"}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-gray-300",children:j})})]})},z)):e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(ae,{className:"w-12 h-12 mx-auto mb-3 text-green-400"}),e.jsx("p",{children:"Nenhuma recomendação específica no momento."}),e.jsx("p",{className:"text-sm",children:"Continue mantendo seus hábitos atuais!"})]})})]})]})]})}function zs({onReuseProtocol:t}){var a,c,d,g,T,f;const[s,r]=b.useState("sessions"),u=ye("month",["workout-history","ai-insights"]),{history:m,isLoadingHistory:x,historyError:i,refetchAll:v}=ze(u.period),{data:y,isLoading:l,error:w,refetch:C}=es({startDate:(a=u.customDates)==null?void 0:a.startDate,endDate:(c=u.customDates)==null?void 0:c.endDate,limit:50}),F=(y==null?void 0:y.workouts)||[],p=[],A=h=>{switch(h){case"active":return"bg-green-500/20 text-green-400 border-green-500/30";case"completed":return"bg-blue-500/20 text-blue-400 border-blue-500/30";case"archived":return"bg-gray-500/20 text-gray-400 border-gray-500/30";default:return"bg-gray-500/20 text-gray-400 border-gray-500/30"}},E=h=>{switch(h){case"active":return e.jsx(de,{className:"w-4 h-4"});case"completed":return e.jsx(Ee,{className:"w-4 h-4"});case"archived":return e.jsx(oe,{className:"w-4 h-4"});default:return e.jsx(de,{className:"w-4 h-4"})}},S=h=>new Date(h).toLocaleDateString("pt-BR"),o=h=>{const[n,P,$]=h.split(":").map(Number);return n>0?`${n.toString().padStart(2,"0")}:${P.toString().padStart(2,"0")}h`:`${P.toString().padStart(2,"0")}:${$.toString().padStart(2,"0")}min`};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(xe,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Análise completa do seu progresso"})]})]}),e.jsx(Ne,{period:u.period,onPeriodChange:u.setPeriod,onCustomDateChange:u.setCustomDates,className:"w-full sm:w-auto"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex bg-snapfit-gray rounded-lg p-1 border border-snapfit-green/20 mb-6",children:[e.jsxs("button",{onClick:()=>r("sessions"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="sessions"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(Q,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Treinos Realizados"})]}),e.jsxs("button",{onClick:()=>r("protocols"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="protocols"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(oe,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Protocolos"})]}),e.jsxs("button",{onClick:()=>r("ai-insights"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="ai-insights"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(X,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Insights IA"})]})]}),e.jsx("div",{className:"space-y-4",children:s==="sessions"?F.map(h=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 rounded-full bg-snapfit-green/20 border border-snapfit-green/30",children:e.jsx(Q,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:h.workout_name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[h.muscle_groups," • ",S(h.date)]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-white font-medium",children:[h.exercise_count," exercícios"]}),e.jsx("div",{className:"text-sm text-snapfit-green",children:"Concluído"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1 bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[e.jsx(se,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:o(h.workout_time)})]}),e.jsxs("div",{className:"flex items-center gap-1 bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[e.jsx(Re,{className:"w-3 h-3 text-orange-400"}),e.jsxs("span",{children:[h.total_kcal,"kcal"]})]}),e.jsx("div",{className:"px-2 py-1 rounded-full bg-snapfit-green/20 text-snapfit-green",children:h.type})]})]},h.id)):s==="protocols"?p.length>0?p.map(h=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`p-2 rounded-full border ${A(h.status)}`,children:E(h.status)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:h.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[S(h.startDate),h.endDate&&` - ${S(h.endDate)}`]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>console.log("Ver detalhes:",h),className:"flex items-center gap-1 px-2 py-1 bg-gray-500/20 text-gray-300 rounded text-xs border border-gray-500/30 hover:bg-gray-500/30 transition-colors",children:[e.jsx(Ae,{className:"w-3 h-3"}),"Ver"]}),t&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>t(h),className:"flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs border border-blue-500/30 hover:bg-blue-500/30 transition-colors",children:[e.jsx(ce,{className:"w-3 h-3"}),"Reutilizar"]}),e.jsxs("button",{onClick:()=>t({...h,edit:!0}),className:"flex items-center gap-1 px-2 py-1 bg-amber-500/20 text-amber-400 rounded text-xs border border-amber-500/30 hover:bg-amber-500/30 transition-colors",children:[e.jsx(ss,{className:"w-3 h-3"}),"Editar"]})]})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-400",children:[e.jsx("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:h.split}),e.jsxs("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[h.frequency,"x/semana"]}),e.jsxs("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[h.completedWorkouts," treinos"]}),e.jsx("div",{className:`px-2 py-1 rounded-full border ${A(h.status)}`,children:h.status==="active"?"Ativo":h.status==="completed"?"Concluído":"Arquivado"})]})]},h.id)):e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(oe,{className:"w-12 h-12 mx-auto mb-3"}),e.jsx("p",{children:"Nenhum protocolo encontrado"}),e.jsx("p",{className:"text-sm",children:"Seus protocolos anteriores aparecerão aqui"})]}):e.jsx("div",{className:"space-y-6",children:x?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx(ce,{className:"w-8 h-8 animate-spin text-snapfit-green mr-3"}),e.jsx("span",{className:"text-gray-400",children:"Carregando insights da IA..."})]}):i?e.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"text-red-400 mb-2",children:"Erro ao carregar insights"}),e.jsx("button",{onClick:()=>v(),className:"text-sm text-red-300 underline",children:"Tentar novamente"})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(X,{className:"w-5 h-5 text-purple-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Total de Insights"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:(m==null?void 0:m.totalInsights)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(Q,{className:"w-5 h-5 text-blue-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Treino"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((d=m==null?void 0:m.workoutAnalyses)==null?void 0:d.length)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(ee,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Recuperação"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((g=m==null?void 0:m.recoveryAnalyses)==null?void 0:g.length)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(Ae,{className:"w-5 h-5 text-orange-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Movimento"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((T=m==null?void 0:m.movementAnalyses)==null?void 0:T.length)||0})]})]}),((f=m==null?void 0:m.workoutAnalyses)==null?void 0:f.length)>0&&e.jsxs("div",{className:"bg-snapfit-gray rounded-lg border border-snapfit-green/10 p-6",children:[e.jsxs("h4",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(X,{className:"w-5 h-5 text-purple-400"}),"Insights Recentes da IA"]}),e.jsx("div",{className:"space-y-4",children:m.workoutAnalyses.slice(0,5).map((h,n)=>e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/5",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center",children:e.jsx(X,{className:"w-4 h-4 text-purple-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"text-white font-medium mb-1",children:h.title||"Análise de Treino"}),e.jsx("p",{className:"text-gray-300 text-sm mb-2",children:h.description||"Análise detalhada do seu desempenho"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-400",children:[e.jsx("span",{children:S(h.date||new Date().toISOString())}),e.jsxs("span",{children:["Confiança: ",Math.round((h.confidence||.8)*100),"%"]})]})]})]})},n))})]}),(!m||m.totalInsights===0)&&e.jsxs("div",{className:"text-center py-12 text-gray-400",children:[e.jsx(X,{className:"w-16 h-16 mx-auto mb-4 text-gray-500"}),e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Nenhum insight disponível"}),e.jsx("p",{className:"text-sm",children:"Continue treinando para gerar insights personalizados da IA"})]})]})})}),e.jsxs("div",{className:"mt-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Estatísticas do Mês"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-snapfit-green",children:"12"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Treinos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:"15h"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Tempo Total"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:"4"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Protocolos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:"3.2k"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Kcal Queimadas"})]})]})]})]})]})}const Pe=[{id:"analysis",label:"Análise de Movimento",icon:be,description:"IA analisa sua técnica e sugere melhorias"},{id:"environment",label:"Adaptação de Ambiente",icon:Le,description:"Exercícios personalizados para seu espaço"},{id:"recovery",label:"Recuperação Inteligente",icon:te,description:"Análise de fadiga e sugestões de descanso"},{id:"history",label:"Histórico",icon:xe,description:"Seus treinos e análises anteriores"}];function Bs({onReuseProtocol:t}){var m;const[s,r]=b.useState("analysis"),u=()=>{switch(s){case"analysis":return e.jsx(Es,{});case"environment":return e.jsx(Fs,{});case"recovery":return e.jsx(Is,{});case"history":return e.jsx(zs,{onReuseProtocol:t});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Análise e Ferramentas IA"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Tecnologia avançada para otimizar seus treinos e recuperação"})]}),e.jsx("div",{className:"flex bg-snapfit-dark-gray rounded-lg p-1 border border-snapfit-green/20 overflow-x-auto",children:Pe.map(x=>{const i=x.icon,v=x.id===s;return e.jsxs("button",{onClick:()=>r(x.id),className:`flex-1 min-w-0 flex items-center justify-center gap-2 px-3 py-3 rounded-md transition-all duration-200 whitespace-nowrap ${v?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(i,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"hidden sm:inline text-sm",children:x.label}),e.jsx("span",{className:"sm:hidden text-xs",children:x.id==="analysis"?"Análise":x.id==="environment"?"Ambiente":x.id==="recovery"?"Recuperação":"Histórico"})]},x.id)})}),e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-sm text-gray-400",children:(m=Pe.find(x=>x.id===s))==null?void 0:m.description})}),e.jsx("div",{className:"min-h-[400px]",children:u()})]})}function Ks(){_e();const t=ye("week",["workout-stats","workout-analytics"]),{protocol:s,stats:r,isLoadingProtocol:u,isLoadingStats:m,protocolError:x,statsError:i,removeProtocol:v,refetchStats:y}=ps(t.period),l=r||{weeklyWorkouts:{completed:0,planned:5},totalTime:0,totalCalories:0,totalVolume:0,currentStreak:0,bestStreak:0,weeklyProgressPercentage:0,protocolCompletionPercentage:0,totalWorkouts:0},w=L.useMemo(()=>l.chartData&&l.chartData.length>0?l.chartData.map(N=>({value:N.training_volume||0,label:N.label||"N/A",color:"#B9FF43"})):[{value:0,label:"Seg",color:"#B9FF43"},{value:0,label:"Ter",color:"#B9FF43"},{value:0,label:"Qua",color:"#B9FF43"},{value:0,label:"Qui",color:"#B9FF43"},{value:0,label:"Sex",color:"#B9FF43"},{value:0,label:"Sáb",color:"#B9FF43"},{value:0,label:"Dom",color:"#B9FF43"}],[l.chartData]),[C,F]=L.useState([{value:0,label:"Supino",color:"#B9FF43"},{value:0,label:"Agachamento",color:"#4CAF50"},{value:0,label:"Levantamento",color:"#FFC107"},{value:0,label:"Remada",color:"#FF5722"}]),[p,A]=L.useState(null),[E,S]=L.useState(!1),[o,a]=L.useState(!1),[c,d]=L.useState(null),[g,T]=L.useState(`Foco em tempo sob tensão nos exercícios de peito.
Aumentar carga progressivamente nos exercícios compostos.
Manter strict form em todos os exercícios.`),[f,h]=L.useState(!1),[n,P]=L.useState(!1),[$,R]=L.useState(null),[j,z]=L.useState(!1),[U,G]=L.useState(null),q=ve(),B=ts(),D=as(),k=rs();L.useEffect(()=>{s!=null&&s.workouts&&s.workouts.length>0?(A(s.workouts[0]),console.log("✅ WorkoutPage: Primeiro treino selecionado:",s.workouts[0])):(console.log("⚠️ WorkoutPage: Nenhum treino disponível para seleção"),A(null))},[s]),L.useEffect(()=>{x&&(console.error("❌ React Query: Erro ao carregar protocolo:",x),ns.error("Erro ao carregar protocolo de treino",{position:"bottom-right"}))},[x]);const M=async()=>{!window.confirm("Tem certeza que deseja remover este protocolo?")||!(s!=null&&s.id)||v(s.id)},_=async()=>{try{const N=await B.mutateAsync("workout");N&&N.id?(z(!0),G(()=>()=>h(!0))):h(!0)}catch(N){console.error("Error checking active protocol:",N),h(!0)}},H=async()=>{var N,le;try{const I=await B.mutateAsync("workout");console.log("🔍 Active protocol found:",I),I&&I.id?(console.log(`🏁 Finalizing active protocol with ID: ${I.id}`),await k.mutateAsync({protocolId:I.id.toString(),protocolType:"workout"}),console.log("✅ Active protocol finalized successfully")):console.log("ℹ️ No active protocol found to finalize"),U&&(U(),G(null)),z(!1)}catch(I){console.error("❌ Error in finalization flow:",I);const W=((le=(N=I==null?void 0:I.response)==null?void 0:N.data)==null?void 0:le.message)||(I==null?void 0:I.message)||"Erro desconhecido";W.includes("não encontrado")||W.includes("já finalizado")?(console.log("ℹ️ Protocol already finalized or not found, proceeding with creation"),U&&(U(),G(null))):console.error("❌ Unexpected error during finalization:",W),z(!1)}},V=()=>{q("/dashboard/workout/create-protocol/ai"),h(!1)},J=()=>{q("/dashboard/workout/create-protocol/manual"),h(!1)},Be=()=>{q("/dashboard/workout/create-protocol/import"),h(!1)},ne=async N=>{const le={name:N.name,type_id:N.type,objective:N.objective,started_at:N.startDate,frequency:N.frequency,split:N.split,goals:N.goals,workouts:N.workouts.map(I=>({exercises:I.exercises.map(W=>({exercise_id:W.exercise.id,sets:W.sets,reps:W.reps,rpe:W.rpe,rest_seconds:W.restTime,notes:W.notes}))})),supplements:N.supplements,general_notes:N.notes};console.log("Saving protocol:",N),a(!0),S(!0),D.mutate({protocolData:le,protocolType:"workout",shouldFinalizeActive:!1},{onSuccess:()=>{setTimeout(()=>{S(!1),a(!1),d(null)},1e3)},onError:()=>{S(!1),a(!1)}})},ie=()=>{d(null),S(!1),a(!1),h(!1)};return E?e.jsx(is,{type:"workout",isSuccess:o,message:o?"Protocolo gerado com sucesso! 🎉":void 0}):e.jsxs(e.Fragment,{children:[u&&e.jsx(ls,{}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white",children:"Treino"}),f&&e.jsx(Cs,{onGenerateAI:V,onCreateManual:J,onImportFromCoach:Be,onReadProtocol:()=>{q("/dashboard/workout/create-protocol/import"),h(!1)},onClose:ie}),c==="ai"&&e.jsx(Ce,{onProtocolGenerated:N=>{console.log("Protocolo de treino gerado pela IA:",N),ne(N)},onClose:ie}),c==="manual"&&e.jsx(os,{onSave:ne,onCancel:ie}),c==="import"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto p-6 border border-snapfit-green/20",children:e.jsx(Ds,{onImport:ne,onCancel:ie})})}),s&&!(s!=null&&s.has_protocol)&&!c&&e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:"Você não tem um protocolo de treino ativo"}),e.jsxs("button",{onClick:_,className:"btn-primary flex items-center justify-center gap-2 w-full sm:w-auto",children:[e.jsx(cs,{className:"w-5 h-5"}),"Novo Protocolo"]})]}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Estatísticas de Treino"}),e.jsx(Ne,{period:t.period,onPeriodChange:t.setPeriod,onCustomDateChange:t.setCustomDates,className:"w-full sm:w-auto"})]}),m?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Carregando estatísticas..."})]}):i?e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 text-center",children:[e.jsx("p",{className:"text-red-600 text-sm",children:"Erro ao carregar estatísticas"}),e.jsx("button",{onClick:()=>y(),className:"mt-2 text-red-700 underline text-xs",children:"Tentar novamente"})]}):e.jsx("div",{className:"mobile-scroll-container",children:e.jsxs("div",{className:"mobile-scroll-content",children:[e.jsx(Z,{title:"Treinos Semana",value:`${l.weeklyWorkouts.completed}/${l.weeklyWorkouts.planned}`,icon:e.jsx(Q,{className:"animate-pulse-slow"}),change:l.weeklyWorkouts.completed>0?10:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(Z,{title:"Tempo Total",value:`${l.totalTime} min`,icon:e.jsx(ds,{className:"animate-pulse-slow"}),change:l.totalTime>0?5:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(Z,{title:"Calorias",value:`${l.totalCalories} kcal`,icon:e.jsx(Re,{className:"animate-pulse-slow"}),change:l.totalCalories>0?8:0,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(Z,{title:"Volume",value:`${l.totalVolume} kg`,icon:e.jsx(ms,{className:"animate-pulse-slow"}),change:l.totalVolume>0?15:0,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(Z,{title:"Sequência Atual",value:`${l.currentStreak} dias`,icon:e.jsx(ee,{className:"animate-pulse-slow"}),change:l.currentStreak>0?5:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(Z,{title:"Melhor Sequência",value:`${l.bestStreak} dias`,icon:e.jsx(xs,{className:"animate-pulse-slow"}),change:l.bestStreak>0?3:0,className:"mobile-card stagger-item animate-slide-in-right"})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6",children:[e.jsx(fe,{data:w,title:"Duração dos Treinos (min)",type:"bar",className:"animate-slide-in-left"}),e.jsx(fe,{data:C,title:"Progresso de Força (kg)",type:"bar",className:"animate-slide-in-right"})]})]})}),(console.log("🔍 WorkoutPage: Verificando condições de renderização:",{protocolWorkouts:!!s,creationMode:c,hasProtocol:s==null?void 0:s.has_protocol,shouldRender:s&&!c&&(s==null?void 0:s.has_protocol)}),s&&!c&&(s==null?void 0:s.has_protocol))&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"card p-6 animate-slide-up",children:e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Protocolo de Treino"}),e.jsx("div",{className:"flex items-center gap-2 px-3 py-1.5 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30",children:e.jsx("span",{className:"text-xs sm:text-sm font-medium text-snapfit-green",children:(s==null?void 0:s.type)||"Hipertrofia"})})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:s==null?void 0:s.objective})]}),e.jsxs("div",{className:"flex gap-2 mt-2 sm:mt-0",children:[e.jsxs("button",{className:"btn-secondary flex items-center justify-center gap-2 text-sm",onClick:()=>{console.log("🔄 Botão Editar Protocolo clicado. Protocolo ID:",s==null?void 0:s.id),s!=null&&s.id?window.location.pathname.includes("/professional/")?(console.log("🏃‍♂️ Navegando para rota profissional:",`/dashboard/professional/protocols/edit/${s.id}?type=workout`),q(`/dashboard/professional/protocols/edit/${s.id}?type=workout`)):(console.log("👤 Navegando para rota de usuário:",`/dashboard/workout/edit-protocol/${s.id}`),q(`/dashboard/workout/edit-protocol/${s.id}`)):(console.log("❌ Nenhum protocolo ID encontrado, navegando para criação"),q("/dashboard/workout/create-protocol/manual"))},children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),e.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]}),"Editar Protocolo"]}),e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2 text-sm",onClick:_,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M12 5v14"}),e.jsx("path",{d:"M5 12h14"})]}),"Criar Protocolo"]})]})]})}),e.jsx(As,{protocolId:s==null?void 0:s.id,protocolName:s==null?void 0:s.name,startDate:s==null?void 0:s.started_at,splitInfo:s==null?void 0:s.split,frequency:`${s==null?void 0:s.frequency}x/semana`,objective:s==null?void 0:s.objective,completedWorkouts:(s==null?void 0:s.workouts_completed)||0,notes:s==null?void 0:s.notes,workouts:(s==null?void 0:s.workouts)||[],workoutsDb:s==null?void 0:s.workouts,selectedWorkout:p,onSelectWorkout:A,onGenerateNewProtocol:_,onEditNotes:()=>{console.log("Editing protocol notes...")},onDeleteProtocol:M}),e.jsxs("div",{className:"card p-6 animate-slide-up",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Estatísticas do Protocolo"}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Progresso: ",Math.round(l.protocolCompletionPercentage),"% completo"]})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Frequência"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:`${(s==null?void 0:s.frequency)||0}x/semana`})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Divisão"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:(s==null?void 0:s.split)||"N/A"})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total de Treinos"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:l.totalWorkouts})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Sequência Atual"}),e.jsxs("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:[l.currentStreak," dias"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsx(fe,{data:w,title:"Duração dos Treinos (min)",type:"line",height:180,className:"animate-slide-in-left"}),e.jsxs("div",{className:"card-glass p-4",children:[e.jsx("h4",{className:"text-base font-bold mb-3",children:"Progresso"}),e.jsxs("div",{className:"flex justify-around gap-4",children:[e.jsx(ue,{value:l.weeklyWorkouts.completed,max:l.weeklyWorkouts.planned,label:"Esta Semana",sublabel:`${l.weeklyWorkouts.completed}/${l.weeklyWorkouts.planned} treinos`,color:"#B9FF43",size:90}),e.jsx(ue,{value:l.weeklyProgressPercentage,max:100,label:"Meta Semanal",sublabel:`${Math.round(l.weeklyProgressPercentage)}% atingido`,color:"#4CAF50",size:90}),e.jsx(ue,{value:l.protocolCompletionPercentage,max:100,label:"Protocolo",sublabel:`${Math.round(l.protocolCompletionPercentage)}% completo`,color:"#FF9800",size:90})]})]})]})]}),n&&e.jsx(Ce,{initialMode:$,onProtocolGenerated:N=>{console.log("Protocolo gerado pela IA:",N),ne(N),P(!1),R(null)},onClose:()=>{P(!1),R(null)}}),e.jsx(Bs,{onReuseProtocol:N=>{N.edit?(console.log("Editando e usando protocolo:",N),alert(`Editando e usando protocolo: ${N.name||N.workout_name}`)):(console.log("Reutilizando protocolo:",N),alert(`Protocolo ${N.name||N.workout_name} reutilizado com sucesso!`))}})]}),!c&&e.jsx("div",{className:"space-y-6",children:e.jsx(ws,{hasActiveProtocol:(s==null?void 0:s.has_protocol)||!1,defaultTab:"sessions",onReuseProtocol:N=>{N.edit?(console.log("Editando e usando protocolo:",N),alert(`Editando e usando protocolo: ${N.name||N.workout_name}`)):(console.log("Reutilizando protocolo:",N),alert(`Protocolo ${N.name||N.workout_name} reutilizado com sucesso!`))}})})]}),e.jsx(gs,{isOpen:j,onClose:()=>{z(!1),G(null)},onConfirm:H,title:"Protocolo Ativo Encontrado",message:"Ao criar um novo, o protocolo atual será finalizado. Deseja continuar?",confirmText:"Sim, Continuar",cancelText:"Cancelar",type:"warning",isLoading:B.isPending||D.isPending||k.isPending})]})}export{Ks as WorkoutPage};
