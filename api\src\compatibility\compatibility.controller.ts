import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  Request, 
  UseGuards,
  Redirect
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

/**
 * Controller para manter compatibilidade com o frontend
 * Redireciona rotas antigas para as novas estruturas
 */
@Controller()
export class CompatibilityController {

  // Redirect /user/data to /users/me
  @Get('user/data')
  @UseGuards(JwtAuthGuard)
  @Redirect('/users/me', 301)
  getUserData() {
    // This will redirect to the new endpoint
  }

  // Redirect /subscriptions to /users/subscriptions
  @Get('subscriptions')
  @UseGuards(JwtAuthGuard)
  @Redirect('/users/subscriptions', 301)
  getSubscriptions() {
    // This will redirect to the new endpoint
  }

  @Post('subscriptions/:id/cancel')
  @UseGuards(JwtAuthGuard)
  @Redirect('/users/subscriptions/:id/cancel', 301)
  cancelSubscription() {
    // This will redirect to the new endpoint
  }

  // Redirect /protocols/coach to /coach/protocols
  @Get('protocols/coach')
  @UseGuards(JwtAuthGuard)
  @Redirect('/coach/protocols', 301)
  getCoachProtocols() {
    // This will redirect to the new endpoint
  }

  // Redirect /protocols/nutritionist to /nutritionist/protocols
  @Get('protocols/nutritionist')
  @UseGuards(JwtAuthGuard)
  @Redirect('/nutritionist/protocols', 301)
  getNutritionistProtocols() {
    // This will redirect to the new endpoint
  }

  // Profile friends endpoint (redirect to friends)
  @Get('profile/friends')
  @UseGuards(JwtAuthGuard)
  @Redirect('/friends', 301)
  getProfileFriends() {
    // This will redirect to the new endpoint
  }
}
