const mysql = require('mysql2/promise');
require('dotenv').config();

async function insertOAuthProviders() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE
  });

  try {
    console.log('Connected to database');

    // Insert OAuth providers
    await connection.execute('INSERT IGNORE INTO auth_providers (id, name) VALUES (1, "local")');
    console.log('✓ Local provider inserted/verified');

    await connection.execute('INSERT IGNORE INTO auth_providers (id, name) VALUES (2, "google")');
    console.log('✓ Google provider inserted');

    await connection.execute('INSERT IGNORE INTO auth_providers (id, name) VALUES (3, "apple")');
    console.log('✓ Apple provider inserted');

    // Verify providers
    const [rows] = await connection.execute('SELECT * FROM auth_providers ORDER BY id');
    console.log('\nCurrent OAuth providers:');
    console.table(rows);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await connection.end();
    console.log('Database connection closed');
  }
}

insertOAuthProviders();
