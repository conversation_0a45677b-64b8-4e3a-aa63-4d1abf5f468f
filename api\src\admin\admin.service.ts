import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { db } from '../database';
import { GetAllUsersQueryDto } from './get-users-query.dto';
// import * as dayjs from 'dayjs';
const dayjs = require('dayjs');
import { CreateUserDto } from './dto/create-user.dto';
import * as bcrypt from 'bcryptjs';
import { UpdateUserDto } from './dto/update-user.dto';
import { GetAllFoodsQueryDto } from './dto/get-all-foods-query.dto';
import { CreateFoodDto } from './dto/create-food.dto';
import { UpdateFoodDto } from './dto/update-food.dto';
import { CreateExerciseDto } from './dto/create-exercise.dto';
import { GetAllExercisesQueryDto } from './dto/get-all-exercises-query.dto';
import { sql } from 'kysely';
import { CreatePlanDto } from './dto/plans/create-plan.dto';
import { UpdatePlanDto } from './dto/plans/update-plan.dto';
import { ConfigPlanDto } from './dto/plans/config-plan.dto';
import { Stripe } from 'stripe';

@Injectable()
export class AdminService {
      private stripe: Stripe;

      constructor() {
        // @ts-ignore
        this.stripe = new Stripe(process.env.STRIPE_SK, {
          // apiVersion: '2025-03-31',
        });
      }

      formatDatetime(datetime: any): string {
            return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
      }

      async getStats() {
        // Contagem de usuários com role_id = 4 (usuários comuns)
        const totalUsers = await db
          .selectFrom('users_roles')
          .where('role_id', '=', 4)
          .select(db.fn.count<number>('user_id').as('count'))
          .executeTakeFirst();

        // Contagem de usuários com role_id = 2 (nutritionistas) ou role_id = 3 (coaches)
        const totalProfessionals = await db
          .selectFrom('users_roles')
          .where('role_id', 'in', [2, 3])
          .select(db.fn.count<number>('user_id').as('count'))
          .executeTakeFirst();

        // Contagem de assinaturas ativas
        const activeSubscriptions = await db
          .selectFrom('users_subscriptions')
          .where('status', '=', 'active')
          .where('deleted_at', 'is', null)
          .select(db.fn.count<number>('id').as('count'))
          .executeTakeFirst();

        // Cálculo da receita mensal (transações pagas no mês atual)
        const currentDate = new Date();
        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const lastDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

        const monthlyRevenue = await db
          .selectFrom('transactions')
          .where('status', '=', 'paid')
          .where('created_at', '>=', firstDayOfMonth)
          .where('created_at', '<=', lastDayOfMonth)
          .select(db.fn.sum<number>('amount').as('total'))
          .executeTakeFirst();

        // Retornar as estatísticas
        return {
          status: 'success',
          data:
            {
              total_users: Number(totalUsers?.count || 0),
              total_professionals: Number(totalProfessionals?.count || 0),
              active_subscriptions: Number(activeSubscriptions?.count || 0),
              monthly_revenue: Number(monthlyRevenue?.total || 0),
            },
        };
      }

      async getAllUsers(query: GetAllUsersQueryDto) {
        const { q, role_id } = query;
        let { page = 1, limit = 100 } = query;
        const offset = (page - 1) * limit;

        let queryBuilder = db
          .selectFrom('users')
          .leftJoin('users_roles', 'users.id', 'users_roles.user_id')
          .leftJoin('roles', 'users_roles.role_id', 'roles.id')
          .select([
            'users.id',
            'users.name',
            'users.email',
            'users.photo',
            'users.created_at',
            db.fn<string>('group_concat', ['roles.name']).as('roles'),
          ])
          .groupBy('users.id')
          .orderBy('users.created_at', 'desc');

        if (q) {
          queryBuilder = queryBuilder.where((eb) =>
            eb.or([
              eb('users.name', 'like', `%${q}%`),
              eb('users.email', 'like', `%${q}%`),
            ])
          );
        }

        if (role_id) {
          queryBuilder = queryBuilder.where('roles.id', '=', role_id);
        }

        const [data, total] = await Promise.all([
          queryBuilder.limit(limit).offset(offset).execute(),
          db.selectFrom('users').select(db.fn.countAll().as('total')).executeTakeFirst(),
        ]);

        return {
          status: 'success',
          data: data.map((row) => ({
            id: row.id,
            name: row.name,
            email: row.email,
            roles: row.roles ? row.roles.split(', ') : [],
            photo: row.photo,
            createdAt: this.formatDatetime(row.created_at),
          })),
          pagination: {
            page,
            limit,
            total: Number(total?.total),
          },
        };
      }


      async createUser(createUserDto: CreateUserDto) {
        const { name, email, password, role_id, photo } = createUserDto;

        // Verifica se o e-mail já está cadastrado
        const existingUser = await db
          .selectFrom('users')
          .where('email', '=', email)
          .select('id')
          .executeTakeFirst();

        if (existingUser) {
          throw new ConflictException('E-mail already exists');
        }

        // Criptografa a senha
        const hashedPassword = await bcrypt.hash(password, 10);

        // Insere o novo usuário no banco de dados
        const result = await db
          .insertInto('users')
          .values({
            name,
            email,
            password: hashedPassword, // Armazena a senha criptografada
            photo,
            created_at: new Date(),
            updated_at: new Date(),
          })
          .executeTakeFirst();

        // Obtém o ID do usuário recém-inserido
        const userId: any = result.insertId;

        // Associa o usuário ao papel (role)
        await db
          .insertInto('users_roles')
          .values({
            user_id: userId,
            role_id,
          })
          .execute();

        return {
          status: 'success',
          data: [],
        };
      }


      async updateUser(id: number, updateUserDto: UpdateUserDto) {
        const { name, email, password, role_id, photo } = updateUserDto;

        // Verifica se o usuário existe
        const existingUser = await db
          .selectFrom('users')
          .where('id', '=', id)
          .select(['id', 'email'])
          .executeTakeFirst();

        if (!existingUser) {
          throw new NotFoundException('User not found');
        }

        // Verifica se o e-mail já está em uso por outro usuário
        if (email !== existingUser.email) {
          const emailExists = await db
            .selectFrom('users')
            .where('email', '=', email)
            .select('id')
            .executeTakeFirst();

          if (emailExists) {
            throw new ConflictException('E-mail already exists');
          }
        }

        // Prepara os valores para atualização
        const updateValues: any = {
          name,
          email,
          photo,
          updated_at: new Date(),
        };

        // Criptografa a nova senha, se fornecida
        if (password) {
          const hashedPassword = await bcrypt.hash(password, 10);
          updateValues.password = hashedPassword;
        }

        // Atualiza o usuário no banco de dados
        await db
          .updateTable('users')
          .set(updateValues)
          .where('id', '=', id)
          .execute();

        // Atualiza o papel (role) do usuário na tabela intermediária
        await db
          .updateTable('users_roles')
          .set({ role_id })
          .where('user_id', '=', id)
          .execute();

        // Retorna o usuário atualizado
        const updatedUser = await db
          .selectFrom('users')
          .where('id', '=', id)
          .select(['id', 'name', 'email', 'photo', 'created_at', 'updated_at'])
          .executeTakeFirstOrThrow();

        return {
          status: 'success',
          data: updatedUser,
        };
      }

      async deleteUser(id: number) {
        // Verifica se o usuário existe
        const existingUser = await db
          .selectFrom('users')
          .where('id', '=', id)
          .select('id')
          .executeTakeFirst();

        if (!existingUser) {
          throw new NotFoundException('User not found');
        }

        // Remove as roles associadas ao usuário
        await db
          .deleteFrom('users_roles')
          .where('user_id', '=', id)
          .execute();

        // Remove o usuário
        await db
          .deleteFrom('users')
          .where('id', '=', id)
          .execute();

        return {
          status: 'success',
          message: 'User deleted successfully',
        };
      }

      async getSelectOptions(type: string) {
        const options =  await db
          .selectFrom('select_options')
          .select(['id', 'value_option'])
          .where('area_key', '=', type)
          .orderBy(['sort_order', 'value_option'])
          .execute();

          return {
            status: 'success',
            data: options,
          }
      }


      async getAllFoods(query: GetAllFoodsQueryDto): Promise<any> {
        const { name, category_id, page = 1, limit = 100 } = query;

        // Cria o construtor de consulta base
        let queryBuilder = db
          .selectFrom('foods')
          .innerJoin('select_options as category', 'category.id', 'foods.category_id')
          .select([
            'foods.id',
            'foods.name',
            'foods.quantity',
            'foods.unit as serving_unit',
            // (qb: any) => qb.ref('serving_unit.value_option').as('serving_unit'), // Referência explícita
            'foods.calories',
            'foods.protein',
            'foods.carbs',
            'foods.fat',
            (qb: any) => qb.ref('category.value_option').as('category'), // Referência explícita
          ]);

        // Aplica filtro por nome, se fornecido
        if (name) {
          queryBuilder = queryBuilder.where('foods.name', 'like', `%${name}%`);
        }

        // Aplica filtro por categoria, se fornecido
        if (category_id) {
          queryBuilder = queryBuilder.where('foods.category_id', '=', category_id);
        }

        // Cria uma nova consulta para calcular o total de registros
        const totalRecordsQuery = db
          .selectFrom('foods')
          .innerJoin('select_options as category', 'category.id', 'foods.category_id');

        // Reaplica os mesmos filtros na nova consulta
        if (name) {
          totalRecordsQuery.where('foods.name', 'like', `%${name}%`);
        }
        if (category_id) {
          totalRecordsQuery.where('foods.category_id', '=', category_id);
        }

        // Calcula o total de registros correspondentes aos filtros
        const totalRecords = await totalRecordsQuery
          .select(db.fn.countAll().as('count'))
          .executeTakeFirst();

        const total = Number(totalRecords?.count || 0);

        // Aplica paginação na consulta principal
        const offset = (page - 1) * limit;
        queryBuilder = queryBuilder.limit(limit).offset(offset);

        // Executa a consulta para obter os alimentos paginados
        const foods = await queryBuilder.execute();

        // Retorna os dados com informações de paginação
        return {
          status: 'success',
          data: foods,
          pagination: {
            page: page,
            limit: limit,
            total: total,
            totalPages: Math.ceil(total / limit),
          },
        };
      }

      async searchFoods(query: { q: string; limit?: number }): Promise<any> {
        const { q, limit = 20 } = query;

        if (!q || q.trim().length === 0) {
          return {
            status: 'success',
            data: [],
          };
        }

        const foods = await db
          .selectFrom('foods')
          .innerJoin('select_options as category', 'category.id', 'foods.category_id')
          .select([
            'foods.id',
            'foods.name',
            'foods.quantity',
            'foods.unit as serving_unit',
            'foods.calories',
            'foods.protein',
            'foods.carbs',
            'foods.fat',
            'foods.fiber',
            (qb: any) => qb.ref('category.value_option').as('category'),
          ])
          .where('foods.name', 'like', `%${q.trim()}%`)
          .limit(limit)
          .execute();

        return {
          status: 'success',
          data: foods,
        };
      }

      async createFood(createFoodDto: CreateFoodDto) {
        const {
          name,
          category_id,
          quantity,
          unit,
          calories,
          protein,
          carbs,
          fat,
          fiber,
        } = createFoodDto;

        // Verifica se a categoria existe
        const categoryExists = await db
          .selectFrom('select_options')
          .where('id', '=', category_id)
          .select('id')
          .executeTakeFirst();

        if (!categoryExists) {
          throw new ConflictException('Category does not exist');
        }


        // Insere o novo alimento no banco de dados
        const result = await db
          .insertInto('foods')
          .values({
            name,
            category_id,
            quantity,
            unit,
            calories,
            protein,
            carbs,
            fat,
            fiber,
            created_at: new Date(),
            updated_at: new Date(),
          })
          // .returning(['id', 'name', 'serving', 'calories'])
          .executeTakeFirstOrThrow();

        return {
          status: 'success',
          data: [],
        };
      }


  async updateFood(id: number, updateFoodDto: UpdateFoodDto) {
    const {
      name,
      category_id,
      quantity,
      unit,
      calories,
      protein,
      carbs,
      fat,
      fiber,
    } = updateFoodDto;

    // Verifica se o alimento existe
    const existingFood = await db
      .selectFrom('foods')
      .where('id', '=', id)
      .select('id')
      .executeTakeFirst();

    if (!existingFood) {
      throw new NotFoundException('Food not found');
    }

    // Verifica se a categoria existe
    const categoryExists = await db
      .selectFrom('select_options')
      .where('id', '=', category_id)
      .select('id')
      .executeTakeFirst();

    if (!categoryExists) {
      throw new ConflictException('Category does not exist');
    }

    // Atualiza o alimento no banco de dados
    const result = await db
      .updateTable('foods')
      .set({
        name,
        category_id,
        quantity,
        unit,
        calories,
        protein,
        carbs,
        fat,
        fiber,
        updated_at: new Date(),
      })
      .where('id', '=', id)
      // .returning(['id', 'name', 'serving', 'calories'])
      .executeTakeFirstOrThrow();

    return {
      status: 'success',
      data: [],
    };
  }

  async deleteFood(id: number) {
    // Verifica se o alimento existe
    const existingFood = await db
      .selectFrom('foods')
      .where('id', '=', id)
      .select('id')
      .executeTakeFirst();

    if (!existingFood) {
      throw new NotFoundException('Food not found');
    }

    // Exclui o alimento do banco de dados
    await db
      .deleteFrom('foods')
      .where('id', '=', id)
      .execute();

    return {
      status: 'success',
      message: 'Food deleted successfully',
    };
  }

  // Exercícios
  async getAllExercises(query: GetAllExercisesQueryDto) {
    const { name, muscle_group_id, page = 1, limit = 100 } = query;

    // Passo 1: Buscar os exercícios principais
    let queryBuilder = db
      .selectFrom('exercises as e')
      .innerJoin('select_options as muscle_group', 'muscle_group.id', 'e.muscle_group_id')
      .innerJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')
      .select([
        'e.id',
        'e.name',
        (qb) => qb.ref('muscle_group.value_option').as('muscle_group'),
        (qb) => qb.ref('equipment.value_option').as('equipment'),
      ])
      .where('e.deleted_at', 'is', null);

    // Aplica filtro por nome
    if (name) {
      queryBuilder = queryBuilder.where('e.name', 'like', `%${name}%`);
    }

    // Aplica filtro por grupo muscular
    if (muscle_group_id) {
      queryBuilder = queryBuilder.where('e.muscle_group_id', '=', muscle_group_id);
    }

    // Calcula o total de registros correspondentes aos filtros
    let totalRecordsQuery = db
      .selectFrom('exercises as e')
      .innerJoin('select_options as muscle_group', 'muscle_group.id', 'e.muscle_group_id')
      .innerJoin('select_options as equipment', 'equipment.id', 'e.equipment_id');

    if (name) {
      totalRecordsQuery = totalRecordsQuery.where('e.name', 'like', `%${name}%`);
    }
    if (muscle_group_id) {
      totalRecordsQuery = totalRecordsQuery.where('e.muscle_group_id', '=', muscle_group_id);
    }

    const totalRecords = await totalRecordsQuery
      .select(db.fn.countAll().as('count'))
      .where('e.deleted_at', 'is', null)
      .executeTakeFirst();

    const total = Number(totalRecords?.count || 0);

    // Aplica paginação
    const offset = (page - 1) * limit;
    queryBuilder = queryBuilder
    .limit(limit).offset(offset);

    // Executa a consulta principal
    const exercises = await queryBuilder.execute();

    // Se não houver exercícios, retorna uma resposta vazia
    if (exercises.length === 0) {
      return {
        status: 'success',
        data: [],
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    }

    // Passo 2: Buscar os IDs dos exercícios
    const exerciseIds = exercises.map((exercise) => exercise.id);

    // Passo 3: Buscar os itens relacionados
    const relatedItems = await db
      .selectFrom('exercises_items')
      .where('exercise_id', 'in', exerciseIds)
      .select(['exercise_id', 'key_item', 'value_item'])
      .execute();

    // Passo 4: Organizar os dados finais
    const formattedExercises = exercises.map((exercise) => {
      const targetMuscles = relatedItems
        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'target_muscles')
        .map((item) => item.value_item);

      const synergisticMuscles = relatedItems
        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'synergistic_muscles')
        .map((item) => item.value_item);

      const instructions = relatedItems
        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'instructions')
        .map((item) => item.value_item);

      const tips = relatedItems
        .filter((item) => item.exercise_id === exercise.id && item.key_item === 'tips')
        .map((item) => item.value_item);

      return {
        id: exercise.id,
        name: exercise.name,
        muscle_group: exercise.muscle_group,
        equipment: exercise.equipment,
        target_muscles: targetMuscles,
        synergistic_muscles: synergisticMuscles,
        instructions: instructions,
        tips: tips,
      };
    });

    return {
      status: 'success',
      data: formattedExercises,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }


  async createExercise(createExerciseDto: CreateExerciseDto) {
    const {
      name,
      muscle_group_id,
      equipment_id,
      media_url,
      target_muscles,
      synergistic_muscles,
      instructions,
      tips,
    } = createExerciseDto;

    // Verifica se o grupo muscular existe
    const muscleGroupExists = await db
      .selectFrom('select_options')
      .where('id', '=', muscle_group_id)
      .select('id')
      .executeTakeFirst();

    if (!muscleGroupExists) {
      throw new ConflictException('Muscle group does not exist');
    }

    // Verifica se o equipamento existe
    const equipmentExists = await db
      .selectFrom('select_options')
      .where('id', '=', equipment_id)
      .select('id')
      .executeTakeFirst();

    if (!equipmentExists) {
      throw new ConflictException('Equipment does not exist');
    }

    // Insere o novo exercício na tabela `exercises`
    const exerciseResult = await db
      .insertInto('exercises')
      .values({
        name,
        muscle_group_id,
        equipment_id,
        media_url: media_url || null,
        created_at: new Date(),
        updated_at: new Date(),
      })
      // .returning(['id'])
      .executeTakeFirstOrThrow();

      const exerciseId = Number(exerciseResult.insertId);

    // Insere os itens relacionados na tabela `exercises_items`
    const itemsToInsert: any[] = [];

    // Target muscles
    target_muscles?.forEach((muscle) => {
      itemsToInsert.push({
        exercise_id: exerciseId,
        key_item: 'target_muscles',
        value_item: muscle,
      });
    });

    // Synergistic muscles
    synergistic_muscles?.forEach((muscle) => {
      itemsToInsert.push({
        exercise_id: exerciseId,
        key_item: 'synergistic_muscles',
        value_item: muscle,
      });
    });

    // Instructions
    instructions?.forEach((instruction, index) => {
      itemsToInsert.push({
        exercise_id: exerciseId,
        key_item: `instructions`,
        value_item: instruction,
      });
    });

    // Tips (opcional)
    if (tips) {
      tips.forEach((tip, index) => {
        itemsToInsert.push({
          exercise_id: exerciseId,
          key_item: `tips`,
          value_item: tip,
        });
      });
    }

    // Insere todos os itens na tabela `exercises_items`
    if (itemsToInsert.length > 0) {
      await db.insertInto('exercises_items').values(itemsToInsert).execute();
    }

    return {
      status: 'success',
      data: { id: exerciseId, name },
    };
  }

  async deleteExercise(id: number) {
    // Verifica se o exercício existe
    const existingExercise = await db
      .selectFrom('exercises')
      .where('id', '=', id)
      .select('id')
      .executeTakeFirst();

    if (!existingExercise) {
      throw new NotFoundException('Exercise not found');
    }

    // Exclui o exercício do banco de dados
    await db
      .deleteFrom('exercises')
      .where('id', '=', id)
      .execute();

    return {
      status: 'success',
      message: 'Exercise deleted successfully',
    };
  }

  // Plans management
  async getAllPlans() {
    const plans = await db
     .selectFrom('plans as p1')
     .leftJoin('plans_payments_providers as p2', 'p1.id', 'p2.plan_id')
     .leftJoin('payment_providers as p3', 'p2.payment_provider_id', 'p3.id')
     .select([
      'p1.id', 'p1.is_active', 'p1.name', 'p1.description',
      'p1.price', 'p1.currency', 'p1.snaptokens',
      sql`COALESCE(p2.price, p1.price)`.as('provider_price'),
      sql`COALESCE(p2.currency, p1.currency)`.as('provider_currency'),
      sql`COALESCE(p2.snaptokens, p1.snaptokens)`.as('provider_snaptokens'),
      'p2.id as config_id',
      'p1.frequency', 'p1.interval_value', 'p1.is_active', 'p1.role_id', 'p1.user_id', 'p1.snaptokens', 'p1.allows_trial', 'p1.trial_period_days', 'p1.affiliate_master_commission_percent', 'p1.affiliate_commission_percent', 'p1.created_at',
      'p2.platform', 'p2.payment_provider_external_id',
      'p3.name as payment_provider_name',
     ])
     .where('p1.deleted_at', 'is', null)
     .where('p2.deleted_at', 'is', null)
     .execute();

     const getPeriod = (frequency: string, interval_value: number): string => {
      // 'monthly' | 'quarterly' | 'semiannual' | 'annual'
      if (frequency === 'monthly' && interval_value === 1) {
        return 'monthly';
      } else if (frequency === 'monthly' && interval_value === 3) {
        return 'quarterly';
      } else if (frequency === 'monthly' && interval_value === 6) {
        return 'semiannual';
      } else if (frequency === 'annually' && interval_value === 1) {
        return 'annual';
      } else {
        return 'other';
      }
    }

    // Agrupar os providers por plano
    const groupedPlans = plans.reduce<Record<number, any>>((acc, plan) => {
      const { id, payment_provider_name, platform, payment_provider_external_id, provider_price, provider_currency, provider_snaptokens } = plan;

      if (!acc[id]) {
        acc[id] = {
          id,
          is_active: plan.is_active,
          name: plan.name,
          description: plan.description,
          price: Number(plan.price),
          period: getPeriod(plan.frequency, plan.interval_value),
          snaptokens: plan.snaptokens,
          affiliate_master_commission_percent: Number(plan.affiliate_master_commission_percent),
          affiliate_commission_percent: Number(plan.affiliate_commission_percent),
          payment_config: []
        };
      }

      // Se houver provider, adicione ao array
      if (payment_provider_name) {
        acc[id].payment_config.push({
          config_id: plan.config_id,
          payment_provider: payment_provider_name.replace(/\s+/g, '_').toLowerCase(),
          platform,
          price: Number(provider_price),

          // currency: provider_currency,
          snaptokens: provider_snaptokens,
          payment_provider_external_id,
        });
      }

      return acc;
    }, {});

    // Converter para array
    const formattedPlans = Object.values(groupedPlans);

    return {
      status:'success',
      data: formattedPlans,
    };
  }

  async createPlan(createPlanDto: CreatePlanDto, userId: number) {
    const user_id = userId;
    const role_id_users = 4;
    const currency = 'BRL';
    const allows_trial = null;
    const trial_period_days = null;

    const {
      name,
      description,
      price,
      period,
      isActive,
      snaptokens,
      affiliate_master_commission_percent,
      affiliate_commission_percent
    } = createPlanDto;

    const is_active = (isActive === true) ? 1 : null;

    const getPeriod = (period: string): { frequency: 'daily' | 'weekly' | 'monthly' | 'annually'; interval_value: number } => {
      if (period === 'monthly') {
        return { frequency: 'monthly', interval_value: 1 };
      } else if (period === 'quarterly') {
        return { frequency: 'monthly', interval_value: 3 };
      } else if (period === 'semiannual') {
        return { frequency: 'monthly', interval_value: 6 };
      } else if (period === 'annually') {
        return { frequency: 'annually', interval_value: 1 };
      } else {
        return { frequency: 'monthly', interval_value: 1 };
      }
    }

    const { frequency, interval_value }: { frequency: 'daily' | 'weekly' | 'monthly' | 'annually'; interval_value: number } = getPeriod(period);

    // Insere o novo plano na tabela `plans`
    const planResult = await db
      .insertInto('plans')
      .values({
        name,
        description,
        price,
        currency,
        frequency,
        interval_value,
        is_active,
        role_id: role_id_users,
        user_id,
        snaptokens,
        allows_trial,
        trial_period_days,
        affiliate_master_commission_percent,
        affiliate_commission_percent,
        created_at: new Date(),
        updated_at: new Date(),
      })
      // .returning(['id'])
      .executeTakeFirstOrThrow();

      const planId = Number(planResult.insertId);

    return {
      status: 'success',
      data: { id: planId, name },
    };
  }

  async updatePlan(id: number, updatePlanDto: UpdatePlanDto) {
    const {
      name,
      description,
      price,
      currency,
      frequency,
      interval_value,
      isActive,
      role_id,
      user_id,
      snaptokens,
      allows_trial,
      trial_period_days,
      affiliate_master_commission_percent,
      affiliate_commission_percent
    } = updatePlanDto;

    const is_active = (isActive === true) ? 1 : null;

    // Atualiza o plano na tabela `plans`
    const planResult = await db
      .updateTable('plans')
      .set({
        name,
        description,
        price,
        currency,
        frequency,
        interval_value,
        is_active,
        role_id,
        user_id,
        snaptokens,
        allows_trial,
        trial_period_days,
        affiliate_master_commission_percent,
        affiliate_commission_percent,
        updated_at: new Date(),
      })
      .where('id', '=', id)
      // .returning(['id'])
      .executeTakeFirstOrThrow();

    return {
      status: 'success',
      data: { id, name },
    };
  }

  async configPlan(id: number, configPlanDto: ConfigPlanDto) {
    console.log('configPlanDto', configPlanDto);


    const payment_provider_ids = {
      'stripe': 1,
      'google_play_billing': 2,
      'apple_iap': 3,
    };

    const payment_provider_id = payment_provider_ids[configPlanDto.payment_provider];

    const {
      platform,
      price,
      // currency,
      snaptokens,
      payment_provider_external_id,
    } = configPlanDto;

    const currency = 'BRL';

    // Verifica se o provedor de pagamento existe
    const existingPaymentProvider = await db
      .selectFrom('payment_providers')
      .where('id', '=', payment_provider_id)
      .select('id')
      .executeTakeFirst();

    if (!existingPaymentProvider) {
      throw new NotFoundException('Payment provider not found');
    }

    // Verifica se o provedor já está configurado para o plano
    const existingPlanConfig = await db
      .selectFrom('plans_payments_providers')
      .where('plan_id', '=', id)
      .where('payment_provider_id', '=', payment_provider_id)
      .where('platform', '=', platform)
      .select('id')
      .executeTakeFirst();

    if (existingPlanConfig) {
      // Atualiza a configuração do plano para o provedor de pagamento
      const planConfigResult = await db
        .updateTable('plans_payments_providers')
        .set({
          price,
          currency,
          snaptokens,
          payment_provider_external_id,
          updated_at: new Date(),
        })
        .where('id', '=', existingPlanConfig.id)
        .executeTakeFirstOrThrow();
    }

    // Insere ou atualiza a configuração do plano para o provedor de pagamento
    if (!existingPlanConfig) {
      const planConfigResult = await db
      .insertInto('plans_payments_providers')
      .values({
        plan_id: id,
        platform,
        payment_provider_id,
        price,
        currency,
        snaptokens,
        payment_provider_external_id,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .executeTakeFirstOrThrow();
    }

    return {
      status: 'success',
      data: { id, payment_provider_id },
    };
  }

  async deletePlan(id: number) {
    // Verifica se o plano existe
    const existingPlan = await db
      .selectFrom('plans')
      .where('id', '=', id)
      .select('id')
      .executeTakeFirst();

    if (!existingPlan) {
      throw new NotFoundException('Plan not found');
    }

    // Atualiza o deleted_at do plano
    await db
      .updateTable('plans')
      .set({
        deleted_at: new Date(),
      })
      .where('id', '=', id)
      .execute();

    return {
      status: 'success',
      message: 'Plan deleted successfully',
    };
  }

  async deletePlanConfig(id: number) {
    // Verifica se a configuração do plano existe
    const existingPlanConfig = await db
      .selectFrom('plans_payments_providers')
      .where('id', '=', id)
      .select('id')
      .executeTakeFirst();

    if (!existingPlanConfig) {
      throw new NotFoundException('Plan config not found');
    }

    // Exclui a configuração do plano
    await db
      .updateTable('plans_payments_providers')
      .set({
        deleted_at: new Date(),
      })
      .where('id', '=', id)
      .execute();

    return {
      status: 'success',
      message: 'Plan config deleted successfully',
    };
  }

  async getAllSubscriptions(query: any) {
    const { q, status } = query;
    let { page = 1, limit = 100 } = query;
    const offset = (page - 1) * limit;

    let queryBuilder = db
      .selectFrom('users_subscriptions')
      .leftJoin('users', 'users_subscriptions.user_id', 'users.id')
      .leftJoin('plans', 'users_subscriptions.plan_id', 'plans.id')
      .leftJoin('plans_payments_providers', 'users_subscriptions.plan_payment_provider_id', 'plans_payments_providers.id')
      .leftJoin('payment_providers', 'plans_payments_providers.payment_provider_id', 'payment_providers.id')
      .select([
        'users_subscriptions.id',
        'users_subscriptions.status',
        'users_subscriptions.platform',
        'users_subscriptions.price',
        'users_subscriptions.currency',
        'users_subscriptions.start_date',
        'users_subscriptions.end_date',
        'users_subscriptions.next_billing_date',
        'users_subscriptions.cancel_at_period_end',
        'users_subscriptions.is_trial',
        'users_subscriptions.trial_start_date',
        'users_subscriptions.trial_end_date',
        'users_subscriptions.snaptokens',
        'users_subscriptions.created_at',
        'users.name as user_name',
        'users.email as user_email',
        'plans.name as plan_name',
        'payment_providers.name as payment_provider_name',
      ])
      .where('users_subscriptions.deleted_at', 'is', null)
      .orderBy('users_subscriptions.created_at', 'desc');

    if (q) {
      queryBuilder = queryBuilder.where((eb) =>
        eb.or([
          eb('users.name', 'like', `%${q}%`),
          eb('users.email', 'like', `%${q}%`)
        ])
      );
    }

    if (status) {
      queryBuilder = queryBuilder.where('users_subscriptions.status', '=', status);
    }

    const [data, total] = await Promise.all([
      queryBuilder.limit(Number(limit)).offset(Number(offset)).execute(),
      db.selectFrom('users_subscriptions').select(db.fn.countAll().as('total')).executeTakeFirst(),
    ]);

    return {
      status: 'success',
      data: data.map((row) => ({
        id: row.id,
        user_name: row.user_name,
        user_email: row.user_email,
        status: row.status,
        platform: row.platform,
        price: row.price,
        currency: row.currency,
        start_date: row.start_date,
        end_date: row.end_date,
        next_billing_date: row.next_billing_date,
        cancel_at_period_end: row.cancel_at_period_end,
        is_trial: row.is_trial,
        trial_start_date: row.trial_start_date,
        trial_end_date: row.trial_end_date,
        snaptokens: row.snaptokens,
        created_at: row.created_at,
        plan_name: row.plan_name,
        payment_provider_name: row.payment_provider_name,
      })),
      pagination: {
        page,
        limit,
        total: Number(total?.total),
      },
    };
  }

  async getAllTransactions(query: any) {
    const { q, status } = query;
    let { page = 1, limit = 100 } = query;
    const offset = (page - 1) * limit;

    let queryBuilder = db
      .selectFrom('transactions')
      .leftJoin('users', 'transactions.user_id', 'users.id')
      .leftJoin('payment_providers', 'transactions.payment_provider_id', 'payment_providers.id')
      .select([
        'transactions.id',
        'transactions.provider_transaction_id',
        'transactions.amount',
        'transactions.currency',
        'transactions.status',
        'transactions.source_type',
        'transactions.source_id',
        'transactions.created_at',
        'users.name as user_name',
        'users.email as user_email',
        'payment_providers.name as payment_provider_name',
      ])
      .where('transactions.deleted_at', 'is', null)
      .orderBy('transactions.created_at', 'desc');

    if (q) {
      queryBuilder = queryBuilder.where((eb) =>
        eb.or([
          eb('users.name', 'like', `%${q}%`),
          eb('users.email', 'like', `%${q}%`)
        ])
      );
    }

    if (status) {
      queryBuilder = queryBuilder.where('transactions.status', '=', status);
    }

    const [data, total] = await Promise.all([
      queryBuilder.limit(Number(limit)).offset(Number(offset)).execute(),
      db.selectFrom('transactions').select(db.fn.countAll().as('total')).executeTakeFirst(),
    ]);

    return {
      status: 'success',
      data: data.map((row) => ({
        id: row.id,
        user_name: row.user_name,
        user_email: row.user_email,
        provider_transaction_id: row.provider_transaction_id,
        amount: row.amount,
        currency: row.currency,
        status: row.status,
        source_type: row.source_type,
        source_id: row.source_id,
        created_at: row.created_at,
        payment_provider_name: row.payment_provider_name,
      })),
      pagination: {
        page,
        limit,
        total: Number(total?.total),
      },
    };
  }

  // Affiliates
async getAffiliates(query: any) {
    const { q, status, is_master } = query;
    let { page = 1, limit = 100 } = query;
    const offset = (page - 1) * limit;

    let queryBuilder = db
      .selectFrom('affiliates')
      .leftJoin('users', 'affiliates.user_id', 'users.id')
      .select([
        'users.id',
        'affiliates.status',
        'affiliates.is_master',
        'affiliates.accepted_at',
        'affiliates.created_at',
        'users.name as name',
        'users.email as email'
      ])
      .where('affiliates.deleted_at', 'is', null)
      .orderBy('affiliates.created_at', 'desc');

    if (q) {
      queryBuilder = queryBuilder.where((eb) =>
        eb.or([
          eb('users.name', 'like', `%${q}%`),
          eb('users.email', 'like', `%${q}%`)
        ])
      );
    }

    if (status) {
      queryBuilder = queryBuilder.where('affiliates.status', '=', status);
    }

    if (is_master) {
      queryBuilder = queryBuilder.where('affiliates.is_master', '=', is_master);
    }

    const [data, total] = await Promise.all([
      queryBuilder.limit(Number(limit)).offset(Number(offset)).execute(),
      db.selectFrom('affiliates').select(db.fn.countAll().as('total')).executeTakeFirst(),
    ]);

    return {
      status: 'success',
      data: data.map((row) => ({
        id: row.id,
        status: row.status,
        name: row.name,
        email: row.email,
        is_master: row.is_master,
        accepted_at: row.accepted_at,
        created_at: row.created_at,
      })),
      pagination: {
        page,
        limit,
        total: Number(total?.total),
      },
    };
  }



      async createAffiliateAccount(email: string, name: string, affiliateId?: string): Promise<string> {
      const account = await this.stripe.accounts.create({
        type: 'express',
        country: 'BR',
        email: email,
        business_type: 'individual',
        individual: {
          first_name: name.split(' ')[0],
          last_name: name.split(' ').slice(1).join(' '),
        },
        metadata: { affiliateId: affiliateId || '' },
      });
      return account.id;
      }

    async updateAffiliate(id: number, updateAffiliateDto: any) {
      const { status, is_master } = updateAffiliateDto;

      // Verifica se o afiliado existe
      const existingAffiliate = await db
        .selectFrom('affiliates')
        .where('user_id', '=', id)
        .select(['id', 'accepted_at'])
        .executeTakeFirst();

      if (!existingAffiliate) {
        throw new NotFoundException('Affiliate not found');
      }

      const isNew = !existingAffiliate.accepted_at;
      let acceptedIfActive = status === 'active' ? new Date() : null;
      let accepted_at = existingAffiliate.accepted_at ? existingAffiliate.accepted_at : acceptedIfActive;

      // Atualiza o afiliado
      await db
        .updateTable('affiliates')
        .set({
          status,
          is_master: is_master ? 1 : null,
          accepted_at,
          updated_at: new Date(),
        })
        .where('user_id', '=', id)
        .execute();


      if (isNew && status === 'active') {
        const affData = await db.selectFrom('affiliates')
        .leftJoin('users', 'users.id', 'affiliates.user_id')
        .select(['users.id', 'users.email', 'users.name'])
        .where('affiliates.user_id', '=', id)
        .executeTakeFirst();

        if (affData) {
          const name: string = affData.name || '';
          const email: string = affData.email || '';
          const account = await this.createAffiliateAccount(email, name, id.toString());

          if(account) {
            await db
            .updateTable('affiliates')
            .set({
              stripeId: account,
              updated_at: new Date(),
            })
            .where('user_id', '=', id)
            .execute();
          }
        }

      }

      return {
        status: 'success',
        data: [],
      };
    }

    // Webhook Stripe
    async webhook(body: any) {
      try {
        // Verificar se o body é válido
        if (!body || !body.type || !body.data || !body.data.object) {
          console.error('Webhook body inválido:', body);
          return {
            status: 'error',
            message: 'Webhook body inválido',
          };
        }

        const event = body;
        const eventType = event.type;

        // Verificar a assinatura do webhook (em produção)
        // const signature = request.headers['stripe-signature'];
        // const event = this.stripe.webhooks.constructEvent(
        //   request.body,
        //   signature,
        //   process.env.STRIPE_WEBHOOK_SECRET
        // );

        // console.log(`Webhook received: ${eventType}`);
        // console.log('Webhook data:', JSON.stringify(event.data.object, null, 2));

        try {
          switch (eventType) {
            // Eventos de assinatura
            case 'customer.subscription.created':
              await this.handleSubscriptionCreated(event.data.object);
              break;
            case 'customer.subscription.updated':
              await this.handleSubscriptionUpdated(event.data.object);
              break;
            case 'customer.subscription.deleted':
              await this.handleSubscriptionDeleted(event.data.object);
              break;
            case 'customer.subscription.trial_will_end':
              await this.handleSubscriptionTrialWillEnd(event.data.object);
              break;

            // Eventos de pagamento
            case 'invoice.created':
              await this.handleInvoiceCreated(event.data.object);
              break;
            case 'invoice.paid':
              await this.handleInvoicePaid(event.data.object);
              break;
            case 'invoice.payment_failed':
              await this.handleInvoicePaymentFailed(event.data.object);
              break;
            case 'charge.succeeded':
              await this.handleChargeSucceeded(event.data.object);
              break;
            case 'charge.refunded':
              await this.handleChargeRefunded(event.data.object);
              break;
            case 'checkout.session.completed':
              await this.handleCheckoutSessionCompleted(event.data.object);
              break;
            case 'payment_intent.succeeded':
              await this.handlePaymentIntentSucceeded(event.data.object);
              break;
            case 'subscription_schedule.created':
              await this.handleSubscriptionScheduleCreated(event.data.object);
              break;

            default:
              console.log(`Evento não tratado: ${eventType}`);
          }
        } catch (handlerError) {
          console.error(`Erro ao processar evento ${eventType}:`, handlerError);
          return {
            status: 'error',
            message: `Erro ao processar evento ${eventType}: ${handlerError.message}`,
          };
        }

        return {
          status: 'success',
          message: 'Webhook processado com sucesso',
        };
      } catch (error) {
        console.error('Erro ao processar webhook:', error);
        return {
          status: 'error',
          message: `Webhook error: ${error.message}`,
        };
      }
    }

    // Manipuladores de eventos de assinatura
    private async handleSubscriptionCreated(subscription: any) {
      try {
        const customerId = subscription.customer;
        const user = await this.getUserByStripeCustomerId(customerId);

        if (!user) {
          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);
          return;
        }

        console.log(`Processando criação de assinatura para o usuário ${user.id}, customer_id: ${customerId}, subscription_id: ${subscription.id}, status: ${subscription.status}`);

        // Verificar se a assinatura já existe no banco de dados
        const existingSubscription = await db
          .selectFrom('users_subscriptions')
          .where('payment_provider_external_id', '=', subscription.id)
          .select(['id', 'status'])
          .executeTakeFirst();

        if (existingSubscription) {
          console.log(`Assinatura ${subscription.id} já existe no banco de dados com status '${existingSubscription.status}'. Atualizando...`);

          // Forçar status 'active' se a assinatura da Stripe estiver ativa
          if (subscription.status === 'active' && existingSubscription.status !== 'active') {
            await db
              .updateTable('users_subscriptions')
              .set({
                status: 'active',
                updated_at: new Date(),
              })
              .where('id', '=', existingSubscription.id)
              .execute();

            console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);
          }

          return existingSubscription.id;
        }

        const planPaymentProviderId = await this.getPlanPaymentProviderIdByPriceId(subscription.items.data[0].price.id);

        if (!planPaymentProviderId) {
          console.error(`Configuração de plano não encontrada para o price_id: ${subscription.items.data[0].price.id}`);
          return;
        }

        const planInfo = await this.getPlanInfoByPlanPaymentProviderId(planPaymentProviderId.id);

        if (!planInfo) {
          console.error(`Informações do plano não encontradas para o plan_payment_provider_id: ${planPaymentProviderId.id}`);
          return;
        }

        // IMPORTANTE: Sempre definir o status como 'active' se a assinatura estiver ativa na Stripe
        let status: 'pending' | 'active' | 'canceled' | 'paused' | 'expired';

        if (subscription.status === 'active') {
          status = 'active';
          console.log(`Assinatura ${subscription.id} está com status 'active' no Stripe, definindo como 'active' no banco de dados`);
        } else if (subscription.status === 'canceled') {
          status = 'canceled';
        } else if (subscription.status === 'past_due') {
          status = 'paused';
        } else if (subscription.status === 'unpaid') {
          status = 'paused';
        } else if (subscription.status === 'trialing') {
          status = 'active'; // Consideramos trial como ativo
        } else if (subscription.status === 'incomplete') {
          status = 'active'; // MUDANÇA IMPORTANTE: Definir como 'active' mesmo se estiver 'incomplete'
          console.log(`Assinatura ${subscription.id} está com status 'incomplete' no Stripe, mas definindo como 'active' no banco de dados`);
        } else {
          status = 'active'; // Default para active em caso de status desconhecido
          console.log(`Assinatura ${subscription.id} está com status '${subscription.status}' no Stripe, definindo como 'active' no banco de dados`);
        }

        // Verificar se é um trial
        const isTrial = subscription.trial_end !== null;

        // Garantir que temos uma data de início válida
        let startDate: Date;
        if (subscription.current_period_start) {
          startDate = new Date(subscription.current_period_start * 1000);
        } else if (subscription.created) {
          startDate = new Date(subscription.created * 1000);
        } else {
          startDate = new Date(); // Usar a data atual como fallback
        }

        // Garantir que temos uma data de próximo faturamento válida
        let nextBillingDate: Date | null = null;
        if (subscription.current_period_end) {
          nextBillingDate = new Date(subscription.current_period_end * 1000);
        }

        // Inserir na tabela users_subscriptions
        const subscriptionResult = await db
          .insertInto('users_subscriptions')
          .values({
            user_id: user.id,
            plan_id: planInfo.plan_id,
            plan_payment_provider_id: planPaymentProviderId.id,
            payment_provider_external_id: subscription.id,
            platform: planPaymentProviderId.platform || 'web',
            status,
            price: Number(subscription.items.data[0].price.unit_amount) / 100, // Convertendo de centavos para reais
            currency: subscription.currency.toUpperCase(),
            start_date: startDate,
            end_date: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : null,
            next_billing_date: nextBillingDate,
            cancel_at_period_end: subscription.cancel_at_period_end || false,
            is_trial: isTrial,
            trial_start_date: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
            trial_end_date: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
            snaptokens: planInfo.snaptokens,
            created_at: new Date(),
            updated_at: new Date(),
          })
          .executeTakeFirst();

        console.log(`Assinatura criada com sucesso para o usuário ${user.id} com status '${status}'`);

        return subscriptionResult && 'insertId' in subscriptionResult ? Number(subscriptionResult.insertId) : null;
      } catch (error) {
        console.error('Erro ao processar criação de assinatura:', error);
        throw error;
      }
    }

    private async handleSubscriptionUpdated(subscription: any) {
      try {
        // Buscar a assinatura existente
        const existingSubscription = await db
          .selectFrom('users_subscriptions')
          .where('payment_provider_external_id', '=', subscription.id)
          .select(['id', 'user_id', 'plan_id'])
          .executeTakeFirst();

        if (!existingSubscription) {
          console.error(`Assinatura não encontrada para atualização: ${subscription.id}`);
          return;
        }

        // Determinar o status da assinatura
        let status: 'pending' | 'active' | 'canceled' | 'paused' | 'expired' = 'pending';
        if (subscription.status === 'active') {
          status = 'active';
        } else if (subscription.status === 'canceled') {
          status = 'canceled';
        } else if (subscription.status === 'past_due') {
          status = 'paused';
        } else if (subscription.status === 'unpaid') {
          status = 'paused';
        } else if (subscription.status === 'trialing') {
          status = 'active'; // Consideramos trial como ativo
        } else if (subscription.status === 'incomplete_expired') {
          status = 'expired';
        } else if (subscription.status === 'incomplete') {
          // Mantém como pending até que o pagamento seja processado
          status = 'pending';
          console.log(`Assinatura ${subscription.id} está com status 'incomplete' no Stripe, mantendo como 'pending' no banco de dados`);
        }

        // Garantir que temos uma data de próximo faturamento válida
        let nextBillingDate: Date | null = null;
        if (subscription.current_period_end) {
          nextBillingDate = new Date(subscription.current_period_end * 1000);
        }

        // Atualizar a assinatura
        await db
          .updateTable('users_subscriptions')
          .set({
            status,
            next_billing_date: nextBillingDate,
            end_date: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : null,
            cancel_at_period_end: subscription.cancel_at_period_end || false,
            updated_at: new Date(),
          })
          .where('id', '=', existingSubscription.id)
          .execute();

        console.log(`Assinatura ${existingSubscription.id} atualizada com sucesso para status '${status}'`);
      } catch (error) {
        console.error('Erro ao processar atualização de assinatura:', error);
        throw error;
      }
    }

    private async handleSubscriptionDeleted(subscription: any) {
      try {
        // Buscar a assinatura existente
        const existingSubscription = await db
          .selectFrom('users_subscriptions')
          .where('payment_provider_external_id', '=', subscription.id)
          .select(['id'])
          .executeTakeFirst();

        if (!existingSubscription) {
          console.error(`Assinatura não encontrada para exclusão: ${subscription.id}`);
          return;
        }

        // Atualizar a assinatura para cancelada
        const status: 'canceled' = 'canceled';
        await db
          .updateTable('users_subscriptions')
          .set({
            status,
            end_date: new Date(),
            updated_at: new Date(),
          })
          .where('id', '=', existingSubscription.id)
          .execute();

        console.log(`Assinatura ${existingSubscription.id} marcada como cancelada`);
      } catch (error) {
        console.error('Erro ao processar exclusão de assinatura:', error);
        throw error;
      }
    }

    private async handleSubscriptionTrialWillEnd(subscription: any) {
      try {
        // Buscar a assinatura existente
        const existingSubscription = await db
          .selectFrom('users_subscriptions')
          .where('payment_provider_external_id', '=', subscription.id)
          .select(['id', 'user_id'])
          .executeTakeFirst();

        if (!existingSubscription) {
          console.error(`Assinatura não encontrada para notificação de fim de trial: ${subscription.id}`);
          return;
        }

        // Aqui você pode implementar lógica para notificar o usuário que o trial está acabando
        console.log(`Trial da assinatura ${existingSubscription.id} está prestes a terminar para o usuário ${existingSubscription.user_id}`);
      } catch (error) {
        console.error('Erro ao processar notificação de fim de trial:', error);
        throw error;
      }
    }

    // Manipuladores de eventos de pagamento
    private async handleInvoiceCreated(invoice: any) {
      console.log('Evento invoice.created recebido em handleInvoiceCreated:', invoice.id);

      try {
        // Verificar se a fatura está relacionada a uma assinatura
        if (!invoice.subscription) {
          console.log(`Fatura ${invoice.id} não está relacionada a uma assinatura`);
          return;
        }

        const customerId = invoice.customer;
        const user = await this.getUserByStripeCustomerId(customerId);

        if (!user) {
          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);
          return;
        }

        // Buscar a assinatura relacionada
        let subscription = await db
          .selectFrom('users_subscriptions')
          .where('payment_provider_external_id', '=', invoice.subscription)
          .select(['id', 'plan_id', 'plan_payment_provider_id'])
          .executeTakeFirst();

        // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado
        // Vamos tentar criar a assinatura com base na fatura
        if (!subscription) {
          console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);

          try {
            // Obter a assinatura do Stripe
            const stripeSubscription = await this.stripe.subscriptions.retrieve(invoice.subscription);

            // Criar a assinatura no banco de dados
            await this.handleSubscriptionCreated(stripeSubscription);

            // Buscar a assinatura novamente
            subscription = await db
              .selectFrom('users_subscriptions')
              .where('payment_provider_external_id', '=', invoice.subscription)
              .select(['id', 'plan_id', 'plan_payment_provider_id'])
              .executeTakeFirst();
          } catch (error) {
            console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);
          }
        }

        if (!subscription) {
          console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);
          return;
        }

        // Buscar o provedor de pagamento
        const paymentProvider = await db
          .selectFrom('plans_payments_providers as ppp')
          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')
          .where('ppp.id', '=', subscription.plan_payment_provider_id)
          .select(['pp.id as payment_provider_id'])
          .executeTakeFirst();

        if (!paymentProvider) {
          console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);
          return;
        }

        // Verificar se já existe uma transação para esta fatura
        const existingTransaction = await db
          .selectFrom('transactions')
          .where('provider_transaction_id', '=', invoice.id)
          .select(['id'])
          .executeTakeFirst();

        if (existingTransaction) {
          console.log(`Transação já existe para a fatura ${invoice.id}`);
          return;
        }

        // Registrar a transação com status pendente
        await db
          .insertInto('transactions')
          .values({
            user_id: user.id,
            provider_transaction_id: invoice.id,
            payment_provider_id: paymentProvider.payment_provider_id,
            amount: Number(invoice.amount_due) / 100, // Convertendo de centavos para reais
            currency: invoice.currency.toUpperCase(),
            status: 'pending', // Status inicial da fatura
            source_type: 'subscription', // Tipo correto para assinaturas
            source_id: subscription.id,
            created_at: new Date(),
            updated_at: new Date(),
          })
          .execute();

        console.log(`Transação pendente registrada com sucesso para a fatura ${invoice.id}`);
      } catch (error) {
        console.error('Erro ao processar criação de fatura:', error);
        throw error;
      }
    }
    private async handleInvoicePaid(invoice: any) {
  console.log('Evento invoice.paid recebido em handleInvoicePaid:', invoice.id);
  try {
    const customerId = invoice.customer;
    const user = await this.getUserByStripeCustomerId(customerId);

    if (!user) {
      console.error(`Usuário não encontrado para o customer_id: ${customerId}`);
      return;
    }

    console.log(`Processando fatura paga para o usuário ${user.id}, customer_id: ${customerId}, invoice_id: ${invoice.id}`);

    // Extrair subscription_id do parent.subscription_details (novo formato)
    let subscriptionId = null;
    if (invoice.parent?.subscription_details?.subscription) {
      subscriptionId = invoice.parent.subscription_details.subscription;
      console.log(`Subscription ID extraído do parent: ${subscriptionId}`);
    } else if (invoice.subscription) {
      // Fallback para o formato antigo
      subscriptionId = invoice.subscription;
      console.log(`Subscription ID extraído do campo subscription: ${subscriptionId}`);
    }

    if (!subscriptionId) {
      console.error(`Nenhum subscription_id encontrado na fatura: ${invoice.id}`);
      return;
    }

    // Buscar a assinatura relacionada
    console.log(`Buscar assinatura relacionada à fatura ${invoice.id}: ${subscriptionId}`);
    let subscription = await db
      .selectFrom('users_subscriptions')
      .where('payment_provider_external_id', '=', subscriptionId)
      .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])
      .executeTakeFirst();

    // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado
    // Vamos tentar criar a assinatura com base na fatura
    if (!subscription && subscriptionId) {
      console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);

      try {
        // Obter a assinatura do Stripe
        const stripeSubscription = await this.stripe.subscriptions.retrieve(subscriptionId);
        console.log(`Assinatura obtida da Stripe: ${stripeSubscription.id}, status: ${stripeSubscription.status}`);

        // Criar a assinatura no banco de dados
        const newSubscriptionId = await this.handleSubscriptionCreated(stripeSubscription);

        if (newSubscriptionId) {
          // Buscar a assinatura novamente
          subscription = await db
            .selectFrom('users_subscriptions')
            .where('id', '=', Number(newSubscriptionId))
            .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])
            .executeTakeFirst();
        } else {
          // Tentar buscar por payment_provider_external_id
          subscription = await db
            .selectFrom('users_subscriptions')
            .where('payment_provider_external_id', '=', subscriptionId)
            .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])
            .executeTakeFirst();
        }
      } catch (error) {
        console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);
      }
    }

    if (!subscription) {
      console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);
      return;
    }

    console.log(`Assinatura encontrada: ${subscription.id}, status atual: ${subscription.status}`);

    // Buscar o provedor de pagamento
    const paymentProvider = await db
      .selectFrom('plans_payments_providers as ppp')
      .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')
      .where('ppp.id', '=', subscription.plan_payment_provider_id)
      .select(['pp.id as payment_provider_id'])
      .executeTakeFirst();

    if (!paymentProvider) {
      console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);
      return;
    }

    // Verificar se já existe uma transação para esta fatura
    let existingTransaction = await db
      .selectFrom('transactions')
      .where('provider_transaction_id', '=', invoice.id)
      .select(['id', 'status'])
      .executeTakeFirst();

    let transactionId: number | null = null;

    if (existingTransaction) {
      // Atualizar a transação existente apenas se não estiver paga
      if (existingTransaction.status !== 'paid') {
        await db
          .updateTable('transactions')
          .set({
            amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais
            status: 'paid',
            updated_at: new Date(),
          })
          .where('id', '=', existingTransaction.id)
          .execute();

        console.log(`Transação ${existingTransaction.id} atualizada para status 'paid'`);
      } else {
        console.log(`Transação ${existingTransaction.id} já está com status 'paid'`);
      }
      transactionId = existingTransaction.id;
    } else {
      // Registrar uma nova transação
      const result = await db
        .insertInto('transactions')
        .values({
          user_id: user.id,
          provider_transaction_id: invoice.id,
          payment_provider_id: paymentProvider.payment_provider_id,
          amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais
          currency: invoice.currency.toUpperCase(),
          status: 'paid',
          source_type: 'subscription', // Tipo correto para assinaturas
          source_id: subscription.id,
          created_at: new Date(),
          updated_at: new Date(),
        })
        .executeTakeFirst();

      console.log(`Nova transação registrada com sucesso para a fatura ${invoice.id}`);

      if (result && result.insertId) {
        transactionId = Number(result.insertId);

        // Buscar a transação recém-criada para ter certeza
        existingTransaction = await db
          .selectFrom('transactions')
          .where('id', '=', transactionId)
          .select(['id', 'status'])
          .executeTakeFirst();

        console.log(`Transação criada com ID: ${transactionId}`);
      }
    }

    // Atualizar o status da assinatura para ativo
    console.log(`Status atual da assinatura ${subscription.id}: ${subscription.status}`);

    if (subscription.status !== 'active') {
      await db
        .updateTable('users_subscriptions')
        .set({
          status: 'active',
          updated_at: new Date(),
        })
        .where('id', '=', subscription.id)
        .execute();

      console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);

      // Verificar se a atualização foi bem-sucedida
      const updatedSubscription = await db
        .selectFrom('users_subscriptions')
        .where('id', '=', subscription.id)
        .select(['status'])
        .executeTakeFirst();

      console.log(`Status da assinatura ${subscription.id} após atualização: ${updatedSubscription?.status}`);
    } else {
      console.log(`Assinatura ${subscription.id} já está com status 'active', não é necessário atualizar`);
    }

    console.log(`Transação registrada e assinatura processada com sucesso para a fatura ${invoice.id}`);

    // Registrar comissões de afiliados
    // Verificar se a fatura está paga usando o status correto
    if (invoice.status === 'paid' && transactionId) {
      console.log(`Fatura ${invoice.id} está paga, registrando comissões...`);

      // Chamar o método específico para registrar comissões
      await this.registerAffiliateCommissions(
        user.id,
        subscription.plan_id,
        transactionId,
        Number(invoice.amount_paid) / 100,
        invoice.currency.toUpperCase()
      );

      console.log(`Comissões registradas com sucesso para a fatura ${invoice.id}`);
    } else {
      console.log(`Fatura ${invoice.id} não está paga ou não tem transação associada, não processando comissões`);
    }

  } catch (error) {
    console.error('Erro ao processar pagamento de fatura:', error);
    throw error;
  }
}

    private async handleInvoicePaidOld(invoice: any) {
      console.log('Evento invoice.paid recebido em handleInvoicePaid:', invoice.id);
      try {
        const customerId = invoice.customer;
        const user = await this.getUserByStripeCustomerId(customerId);

        if (!user) {
          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);
          return;
        }

        console.log(`Processando fatura paga para o usuário ${user.id}, customer_id: ${customerId}, invoice_id: ${invoice.id}`);

        // Buscar a assinatura relacionada
        console.log(`Buscar assinatura relacionada à fatura ${invoice.id}: ${invoice.subscription}`);
        console.log(invoice);
        let subscription = await db
          .selectFrom('users_subscriptions')
          .where('payment_provider_external_id', '=', invoice.subscription)
          .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])
          .executeTakeFirst();
          

        // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado
        // Vamos tentar criar a assinatura com base na fatura
        if (!subscription && invoice.subscription) {
          console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);

          try {
            // Obter a assinatura do Stripe
            const stripeSubscription = await this.stripe.subscriptions.retrieve(invoice.subscription);
            console.log(`Assinatura obtida da Stripe: ${stripeSubscription.id}, status: ${stripeSubscription.status}`);

            // Criar a assinatura no banco de dados
            const subscriptionId = await this.handleSubscriptionCreated(stripeSubscription);

            if (subscriptionId) {
              // Buscar a assinatura novamente
              subscription = await db
                .selectFrom('users_subscriptions')
                .where('id', '=', Number(subscriptionId))
                .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])
                .executeTakeFirst();
            } else {
              // Tentar buscar por payment_provider_external_id
              subscription = await db
                .selectFrom('users_subscriptions')
                .where('payment_provider_external_id', '=', invoice.subscription)
                .select(['id', 'plan_id', 'plan_payment_provider_id', 'status'])
                .executeTakeFirst();
            }
          } catch (error) {
            console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);
          }
        }

        if (!subscription) {
          console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);
          return;
        }

        console.log(`Assinatura encontrada: ${subscription.id}, status atual: ${subscription.status}`);

        // IMPORTANTE: Sempre atualizar o status para 'active' quando a fatura for paga
        if (subscription.status !== 'active') {
          await db
            .updateTable('users_subscriptions')
            .set({
              status: 'active',
              updated_at: new Date(),
            })
            .where('id', '=', subscription.id)
            .execute();

          console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);

          // Verificar se a atualização foi bem-sucedida
          const updatedSubscription = await db
            .selectFrom('users_subscriptions')
            .where('id', '=', subscription.id)
            .select(['status'])
            .executeTakeFirst();

          console.log(`Status da assinatura ${subscription.id} após atualização: ${updatedSubscription?.status}`);
        }

        // Buscar o provedor de pagamento
        const paymentProvider = await db
          .selectFrom('plans_payments_providers as ppp')
          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')
          .where('ppp.id', '=', subscription.plan_payment_provider_id)
          .select(['pp.id as payment_provider_id'])
          .executeTakeFirst();

        if (!paymentProvider) {
          console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);
          return;
        }

        // Verificar se já existe uma transação para esta fatura
        let existingTransaction = await db
          .selectFrom('transactions')
          .where('provider_transaction_id', '=', invoice.id)
          .select(['id', 'status'])
          .executeTakeFirst();

        let transactionId: number | null = null;

        if (existingTransaction) {
          // Atualizar a transação existente apenas se não estiver paga
          if (existingTransaction.status !== 'paid') {
            await db
              .updateTable('transactions')
              .set({
                amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais
                status: 'paid',
                updated_at: new Date(),
              })
              .where('id', '=', existingTransaction.id)
              .execute();

            console.log(`Transação ${existingTransaction.id} atualizada para status 'paid'`);
          } else {
            console.log(`Transação ${existingTransaction.id} já está com status 'paid'`);
          }
          transactionId = existingTransaction.id;
        } else {
          // Registrar uma nova transação
          const result = await db
            .insertInto('transactions')
            .values({
              user_id: user.id,
              provider_transaction_id: invoice.id,
              payment_provider_id: paymentProvider.payment_provider_id,
              amount: Number(invoice.amount_paid) / 100, // Convertendo de centavos para reais
              currency: invoice.currency.toUpperCase(),
              status: 'paid',
              source_type: 'subscription', // Tipo correto para assinaturas
              source_id: subscription.id,
              created_at: new Date(),
              updated_at: new Date(),
            })
            .executeTakeFirst();

          console.log(`Nova transação registrada com sucesso para a fatura ${invoice.id}`);

          if (result && result.insertId) {
            transactionId = Number(result.insertId);

            // Buscar a transação recém-criada para ter certeza
            existingTransaction = await db
              .selectFrom('transactions')
              .where('id', '=', transactionId)
              .select(['id', 'status'])
              .executeTakeFirst();

            console.log(`Transação criada com ID: ${transactionId}`);
          }
        }

        // Verificar o status atual da assinatura
        const currentSubscription = await db
          .selectFrom('users_subscriptions')
          .where('id', '=', subscription.id)
          .select(['status'])
          .executeTakeFirst();

        // Atualizar o status da assinatura para ativo apenas se não estiver já ativo
        if (currentSubscription) {
          console.log(`Status atual da assinatura ${subscription.id}: ${currentSubscription.status}`);

          if (currentSubscription.status !== 'active') {
            const status: 'active' = 'active';
            await db
              .updateTable('users_subscriptions')
              .set({
                status,
                updated_at: new Date(),
              })
              .where('id', '=', subscription.id)
              .execute();

            console.log(`Assinatura ${subscription.id} atualizada para status 'active'`);

            // Verificar se a atualização foi bem-sucedida
            const updatedSubscription = await db
              .selectFrom('users_subscriptions')
              .where('id', '=', subscription.id)
              .select(['status'])
              .executeTakeFirst();

            console.log(`Status da assinatura ${subscription.id} após atualização: ${updatedSubscription?.status}`);
          } else {
            console.log(`Assinatura ${subscription.id} já está com status 'active', não é necessário atualizar`);
          }
        } else {
          console.error(`Não foi possível encontrar a assinatura ${subscription.id} para atualizar o status`);
        }

        console.log(`Transação registrada e assinatura processada com sucesso para a fatura ${subscription.id}`);

        // Registrar comissões de afiliados
        if(invoice.paid && transactionId){
          console.log(`Fatura ${invoice.id} está paga, registrando comissões...`);

          // Chamar o método específico para registrar comissões
          await this.registerAffiliateCommissions(
            user.id,
            subscription.plan_id,
            transactionId,
            Number(invoice.amount_paid) / 100,
            invoice.currency.toUpperCase()
          );

          console.log(`Comissões registradas com sucesso para a fatura ${invoice.id}`);
        } else {
          console.log(`Fatura ${invoice.id} não está paga ou não tem transação associada, não processando comissões`);
        }




      } catch (error) {
        console.error('Erro ao processar pagamento de fatura:', error);
        throw error;
      }
    }

    private async getAffiliatesData(aff_id: number) {
      console.log(`Buscando dados do afiliado com ID: ${aff_id}`);

      if (!aff_id) {
        console.log('ID do afiliado é nulo ou indefinido');
        return {
          affiliate: null,
          affiliate_master: null,
          affiliate_master_user_id: null
        };
      }

      // Verificar se o usuário existe
      const user = await db.selectFrom('users')
        .where('id', '=', aff_id)
        .select(['id', 'email'])
        .executeTakeFirst();

      if (!user) {
        console.log(`Usuário com ID ${aff_id} não encontrado`);
        return {
          affiliate: null,
          affiliate_master: null,
          affiliate_master_user_id: null
        };
      }

      console.log(`Usuário afiliado encontrado: ID=${user.id}, Email=${user.email}`);

      // Buscar o registro de afiliado
      const affiliate = await db.selectFrom('affiliates')
        .where('user_id', '=', aff_id)
        .select(['id', 'stripeId', 'ref_user_id', 'user_id', 'status'])
        .executeTakeFirst();

      if (!affiliate) {
        console.log(`Registro de afiliado para o usuário ${aff_id} não encontrado. Criando registro...`);

        // Criar um registro de afiliado para este usuário
        try {
          await db.insertInto('affiliates')
            .values({
              status: 'active',
              user_id: aff_id,
              ref_user_id: 0, // Valor padrão para ref_user_id
              invite: '', // Valor padrão para invite
              created_at: new Date(),
              updated_at: new Date(),
            })
            .executeTakeFirst();

          console.log(`Registro de afiliado criado com sucesso para o usuário ${aff_id}`);

          return {
            affiliate: null, // Sem conta Stripe ainda
            affiliate_master: null,
            affiliate_master_user_id: null
          };
        } catch (error) {
          console.error(`Erro ao criar registro de afiliado para o usuário ${aff_id}:`, error);
          return {
            affiliate: null,
            affiliate_master: null,
            affiliate_master_user_id: null
          };
        }
      }

      console.log(`Afiliado encontrado: ${JSON.stringify(affiliate)}`);

      // Verificar se o afiliado está ativo
      if (affiliate.status !== 'active') {
        console.log(`Afiliado ${aff_id} não está ativo (status: ${affiliate.status}). Atualizando para ativo...`);

        // Atualizar o status para ativo
        try {
          await db.updateTable('affiliates')
            .set({
              status: 'active',
              updated_at: new Date(),
            })
            .where('id', '=', affiliate.id)
            .execute();

          console.log(`Status do afiliado ${aff_id} atualizado para 'active'`);
        } catch (error) {
          console.error(`Erro ao atualizar status do afiliado ${aff_id}:`, error);
        }
      }

      let affiliate_master: any = null;
      if(affiliate.ref_user_id) {
        affiliate_master = await db.selectFrom('affiliates')
          .where('user_id', '=', affiliate.ref_user_id)
          .select(['id', 'stripeId', 'user_id', 'status'])
          .executeTakeFirst();

        console.log(`Afiliado master encontrado: ${JSON.stringify(affiliate_master)}`);

        // Verificar se o afiliado master está ativo
        if (affiliate_master && affiliate_master.status !== 'active') {
          console.log(`Afiliado master ${affiliate.ref_user_id} não está ativo (status: ${affiliate_master.status}). Atualizando para ativo...`);

          // Atualizar o status para ativo
          try {
            await db.updateTable('affiliates')
              .set({
                status: 'active',
                updated_at: new Date(),
              })
              .where('id', '=', affiliate_master.id)
              .execute();

            console.log(`Status do afiliado master ${affiliate.ref_user_id} atualizado para 'active'`);
          } catch (error) {
            console.error(`Erro ao atualizar status do afiliado master ${affiliate.ref_user_id}:`, error);
          }
        }
      }

      return {
        affiliate: affiliate?.stripeId || null,
        affiliate_master: affiliate_master?.stripeId || null,
        affiliate_master_user_id: affiliate.ref_user_id || null
      }
    }

    private async getPlanComissionsData(plan_id: number) {
      console.log(`Buscando dados de comissão para o plano com ID: ${plan_id}`);

      if (!plan_id) {
        console.log('ID do plano é nulo ou indefinido');
        return {
          affiliate: 0,
          affiliate_percent: 0,
          affiliate_master: 0,
          affiliate_master_percent: 0
        };
      }

      const comission = await db.selectFrom('plans')
        .where('id', '=', plan_id)
        .select(['price','affiliate_master_commission_percent', 'affiliate_commission_percent'])
        .executeTakeFirst();

      if (!comission) {
        console.log(`Plano com ID ${plan_id} não encontrado. Usando valores padrão.`);
        return {
          affiliate: 0,
          affiliate_percent: 10, // Valor padrão de 10%
          affiliate_master: 0,
          affiliate_master_percent: 5 // Valor padrão de 5%
        };
      }

      console.log(`Dados do plano encontrados: ${JSON.stringify(comission)}`);

      // Verificar se os percentuais de comissão estão definidos
      const affiliatePercent = comission.affiliate_commission_percent ? Number(comission.affiliate_commission_percent) : 10; // Valor padrão de 10%
      const affiliateMasterPercent = comission.affiliate_master_commission_percent ? Number(comission.affiliate_master_commission_percent) : 5; // Valor padrão de 5%

      // Calcular os valores de comissão
      const price = Number(comission.price || 0);
      const affiliateValue = price * (affiliatePercent / 100);
      const affiliateMasterValue = price * (affiliateMasterPercent / 100);

      console.log(`Valores de comissão calculados:
        - Afiliado: ${affiliateValue} (${affiliatePercent}%)
        - Afiliado Master: ${affiliateMasterValue} (${affiliateMasterPercent}%)`);

      return {
          affiliate: affiliateValue,
          affiliate_percent: affiliatePercent,
          affiliate_master: affiliateMasterValue,
          affiliate_master_percent: affiliateMasterPercent,
      }
    }

    private async handleInvoicePaymentFailed(invoice: any) {
      try {
        const customerId = invoice.customer;
        const user = await this.getUserByStripeCustomerId(customerId);

        if (!user) {
          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);
          return;
        }

        // Buscar a assinatura relacionada
        let subscription = await db
          .selectFrom('users_subscriptions')
          .where('payment_provider_external_id', '=', invoice.subscription)
          .select(['id', 'plan_id', 'plan_payment_provider_id'])
          .executeTakeFirst();

        // Se a assinatura não existir, pode ser que o evento de criação da assinatura ainda não tenha sido processado
        // Vamos tentar criar a assinatura com base na fatura
        if (!subscription && invoice.subscription) {
          console.log(`Assinatura não encontrada para a fatura: ${invoice.id}. Tentando obter da API do Stripe...`);

          try {
            // Obter a assinatura do Stripe
            const stripeSubscription = await this.stripe.subscriptions.retrieve(invoice.subscription);

            // Criar a assinatura no banco de dados
            await this.handleSubscriptionCreated(stripeSubscription);

            // Buscar a assinatura novamente
            subscription = await db
              .selectFrom('users_subscriptions')
              .where('payment_provider_external_id', '=', invoice.subscription)
              .select(['id', 'plan_id', 'plan_payment_provider_id'])
              .executeTakeFirst();
          } catch (error) {
            console.error(`Erro ao obter assinatura do Stripe: ${error.message}`);
          }
        }

        if (!subscription) {
          console.error(`Assinatura não encontrada para a fatura: ${invoice.id} após tentativa de criação`);
          return;
        }

        // Buscar o provedor de pagamento
        const paymentProvider = await db
          .selectFrom('plans_payments_providers as ppp')
          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')
          .where('ppp.id', '=', subscription.plan_payment_provider_id)
          .select(['pp.id as payment_provider_id'])
          .executeTakeFirst();

        if (!paymentProvider) {
          console.error(`Provedor de pagamento não encontrado para a assinatura: ${subscription.id}`);
          return;
        }

        // Verificar se já existe uma transação para esta fatura
        const existingTransaction = await db
          .selectFrom('transactions')
          .where('provider_transaction_id', '=', invoice.id)
          .select(['id'])
          .executeTakeFirst();

        if (existingTransaction) {
          // Atualizar a transação existente
          await db
            .updateTable('transactions')
            .set({
              amount: Number(invoice.amount_due) / 100, // Convertendo de centavos para reais
              status: 'failed',
              updated_at: new Date(),
            })
            .where('id', '=', existingTransaction.id)
            .execute();

          console.log(`Transação ${existingTransaction.id} atualizada para status 'failed'`);
        } else {
          // Registrar uma nova transação falha
          await db
            .insertInto('transactions')
            .values({
              user_id: user.id,
              provider_transaction_id: invoice.id,
              payment_provider_id: paymentProvider.payment_provider_id,
              amount: Number(invoice.amount_due) / 100, // Convertendo de centavos para reais
              currency: invoice.currency.toUpperCase(),
              status: 'failed',
              source_type: 'subscription', // Tipo correto para assinaturas
              source_id: subscription.id,
              created_at: new Date(),
              updated_at: new Date(),
            })
            .execute();

          console.log(`Nova transação falha registrada para a fatura ${invoice.id}`);
        }

        // Atualizar o status da assinatura para pausada
        const status: 'paused' = 'paused';
        await db
          .updateTable('users_subscriptions')
          .set({
            status,
            updated_at: new Date(),
          })
          .where('id', '=', subscription.id)
          .execute();

        console.log(`Falha de pagamento registrada para a fatura ${invoice.id}`);
      } catch (error) {
        console.error('Erro ao processar falha de pagamento:', error);
        throw error;
      }
    }

    // Manipulador para pagamentos bem-sucedidos (não relacionados a assinaturas)
    private async handleChargeSucceeded(charge: any) {
      try {
        // Verificar se a cobrança está relacionada a uma fatura
        if (charge.invoice) {
          console.log(`Cobrança ${charge.id} está relacionada a uma fatura. Será processada pelo evento invoice.paid`);
          return;
        }

        const customerId = charge.customer;
        if (!customerId) {
          console.error(`Cobrança ${charge.id} não tem customer_id associado`);
          return;
        }

        const user = await this.getUserByStripeCustomerId(customerId);

        if (!user) {
          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);
          return;
        }

        // Buscar o provedor de pagamento (Stripe)
        const paymentProvider = await db
          .selectFrom('payment_providers')
          .where('name', '=', 'Stripe')
          .select(['id'])
          .executeTakeFirst();

        if (!paymentProvider) {
          console.error('Provedor de pagamento Stripe não encontrado');
          return;
        }

        // Verificar se já existe uma transação para esta cobrança
        const existingTransaction = await db
          .selectFrom('transactions')
          .where('provider_transaction_id', '=', charge.id)
          .select(['id'])
          .executeTakeFirst();

        if (existingTransaction) {
          console.log(`Transação já existe para a cobrança ${charge.id}`);
          return;
        }

        // Registrar a transação
        await db
          .insertInto('transactions')
          .values({
            user_id: user.id,
            provider_transaction_id: charge.id,
            payment_provider_id: paymentProvider.id,
            amount: Number(charge.amount) / 100, // Convertendo de centavos para reais
            currency: charge.currency.toUpperCase(),
            status: 'paid',
            source_type: 'invoice_item', // Compra avulsa
            source_id: 0, // Não está associado a uma assinatura
            created_at: new Date(charge.created * 1000),
            updated_at: new Date(),
          })
          .execute();

        console.log(`Transação avulsa registrada com sucesso para a cobrança ${charge.id}`);
      } catch (error) {
        console.error('Erro ao processar cobrança bem-sucedida:', error);
        throw error;
      }
    }

    // Manipulador para reembolsos
    private async handleChargeRefunded(charge: any) {
      try {
        // Buscar a transação relacionada
        const transaction = await db
          .selectFrom('transactions')
          .where('provider_transaction_id', '=', charge.id)
          .select(['id', 'user_id', 'payment_provider_id', 'source_type', 'source_id'])
          .executeTakeFirst();

        if (!transaction) {
          console.error(`Transação não encontrada para a cobrança: ${charge.id}`);
          return;
        }

        // Verificar se o reembolso é total ou parcial
        const isFullRefund = charge.refunded;

        if (isFullRefund) {
          // Atualizar o status da transação para reembolsada
          await db
            .updateTable('transactions')
            .set({
              status: 'refunded',
              updated_at: new Date(),
            })
            .where('id', '=', transaction.id)
            .execute();

          console.log(`Transação ${transaction.id} marcada como reembolsada`);

          // Se for uma assinatura, atualizar o status da assinatura para cancelada
          if (transaction.source_type === 'subscription' && transaction.source_id) {
            const status: 'canceled' = 'canceled';
            await db
              .updateTable('users_subscriptions')
              .set({
                status,
                end_date: new Date(),
                updated_at: new Date(),
              })
              .where('id', '=', transaction.source_id)
              .execute();

            console.log(`Assinatura ${transaction.source_id} marcada como cancelada devido a reembolso`);
          }
        } else {
          // Para reembolsos parciais, podemos criar uma nova transação com status 'refunded'
          // e o valor negativo do reembolso
          const refundAmount = Number(charge.amount_refunded) / 100;

          await db
            .insertInto('transactions')
            .values({
              user_id: transaction.user_id,
              provider_transaction_id: `${charge.id}_refund`,
              payment_provider_id: transaction.payment_provider_id,
              amount: -refundAmount, // Valor negativo para indicar reembolso
              currency: charge.currency.toUpperCase(),
              status: 'refunded',
              source_type: transaction.source_type,
              source_id: transaction.source_id,
              created_at: new Date(),
              updated_at: new Date(),
            })
            .execute();

          console.log(`Reembolso parcial registrado para a transação ${transaction.id}`);
        }
      } catch (error) {
        console.error('Erro ao processar reembolso:', error);
        throw error;
      }
    }

    // Manipulador para pagamentos bem-sucedidos via PaymentIntent
    private async handlePaymentIntentSucceeded(paymentIntent: any) {
      try {
        console.log(`PaymentIntent ${paymentIntent.id} foi bem-sucedido`);

        // Verificar se o PaymentIntent está relacionado a uma assinatura
        // Isso pode ser verificado através dos metadados ou da fatura associada
        if (paymentIntent.invoice) {
          try {
            // Buscar a fatura para obter a assinatura relacionada
            const invoice = await this.stripe.invoices.retrieve(paymentIntent.invoice);

            // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente
            if (invoice.subscription) {
              // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente
              console.log(`PaymentIntent ${paymentIntent.id} está relacionado à assinatura ${invoice.subscription}`);

              // Buscar a assinatura no banco de dados
              const subscription = await db
                .selectFrom('users_subscriptions')
                // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente
                .where('payment_provider_external_id', '=', invoice.subscription)
                .select(['id', 'status'])
                .executeTakeFirst();

              if (subscription) {
                // Se a assinatura estiver com status pendente, atualizá-la para ativa
                if (subscription.status === 'pending') {
                  const status: 'active' = 'active';
                  await db
                    .updateTable('users_subscriptions')
                    .set({
                      status,
                      updated_at: new Date(),
                    })
                    .where('id', '=', subscription.id)
                    .execute();

                  console.log(`Assinatura ${subscription.id} ativada após pagamento bem-sucedido via PaymentIntent ${paymentIntent.id}`);
                }
              }
            }
          } catch (error) {
            console.error(`Erro ao processar PaymentIntent relacionado à fatura: ${error.message}`);
          }
        }
      } catch (error) {
        console.error('Erro ao processar PaymentIntent bem-sucedido:', error);
        throw error;
      }
    }

    // Manipulador para agendamentos de assinatura
    private async handleSubscriptionScheduleCreated(schedule: any) {
      try {
        console.log(`SubscriptionSchedule ${schedule.id} foi criado`);

        // Se o agendamento tiver uma assinatura atual, verificar e atualizar seu status
        if (schedule.subscription) {
          // Buscar a assinatura no banco de dados
          const subscription = await db
            .selectFrom('users_subscriptions')
            .where('payment_provider_external_id', '=', schedule.subscription)
            .select(['id', 'status'])
            .executeTakeFirst();

          if (subscription && subscription.status === 'pending') {
            // Obter a assinatura do Stripe para verificar seu status atual
            const stripeSubscription = await this.stripe.subscriptions.retrieve(schedule.subscription);

            if (stripeSubscription.status === 'active') {
              const status: 'active' = 'active';
              await db
                .updateTable('users_subscriptions')
                .set({
                  status,
                  updated_at: new Date(),
                })
                .where('id', '=', subscription.id)
                .execute();

              console.log(`Assinatura ${subscription.id} ativada após criação de agendamento`);
            }
          }
        }
      } catch (error) {
        console.error('Erro ao processar criação de agendamento de assinatura:', error);
        throw error;
      }
    }

    // Manipulador para sessões de checkout concluídas
    private async handleCheckoutSessionCompleted(session: any) {
      try {
        // Verificar se a sessão está relacionada a uma assinatura
        if (session.mode === 'subscription') {
          console.log(`Sessão de checkout ${session.id} é para uma assinatura. Verificando se precisamos atualizar o status...`);

          // Se tiver subscription, vamos verificar e atualizar o status da assinatura
          if (session.subscription) {
            try {
              // Buscar a assinatura no banco de dados
              const subscription = await db
                .selectFrom('users_subscriptions')
                .where('payment_provider_external_id', '=', session.subscription)
                .select(['id', 'status', 'user_id'])
                .executeTakeFirst();

              if (subscription) {
                // Se a assinatura estiver com status diferente de ativo, atualizá-la
                if (subscription.status !== 'active') {
                  const status: 'active' = 'active';
                  await db
                    .updateTable('users_subscriptions')
                    .set({
                      status,
                      updated_at: new Date(),
                    })
                    .where('id', '=', subscription.id)
                    .execute();

                  console.log(`Assinatura ${subscription.id} ativada após conclusão do checkout`);
                }

                // Verificar se já existe uma transação para esta sessão
                const existingTransaction = await db
                  .selectFrom('transactions')
                  .where('provider_transaction_id', '=', session.id)
                  .select(['id'])
                  .executeTakeFirst();

                if (!existingTransaction) {
                  // Buscar o provedor de pagamento (Stripe)
                  const paymentProvider = await db
                    .selectFrom('payment_providers')
                    .where('name', '=', 'Stripe')
                    .select(['id'])
                    .executeTakeFirst();

                  if (paymentProvider) {
                    // Registrar a transação como assinatura
                    await db
                      .insertInto('transactions')
                      .values({
                        user_id: subscription.user_id,
                        provider_transaction_id: session.id,
                        payment_provider_id: paymentProvider.id,
                        amount: Number(session.amount_total) / 100, // Convertendo de centavos para reais
                        currency: session.currency.toUpperCase(),
                        status: 'paid',
                        source_type: 'subscription', // Tipo correto para assinaturas
                        source_id: subscription.id, // ID da assinatura
                        created_at: new Date(session.created * 1000),
                        updated_at: new Date(),
                      })
                      .execute();

                    console.log(`Transação de assinatura registrada com sucesso para a sessão de checkout ${session.id}`);
                  }
                }
              }
            } catch (error) {
              console.error(`Erro ao atualizar status da assinatura após checkout: ${error.message}`);
            }
          }

          return;
        }

        const customerId = session.customer;
        if (!customerId) {
          console.error(`Sessão de checkout ${session.id} não tem customer_id associado`);
          return;
        }

        const user = await this.getUserByStripeCustomerId(customerId);

        if (!user) {
          console.error(`Usuário não encontrado para o customer_id: ${customerId}`);
          return;
        }

        // Buscar o provedor de pagamento (Stripe)
        const paymentProvider = await db
          .selectFrom('payment_providers')
          .where('name', '=', 'Stripe')
          .select(['id'])
          .executeTakeFirst();

        if (!paymentProvider) {
          console.error('Provedor de pagamento Stripe não encontrado');
          return;
        }

        // Verificar se já existe uma transação para esta sessão
        const existingTransaction = await db
          .selectFrom('transactions')
          .where('provider_transaction_id', '=', session.id)
          .select(['id'])
          .executeTakeFirst();

        if (existingTransaction) {
          console.log(`Transação já existe para a sessão de checkout ${session.id}`);
          return;
        }

        // Registrar a transação
        await db
          .insertInto('transactions')
          .values({
            user_id: user.id,
            provider_transaction_id: session.id,
            payment_provider_id: paymentProvider.id,
            amount: Number(session.amount_total) / 100, // Convertendo de centavos para reais
            currency: session.currency.toUpperCase(),
            status: 'paid',
            source_type: 'invoice_item', // Compra avulsa
            source_id: 0, // Não está associado a uma assinatura
            created_at: new Date(session.created * 1000),
            updated_at: new Date(),
          })
          .execute();

        console.log(`Transação avulsa registrada com sucesso para a sessão de checkout ${session.id}`);
      } catch (error) {
        console.error('Erro ao processar sessão de checkout concluída:', error);
        throw error;
      }
    }

    // Métodos auxiliares
    private async getUserByStripeCustomerId(customerId: string) {
      const user = await db
        .selectFrom('users')
        .where('stripeId', '=', customerId)
        .select(['id', 'email', 'aff_id'])
        .executeTakeFirst();

      if (user) {
        console.log(`Usuário encontrado para o customer_id ${customerId}: ID=${user.id}, Email=${user.email}, Aff_ID=${user.aff_id}`);
      } else {
        console.log(`Nenhum usuário encontrado para o customer_id ${customerId}`);
      }

      return user;
    }

    private async getPlanPaymentProviderIdByPriceId(priceId: string) {
      return db
        .selectFrom('plans_payments_providers')
        .where('payment_provider_external_id', '=', priceId)
        .select(['id', 'plan_id', 'platform'])
        .executeTakeFirst();
    }

    private async getPlanInfoByPlanPaymentProviderId(planPaymentProviderId: number) {
      return db
        .selectFrom('plans_payments_providers as ppp')
        .innerJoin('plans as p', 'ppp.plan_id', 'p.id')
        .where('ppp.id', '=', planPaymentProviderId)
        .select(['p.id as plan_id', 'p.snaptokens'])
        .executeTakeFirst();
    }

    // Método específico para registrar comissões
    private async registerAffiliateCommissions(userId: number, planId: number, transactionId: number, invoiceAmount: number, currency: string) {
      try {
        console.log(`Registrando comissões para usuário ${userId}, plano ${planId}, transação ${transactionId}`);

        // Buscar o usuário para obter o aff_id
        const user = await db
          .selectFrom('users')
          .where('id', '=', userId)
          .select(['id', 'email', 'aff_id'])
          .executeTakeFirst();

        if (!user) {
          console.error(`Usuário ${userId} não encontrado`);
          return;
        }

        console.log(`Usuário encontrado: ID=${user.id}, Email=${user.email}, Aff_ID=${user.aff_id || 'não definido'}`);

        // Verificar se já existe comissão registrada para esta transação
        const existingCommission = await db
          .selectFrom('affiliate_commissions')
          .where('transaction_id', '=', transactionId)
          .select(['id'])
          .executeTakeFirst();

        if (existingCommission) {
          console.log(`Comissão já registrada para a transação ${transactionId}, excluindo para recriar`);

          // Excluir comissões existentes para recriar
          await db
            .deleteFrom('affiliate_commissions')
            .where('transaction_id', '=', transactionId)
            .execute();

          console.log(`Comissões existentes para a transação ${transactionId} foram excluídas`);
        }

        // Obter dados do afiliado
        const aff_id = user.aff_id || 0;
        const affiliates = await this.getAffiliatesData(aff_id);
        console.log(`Dados dos afiliados: ${JSON.stringify(affiliates)}`);

        // Obter dados de comissão do plano
        const comissions = await this.getPlanComissionsData(planId);
        console.log(`Dados de comissão do plano ${planId}: ${JSON.stringify(comissions)}`);

        if (!comissions) {
          console.log(`Não foi possível obter dados de comissões para o plano ${planId}`);
          return;
        }

        // Registrar comissão para o afiliado nível 1
        if (aff_id > 0) {
          try {
            console.log(`Registrando comissão para afiliado ${aff_id}`);

            const result = await db
              .insertInto('affiliate_commissions')
              .values({
                aff_level: 1,
                aff_user_id: aff_id,
                user_id: userId,
                plan_id: planId,
                transaction_id: transactionId,
                commission_percent: Number(comissions.affiliate_percent || 0),
                commission_value: Number(comissions.affiliate || 0),
                status: 'pending',
                metadata: JSON.stringify({
                  transaction_amount: invoiceAmount,
                  currency: currency,
                }),
                created_at: new Date(),
                updated_at: new Date(),
              })
              .executeTakeFirst();

            console.log(`Comissão registrada para afiliado ${aff_id}:`, result);

            // Verificar se a comissão foi registrada
            if (result && 'insertId' in result) {
              const commissionId = Number(result.insertId);
              console.log(`Comissão registrada com ID: ${commissionId}`);

              // Verificar se a comissão existe no banco de dados
              const commission = await db
                .selectFrom('affiliate_commissions')
                .where('id', '=', commissionId)
                .select(['id', 'status'])
                .executeTakeFirst();

              if (commission) {
                console.log(`Comissão ${commissionId} verificada no banco de dados com status: ${commission.status}`);
              } else {
                console.error(`Comissão ${commissionId} não encontrada no banco de dados após inserção`);
              }
            }
          } catch (error) {
            console.error(`Erro ao registrar comissão para afiliado ${aff_id}:`, error);
          }
        }

        // Registrar comissão para o afiliado master (nível 2)
        if (affiliates.affiliate_master_user_id) {
          try {
            console.log(`Registrando comissão para afiliado master ${affiliates.affiliate_master_user_id}`);

            const result = await db
              .insertInto('affiliate_commissions')
              .values({
                aff_level: 2,
                aff_user_id: affiliates.affiliate_master_user_id,
                user_id: userId,
                plan_id: planId,
                transaction_id: transactionId,
                commission_percent: Number(comissions.affiliate_master_percent || 0),
                commission_value: Number(comissions.affiliate_master || 0),
                status: 'pending',
                metadata: JSON.stringify({
                  transaction_amount: invoiceAmount,
                  currency: currency,
                }),
                created_at: new Date(),
                updated_at: new Date(),
              })
              .executeTakeFirst();

            console.log(`Comissão registrada para afiliado master ${affiliates.affiliate_master_user_id}:`, result);

            // Verificar se a comissão foi registrada
            if (result && 'insertId' in result) {
              const commissionId = Number(result.insertId);
              console.log(`Comissão master registrada com ID: ${commissionId}`);

              // Verificar se a comissão existe no banco de dados
              const commission = await db
                .selectFrom('affiliate_commissions')
                .where('id', '=', commissionId)
                .select(['id', 'status'])
                .executeTakeFirst();

              if (commission) {
                console.log(`Comissão master ${commissionId} verificada no banco de dados com status: ${commission.status}`);
              } else {
                console.error(`Comissão master ${commissionId} não encontrada no banco de dados após inserção`);
              }
            }
          } catch (error) {
            console.error(`Erro ao registrar comissão para afiliado master ${affiliates.affiliate_master_user_id}:`, error);
          }
        }

        console.log(`Processamento de comissões concluído para a transação ${transactionId}`);
      } catch (error) {
        console.error(`Erro ao registrar comissões:`, error);
      }
    }

    // Método para obter as assinaturas de um usuário
    async getUserSubscriptions(userId: number) {
      try {
        const subscriptions = await db
          .selectFrom('users_subscriptions as us')
          .innerJoin('plans as p', 'us.plan_id', 'p.id')
          .innerJoin('plans_payments_providers as ppp', 'us.plan_payment_provider_id', 'ppp.id')
          .innerJoin('payment_providers as pp', 'ppp.payment_provider_id', 'pp.id')
          .where('us.user_id', '=', userId)
          .where('us.deleted_at', 'is', null)
          .select([
            'us.id',
            'us.status',
            'us.platform',
            'us.price',
            'us.currency',
            'us.start_date',
            'us.end_date',
            'us.next_billing_date',
            'us.cancel_at_period_end',
            'us.is_trial',
            'us.trial_start_date',
            'us.trial_end_date',
            'us.snaptokens',
            'us.created_at',
            'p.name as plan_name',
            'p.description as plan_description',
            'pp.name as payment_provider_name',
          ])
          .orderBy('us.created_at', 'desc')
          .execute();

        return {
          status: 'success',
          data: subscriptions.map(subscription => ({
            id: subscription.id,
            status: subscription.status,
            platform: subscription.platform,
            price: Number(subscription.price),
            currency: subscription.currency,
            start_date: this.formatDatetime(subscription.start_date),
            end_date: subscription.end_date ? this.formatDatetime(subscription.end_date) : null,
            next_billing_date: subscription.next_billing_date ? this.formatDatetime(subscription.next_billing_date) : null,
            cancel_at_period_end: subscription.cancel_at_period_end,
            is_trial: subscription.is_trial,
            trial_start_date: subscription.trial_start_date ? this.formatDatetime(subscription.trial_start_date) : null,
            trial_end_date: subscription.trial_end_date ? this.formatDatetime(subscription.trial_end_date) : null,
            snaptokens: subscription.snaptokens,
            created_at: this.formatDatetime(subscription.created_at),
            plan_name: subscription.plan_name,
            plan_description: subscription.plan_description,
            payment_provider: subscription.payment_provider_name,
          })),
        };
      } catch (error) {
        console.error('Erro ao obter assinaturas do usuário:', error);
        throw error;
      }
    }

    // Método para obter as transações de um usuário
    async getUserTransactions(userId: number) {
      try {
        const transactions = await db
          .selectFrom('transactions as t')
          .innerJoin('payment_providers as pp', 't.payment_provider_id', 'pp.id')
          .where('t.user_id', '=', userId)
          .where('t.deleted_at', 'is', null)
          .select([
            't.id',
            't.provider_transaction_id',
            't.amount',
            't.currency',
            't.status',
            't.source_type',
            't.source_id',
            't.created_at',
            'pp.name as payment_provider_name',
          ])
          .orderBy('t.created_at', 'desc')
          .execute();

        return {
          status: 'success',
          data: transactions.map(transaction => ({
            id: transaction.id,
            provider_transaction_id: transaction.provider_transaction_id,
            amount: Number(transaction.amount),
            currency: transaction.currency,
            status: transaction.status,
            source_type: transaction.source_type,
            source_id: transaction.source_id,
            created_at: this.formatDatetime(transaction.created_at),
            payment_provider: transaction.payment_provider_name,
          })),
        };
      } catch (error) {
        console.error('Erro ao obter transações do usuário:', error);
        throw error;
      }
    }

    // Método para obter detalhes de uma transação específica
    async getTransactionDetails(transactionId: number, userId: number) {
      try {
        // Buscar a transação
        const transaction = await db
          .selectFrom('transactions as t')
          .innerJoin('payment_providers as pp', 't.payment_provider_id', 'pp.id')
          .where('t.id', '=', transactionId)
          .where('t.user_id', '=', userId)
          .where('t.deleted_at', 'is', null)
          .select([
            't.id',
            't.provider_transaction_id',
            't.amount',
            't.currency',
            't.status',
            't.source_type',
            't.source_id',
            't.created_at',
            't.updated_at',
            'pp.name as payment_provider_name',
          ])
          .executeTakeFirst();

        if (!transaction) {
          return {
            status: 'error',
            message: 'Transação não encontrada ou não pertence ao usuário',
          };
        }

        // Buscar informações adicionais com base no source_type
        let sourceDetails: any = null;
        if (transaction.source_type === 'subscription' && transaction.source_id) {
          // Buscar detalhes da assinatura
          const subscription = await db
            .selectFrom('users_subscriptions as us')
            .innerJoin('plans as p', 'us.plan_id', 'p.id')
            .where('us.id', '=', transaction.source_id)
            .select([
              'us.id',
              'us.status',
              'us.platform',
              'us.price',
              'us.currency',
              'us.start_date',
              'us.end_date',
              'p.name as plan_name',
              'p.description as plan_description',
            ])
            .executeTakeFirst();

          if (subscription) {
            sourceDetails = {
              type: 'subscription',
              id: subscription.id,
              status: subscription.status,
              plan_name: subscription.plan_name,
              plan_description: subscription.plan_description,
              price: Number(subscription.price),
              currency: subscription.currency,
              start_date: this.formatDatetime(subscription.start_date),
              end_date: subscription.end_date ? this.formatDatetime(subscription.end_date) : null,
            };
          }
        } else if (transaction.source_type === 'invoice_item') {
          // Para itens avulsos, podemos buscar informações adicionais se necessário
          sourceDetails = {
            type: 'invoice_item',
            description: 'Compra avulsa',
          };
        }

        // Buscar informações do Stripe se disponível
        let stripeDetails: any = null;
        if (transaction.provider_transaction_id && transaction.payment_provider_name === 'Stripe') {
          try {
            // Verificar se é uma fatura ou uma cobrança
            if (transaction.provider_transaction_id.startsWith('in_')) {
              // É uma fatura
              const invoice = await this.stripe.invoices.retrieve(transaction.provider_transaction_id);
              stripeDetails = {
                invoice_number: invoice.number,
                invoice_url: invoice.hosted_invoice_url,
                pdf_url: invoice.invoice_pdf,
                // @ts-ignore - O tipo do Stripe pode não ter essa propriedade explicitamente
                payment_intent: invoice.payment_intent,
                billing_reason: invoice.billing_reason,
              };
            } else if (transaction.provider_transaction_id.startsWith('ch_')) {
              // É uma cobrança
              const charge = await this.stripe.charges.retrieve(transaction.provider_transaction_id);
              stripeDetails = {
                receipt_url: charge.receipt_url,
                payment_method: charge.payment_method_details?.type,
                payment_intent: charge.payment_intent,
                description: charge.description,
              };
            }
          } catch (stripeError) {
            console.error('Erro ao obter detalhes do Stripe:', stripeError);
            // Continuamos mesmo sem os detalhes do Stripe
          }
        }

        return {
          status: 'success',
          data: {
            id: transaction.id,
            provider_transaction_id: transaction.provider_transaction_id,
            amount: Number(transaction.amount),
            currency: transaction.currency,
            status: transaction.status,
            source_type: transaction.source_type,
            source_id: transaction.source_id,
            created_at: this.formatDatetime(transaction.created_at),
            updated_at: this.formatDatetime(transaction.updated_at),
            payment_provider: transaction.payment_provider_name,
            source_details: sourceDetails,
            provider_details: stripeDetails,
          },
        };
      } catch (error) {
        console.error('Erro ao obter detalhes da transação:', error);
        throw error;
      }
    }

    // Método para cancelar uma assinatura
    async cancelSubscription(subscriptionId: number, userId: number) {
      try {
        // Verificar se a assinatura existe e pertence ao usuário
        const subscription = await db
          .selectFrom('users_subscriptions')
          .where('id', '=', subscriptionId)
          .where('user_id', '=', userId)
          .where('deleted_at', 'is', null)
          .select(['payment_provider_external_id', 'status'])
          .executeTakeFirst();

        if (!subscription) {
          return {
            status: 'error',
            message: 'Assinatura não encontrada ou não pertence ao usuário',
          };
        }

        // Verificar se a assinatura já está cancelada
        if (subscription.status === 'canceled') {
          return {
            status: 'error',
            message: 'Assinatura já está cancelada',
          };
        }

        // Cancelar a assinatura no Stripe
        if (subscription.payment_provider_external_id) {
          try {
            await this.stripe.subscriptions.update(subscription.payment_provider_external_id, {
              cancel_at_period_end: true,
            });
          } catch (stripeError) {
            console.error('Erro ao cancelar assinatura no Stripe:', stripeError);
            // Continuamos mesmo com erro no Stripe para atualizar nosso banco
          }
        }

        // Atualizar a assinatura no banco de dados
        await db
          .updateTable('users_subscriptions')
          .set({
            cancel_at_period_end: true,
            updated_at: new Date(),
          })
          .where('id', '=', subscriptionId)
          .execute();

        return {
          status: 'success',
          message: 'Assinatura será cancelada ao final do período atual',
        };
      } catch (error) {
        console.error('Erro ao cancelar assinatura:', error);
        throw error;
      }
    }

    // Método para cancelar uma assinatura imediatamente
    async cancelSubscriptionImmediately(subscriptionId: number, userId: number) {
      try {
        // Verificar se a assinatura existe e pertence ao usuário
        const subscription = await db
          .selectFrom('users_subscriptions')
          .where('id', '=', subscriptionId)
          .where('user_id', '=', userId)
          .where('deleted_at', 'is', null)
          .select(['payment_provider_external_id', 'status'])
          .executeTakeFirst();

        if (!subscription) {
          return {
            status: 'error',
            message: 'Assinatura não encontrada ou não pertence ao usuário',
          };
        }

        // Verificar se a assinatura já está cancelada
        if (subscription.status === 'canceled') {
          return {
            status: 'error',
            message: 'Assinatura já está cancelada',
          };
        }

        // Cancelar a assinatura no Stripe imediatamente
        if (subscription.payment_provider_external_id) {
          try {
            await this.stripe.subscriptions.cancel(subscription.payment_provider_external_id);
          } catch (stripeError) {
            console.error('Erro ao cancelar assinatura no Stripe:', stripeError);
            // Continuamos mesmo com erro no Stripe para atualizar nosso banco
          }
        }

        // Atualizar a assinatura no banco de dados
        const status: 'canceled' = 'canceled';
        await db
          .updateTable('users_subscriptions')
          .set({
            status,
            end_date: new Date(),
            updated_at: new Date(),
          })
          .where('id', '=', subscriptionId)
          .execute();

        return {
          status: 'success',
          message: 'Assinatura cancelada com sucesso',
        };
      } catch (error) {
        console.error('Erro ao cancelar assinatura imediatamente:', error);
        throw error;
      }
    }

  // Método para registrar comissões manualmente para uma assinatura
  async registerCommissionsForSubscription(subscriptionId: number) {
    try {
      console.log(`Registrando comissões manualmente para a assinatura ${subscriptionId}`);

      // Buscar a assinatura
      const subscription = await db
        .selectFrom('users_subscriptions')
        .where('id', '=', subscriptionId)
        .select(['id', 'user_id', 'plan_id', 'price', 'currency'])
        .executeTakeFirst();

      if (!subscription) {
        console.error(`Assinatura ${subscriptionId} não encontrada`);
        return {
          status: 'error',
          message: 'Assinatura não encontrada'
        };
      }

      // Buscar a transação mais recente para esta assinatura
      const transaction = await db
        .selectFrom('transactions')
        .where('source_id', '=', subscriptionId)
        .where('source_type', '=', 'subscription')
        .where('status', '=', 'paid')
        .orderBy('id', 'desc')
        .select(['id', 'amount', 'currency'])
        .executeTakeFirst();

      if (!transaction) {
        console.error(`Nenhuma transação encontrada para a assinatura ${subscriptionId}`);

        // Criar uma transação para esta assinatura
        const newTransaction = await db
          .insertInto('transactions')
          .values({
            user_id: subscription.user_id,
            provider_transaction_id: `manual_${Date.now()}`,
            payment_provider_id: 1, // Stripe
            amount: Number(subscription.price || 0),
            currency: subscription.currency || 'BRL',
            status: 'paid',
            source_type: 'subscription',
            source_id: subscription.id,
            created_at: new Date(),
            updated_at: new Date(),
          })
          .executeTakeFirst();

        if (newTransaction && 'insertId' in newTransaction) {
          const transactionId = Number(newTransaction.insertId);
          console.log(`Nova transação criada com ID: ${transactionId}`);

          // Registrar comissões
          await this.registerAffiliateCommissions(
            subscription.user_id,
            subscription.plan_id,
            transactionId,
            Number(subscription.price || 0),
            subscription.currency || 'BRL'
          );

          return {
            status: 'success',
            message: 'Comissões registradas com sucesso',
            data: {
              transaction_id: transactionId
            }
          };
        } else {
          console.error(`Erro ao criar transação para a assinatura ${subscriptionId}`);
          return {
            status: 'error',
            message: 'Erro ao criar transação'
          };
        }
      } else {
        console.log(`Transação encontrada: ${transaction.id}`);

        // Registrar comissões
        await this.registerAffiliateCommissions(
          subscription.user_id,
          subscription.plan_id,
          transaction.id,
          Number(transaction.amount || 0),
          transaction.currency || 'BRL'
        );

        return {
          status: 'success',
          message: 'Comissões registradas com sucesso',
          data: {
            transaction_id: transaction.id
          }
        };
      }
    } catch (error) {
      console.error('Erro ao registrar comissões:', error);
      return {
        status: 'error',
        message: 'Erro ao registrar comissões'
      };
    }
  }
}
