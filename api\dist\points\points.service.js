"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "PointsService", {
    enumerable: true,
    get: function() {
        return PointsService;
    }
});
const _common = require("@nestjs/common");
const _database = require("../database");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let PointsService = class PointsService {
    async getPointsBalance(userId) {
        try {
            const sql = `
        SELECT 
          total_points as totalPoints,
          monthly_points as monthlyPoints,
          yearly_points as yearlyPoints,
          updated_at as lastUpdated
        FROM user_points 
        WHERE user_id = ?
      `;
            const [result] = await _database.db.execute(sql, [
                userId
            ]);
            const balance = result[0] || {
                totalPoints: 0,
                monthlyPoints: 0,
                yearlyPoints: 0,
                lastUpdated: null
            };
            // Get user rank
            const [rankResult] = await _database.db.execute(`SELECT COUNT(*) + 1 as rank
         FROM user_points 
         WHERE total_points > (
           SELECT COALESCE(total_points, 0) 
           FROM user_points 
           WHERE user_id = ?
         )`, [
                userId
            ]);
            balance.rank = rankResult[0]?.rank || 1;
            return {
                status: 'success',
                data: balance
            };
        } catch (error) {
            console.error('Error getting points balance:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get points balance'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPointsHistory(userId, query) {
        try {
            const { page = 1, limit = 20, type, startDate, endDate } = query;
            const offset = (page - 1) * limit;
            let sql = `
        SELECT 
          id,
          points,
          type,
          reason,
          created_at as createdAt
        FROM points_transactions 
        WHERE user_id = ?
      `;
            const params = [
                userId
            ];
            if (type) {
                sql += ` AND type = ?`;
                params.push(type);
            }
            if (startDate) {
                sql += ` AND created_at >= ?`;
                params.push(startDate);
            }
            if (endDate) {
                sql += ` AND created_at <= ?`;
                params.push(endDate);
            }
            sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
            params.push(limit, offset);
            const [transactions] = await _database.db.execute(sql, params);
            // Get total count
            let countSql = `SELECT COUNT(*) as total FROM points_transactions WHERE user_id = ?`;
            const countParams = [
                userId
            ];
            if (type) {
                countSql += ` AND type = ?`;
                countParams.push(type);
            }
            if (startDate) {
                countSql += ` AND created_at >= ?`;
                countParams.push(startDate);
            }
            if (endDate) {
                countSql += ` AND created_at <= ?`;
                countParams.push(endDate);
            }
            const [countResult] = await _database.db.execute(countSql, countParams);
            const total = countResult[0]?.total || 0;
            return {
                status: 'success',
                data: {
                    transactions,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total,
                        totalPages: Math.ceil(total / limit)
                    }
                }
            };
        } catch (error) {
            console.error('Error getting points history:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get points history'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async earnPoints(userId, points, reason) {
        try {
            if (points <= 0) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Points must be positive'
                }, _common.HttpStatus.BAD_REQUEST);
            }
            // Start transaction
            await _database.db.execute('START TRANSACTION');
            try {
                // Update user points
                await _database.db.execute(`INSERT INTO user_points (user_id, total_points, monthly_points, yearly_points, updated_at)
           VALUES (?, ?, ?, ?, NOW())
           ON DUPLICATE KEY UPDATE
           total_points = total_points + ?,
           monthly_points = monthly_points + ?,
           yearly_points = yearly_points + ?,
           updated_at = NOW()`, [
                    userId,
                    points,
                    points,
                    points,
                    points,
                    points,
                    points
                ]);
                // Log transaction
                await _database.db.execute(`INSERT INTO points_transactions (user_id, points, type, reason, created_at)
           VALUES (?, ?, 'earned', ?, NOW())`, [
                    userId,
                    points,
                    reason
                ]);
                await _database.db.execute('COMMIT');
                return {
                    status: 'success',
                    message: `Earned ${points} points for ${reason}`,
                    data: {
                        points,
                        reason,
                        type: 'earned'
                    }
                };
            } catch (error) {
                await _database.db.execute('ROLLBACK');
                throw error;
            }
        } catch (error) {
            console.error('Error earning points:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to earn points'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async spendPoints(userId, points, reason) {
        try {
            if (points <= 0) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Points must be positive'
                }, _common.HttpStatus.BAD_REQUEST);
            }
            // Check if user has enough points
            const [balance] = await _database.db.execute('SELECT total_points FROM user_points WHERE user_id = ?', [
                userId
            ]);
            const currentPoints = balance[0]?.total_points || 0;
            if (currentPoints < points) {
                throw new _common.HttpException({
                    status: 'error',
                    message: 'Insufficient points'
                }, _common.HttpStatus.BAD_REQUEST);
            }
            // Start transaction
            await _database.db.execute('START TRANSACTION');
            try {
                // Update user points
                await _database.db.execute(`UPDATE user_points 
           SET total_points = total_points - ?, updated_at = NOW()
           WHERE user_id = ?`, [
                    points,
                    userId
                ]);
                // Log transaction
                await _database.db.execute(`INSERT INTO points_transactions (user_id, points, type, reason, created_at)
           VALUES (?, ?, 'spent', ?, NOW())`, [
                    userId,
                    points,
                    reason
                ]);
                await _database.db.execute('COMMIT');
                return {
                    status: 'success',
                    message: `Spent ${points} points on ${reason}`,
                    data: {
                        points,
                        reason,
                        type: 'spent',
                        remainingPoints: currentPoints - points
                    }
                };
            } catch (error) {
                await _database.db.execute('ROLLBACK');
                throw error;
            }
        } catch (error) {
            console.error('Error spending points:', error);
            if (error instanceof _common.HttpException) throw error;
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to spend points'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getLeaderboard(query) {
        try {
            const { period = 'all', limit = 50 } = query;
            let pointsColumn = 'total_points';
            if (period === 'monthly') pointsColumn = 'monthly_points';
            if (period === 'yearly') pointsColumn = 'yearly_points';
            const sql = `
        SELECT 
          u.id,
          u.name,
          u.username,
          u.photo,
          COALESCE(up.${pointsColumn}, 0) as points,
          u.last_active as lastActive
        FROM users u
        LEFT JOIN user_points up ON u.id = up.user_id
        WHERE u.role = 'client'
        ORDER BY up.${pointsColumn} DESC, u.name ASC
        LIMIT ?
      `;
            const [results] = await _database.db.execute(sql, [
                limit
            ]);
            // Add ranking
            const leaderboard = results.map((user, index)=>({
                    ...user,
                    rank: index + 1
                }));
            return {
                status: 'success',
                data: {
                    period,
                    leaderboard
                }
            };
        } catch (error) {
            console.error('Error getting leaderboard:', error);
            throw new _common.HttpException({
                status: 'error',
                message: 'Failed to get leaderboard'
            }, _common.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // Helper method for automatic point earning
    async awardPointsForActivity(userId, activity, value) {
        try {
            const pointsRules = {
                'workout_completed': 50,
                'meal_logged': 10,
                'water_goal_reached': 25,
                'daily_goal_completed': 100,
                'weekly_goal_completed': 500,
                'friend_added': 20,
                'challenge_joined': 30,
                'challenge_completed': 200,
                'profile_completed': 100,
                'first_workout': 150,
                'streak_7_days': 300,
                'streak_30_days': 1000
            };
            const points = value || pointsRules[activity] || 0;
            if (points > 0) {
                await this.earnPoints(userId, points, activity.replace('_', ' '));
                // Create notification
                await this.createPointsNotification(userId, points, activity);
            }
            return points;
        } catch (error) {
            console.error('Error awarding points for activity:', error);
            return 0;
        }
    }
    async createPointsNotification(userId, points, activity) {
        try {
            const activityNames = {
                'workout_completed': 'treino concluído',
                'meal_logged': 'refeição registrada',
                'water_goal_reached': 'meta de água atingida',
                'daily_goal_completed': 'meta diária concluída',
                'weekly_goal_completed': 'meta semanal concluída',
                'friend_added': 'amigo adicionado',
                'challenge_joined': 'desafio iniciado',
                'challenge_completed': 'desafio concluído',
                'profile_completed': 'perfil completado',
                'first_workout': 'primeiro treino',
                'streak_7_days': 'sequência de 7 dias',
                'streak_30_days': 'sequência de 30 dias'
            };
            const activityName = activityNames[activity] || activity;
            await _database.db.execute(`INSERT INTO notifications (user_id, type, title, message, data, created_at)
         VALUES (?, 'points_earned', 'SnapCoins Ganhos!', ?, ?, NOW())`, [
                userId,
                `Você ganhou ${points} SnapCoins por ${activityName}!`,
                JSON.stringify({
                    points,
                    activity,
                    type: 'points_earned'
                })
            ]);
        } catch (error) {
            console.error('Error creating points notification:', error);
        }
    }
    // Method to reset monthly/yearly points (to be called by cron job)
    async resetPeriodicPoints(period) {
        try {
            const column = period === 'monthly' ? 'monthly_points' : 'yearly_points';
            await _database.db.execute(`UPDATE user_points SET ${column} = 0, updated_at = NOW()`, []);
            console.log(`Reset ${period} points for all users`);
            return true;
        } catch (error) {
            console.error(`Error resetting ${period} points:`, error);
            return false;
        }
    }
};
PointsService = _ts_decorate([
    (0, _common.Injectable)()
], PointsService);

//# sourceMappingURL=points.service.js.map