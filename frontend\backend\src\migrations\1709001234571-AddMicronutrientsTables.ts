import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMicronutrientsTables1709001234571 implements MigrationInterface {
  name = 'AddMicronutrientsTables1709001234571';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Blood tests table
    await queryRunner.query(`
      CREATE TABLE blood_tests (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        test_date DATE NOT NULL,
        uploaded_file_name VARCHAR(255),
        uploaded_file_path VARCHAR(500),
        results JSON NOT NULL,
        analyzed_by_ai BOOLEAN DEFAULT FALSE,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Micronutrient analyses table
    await queryRunner.query(`
      CREATE TABLE micronutrient_analyses (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        analysis_date DATE NOT NULL,
        total_intake JSON NOT NULL,
        deficiencies TEXT,
        excesses TEXT,
        overall_score INT NOT NULL,
        improvement_areas TEXT,
        weekly_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Supplement recommendations table
    await queryRunner.query(`
      CREATE TABLE supplement_recommendations (
        id VARCHAR(36) NOT NULL PRIMARY KEY,
        user_id VARCHAR(36) NOT NULL,
        micronutrient_id VARCHAR(100) NOT NULL,
        name VARCHAR(255) NOT NULL,
        dosage DECIMAL(10,2) NOT NULL,
        unit VARCHAR(50) NOT NULL,
        timing VARCHAR(100) NOT NULL,
        duration VARCHAR(100) NOT NULL,
        reason TEXT NOT NULL,
        priority ENUM('high', 'medium', 'low') NOT NULL,
        interactions TEXT,
        side_effects TEXT,
        cost DECIMAL(10,2),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX idx_blood_tests_user_id ON blood_tests(user_id);`);
    await queryRunner.query(`CREATE INDEX idx_blood_tests_test_date ON blood_tests(test_date);`);
    await queryRunner.query(`CREATE INDEX idx_micronutrient_analyses_user_id ON micronutrient_analyses(user_id);`);
    await queryRunner.query(`CREATE INDEX idx_micronutrient_analyses_date ON micronutrient_analyses(analysis_date);`);
    await queryRunner.query(`CREATE INDEX idx_supplement_recommendations_user_id ON supplement_recommendations(user_id);`);
    await queryRunner.query(`CREATE INDEX idx_supplement_recommendations_active ON supplement_recommendations(is_active);`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX idx_supplement_recommendations_active ON supplement_recommendations;`);
    await queryRunner.query(`DROP INDEX idx_supplement_recommendations_user_id ON supplement_recommendations;`);
    await queryRunner.query(`DROP INDEX idx_micronutrient_analyses_date ON micronutrient_analyses;`);
    await queryRunner.query(`DROP INDEX idx_micronutrient_analyses_user_id ON micronutrient_analyses;`);
    await queryRunner.query(`DROP INDEX idx_blood_tests_test_date ON blood_tests;`);
    await queryRunner.query(`DROP INDEX idx_blood_tests_user_id ON blood_tests;`);

    // Drop tables
    await queryRunner.query(`DROP TABLE supplement_recommendations;`);
    await queryRunner.query(`DROP TABLE micronutrient_analyses;`);
    await queryRunner.query(`DROP TABLE blood_tests;`);
  }
}
