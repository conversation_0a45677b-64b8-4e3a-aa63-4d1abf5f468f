<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Nova Refeição</title>
  <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
  <style>
    body {
      background-color: #000;
      color: #fff;
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background-image: radial-gradient(circle at center, rgba(185, 255, 67, 0.03) 0, transparent 70%);
    }
    .header {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 16px 20px;
      background-color: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(185, 255, 67, 0.2);
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .back-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      background: none;
      border: none;
      color: #B9FF43;
      cursor: pointer;
      font-size: 24px;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    .back-button:hover {
      background-color: rgba(185, 255, 67, 0.1);
    }
    .title-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .icon-container {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(185, 255, 67, 0.2);
      border-radius: 50%;
      border: 1px solid rgba(185, 255, 67, 0.3);
    }
    .title {
      font-size: 20px;
      font-weight: bold;
    }
    .main-content {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .chat-container {
      background-color: rgba(17, 17, 17, 0.8);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 20px;
      border: 1px solid rgba(185, 255, 67, 0.2);
      display: flex;
      flex-direction: column;
      height: calc(100vh - 180px);
    }
    .messages {
      flex: 1;
      overflow-y: auto;
      padding-right: 10px;
      margin-bottom: 20px;
    }
    .message {
      margin-bottom: 16px;
      max-width: 80%;
    }
    .message.assistant {
      align-self: flex-start;
      background-color: #222;
      border: 1px solid rgba(185, 255, 67, 0.1);
      border-radius: 12px 12px 12px 0;
      padding: 12px 16px;
    }
    .message.user {
      align-self: flex-end;
      margin-left: auto;
      background-color: rgba(185, 255, 67, 0.1);
      border: 1px solid rgba(185, 255, 67, 0.3);
      border-radius: 12px 12px 0 12px;
      padding: 12px 16px;
    }
    .input-container {
      display: flex;
      gap: 10px;
      background-color: #222;
      border-radius: 12px;
      padding: 8px;
      border: 1px solid rgba(185, 255, 67, 0.2);
    }
    .message-input {
      flex: 1;
      background: none;
      border: none;
      color: #fff;
      padding: 8px;
      font-family: 'Inter', sans-serif;
      resize: none;
      min-height: 24px;
      max-height: 120px;
      outline: none;
    }
    .action-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
    }
    .action-button {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(185, 255, 67, 0.1);
      border: 1px solid rgba(185, 255, 67, 0.2);
      border-radius: 50%;
      color: #B9FF43;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .action-button:hover {
      background-color: rgba(185, 255, 67, 0.2);
    }
    .send-button {
      background-color: #B9FF43;
      color: #000;
    }
    .send-button:hover {
      background-color: #a8e63c;
    }
    .suggestions {
      margin-top: 20px;
    }
    .suggestion-title {
      font-size: 14px;
      color: #999;
      margin-bottom: 10px;
    }
    .suggestion-items {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
    .suggestion-item {
      background-color: rgba(185, 255, 67, 0.1);
      border: 1px solid rgba(185, 255, 67, 0.2);
      border-radius: 16px;
      padding: 6px 12px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    .suggestion-item:hover {
      background-color: rgba(185, 255, 67, 0.2);
    }
    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
    .loading-dots {
      display: flex;
      gap: 4px;
    }
    .loading-dot {
      width: 8px;
      height: 8px;
      background-color: #B9FF43;
      border-radius: 50%;
      animation: pulse 1.5s infinite ease-in-out;
    }
    .loading-dot:nth-child(2) {
      animation-delay: 0.2s;
    }
    .loading-dot:nth-child(3) {
      animation-delay: 0.4s;
    }
    @keyframes pulse {
      0%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      50% {
        transform: scale(1.2);
        opacity: 1;
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <button class="back-button" onclick="window.location.href='/dashboard'">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="m15 18-6-6 6-6"/>
      </svg>
    </button>
    <div class="title-container">
      <div class="icon-container">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#B9FF43" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M17 10h-1V8A6 6 0 0 0 6 8v2H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2Z"/>
          <path d="M10 15a1 1 0 1 0 2 0 1 1 0 0 0-2 0Z"/>
        </svg>
      </div>
      <div class="title">Nova Refeição</div>
    </div>
  </div>

  <div class="main-content">
    <div class="chat-container">
      <div class="messages">
        <div class="message assistant">
          O que você comeu?
        </div>
      </div>

      <div class="input-container">
        <textarea class="message-input" placeholder="Digite sua mensagem..." rows="1"></textarea>
        <div class="action-buttons">
          <button class="action-button" title="Tirar foto">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"/>
              <circle cx="12" cy="13" r="3"/>
            </svg>
          </button>
          <button class="action-button" title="Gravar áudio">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
              <line x1="12" y1="19" x2="12" y2="22"/>
            </svg>
          </button>
          <button class="action-button send-button" title="Enviar">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="22" y1="2" x2="11" y2="13"/>
              <polygon points="22 2 15 22 11 13 2 9 22 2"/>
            </svg>
          </button>
        </div>
      </div>

      <div class="suggestions">
        <div class="suggestion-title">Sugestões:</div>
        <div class="suggestion-items">
          <div class="suggestion-item">Café da manhã</div>
          <div class="suggestion-item">Almoço</div>
          <div class="suggestion-item">Jantar</div>
          <div class="suggestion-item">Lanche</div>
          <div class="suggestion-item">Pré-treino</div>
          <div class="suggestion-item">Pós-treino</div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Simple chat functionality
    const messageInput = document.querySelector('.message-input');
    const sendButton = document.querySelector('.send-button');
    const messagesContainer = document.querySelector('.messages');
    const suggestionItems = document.querySelectorAll('.suggestion-item');

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });

    // Send message function
    function sendMessage() {
      const message = messageInput.value.trim();
      if (!message) return;

      // Add user message
      const userMessageElement = document.createElement('div');
      userMessageElement.className = 'message user';
      userMessageElement.textContent = message;
      messagesContainer.appendChild(userMessageElement);

      // Clear input
      messageInput.value = '';
      messageInput.style.height = 'auto';

      // Scroll to bottom
      messagesContainer.scrollTop = messagesContainer.scrollHeight;

      // Simulate AI response after a delay
      setTimeout(() => {
        // Add loading indicator
        const loadingElement = document.createElement('div');
        loadingElement.className = 'loading';
        loadingElement.innerHTML = `
          <div class="loading-dots">
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
          </div>
        `;
        messagesContainer.appendChild(loadingElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Remove loading and add AI response after 2 seconds
        setTimeout(() => {
          messagesContainer.removeChild(loadingElement);

          const aiMessageElement = document.createElement('div');
          aiMessageElement.className = 'message assistant';
          aiMessageElement.textContent = `Entendi que você comeu "${message}". Deseja adicionar mais algum alimento?`;
          messagesContainer.appendChild(aiMessageElement);

          // Scroll to bottom
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 2000);
      }, 500);
    }

    // Send message on button click
    sendButton.addEventListener('click', sendMessage);

    // Send message on Enter key (but allow Shift+Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    });

    // Handle suggestion clicks
    suggestionItems.forEach(item => {
      item.addEventListener('click', function() {
        messageInput.value = this.textContent;
        messageInput.dispatchEvent(new Event('input'));
        sendMessage();
      });
    });

    // Handle camera and microphone buttons
    document.querySelectorAll('.action-button').forEach(button => {
      if (button !== sendButton) {
        button.addEventListener('click', function() {
          alert('Esta funcionalidade está em desenvolvimento.');
        });
      }
    });
  </script>
</body>
</html>
