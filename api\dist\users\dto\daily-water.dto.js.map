{"version": 3, "sources": ["../../../src/users/dto/daily-water.dto.ts"], "sourcesContent": ["import { Type } from 'class-transformer';\r\nimport { IsDate, IsNotEmpty, IsNumber, IsString } from 'class-validator';\r\n\r\nexport class DailyWaterDto {\r\n  @IsNotEmpty()\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  consumed: number;\r\n\r\n  @IsNotEmpty()\r\n  @IsString()\r\n  daily_at: string;\r\n}"], "names": ["DailyWaterDto", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;kCAHQ;gCACkC;;;;;;;;;;AAEhD,IAAA,AAAMA,gBAAN,MAAMA;AASb;;;;oCANcC"}