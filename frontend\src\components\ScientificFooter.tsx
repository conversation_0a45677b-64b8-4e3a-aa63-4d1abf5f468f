import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Info, ExternalLink } from 'lucide-react';

interface ScientificFooterProps {
  className?: string;
  variant?: 'default' | 'compact' | 'minimal';
  showIcon?: boolean;
}

export function ScientificFooter({ 
  className = '', 
  variant = 'default',
  showIcon = true 
}: ScientificFooterProps) {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate('/dashboard/scientific-sources');
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'compact':
        return 'py-2 px-3 text-xs';
      case 'minimal':
        return 'py-1 px-2 text-xs';
      default:
        return 'py-3 px-4 text-sm';
    }
  };

  const getTextContent = () => {
    switch (variant) {
      case 'minimal':
        return 'Ver fontes científicas';
      default:
        return 'Baseado em diretrizes oficiais – ver fontes';
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`
        w-full flex items-center justify-center gap-2 
        bg-snapfit-dark-gray/50 border border-snapfit-green/10 
        rounded-lg text-gray-400 hover:text-gray-300 
        hover:border-snapfit-green/20 hover:bg-snapfit-dark-gray/70
        transition-all duration-200 group
        ${getVariantStyles()}
        ${className}
      `}
    >
      {showIcon && (
        <Info className={`
          ${variant === 'minimal' ? 'w-3 h-3' : 'w-4 h-4'} 
          text-gray-500 group-hover:text-snapfit-green/70 
          transition-colors duration-200
        `} />
      )}
      <span className="font-medium">
        {getTextContent()}
      </span>
      <ExternalLink className={`
        ${variant === 'minimal' ? 'w-3 h-3' : 'w-3.5 h-3.5'} 
        text-gray-500 group-hover:text-snapfit-green/70 
        transition-colors duration-200
      `} />
    </button>
  );
}

// Componente específico para uso em cards de cálculo
export function CalculationScientificFooter({ className = '' }: { className?: string }) {
  return (
    <ScientificFooter 
      variant="compact"
      className={`mt-4 ${className}`}
    />
  );
}

// Componente específico para uso em modais
export function ModalScientificFooter({ className = '' }: { className?: string }) {
  return (
    <ScientificFooter 
      variant="default"
      className={`mt-6 ${className}`}
    />
  );
}

// Componente específico para uso inline/discreto
export function InlineScientificFooter({ className = '' }: { className?: string }) {
  return (
    <ScientificFooter
      variant="minimal"
      showIcon={false}
      className={`inline-flex w-auto ${className}`}
    />
  );
}

// Componente discreto para estatísticas
export function StatScientificNote({ className = '' }: { className?: string }) {
  const navigate = useNavigate();

  return (
    <button
      onClick={() => navigate('/dashboard/scientific-sources')}
      className={`
        text-xs text-gray-500 hover:text-gray-400
        transition-colors underline decoration-dotted
        ${className}
      `}
    >
      *baseado em diretrizes científicas
    </button>
  );
}

// Componente para análises complexas
export function AnalysisScientificNote({
  context = "análise",
  className = ''
}: {
  context?: string;
  className?: string;
}) {
  const navigate = useNavigate();

  return (
    <div className={`flex items-center justify-center gap-2 mt-3 ${className}`}>
      <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
      <button
        onClick={() => navigate('/dashboard/scientific-sources')}
        className="flex items-center gap-1 px-2 py-1 text-xs text-gray-500 hover:text-blue-400 transition-colors"
      >
        <Info className="w-3 h-3" />
        <span>{context} baseada em evidências científicas</span>
      </button>
      <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
    </div>
  );
}
