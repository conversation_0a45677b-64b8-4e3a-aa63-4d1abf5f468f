{"version": 3, "sources": ["../../src/nutritionist/nutritionist.controller.ts"], "sourcesContent": ["import { Body, Controller, Get, Param, Post, Query, Request, UseGuards } from '@nestjs/common';\r\nimport { NutritionistService } from './nutritionist.service';\r\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\r\nimport { GetNutritionistClientsQueryDto } from '../admin/dto/get-nutritionist-clients-query.dto';\r\nimport { CreateNutritionistProtocolDto } from '../dto/create-nutritionist-protocol.dto';\r\nimport { CreateNutritionistClientProtocolDto } from '../dto/create-nutritionist-client-protocol.dto';\r\nimport { ImportNutritionistClientProtocolDto } from '../dto/import-nutritionist-client-protocol.dto';\r\n\r\n@Controller('nutritionist')\r\nexport class NutritionistController {\r\n    constructor(private readonly nutritionistService: NutritionistService) {}\r\n\r\n    @Get('stats')\r\n    getStats() {\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                total_clients: 99,\r\n                active_protocols: 99,\r\n                completion_rate: 99,\r\n            },\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('clients')\r\n    getClients(@Query() query: GetNutritionistClientsQueryDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.nutritionistService.getClients(query, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('clients/:id')\r\n    getClient(@Param('id') id: number, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.nutritionistService.getClient(id, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols')\r\n    createProtocol(@Body() createNutritionistProtocolDto: CreateNutritionistProtocolDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.nutritionistService.createProtocol(createNutritionistProtocolDto, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols')\r\n    getProtocols(@Request() req: any) {\r\n        const userId: number = req.user?.userId;\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.nutritionistService.getProtocols(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('clients/:id/protocols')\r\n    getClientProtocols(@Param('id') id: number, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.nutritionistService.clientProtocols(id, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('clients/:id/protocols')\r\n    createClientProtocol(@Param('id') id: number, @Body() createNutritionistClientProtocolDto: CreateNutritionistClientProtocolDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.nutritionistService.createClientProtocol(id, createNutritionistClientProtocolDto, userId);\r\n    }\r\n\r\n    // import protocol\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('clients/:id/protocols/import')\r\n    importClientProtocol(@Param('id') id: number, @Body() importNutritionistClientProtocolDto: ImportNutritionistClientProtocolDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.nutritionistService.importClientProtocol(id, importNutritionistClientProtocolDto, userId);\r\n    }\r\n    \r\n}\r\n"], "names": ["NutritionistController", "getStats", "status", "data", "total_clients", "active_protocols", "completion_rate", "getClients", "query", "req", "userId", "user", "message", "nutritionistService", "getClient", "id", "createProtocol", "createNutritionistProtocolDto", "getProtocols", "getClientProtocols", "clientProtocols", "createClientProtocol", "createNutritionistClientProtocolDto", "importClientProtocol", "importNutritionistClientProtocolDto", "constructor"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATiE;qCAC1C;8BACP;gDACkB;+CACD;qDACM;qDACA;;;;;;;;;;;;;;;AAG7C,IAAA,AAAMA,yBAAN,MAAMA;IAITC,WAAW;QACP,OAAO;YACHC,QAAQ;YACRC,MAAM;gBACFC,eAAe;gBACfC,kBAAkB;gBAClBC,iBAAiB;YACrB;QACJ;IACJ;IAIAC,WAAW,AAASC,KAAqC,EAAE,AAAWC,GAAQ,EAAE;QAC5E,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,mBAAmB,CAACN,UAAU,CAACC,OAAOE;IACtD;IAIAI,UAAU,AAAaC,EAAU,EAAE,AAAWN,GAAQ,EAAE;QACpD,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,mBAAmB,CAACC,SAAS,CAACC,IAAIL;IAClD;IAIAM,eAAe,AAAQC,6BAA4D,EAAE,AAAWR,GAAQ,EAAE;QACtG,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,mBAAmB,CAACG,cAAc,CAACC,+BAA+BP;IAClF;IAIAQ,aAAa,AAAWT,GAAQ,EAAE;QAC9B,MAAMC,SAAiBD,IAAIE,IAAI,EAAED;QACjC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,mBAAmB,CAACK,YAAY,CAACR;IACjD;IAIAS,mBAAmB,AAAaJ,EAAU,EAAE,AAAWN,GAAQ,EAAE;QAC7D,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,mBAAmB,CAACO,eAAe,CAACL,IAAIL;IACxD;IAIAW,qBAAqB,AAAaN,EAAU,EAAE,AAAQO,mCAAwE,EAAE,AAAWb,GAAQ,EAAE;QACjJ,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,mBAAmB,CAACQ,oBAAoB,CAACN,IAAIO,qCAAqCZ;IAClG;IAEA,kBAAkB;IAGlBa,qBAAqB,AAAaR,EAAU,EAAE,AAAQS,mCAAwE,EAAE,AAAWf,GAAQ,EAAE;QACjJ,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,mBAAmB,CAACU,oBAAoB,CAACR,IAAIS,qCAAqCd;IAClG;IAlHAe,YAAY,AAAiBZ,mBAAwC,CAAE;aAA1CA,sBAAAA;IAA2C;AAoH5E"}