// ============================================================================
// ENHANCED PROTOCOL HISTORY - VERSÃO ATUALIZADA COM DADOS REAIS
// ============================================================================

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { History } from 'lucide-react';
import { ProtocolHistoryItem } from '../services/protocolHistory';
import { UnifiedProtocolHistory } from './UnifiedProtocolHistory';

interface EnhancedProtocolHistoryProps {
  type: 'workout' | 'diet';
  onReuseProtocol?: (protocol: ProtocolHistoryItem, shouldEdit?: boolean) => void;
  onViewDetails?: (protocol: ProtocolHistoryItem) => void;
}

/**
 * Componente Enhanced Protocol History atualizado para usar dados reais
 * em vez de dados mock. Agora utiliza o UnifiedProtocolHistory que
 * consome os endpoints reais implementados.
 */
export function EnhancedProtocolHistory({
  type,
  onReuseProtocol,
  onViewDetails
}: EnhancedProtocolHistoryProps) {
  const navigate = useNavigate();

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleProtocolSelect = (protocol: any) => {
    console.log(`🔍 EnhancedProtocolHistory: Protocolo selecionado`, protocol);
    console.log(`🔍 EnhancedProtocolHistory: Protocol ID:`, protocol?.id);
    console.log(`🔍 EnhancedProtocolHistory: Protocol structure:`, Object.keys(protocol || {}));
    console.log(`🔍 EnhancedProtocolHistory: onViewDetails function:`, onViewDetails);

    if (onViewDetails) {
      // Transform protocol to match expected interface
      const transformedProtocol = {
        id: protocol?.id || protocol?.mock_id || 'unknown',
        name: protocol?.name || 'Protocolo sem nome',
        type: protocol?.objective || 'workout',
        objective: protocol?.objective || 'Não especificado',
        startDate: protocol?.started_at || new Date().toISOString(),
        endDate: protocol?.ended_at || null,
        status: protocol?.status || 'unknown',
        ...protocol
      };

      console.log(`🔍 EnhancedProtocolHistory: Transformed protocol:`, transformedProtocol);
      onViewDetails(transformedProtocol);
    } else {
      console.error(`❌ EnhancedProtocolHistory: onViewDetails function not available`);
    }
  };

  const handleProtocolDuplicate = (protocol: any) => {
    console.log(`📋 EnhancedProtocolHistory: Protocolo duplicado`, protocol);
    if (onReuseProtocol) {
      // Transform protocol to match expected interface
      const transformedProtocol = {
        id: protocol?.id || protocol?.mock_id || 'unknown',
        name: protocol?.name || 'Protocolo sem nome',
        type: protocol?.objective || 'workout',
        objective: protocol?.objective || 'Não especificado',
        startDate: protocol?.started_at || new Date().toISOString(),
        endDate: protocol?.ended_at || null,
        status: protocol?.status || 'unknown',
        ...protocol
      };
      onReuseProtocol(transformedProtocol, false);
    }
  };

  const handleProtocolFinish = (protocol: any) => {
    console.log(`🏁 EnhancedProtocolHistory: Protocolo finalizado`, protocol);
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className="space-y-6">
      {/* Header informativo */}
      <div className="bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20">
        <div className="flex items-center gap-3">
          <History className="w-6 h-6 text-snapfit-green" />
          <div>
            <h2 className="text-lg font-semibold text-white">
              Histórico de Protocolos de {type === 'diet' ? 'Dieta' : 'Treino'}
            </h2>
            <p className="text-sm text-gray-400">
              Visualize, duplique e gerencie seus protocolos anteriores com dados reais do banco de dados
            </p>
          </div>
        </div>
      </div>

      {/* Componente unificado com dados reais */}
      <UnifiedProtocolHistory
        protocolType={type}
        onProtocolSelect={handleProtocolSelect}
        onProtocolDuplicate={handleProtocolDuplicate}
        onProtocolFinish={handleProtocolFinish}
      />
    </div>
  );
}
