{"version": 3, "sources": ["../../src/clients/clients.controller.ts"], "sourcesContent": ["import { \n  Controller, \n  Get, \n  Post, \n  Put, \n  Delete, \n  Body, \n  Param, \n  Query, \n  Request, \n  UseGuards \n} from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { ClientsService } from './clients.service';\nimport { CreateClientDto } from './dto/create-client.dto';\nimport { UpdateClientDto } from './dto/update-client.dto';\n\n@Controller('clients')\n@UseGuards(JwtAuthGuard)\nexport class ClientsController {\n  constructor(private readonly clientsService: ClientsService) {}\n\n  @Get()\n  async getClients(@Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.clientsService.getClients(userId, query);\n  }\n\n  @Get(':id')\n  async getClient(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.clientsService.getClient(Number(id), userId);\n  }\n\n  @Post()\n  async createClient(@Body() createClientDto: CreateClientDto, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.clientsService.createClient(createClientDto, userId);\n  }\n\n  @Put(':id')\n  async updateClient(\n    @Param('id') id: string, \n    @Body() updateClientDto: UpdateClientDto, \n    @Request() req: any\n  ) {\n    const userId = req.user.userId;\n    return this.clientsService.updateClient(Number(id), updateClientDto, userId);\n  }\n\n  @Delete(':id')\n  async deleteClient(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.clientsService.deleteClient(Number(id), userId);\n  }\n\n  @Get(':id/protocols')\n  async getClientProtocols(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.clientsService.getClientProtocols(Number(id), userId);\n  }\n\n  @Get(':id/assessments')\n  async getClientAssessments(@Param('id') id: string, @Request() req: any) {\n    const userId = req.user.userId;\n    return this.clientsService.getClientAssessments(Number(id), userId);\n  }\n\n  @Post(':id/protocols')\n  async createClientProtocol(\n    @Param('id') id: string, \n    @Body() protocolData: any, \n    @Request() req: any\n  ) {\n    const userId = req.user.userId;\n    return this.clientsService.createClientProtocol(Number(id), protocolData, userId);\n  }\n\n  @Get(':id/progress')\n  async getClientProgress(@Param('id') id: string, @Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.clientsService.getClientProgress(Number(id), userId, query);\n  }\n\n  @Get(':id/nutrition-data')\n  async getClientNutritionData(@Param('id') id: string, @Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.clientsService.getClientNutritionData(Number(id), userId, query);\n  }\n\n  @Get(':id/workout-data')\n  async getClientWorkoutData(@Param('id') id: string, @Request() req: any, @Query() query: any) {\n    const userId = req.user.userId;\n    return this.clientsService.getClientWorkoutData(Number(id), userId, query);\n  }\n}\n"], "names": ["ClientsController", "getClients", "req", "query", "userId", "user", "clientsService", "getClient", "id", "Number", "createClient", "createClientDto", "updateClient", "updateClientDto", "deleteClient", "getClientProtocols", "getClientAssessments", "createClientProtocol", "protocolData", "getClientProgress", "getClientNutritionData", "getClientWorkoutData", "constructor"], "mappings": ";;;;+BAmBaA;;;eAAAA;;;wBARN;8BACsB;gCACE;iCACC;iCACA;;;;;;;;;;;;;;;AAIzB,IAAA,AAAMA,oBAAN,MAAMA;IAGX,MACMC,WAAW,AAAWC,GAAQ,EAAE,AAASC,KAAU,EAAE;QACzD,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACL,UAAU,CAACG,QAAQD;IAChD;IAEA,MACMI,UAAU,AAAaC,EAAU,EAAE,AAAWN,GAAQ,EAAE;QAC5D,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACC,SAAS,CAACE,OAAOD,KAAKJ;IACnD;IAEA,MACMM,aAAa,AAAQC,eAAgC,EAAE,AAAWT,GAAQ,EAAE;QAChF,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACI,YAAY,CAACC,iBAAiBP;IAC3D;IAEA,MACMQ,aACJ,AAAaJ,EAAU,EACvB,AAAQK,eAAgC,EACxC,AAAWX,GAAQ,EACnB;QACA,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACM,YAAY,CAACH,OAAOD,KAAKK,iBAAiBT;IACvE;IAEA,MACMU,aAAa,AAAaN,EAAU,EAAE,AAAWN,GAAQ,EAAE;QAC/D,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACQ,YAAY,CAACL,OAAOD,KAAKJ;IACtD;IAEA,MACMW,mBAAmB,AAAaP,EAAU,EAAE,AAAWN,GAAQ,EAAE;QACrE,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACS,kBAAkB,CAACN,OAAOD,KAAKJ;IAC5D;IAEA,MACMY,qBAAqB,AAAaR,EAAU,EAAE,AAAWN,GAAQ,EAAE;QACvE,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACU,oBAAoB,CAACP,OAAOD,KAAKJ;IAC9D;IAEA,MACMa,qBACJ,AAAaT,EAAU,EACvB,AAAQU,YAAiB,EACzB,AAAWhB,GAAQ,EACnB;QACA,MAAME,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACW,oBAAoB,CAACR,OAAOD,KAAKU,cAAcd;IAC5E;IAEA,MACMe,kBAAkB,AAAaX,EAAU,EAAE,AAAWN,GAAQ,EAAE,AAASC,KAAU,EAAE;QACzF,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACa,iBAAiB,CAACV,OAAOD,KAAKJ,QAAQD;IACnE;IAEA,MACMiB,uBAAuB,AAAaZ,EAAU,EAAE,AAAWN,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC9F,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACc,sBAAsB,CAACX,OAAOD,KAAKJ,QAAQD;IACxE;IAEA,MACMkB,qBAAqB,AAAab,EAAU,EAAE,AAAWN,GAAQ,EAAE,AAASC,KAAU,EAAE;QAC5F,MAAMC,SAASF,IAAIG,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,cAAc,CAACe,oBAAoB,CAACZ,OAAOD,KAAKJ,QAAQD;IACtE;IA1EAmB,YAAY,AAAiBhB,cAA8B,CAAE;aAAhCA,iBAAAA;IAAiC;AA2EhE"}