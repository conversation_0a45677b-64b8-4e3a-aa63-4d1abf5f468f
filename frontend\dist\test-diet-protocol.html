<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Diet Protocol API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #fff;
        }
        .container {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
        pre {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        input {
            background-color: #3a3a3a;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>Diet Protocol API Test</h1>
    
    <div class="container">
        <h2>Authentication</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="Teste123">
        <button onclick="login()">Login</button>
        <div id="auth-status"></div>
    </div>

    <div class="container">
        <h2>Test Diet Protocol Creation</h2>
        <button onclick="testDietProtocolCreation()">Test Create Diet Protocol</button>
        <div id="test-result"></div>
    </div>

    <div class="container">
        <h2>API Response</h2>
        <pre id="api-response"></pre>
    </div>

    <script>
        let authToken = localStorage.getItem('token');
        
        // Update auth status on page load
        updateAuthStatus();
        
        function updateAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            if (authToken) {
                statusDiv.innerHTML = '<span class="success">✓ Authenticated</span>';
            } else {
                statusDiv.innerHTML = '<span class="error">✗ Not authenticated</span>';
            }
        }
        
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:3000/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    authToken = data.access_token;
                    localStorage.setItem('token', authToken);
                    updateAuthStatus();
                    document.getElementById('api-response').textContent = 'Login successful!';
                } else {
                    throw new Error(data.message || 'Login failed');
                }
            } catch (error) {
                document.getElementById('api-response').textContent = 'Login error: ' + error.message;
            }
        }
        
        async function testDietProtocolCreation() {
            if (!authToken) {
                document.getElementById('test-result').innerHTML = '<span class="error">Please login first</span>';
                return;
            }
            
            // Test data matching the expected backend format
            const testData = {
                name: "Test Diet Protocol",
                type_id: 1, // weight-loss
                objective: "Test protocol creation",
                nutritional_goals: {
                    calories: 2000,
                    protein: 150,
                    carbs: 200,
                    fat: 80,
                    water: 2500
                },
                meals: [
                    {
                        name: "Breakfast",
                        day_of_week: "monday",
                        meal_time: "08:00",
                        foods: [
                            {
                                name: "Oatmeal",
                                quantity: 100,
                                unit: "g",
                                calories: 350,
                                protein: 12,
                                carbs: 60,
                                fat: 6,
                                fiber: 8
                            }
                        ]
                    }
                ],
                supplements: [
                    {
                        name: "Whey Protein",
                        dosage: "30g",
                        supplement_time: "post-workout",
                        notes: "Test supplement"
                    }
                ],
                general_notes: "Test protocol notes"
            };
            
            try {
                document.getElementById('test-result').innerHTML = 'Testing...';
                document.getElementById('api-response').textContent = 'Sending request...';
                
                const response = await fetch('http://localhost:3000/users/protocols/diet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(testData)
                });
                
                const responseData = await response.json();
                
                if (response.ok) {
                    document.getElementById('test-result').innerHTML = '<span class="success">✓ Protocol created successfully!</span>';
                } else {
                    document.getElementById('test-result').innerHTML = '<span class="error">✗ Failed to create protocol</span>';
                }
                
                document.getElementById('api-response').textContent = JSON.stringify({
                    status: response.status,
                    statusText: response.statusText,
                    data: responseData
                }, null, 2);
                
            } catch (error) {
                document.getElementById('test-result').innerHTML = '<span class="error">✗ Error: ' + error.message + '</span>';
                document.getElementById('api-response').textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
