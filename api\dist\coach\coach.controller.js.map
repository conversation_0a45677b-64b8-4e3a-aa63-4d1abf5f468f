{"version": 3, "sources": ["../../src/coach/coach.controller.ts"], "sourcesContent": ["import { Body, Controller, Delete, Get, Param, Post, Put, Query, Request, UseGuards } from '@nestjs/common';\r\nimport { GetCoachClientsQueryDto } from 'src/admin/dto/get-coach-clients-query.dto';\r\nimport { CoachService } from './coach.service';\r\nimport { JwtAuthGuard } from 'src/auth/jwt-auth.guard';\r\nimport { ImportCoachProtocolDto } from 'src/admin/dto/import-coach-protocol.dto';\r\nimport { CreateCoachProtocolDto } from 'src/admin/dto/create-coach-protocol.dto';\r\nimport { CreateCoachClientProtocolDto } from './dto/create-coach-client-protocol.dto';\r\n\r\n@Controller('coach')\r\nexport class CoachController {\r\n    constructor(private readonly coachService: CoachService) {}\r\n\r\n    @Get('stats')\r\n    getStats() {\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                total_clients: 99,\r\n                active_protocols: 99,\r\n                completion_rate: 99,\r\n            },\r\n        }\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('clients')\r\n    getClients(@Query() query: GetCoachClientsQueryDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.coachService.getClients(query, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('clients/:id')\r\n    getClient(@Param('id') id: number, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.coachService.getClient(id, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('clients/:id/protocols')\r\n    getClientProtocols(@Param('id') id: number, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.coachService.getClientProtocols(id, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('clients/:id/protocols/import')\r\n    importProtocol(@Param('id') id: number, @Body() importCoachProtocolDto: ImportCoachProtocolDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.coachService.importProtocol(id, importCoachProtocolDto, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('clients/:id/protocols')\r\n    createClientProtocol(@Param('id') id: number, @Body() createCoachClientProtocolDto: CreateCoachClientProtocolDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.coachService.createClientProtocol(id, createCoachClientProtocolDto, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols')\r\n    getProtocols(@Request() req: any) {\r\n        const userId: number = req.user?.userId;\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.coachService.getProtocols(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/:id')\r\n    getProtocol(@Param('id') id: number, @Request() req: any) {\r\n        const userId: number = req.user?.userId;\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n        return this.coachService.getProtocol(id, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols')\r\n    createProtocol(@Body() createCoachProtocolDto: CreateCoachProtocolDto, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.coachService.createProtocol(createCoachProtocolDto, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Put('protocols/:id')\r\n    updateProtocol(@Param('id') id: number, @Body() updateData: any, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.coachService.updateProtocol(id, updateData, userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Delete('protocols/:id')\r\n    deleteProtocol(@Param('id') id: number, @Request() req: any) {\r\n        const userId: number = req.user.userId;\r\n\r\n        if (!userId) {\r\n            return {\r\n                status: 'error',\r\n                message: 'User not found',\r\n            };\r\n        }\r\n\r\n        return this.coachService.deleteProtocol(id, userId);\r\n    }\r\n}\r\n"], "names": ["Coach<PERSON><PERSON><PERSON><PERSON>", "getStats", "status", "data", "total_clients", "active_protocols", "completion_rate", "getClients", "query", "req", "userId", "user", "message", "coachService", "getClient", "id", "getClientProtocols", "importProtocol", "importCoachProtocolDto", "createClientProtocol", "createCoachClientProtocolDto", "getProtocols", "getProtocol", "createProtocol", "createCoachProtocolDto", "updateProtocol", "updateData", "deleteProtocol", "constructor"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBAT8E;yCACnD;8BACX;8BACA;wCACU;wCACA;8CACM;;;;;;;;;;;;;;;AAGtC,IAAA,AAAMA,kBAAN,MAAMA;IAITC,WAAW;QACP,OAAO;YACHC,QAAQ;YACRC,MAAM;gBACFC,eAAe;gBACfC,kBAAkB;gBAClBC,iBAAiB;YACrB;QACJ;IACJ;IAIAC,WAAW,AAASC,KAA8B,EAAE,AAAWC,GAAQ,EAAE;QACrE,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,YAAY,CAACN,UAAU,CAACC,OAAOE;IAC/C;IAIAI,UAAU,AAAaC,EAAU,EAAE,AAAWN,GAAQ,EAAE;QACpD,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,YAAY,CAACC,SAAS,CAACC,IAAIL;IAC3C;IAIAM,mBAAmB,AAAaD,EAAU,EAAE,AAAWN,GAAQ,EAAE;QAC7D,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,YAAY,CAACG,kBAAkB,CAACD,IAAIL;IACpD;IAIAO,eAAe,AAAaF,EAAU,EAAE,AAAQG,sBAA8C,EAAE,AAAWT,GAAQ,EAAE;QACjH,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,YAAY,CAACI,cAAc,CAACF,IAAIG,wBAAwBR;IACxE;IAIAS,qBAAqB,AAAaJ,EAAU,EAAE,AAAQK,4BAA0D,EAAE,AAAWX,GAAQ,EAAE;QACnI,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QACtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,YAAY,CAACM,oBAAoB,CAACJ,IAAIK,8BAA8BV;IACpF;IAIAW,aAAa,AAAWZ,GAAQ,EAAE;QAC9B,MAAMC,SAAiBD,IAAIE,IAAI,EAAED;QACjC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,YAAY,CAACQ,YAAY,CAACX;IAC1C;IAIAY,YAAY,AAAaP,EAAU,EAAE,AAAWN,GAAQ,EAAE;QACtD,MAAMC,SAAiBD,IAAIE,IAAI,EAAED;QACjC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QACA,OAAO,IAAI,CAACC,YAAY,CAACS,WAAW,CAACP,IAAIL;IAC7C;IAIAa,eAAe,AAAQC,sBAA8C,EAAE,AAAWf,GAAQ,EAAE;QACxF,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,YAAY,CAACU,cAAc,CAACC,wBAAwBd;IACpE;IAIAe,eAAe,AAAaV,EAAU,EAAE,AAAQW,UAAe,EAAE,AAAWjB,GAAQ,EAAE;QAClF,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,YAAY,CAACY,cAAc,CAACV,IAAIW,YAAYhB;IAC5D;IAIAiB,eAAe,AAAaZ,EAAU,EAAE,AAAWN,GAAQ,EAAE;QACzD,MAAMC,SAAiBD,IAAIE,IAAI,CAACD,MAAM;QAEtC,IAAI,CAACA,QAAQ;YACT,OAAO;gBACHR,QAAQ;gBACRU,SAAS;YACb;QACJ;QAEA,OAAO,IAAI,CAACC,YAAY,CAACc,cAAc,CAACZ,IAAIL;IAChD;IA1JAkB,YAAY,AAAiBf,YAA0B,CAAE;aAA5BA,eAAAA;IAA6B;AA2J9D"}