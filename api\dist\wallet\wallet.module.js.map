{"version": 3, "sources": ["../../src/wallet/wallet.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\nimport { WalletController } from './wallet.controller';\nimport { WalletService } from './wallet.service';\n\n@Module({\n  controllers: [WalletController],\n  providers: [WalletService],\n  exports: [WalletService]\n})\nexport class WalletModule {}\n"], "names": ["WalletModule", "controllers", "WalletController", "providers", "WalletService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;kCACU;+BACH;;;;;;;AAOvB,IAAA,AAAMA,eAAN,MAAMA;AAAc;;;QAJzBC,aAAa;YAACC,kCAAgB;SAAC;QAC/BC,WAAW;YAACC,4BAAa;SAAC;QAC1BC,SAAS;YAACD,4BAAa;SAAC"}