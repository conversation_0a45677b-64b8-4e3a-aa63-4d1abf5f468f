import { 
  Controller, 
  Get, 
  Post, 
  Delete, 
  Body, 
  Param, 
  Query, 
  Request, 
  UseGuards 
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { WearablesService } from './wearables.service';

@Controller('wearables')
@UseGuards(JwtAuthGuard)
export class WearablesController {
  constructor(private readonly wearablesService: WearablesService) {}

  @Get('devices')
  async getConnectedDevices(@Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.getConnectedDevices(userId);
  }

  @Get('supported')
  async getSupportedDevices() {
    return this.wearablesService.getSupportedDevices();
  }

  @Post('connect')
  async connectDevice(@Body() deviceData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.connectDevice(userId, deviceData);
  }

  @Delete(':id')
  async disconnectDevice(@Param('id') deviceId: string, @Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.disconnectDevice(userId, Number(deviceId));
  }

  @Get('data')
  async getSyncedData(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.wearablesService.getSyncedData(userId, query);
  }

  @Post('sync')
  async syncData(@Request() req: any, @Body() syncOptions?: any) {
    const userId = req.user.userId;
    return this.wearablesService.syncData(userId, syncOptions);
  }

  @Get('data/steps')
  async getStepsData(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.wearablesService.getStepsData(userId, query);
  }

  @Get('data/heart-rate')
  async getHeartRateData(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.wearablesService.getHeartRateData(userId, query);
  }

  @Get('data/sleep')
  async getSleepData(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.wearablesService.getSleepData(userId, query);
  }

  @Get('data/calories')
  async getCaloriesData(@Request() req: any, @Query() query: any) {
    const userId = req.user.userId;
    return this.wearablesService.getCaloriesData(userId, query);
  }

  @Post('data/manual')
  async addManualData(@Body() data: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.addManualData(userId, data);
  }

  @Get('sync-status')
  async getSyncStatus(@Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.getSyncStatus(userId);
  }

  @Post('oauth/fitbit')
  async connectFitbit(@Body() authData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.connectFitbit(userId, authData);
  }

  @Post('oauth/garmin')
  async connectGarmin(@Body() authData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.connectGarmin(userId, authData);
  }

  @Post('oauth/apple-health')
  async connectAppleHealth(@Body() authData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.connectAppleHealth(userId, authData);
  }

  @Post('oauth/google-fit')
  async connectGoogleFit(@Body() authData: any, @Request() req: any) {
    const userId = req.user.userId;
    return this.wearablesService.connectGoogleFit(userId, authData);
  }
}
