{"version": 3, "sources": ["../../src/analytics/analytics.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { db } from '../database';\nimport * as dayjs from 'dayjs';\n\n// TypeScript interfaces for data structures\nexport interface WeightProgressData {\n  date: string;\n  weight: number;\n  bodyFat?: number;\n}\n\nexport interface BodyFatProgressData {\n  date: string;\n  bodyFat: number;\n}\n\nexport interface StrengthHistoryData {\n  date: string;\n  weight: number;\n}\n\nexport interface StrengthProgressData {\n  exercise: string;\n  current: number;\n  previous: number;\n  change: number;\n  history: StrengthHistoryData[];\n}\n\nexport interface CaloriesTrendData {\n  date: string;\n  calories: number;\n  target: number;\n  adherence: number;\n}\n\nexport interface MacrosTrendData {\n  date: string;\n  protein: number;\n  carbs: number;\n  fat: number;\n  fiber: number;\n}\n\nexport interface WorkoutFrequencyData {\n  date: string;\n  workouts: number;\n  duration: number;\n  calories: number;\n}\n\nexport interface IntensityTrendData {\n  date: string;\n  lowIntensity: number;\n  moderateIntensity: number;\n  highIntensity: number;\n}\n\n@Injectable()\nexport class AnalyticsService {\n  \n  async getDashboardAnalytics(userId: number, query: any) {\n    try {\n      // Calculate period dates\n      const period = query?.period || '7d';\n      const endDate = new Date();\n      const startDate = new Date();\n\n      switch (period) {\n        case '7d':\n          startDate.setDate(endDate.getDate() - 7);\n          break;\n        case '30d':\n          startDate.setDate(endDate.getDate() - 30);\n          break;\n        case '90d':\n          startDate.setDate(endDate.getDate() - 90);\n          break;\n        default:\n          startDate.setDate(endDate.getDate() - 7);\n      }\n\n      // For now, return enhanced mock data with realistic values\n      // TODO: Replace with actual database queries\n      const mockData = {\n        totalWorkouts: this.generateRealisticWorkoutCount(period),\n        totalCalories: this.generateRealisticCalorieCount(period),\n        averageWeight: this.generateRealisticWeight(),\n        progressPercentage: this.generateRealisticProgress(),\n        weightTrend: this.generateWeightTrend(),\n        calorieAdherence: this.generateCalorieAdherence(),\n        workoutConsistency: this.generateWorkoutConsistency(period),\n        strengthGains: this.generateStrengthGains(),\n        bodyFatPercentage: this.generateBodyFatPercentage(),\n        recommendations: this.generateRecommendations()\n      };\n\n      return {\n        status: 'success',\n        data: mockData\n      };\n    } catch (error) {\n      console.error('Error in getDashboardAnalytics:', error);\n      return {\n        status: 'error',\n        message: 'Failed to fetch dashboard analytics',\n        data: null\n      };\n    }\n  }\n\n  async getProgressSummary(userId: number, query: any) {\n    try {\n      const period = query?.period || '30d';\n\n      // Generate realistic progress data\n      const weightProgress = this.generateWeightProgressData(period);\n      const bodyFatProgress = this.generateBodyFatProgressData(period);\n      const strengthProgress = this.generateStrengthProgressData(period);\n\n      return {\n        status: 'success',\n        data: {\n          weightProgress,\n          bodyFatProgress,\n          strengthProgress\n        }\n      };\n    } catch (error) {\n      console.error('Error in getProgressSummary:', error);\n      return {\n        status: 'error',\n        message: 'Failed to fetch progress summary',\n        data: {\n          weightProgress: [],\n          bodyFatProgress: [],\n          strengthProgress: []\n        }\n      };\n    }\n  }\n\n  async getNutritionTrends(userId: number, query: any) {\n    try {\n      const period = query?.period || '7d';\n\n      // Generate realistic nutrition trends\n      const caloriesTrend = this.generateCaloriesTrend(period);\n      const macrosTrend = this.generateMacrosTrend(period);\n      const mealFrequency = this.generateMealFrequency(period);\n\n      return {\n        status: 'success',\n        data: {\n          caloriesTrend,\n          macrosTrend,\n          mealFrequency\n        }\n      };\n    } catch (error) {\n      console.error('Error in getNutritionTrends:', error);\n      return {\n        status: 'error',\n        message: 'Failed to fetch nutrition trends',\n        data: {\n          caloriesTrend: [],\n          macrosTrend: [],\n          mealFrequency: []\n        }\n      };\n    }\n  }\n\n  async getWorkoutTrends(userId: number, query: any) {\n    try {\n      const period = query?.period || '7d';\n\n      // Generate realistic workout trends\n      const workoutFrequency = this.generateWorkoutFrequency(period);\n      const exerciseTypes = this.generateExerciseTypes();\n      const intensityTrends = this.generateIntensityTrends(period);\n\n      return {\n        status: 'success',\n        data: {\n          workoutFrequency,\n          exerciseTypes,\n          intensityTrends\n        }\n      };\n    } catch (error) {\n      console.error('Error in getWorkoutTrends:', error);\n      return {\n        status: 'error',\n        message: 'Failed to fetch workout trends',\n        data: {\n          workoutFrequency: [],\n          exerciseTypes: [],\n          intensityTrends: []\n        }\n      };\n    }\n  }\n\n  async getBodyCompositionAnalysis(userId: number, query: any) {\n    return {\n      status: 'success',\n      data: {\n        bodyFatHistory: [],\n        muscleGainHistory: [],\n        weightHistory: []\n      }\n    };\n  }\n\n  async getHabitsAnalysis(userId: number, query: any) {\n    return {\n      status: 'success',\n      data: {\n        sleepPatterns: [],\n        hydrationLevels: [],\n        consistencyScore: 0\n      }\n    };\n  }\n\n  async getGoalsTracking(userId: number, query: any) {\n    return {\n      status: 'success',\n      data: {\n        activeGoals: [],\n        completedGoals: [],\n        progressTowardsGoals: []\n      }\n    };\n  }\n\n  async getSocialStats(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        friendsCount: 0,\n        challengesCompleted: 0,\n        socialInteractions: 0\n      }\n    };\n  }\n\n  async getGamificationStats(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        totalPoints: 0,\n        level: 1,\n        badges: [],\n        achievements: []\n      }\n    };\n  }\n\n  async getWeeklyReport(userId: number, query: any) {\n    return {\n      status: 'success',\n      data: {\n        weekSummary: {},\n        workoutsSummary: {},\n        nutritionSummary: {}\n      }\n    };\n  }\n\n  async getMonthlyReport(userId: number, query: any) {\n    return {\n      status: 'success',\n      data: {\n        monthSummary: {},\n        progressSummary: {},\n        goalsSummary: {}\n      }\n    };\n  }\n\n  async getAIInsights(userId: number, query: any) {\n    try {\n      const { category, period = 'week' } = query;\n\n      // Enhanced AI insights with category-specific analysis\n      if (category === 'recovery') {\n        return this.getRecoveryAIInsights(userId, period);\n      }\n\n      // General AI insights\n      return {\n        status: 'success',\n        data: {\n          insights: [\n            {\n              type: 'nutrition',\n              title: 'Padrão Alimentar Detectado',\n              description: 'Você tem consumido mais carboidratos nos fins de semana',\n              confidence: 0.85,\n              recommendation: 'Considere manter a consistência durante toda a semana'\n            },\n            {\n              type: 'workout',\n              title: 'Progresso de Força',\n              description: 'Aumento de 12% na força dos membros superiores no último mês',\n              confidence: 0.92,\n              recommendation: 'Continue com o protocolo atual, está funcionando bem'\n            }\n          ],\n          recommendations: [\n            'Mantenha a consistência alimentar durante toda a semana',\n            'Continue com o protocolo de treino atual'\n          ],\n          predictions: []\n        }\n      };\n    } catch (error) {\n      console.error('Error getting AI insights:', error);\n      return {\n        status: 'error',\n        data: {\n          insights: [],\n          recommendations: [],\n          predictions: []\n        }\n      };\n    }\n  }\n\n  async getRecoveryAIInsights(userId: number, period: string) {\n    try {\n      console.log('🔄 Getting recovery AI insights for user:', userId, 'period:', period);\n\n      // Calculate date range\n      const endDate = new Date();\n      const startDate = new Date();\n\n      switch (period) {\n        case 'week':\n          startDate.setDate(endDate.getDate() - 7);\n          break;\n        case 'month':\n          startDate.setMonth(endDate.getMonth() - 1);\n          break;\n        case 'semester':\n          startDate.setMonth(endDate.getMonth() - 6);\n          break;\n        case 'year':\n          startDate.setFullYear(endDate.getFullYear() - 1);\n          break;\n        default:\n          startDate.setDate(endDate.getDate() - 7);\n      }\n\n      // Get wearables data for recovery analysis\n      // Note: This would integrate with actual wearables service\n      const mockWearablesData = {\n        sleep: {\n          average_duration: 7.2,\n          quality_score: 78,\n          deep_sleep_percentage: 22,\n          rem_sleep_percentage: 18,\n          efficiency: 85\n        },\n        hrv: {\n          average: 42,\n          trend: 'improving',\n          recovery_score: 75\n        },\n        stress: {\n          average_level: 35,\n          trend: 'stable'\n        },\n        energy: {\n          average_level: 72,\n          trend: 'improving'\n        },\n        heart_rate: {\n          resting_average: 62,\n          max_recorded: 185,\n          zones: {\n            zone1: 25,\n            zone2: 35,\n            zone3: 25,\n            zone4: 10,\n            zone5: 5\n          }\n        }\n      };\n\n      // AI analysis based on the data\n      const recoveryStatus = this.calculateRecoveryStatus(mockWearablesData);\n      const fatigueLevel = this.calculateFatigueLevel(mockWearablesData);\n      const recommendations = this.generateRecoveryRecommendations(mockWearablesData, recoveryStatus);\n\n      return {\n        status: 'success',\n        data: {\n          recovery_status: recoveryStatus,\n          fatigue_level: fatigueLevel,\n          overall_recovery_score: this.calculateOverallRecoveryScore(mockWearablesData),\n          recommendations,\n          insights: [\n            {\n              type: 'sleep',\n              title: 'Qualidade do Sono',\n              description: `Sua qualidade de sono está em ${mockWearablesData.sleep.quality_score}%`,\n              confidence: 0.88\n            },\n            {\n              type: 'hrv',\n              title: 'Variabilidade Cardíaca',\n              description: `HRV mostra tendência ${mockWearablesData.hrv.trend === 'improving' ? 'de melhoria' : 'estável'}`,\n              confidence: 0.82\n            }\n          ],\n          confidence: 0.85,\n          period,\n          analysis_date: new Date().toISOString()\n        }\n      };\n\n    } catch (error) {\n      console.error('Error getting recovery AI insights:', error);\n      return {\n        status: 'error',\n        data: {\n          recovery_status: 'unknown',\n          fatigue_level: 0,\n          overall_recovery_score: 0,\n          recommendations: [],\n          insights: [],\n          confidence: 0\n        }\n      };\n    }\n  }\n\n  async getWeightPredictions(userId: number, query: any) {\n    return {\n      status: 'success',\n      data: {\n        predictions: [],\n        confidence: 0,\n        factors: []\n      }\n    };\n  }\n\n  async getBenchmarkComparison(userId: number, query: any) {\n    return {\n      status: 'success',\n      data: {\n        userStats: {},\n        benchmarks: {},\n        comparison: {}\n      }\n    };\n  }\n\n  // Helper methods for recovery analysis\n  private calculateRecoveryStatus(data: any): string {\n    const sleepScore = data.sleep.quality_score;\n    const hrvScore = data.hrv.recovery_score;\n    const stressLevel = data.stress.average_level;\n\n    const overallScore = (sleepScore + hrvScore + (100 - stressLevel)) / 3;\n\n    if (overallScore >= 80) return 'recovered';\n    if (overallScore >= 60) return 'recovering';\n    if (overallScore >= 40) return 'fatigued';\n    return 'overworked';\n  }\n\n  private calculateFatigueLevel(data: any): number {\n    const sleepScore = data.sleep.quality_score;\n    const stressLevel = data.stress.average_level;\n    const energyLevel = data.energy.average_level;\n\n    // Higher fatigue = lower sleep quality + higher stress + lower energy\n    const fatigueScore = ((100 - sleepScore) + stressLevel + (100 - energyLevel)) / 3;\n    return Math.round(fatigueScore);\n  }\n\n  private calculateOverallRecoveryScore(data: any): number {\n    const sleepScore = data.sleep.quality_score;\n    const hrvScore = data.hrv.recovery_score;\n    const energyScore = data.energy.average_level;\n    const stressScore = 100 - data.stress.average_level;\n\n    return Math.round((sleepScore + hrvScore + energyScore + stressScore) / 4);\n  }\n\n  private generateRecoveryRecommendations(data: any, status: string): string[] {\n    const recommendations: string[] = [];\n\n    if (data.sleep.quality_score < 70) {\n      recommendations.push('Melhore a higiene do sono: evite telas 1h antes de dormir');\n    }\n\n    if (data.stress.average_level > 60) {\n      recommendations.push('Pratique técnicas de relaxamento como meditação ou respiração profunda');\n    }\n\n    if (data.energy.average_level < 60) {\n      recommendations.push('Considere ajustar a intensidade dos treinos por alguns dias');\n    }\n\n    if (status === 'overworked') {\n      recommendations.push('Tome um dia de descanso completo para recuperação');\n    }\n\n    if (recommendations.length === 0) {\n      recommendations.push('Continue mantendo seus hábitos atuais de recuperação');\n    }\n\n    return recommendations;\n  }\n\n  // Helper methods for generating realistic mock data\n  private generateRealisticWorkoutCount(period: string): number {\n    const baseWorkouts = { '7d': 3, '30d': 12, '90d': 36 };\n    return baseWorkouts[period] || 3;\n  }\n\n  private generateRealisticCalorieCount(period: string): number {\n    const baseCalories = { '7d': 14000, '30d': 60000, '90d': 180000 };\n    return baseCalories[period] || 14000;\n  }\n\n  private generateRealisticWeight(): number {\n    return Math.round((70 + Math.random() * 20) * 10) / 10; // 70-90kg\n  }\n\n  private generateRealisticProgress(): number {\n    return Math.round((60 + Math.random() * 30) * 10) / 10; // 60-90%\n  }\n\n  private generateWeightTrend(): string {\n    const trends = ['decreasing', 'stable', 'increasing'];\n    return trends[Math.floor(Math.random() * trends.length)];\n  }\n\n  private generateCalorieAdherence(): number {\n    return Math.round((1800 + Math.random() * 400) * 10) / 10; // 1800-2200 kcal\n  }\n\n  private generateWorkoutConsistency(period: string): number {\n    return Math.round((70 + Math.random() * 25) * 10) / 10; // 70-95%\n  }\n\n  private generateStrengthGains(): number {\n    return Math.round((5 + Math.random() * 15) * 10) / 10; // 5-20%\n  }\n\n  private generateBodyFatPercentage(): number {\n    return Math.round((15 + Math.random() * 10) * 10) / 10; // 15-25%\n  }\n\n  private generateRecommendations(): string[] {\n    const recommendations = [\n      'Manter consistência nos treinos',\n      'Aumentar ingestão de proteínas',\n      'Focar em exercícios compostos',\n      'Melhorar qualidade do sono',\n      'Aumentar hidratação diária',\n      'Incluir mais vegetais na dieta'\n    ];\n\n    // Return 2-4 random recommendations\n    const count = 2 + Math.floor(Math.random() * 3);\n    return recommendations.sort(() => 0.5 - Math.random()).slice(0, count);\n  }\n\n  private generateWeightProgressData(period: string): WeightProgressData[] {\n    const days = this.getPeriodDays(period);\n    const data: WeightProgressData[] = [];\n    const startWeight = 75 + Math.random() * 10; // 75-85kg starting weight\n\n    for (let i = 0; i < days; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - (days - i));\n\n      // Simulate gradual weight loss/gain\n      const progress = i / days;\n      const weightChange = (Math.random() - 0.5) * 2; // -1 to +1 kg variation\n      const weight = startWeight - (progress * 2) + weightChange; // Gradual 2kg loss over period\n\n      data.push({\n        date: date.toISOString().split('T')[0],\n        weight: Math.round(weight * 10) / 10,\n        bodyFat: Math.round((18 + Math.random() * 4) * 10) / 10 // 18-22%\n      });\n    }\n\n    return data;\n  }\n\n  private generateBodyFatProgressData(period: string): BodyFatProgressData[] {\n    const days = this.getPeriodDays(period);\n    const data: BodyFatProgressData[] = [];\n    const startBodyFat = 20 + Math.random() * 5; // 20-25% starting\n\n    for (let i = 0; i < days; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - (days - i));\n\n      const progress = i / days;\n      const bodyFatChange = (Math.random() - 0.5) * 0.5; // -0.25 to +0.25% variation\n      const bodyFat = startBodyFat - (progress * 1.5) + bodyFatChange; // Gradual 1.5% loss\n\n      data.push({\n        date: date.toISOString().split('T')[0],\n        bodyFat: Math.round(bodyFat * 10) / 10\n      });\n    }\n\n    return data;\n  }\n\n  private generateStrengthProgressData(period: string): StrengthProgressData[] {\n    const exercises = ['Supino', 'Agachamento', 'Levantamento Terra', 'Desenvolvimento'];\n    const data: StrengthProgressData[] = [];\n\n    exercises.forEach(exercise => {\n      const baseWeight = 60 + Math.random() * 40; // 60-100kg base\n      const history: StrengthHistoryData[] = [];\n      const days = this.getPeriodDays(period);\n\n      for (let i = 0; i < Math.min(days / 7, 10); i++) { // Weekly data points\n        const date = new Date();\n        date.setDate(date.getDate() - (days - (i * 7)));\n\n        const progress = i / 10;\n        const weight = baseWeight + (progress * 10) + (Math.random() - 0.5) * 2;\n\n        history.push({\n          date: date.toISOString().split('T')[0],\n          weight: Math.round(weight * 2) / 2 // Round to nearest 0.5kg\n        });\n      }\n\n      data.push({\n        exercise,\n        current: history[history.length - 1]?.weight || baseWeight,\n        previous: history[0]?.weight || baseWeight,\n        change: history.length > 1 ?\n          Math.round((history[history.length - 1].weight - history[0].weight) * 10) / 10 : 0,\n        history\n      });\n    });\n\n    return data;\n  }\n\n  private getPeriodDays(period: string): number {\n    switch (period) {\n      case '7d': return 7;\n      case '30d': return 30;\n      case '90d': return 90;\n      default: return 30;\n    }\n  }\n\n  private generateCaloriesTrend(period: string): CaloriesTrendData[] {\n    const days = this.getPeriodDays(period);\n    const data: CaloriesTrendData[] = [];\n    const targetCalories = 2000;\n\n    for (let i = 0; i < days; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - (days - i));\n\n      const variation = (Math.random() - 0.5) * 400; // ±200 calories variation\n      const calories = targetCalories + variation;\n\n      data.push({\n        date: date.toISOString().split('T')[0],\n        calories: Math.round(calories),\n        target: targetCalories,\n        adherence: Math.round((calories / targetCalories) * 100)\n      });\n    }\n\n    return data;\n  }\n\n  private generateMacrosTrend(period: string): MacrosTrendData[] {\n    const days = this.getPeriodDays(period);\n    const data: MacrosTrendData[] = [];\n\n    for (let i = 0; i < days; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - (days - i));\n\n      data.push({\n        date: date.toISOString().split('T')[0],\n        protein: Math.round(120 + (Math.random() - 0.5) * 40), // 100-140g\n        carbs: Math.round(200 + (Math.random() - 0.5) * 60), // 170-230g\n        fat: Math.round(70 + (Math.random() - 0.5) * 20), // 60-80g\n        fiber: Math.round(25 + (Math.random() - 0.5) * 10) // 20-30g\n      });\n    }\n\n    return data;\n  }\n\n  private generateMealFrequency(period: string): any[] {\n    const mealTypes = ['Café da Manhã', 'Almoço', 'Lanche', 'Jantar'];\n    const days = this.getPeriodDays(period);\n\n    return mealTypes.map(mealType => ({\n      mealType,\n      frequency: Math.round((0.7 + Math.random() * 0.3) * days), // 70-100% frequency\n      averageCalories: Math.round(300 + Math.random() * 400) // 300-700 calories\n    }));\n  }\n\n  private generateWorkoutFrequency(period: string): WorkoutFrequencyData[] {\n    const days = this.getPeriodDays(period);\n    const data: WorkoutFrequencyData[] = [];\n\n    for (let i = 0; i < days; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - (days - i));\n\n      // Simulate workout frequency (higher on weekdays)\n      const dayOfWeek = date.getDay();\n      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;\n      const workoutProbability = isWeekend ? 0.3 : 0.7;\n      const hasWorkout = Math.random() < workoutProbability;\n\n      data.push({\n        date: date.toISOString().split('T')[0],\n        workouts: hasWorkout ? 1 : 0,\n        duration: hasWorkout ? Math.round(45 + Math.random() * 30) : 0, // 45-75 minutes\n        calories: hasWorkout ? Math.round(300 + Math.random() * 200) : 0 // 300-500 calories\n      });\n    }\n\n    return data;\n  }\n\n  private generateExerciseTypes(): any[] {\n    const exercises = [\n      { type: 'Cardio', percentage: 30, color: '#FF6B6B' },\n      { type: 'Força', percentage: 40, color: '#4ECDC4' },\n      { type: 'Flexibilidade', percentage: 15, color: '#45B7D1' },\n      { type: 'Funcional', percentage: 15, color: '#96CEB4' }\n    ];\n\n    return exercises;\n  }\n\n  private generateIntensityTrends(period: string): IntensityTrendData[] {\n    const days = this.getPeriodDays(period);\n    const data: IntensityTrendData[] = [];\n\n    for (let i = 0; i < days; i++) {\n      const date = new Date();\n      date.setDate(date.getDate() - (days - i));\n\n      data.push({\n        date: date.toISOString().split('T')[0],\n        lowIntensity: Math.round(10 + Math.random() * 20), // 10-30 minutes\n        moderateIntensity: Math.round(20 + Math.random() * 25), // 20-45 minutes\n        highIntensity: Math.round(5 + Math.random() * 15) // 5-20 minutes\n      });\n    }\n\n    return data;\n  }\n}\n"], "names": ["AnalyticsService", "getDashboardAnalytics", "userId", "query", "period", "endDate", "Date", "startDate", "setDate", "getDate", "mockData", "totalWorkouts", "generateRealisticWorkoutCount", "totalCalories", "generateRealisticCalorieCount", "averageWeight", "generateRealisticWeight", "progressPercentage", "generateRealisticProgress", "weightTrend", "generateWeightTrend", "calorieAdherence", "generateCalorieAdherence", "workoutConsistency", "generateWorkoutConsistency", "strengthGains", "generateStrengthGains", "bodyFatPercentage", "generateBodyFatPercentage", "recommendations", "generateRecommendations", "status", "data", "error", "console", "message", "getProgressSummary", "weightProgress", "generateWeightProgressData", "bodyFatProgress", "generateBodyFatProgressData", "strengthProgress", "generateStrengthProgressData", "getNutritionTrends", "caloriesTrend", "generateCaloriesTrend", "macrosTrend", "generateMacrosTrend", "mealFrequency", "generateMealFrequency", "getWorkoutTrends", "workoutFrequency", "generateWorkoutFrequency", "exerciseTypes", "generateExerciseTypes", "intensityTrends", "generateIntensityTrends", "getBodyCompositionAnalysis", "bodyFatHistory", "muscleGainHistory", "weightHistory", "getHabitsAnalysis", "sleepPatterns", "hydrationLevels", "consistencyScore", "getGoalsTracking", "activeGoals", "completedGoals", "progressTowardsGoals", "getSocialStats", "friendsCount", "challengesCompleted", "socialInteractions", "getGamificationStats", "totalPoints", "level", "badges", "achievements", "getWeeklyReport", "weekSummary", "workoutsSummary", "nutritionSummary", "getMonthlyReport", "month<PERSON><PERSON><PERSON>y", "progressSummary", "goalsSummary", "getAIInsights", "category", "getRecoveryAIInsights", "insights", "type", "title", "description", "confidence", "recommendation", "predictions", "log", "setMonth", "getMonth", "setFullYear", "getFullYear", "mockWearablesData", "sleep", "average_duration", "quality_score", "deep_sleep_percentage", "rem_sleep_percentage", "efficiency", "hrv", "average", "trend", "recovery_score", "stress", "average_level", "energy", "heart_rate", "resting_average", "max_recorded", "zones", "zone1", "zone2", "zone3", "zone4", "zone5", "recoveryStatus", "calculateRecoveryStatus", "fatigueLevel", "calculateFatigueLevel", "generateRecoveryRecommendations", "recovery_status", "fatigue_level", "overall_recovery_score", "calculateOverallRecoveryScore", "analysis_date", "toISOString", "getWeightPredictions", "factors", "getBenchmarkComparison", "userStats", "benchmarks", "comparison", "sleepScore", "hrvScore", "stressLevel", "overallScore", "energyLevel", "fatigueScore", "Math", "round", "energyScore", "stressScore", "push", "length", "baseWorkouts", "baseCalories", "random", "trends", "floor", "count", "sort", "slice", "days", "getPeriodDays", "startWeight", "i", "date", "progress", "weightChange", "weight", "split", "bodyFat", "startBodyFat", "bodyFatChange", "exercises", "for<PERSON>ach", "exercise", "baseWeight", "history", "min", "current", "previous", "change", "targetCalories", "variation", "calories", "target", "adherence", "protein", "carbs", "fat", "fiber", "mealTypes", "map", "mealType", "frequency", "averageCalories", "dayOfWeek", "getDay", "isWeekend", "workoutProbability", "hasWorkout", "workouts", "duration", "percentage", "color", "lowIntensity", "moderateIntensity", "highIntensity"], "mappings": ";;;;+BA2DaA;;;eAAAA;;;wBA3Dc;;;;;;;AA2DpB,IAAA,AAAMA,mBAAN,MAAMA;IAEX,MAAMC,sBAAsBC,MAAc,EAAEC,KAAU,EAAE;QACtD,IAAI;YACF,yBAAyB;YACzB,MAAMC,SAASD,OAAOC,UAAU;YAChC,MAAMC,UAAU,IAAIC;YACpB,MAAMC,YAAY,IAAID;YAEtB,OAAQF;gBACN,KAAK;oBACHG,UAAUC,OAAO,CAACH,QAAQI,OAAO,KAAK;oBACtC;gBACF,KAAK;oBACHF,UAAUC,OAAO,CAACH,QAAQI,OAAO,KAAK;oBACtC;gBACF,KAAK;oBACHF,UAAUC,OAAO,CAACH,QAAQI,OAAO,KAAK;oBACtC;gBACF;oBACEF,UAAUC,OAAO,CAACH,QAAQI,OAAO,KAAK;YAC1C;YAEA,2DAA2D;YAC3D,6CAA6C;YAC7C,MAAMC,WAAW;gBACfC,eAAe,IAAI,CAACC,6BAA6B,CAACR;gBAClDS,eAAe,IAAI,CAACC,6BAA6B,CAACV;gBAClDW,eAAe,IAAI,CAACC,uBAAuB;gBAC3CC,oBAAoB,IAAI,CAACC,yBAAyB;gBAClDC,aAAa,IAAI,CAACC,mBAAmB;gBACrCC,kBAAkB,IAAI,CAACC,wBAAwB;gBAC/CC,oBAAoB,IAAI,CAACC,0BAA0B,CAACpB;gBACpDqB,eAAe,IAAI,CAACC,qBAAqB;gBACzCC,mBAAmB,IAAI,CAACC,yBAAyB;gBACjDC,iBAAiB,IAAI,CAACC,uBAAuB;YAC/C;YAEA,OAAO;gBACLC,QAAQ;gBACRC,MAAMtB;YACR;QACF,EAAE,OAAOuB,OAAO;YACdC,QAAQD,KAAK,CAAC,mCAAmCA;YACjD,OAAO;gBACLF,QAAQ;gBACRI,SAAS;gBACTH,MAAM;YACR;QACF;IACF;IAEA,MAAMI,mBAAmBlC,MAAc,EAAEC,KAAU,EAAE;QACnD,IAAI;YACF,MAAMC,SAASD,OAAOC,UAAU;YAEhC,mCAAmC;YACnC,MAAMiC,iBAAiB,IAAI,CAACC,0BAA0B,CAAClC;YACvD,MAAMmC,kBAAkB,IAAI,CAACC,2BAA2B,CAACpC;YACzD,MAAMqC,mBAAmB,IAAI,CAACC,4BAA4B,CAACtC;YAE3D,OAAO;gBACL2B,QAAQ;gBACRC,MAAM;oBACJK;oBACAE;oBACAE;gBACF;YACF;QACF,EAAE,OAAOR,OAAO;YACdC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;gBACLF,QAAQ;gBACRI,SAAS;gBACTH,MAAM;oBACJK,gBAAgB,EAAE;oBAClBE,iBAAiB,EAAE;oBACnBE,kBAAkB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,MAAME,mBAAmBzC,MAAc,EAAEC,KAAU,EAAE;QACnD,IAAI;YACF,MAAMC,SAASD,OAAOC,UAAU;YAEhC,sCAAsC;YACtC,MAAMwC,gBAAgB,IAAI,CAACC,qBAAqB,CAACzC;YACjD,MAAM0C,cAAc,IAAI,CAACC,mBAAmB,CAAC3C;YAC7C,MAAM4C,gBAAgB,IAAI,CAACC,qBAAqB,CAAC7C;YAEjD,OAAO;gBACL2B,QAAQ;gBACRC,MAAM;oBACJY;oBACAE;oBACAE;gBACF;YACF;QACF,EAAE,OAAOf,OAAO;YACdC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;gBACLF,QAAQ;gBACRI,SAAS;gBACTH,MAAM;oBACJY,eAAe,EAAE;oBACjBE,aAAa,EAAE;oBACfE,eAAe,EAAE;gBACnB;YACF;QACF;IACF;IAEA,MAAME,iBAAiBhD,MAAc,EAAEC,KAAU,EAAE;QACjD,IAAI;YACF,MAAMC,SAASD,OAAOC,UAAU;YAEhC,oCAAoC;YACpC,MAAM+C,mBAAmB,IAAI,CAACC,wBAAwB,CAAChD;YACvD,MAAMiD,gBAAgB,IAAI,CAACC,qBAAqB;YAChD,MAAMC,kBAAkB,IAAI,CAACC,uBAAuB,CAACpD;YAErD,OAAO;gBACL2B,QAAQ;gBACRC,MAAM;oBACJmB;oBACAE;oBACAE;gBACF;YACF;QACF,EAAE,OAAOtB,OAAO;YACdC,QAAQD,KAAK,CAAC,8BAA8BA;YAC5C,OAAO;gBACLF,QAAQ;gBACRI,SAAS;gBACTH,MAAM;oBACJmB,kBAAkB,EAAE;oBACpBE,eAAe,EAAE;oBACjBE,iBAAiB,EAAE;gBACrB;YACF;QACF;IACF;IAEA,MAAME,2BAA2BvD,MAAc,EAAEC,KAAU,EAAE;QAC3D,OAAO;YACL4B,QAAQ;YACRC,MAAM;gBACJ0B,gBAAgB,EAAE;gBAClBC,mBAAmB,EAAE;gBACrBC,eAAe,EAAE;YACnB;QACF;IACF;IAEA,MAAMC,kBAAkB3D,MAAc,EAAEC,KAAU,EAAE;QAClD,OAAO;YACL4B,QAAQ;YACRC,MAAM;gBACJ8B,eAAe,EAAE;gBACjBC,iBAAiB,EAAE;gBACnBC,kBAAkB;YACpB;QACF;IACF;IAEA,MAAMC,iBAAiB/D,MAAc,EAAEC,KAAU,EAAE;QACjD,OAAO;YACL4B,QAAQ;YACRC,MAAM;gBACJkC,aAAa,EAAE;gBACfC,gBAAgB,EAAE;gBAClBC,sBAAsB,EAAE;YAC1B;QACF;IACF;IAEA,MAAMC,eAAenE,MAAc,EAAE;QACnC,OAAO;YACL6B,QAAQ;YACRC,MAAM;gBACJsC,cAAc;gBACdC,qBAAqB;gBACrBC,oBAAoB;YACtB;QACF;IACF;IAEA,MAAMC,qBAAqBvE,MAAc,EAAE;QACzC,OAAO;YACL6B,QAAQ;YACRC,MAAM;gBACJ0C,aAAa;gBACbC,OAAO;gBACPC,QAAQ,EAAE;gBACVC,cAAc,EAAE;YAClB;QACF;IACF;IAEA,MAAMC,gBAAgB5E,MAAc,EAAEC,KAAU,EAAE;QAChD,OAAO;YACL4B,QAAQ;YACRC,MAAM;gBACJ+C,aAAa,CAAC;gBACdC,iBAAiB,CAAC;gBAClBC,kBAAkB,CAAC;YACrB;QACF;IACF;IAEA,MAAMC,iBAAiBhF,MAAc,EAAEC,KAAU,EAAE;QACjD,OAAO;YACL4B,QAAQ;YACRC,MAAM;gBACJmD,cAAc,CAAC;gBACfC,iBAAiB,CAAC;gBAClBC,cAAc,CAAC;YACjB;QACF;IACF;IAEA,MAAMC,cAAcpF,MAAc,EAAEC,KAAU,EAAE;QAC9C,IAAI;YACF,MAAM,EAAEoF,QAAQ,EAAEnF,SAAS,MAAM,EAAE,GAAGD;YAEtC,uDAAuD;YACvD,IAAIoF,aAAa,YAAY;gBAC3B,OAAO,IAAI,CAACC,qBAAqB,CAACtF,QAAQE;YAC5C;YAEA,sBAAsB;YACtB,OAAO;gBACL2B,QAAQ;gBACRC,MAAM;oBACJyD,UAAU;wBACR;4BACEC,MAAM;4BACNC,OAAO;4BACPC,aAAa;4BACbC,YAAY;4BACZC,gBAAgB;wBAClB;wBACA;4BACEJ,MAAM;4BACNC,OAAO;4BACPC,aAAa;4BACbC,YAAY;4BACZC,gBAAgB;wBAClB;qBACD;oBACDjE,iBAAiB;wBACf;wBACA;qBACD;oBACDkE,aAAa,EAAE;gBACjB;YACF;QACF,EAAE,OAAO9D,OAAO;YACdC,QAAQD,KAAK,CAAC,8BAA8BA;YAC5C,OAAO;gBACLF,QAAQ;gBACRC,MAAM;oBACJyD,UAAU,EAAE;oBACZ5D,iBAAiB,EAAE;oBACnBkE,aAAa,EAAE;gBACjB;YACF;QACF;IACF;IAEA,MAAMP,sBAAsBtF,MAAc,EAAEE,MAAc,EAAE;QAC1D,IAAI;YACF8B,QAAQ8D,GAAG,CAAC,6CAA6C9F,QAAQ,WAAWE;YAE5E,uBAAuB;YACvB,MAAMC,UAAU,IAAIC;YACpB,MAAMC,YAAY,IAAID;YAEtB,OAAQF;gBACN,KAAK;oBACHG,UAAUC,OAAO,CAACH,QAAQI,OAAO,KAAK;oBACtC;gBACF,KAAK;oBACHF,UAAU0F,QAAQ,CAAC5F,QAAQ6F,QAAQ,KAAK;oBACxC;gBACF,KAAK;oBACH3F,UAAU0F,QAAQ,CAAC5F,QAAQ6F,QAAQ,KAAK;oBACxC;gBACF,KAAK;oBACH3F,UAAU4F,WAAW,CAAC9F,QAAQ+F,WAAW,KAAK;oBAC9C;gBACF;oBACE7F,UAAUC,OAAO,CAACH,QAAQI,OAAO,KAAK;YAC1C;YAEA,2CAA2C;YAC3C,2DAA2D;YAC3D,MAAM4F,oBAAoB;gBACxBC,OAAO;oBACLC,kBAAkB;oBAClBC,eAAe;oBACfC,uBAAuB;oBACvBC,sBAAsB;oBACtBC,YAAY;gBACd;gBACAC,KAAK;oBACHC,SAAS;oBACTC,OAAO;oBACPC,gBAAgB;gBAClB;gBACAC,QAAQ;oBACNC,eAAe;oBACfH,OAAO;gBACT;gBACAI,QAAQ;oBACND,eAAe;oBACfH,OAAO;gBACT;gBACAK,YAAY;oBACVC,iBAAiB;oBACjBC,cAAc;oBACdC,OAAO;wBACLC,OAAO;wBACPC,OAAO;wBACPC,OAAO;wBACPC,OAAO;wBACPC,OAAO;oBACT;gBACF;YACF;YAEA,gCAAgC;YAChC,MAAMC,iBAAiB,IAAI,CAACC,uBAAuB,CAACxB;YACpD,MAAMyB,eAAe,IAAI,CAACC,qBAAqB,CAAC1B;YAChD,MAAMxE,kBAAkB,IAAI,CAACmG,+BAA+B,CAAC3B,mBAAmBuB;YAEhF,OAAO;gBACL7F,QAAQ;gBACRC,MAAM;oBACJiG,iBAAiBL;oBACjBM,eAAeJ;oBACfK,wBAAwB,IAAI,CAACC,6BAA6B,CAAC/B;oBAC3DxE;oBACA4D,UAAU;wBACR;4BACEC,MAAM;4BACNC,OAAO;4BACPC,aAAa,CAAC,8BAA8B,EAAES,kBAAkBC,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC;4BACtFX,YAAY;wBACd;wBACA;4BACEH,MAAM;4BACNC,OAAO;4BACPC,aAAa,CAAC,qBAAqB,EAAES,kBAAkBO,GAAG,CAACE,KAAK,KAAK,cAAc,gBAAgB,WAAW;4BAC9GjB,YAAY;wBACd;qBACD;oBACDA,YAAY;oBACZzF;oBACAiI,eAAe,IAAI/H,OAAOgI,WAAW;gBACvC;YACF;QAEF,EAAE,OAAOrG,OAAO;YACdC,QAAQD,KAAK,CAAC,uCAAuCA;YACrD,OAAO;gBACLF,QAAQ;gBACRC,MAAM;oBACJiG,iBAAiB;oBACjBC,eAAe;oBACfC,wBAAwB;oBACxBtG,iBAAiB,EAAE;oBACnB4D,UAAU,EAAE;oBACZI,YAAY;gBACd;YACF;QACF;IACF;IAEA,MAAM0C,qBAAqBrI,MAAc,EAAEC,KAAU,EAAE;QACrD,OAAO;YACL4B,QAAQ;YACRC,MAAM;gBACJ+D,aAAa,EAAE;gBACfF,YAAY;gBACZ2C,SAAS,EAAE;YACb;QACF;IACF;IAEA,MAAMC,uBAAuBvI,MAAc,EAAEC,KAAU,EAAE;QACvD,OAAO;YACL4B,QAAQ;YACRC,MAAM;gBACJ0G,WAAW,CAAC;gBACZC,YAAY,CAAC;gBACbC,YAAY,CAAC;YACf;QACF;IACF;IAEA,uCAAuC;IAC/Bf,wBAAwB7F,IAAS,EAAU;QACjD,MAAM6G,aAAa7G,KAAKsE,KAAK,CAACE,aAAa;QAC3C,MAAMsC,WAAW9G,KAAK4E,GAAG,CAACG,cAAc;QACxC,MAAMgC,cAAc/G,KAAKgF,MAAM,CAACC,aAAa;QAE7C,MAAM+B,eAAe,AAACH,CAAAA,aAAaC,WAAY,CAAA,MAAMC,WAAU,CAAC,IAAK;QAErE,IAAIC,gBAAgB,IAAI,OAAO;QAC/B,IAAIA,gBAAgB,IAAI,OAAO;QAC/B,IAAIA,gBAAgB,IAAI,OAAO;QAC/B,OAAO;IACT;IAEQjB,sBAAsB/F,IAAS,EAAU;QAC/C,MAAM6G,aAAa7G,KAAKsE,KAAK,CAACE,aAAa;QAC3C,MAAMuC,cAAc/G,KAAKgF,MAAM,CAACC,aAAa;QAC7C,MAAMgC,cAAcjH,KAAKkF,MAAM,CAACD,aAAa;QAE7C,sEAAsE;QACtE,MAAMiC,eAAe,AAAC,CAAA,AAAC,MAAML,aAAcE,cAAe,CAAA,MAAME,WAAU,CAAC,IAAK;QAChF,OAAOE,KAAKC,KAAK,CAACF;IACpB;IAEQd,8BAA8BpG,IAAS,EAAU;QACvD,MAAM6G,aAAa7G,KAAKsE,KAAK,CAACE,aAAa;QAC3C,MAAMsC,WAAW9G,KAAK4E,GAAG,CAACG,cAAc;QACxC,MAAMsC,cAAcrH,KAAKkF,MAAM,CAACD,aAAa;QAC7C,MAAMqC,cAAc,MAAMtH,KAAKgF,MAAM,CAACC,aAAa;QAEnD,OAAOkC,KAAKC,KAAK,CAAC,AAACP,CAAAA,aAAaC,WAAWO,cAAcC,WAAU,IAAK;IAC1E;IAEQtB,gCAAgChG,IAAS,EAAED,MAAc,EAAY;QAC3E,MAAMF,kBAA4B,EAAE;QAEpC,IAAIG,KAAKsE,KAAK,CAACE,aAAa,GAAG,IAAI;YACjC3E,gBAAgB0H,IAAI,CAAC;QACvB;QAEA,IAAIvH,KAAKgF,MAAM,CAACC,aAAa,GAAG,IAAI;YAClCpF,gBAAgB0H,IAAI,CAAC;QACvB;QAEA,IAAIvH,KAAKkF,MAAM,CAACD,aAAa,GAAG,IAAI;YAClCpF,gBAAgB0H,IAAI,CAAC;QACvB;QAEA,IAAIxH,WAAW,cAAc;YAC3BF,gBAAgB0H,IAAI,CAAC;QACvB;QAEA,IAAI1H,gBAAgB2H,MAAM,KAAK,GAAG;YAChC3H,gBAAgB0H,IAAI,CAAC;QACvB;QAEA,OAAO1H;IACT;IAEA,oDAAoD;IAC5CjB,8BAA8BR,MAAc,EAAU;QAC5D,MAAMqJ,eAAe;YAAE,MAAM;YAAG,OAAO;YAAI,OAAO;QAAG;QACrD,OAAOA,YAAY,CAACrJ,OAAO,IAAI;IACjC;IAEQU,8BAA8BV,MAAc,EAAU;QAC5D,MAAMsJ,eAAe;YAAE,MAAM;YAAO,OAAO;YAAO,OAAO;QAAO;QAChE,OAAOA,YAAY,CAACtJ,OAAO,IAAI;IACjC;IAEQY,0BAAkC;QACxC,OAAOmI,KAAKC,KAAK,CAAC,AAAC,CAAA,KAAKD,KAAKQ,MAAM,KAAK,EAAC,IAAK,MAAM,IAAI,UAAU;IACpE;IAEQzI,4BAAoC;QAC1C,OAAOiI,KAAKC,KAAK,CAAC,AAAC,CAAA,KAAKD,KAAKQ,MAAM,KAAK,EAAC,IAAK,MAAM,IAAI,SAAS;IACnE;IAEQvI,sBAA8B;QACpC,MAAMwI,SAAS;YAAC;YAAc;YAAU;SAAa;QACrD,OAAOA,MAAM,CAACT,KAAKU,KAAK,CAACV,KAAKQ,MAAM,KAAKC,OAAOJ,MAAM,EAAE;IAC1D;IAEQlI,2BAAmC;QACzC,OAAO6H,KAAKC,KAAK,CAAC,AAAC,CAAA,OAAOD,KAAKQ,MAAM,KAAK,GAAE,IAAK,MAAM,IAAI,iBAAiB;IAC9E;IAEQnI,2BAA2BpB,MAAc,EAAU;QACzD,OAAO+I,KAAKC,KAAK,CAAC,AAAC,CAAA,KAAKD,KAAKQ,MAAM,KAAK,EAAC,IAAK,MAAM,IAAI,SAAS;IACnE;IAEQjI,wBAAgC;QACtC,OAAOyH,KAAKC,KAAK,CAAC,AAAC,CAAA,IAAID,KAAKQ,MAAM,KAAK,EAAC,IAAK,MAAM,IAAI,QAAQ;IACjE;IAEQ/H,4BAAoC;QAC1C,OAAOuH,KAAKC,KAAK,CAAC,AAAC,CAAA,KAAKD,KAAKQ,MAAM,KAAK,EAAC,IAAK,MAAM,IAAI,SAAS;IACnE;IAEQ7H,0BAAoC;QAC1C,MAAMD,kBAAkB;YACtB;YACA;YACA;YACA;YACA;YACA;SACD;QAED,oCAAoC;QACpC,MAAMiI,QAAQ,IAAIX,KAAKU,KAAK,CAACV,KAAKQ,MAAM,KAAK;QAC7C,OAAO9H,gBAAgBkI,IAAI,CAAC,IAAM,MAAMZ,KAAKQ,MAAM,IAAIK,KAAK,CAAC,GAAGF;IAClE;IAEQxH,2BAA2BlC,MAAc,EAAwB;QACvE,MAAM6J,OAAO,IAAI,CAACC,aAAa,CAAC9J;QAChC,MAAM4B,OAA6B,EAAE;QACrC,MAAMmI,cAAc,KAAKhB,KAAKQ,MAAM,KAAK,IAAI,0BAA0B;QAEvE,IAAK,IAAIS,IAAI,GAAGA,IAAIH,MAAMG,IAAK;YAC7B,MAAMC,OAAO,IAAI/J;YACjB+J,KAAK7J,OAAO,CAAC6J,KAAK5J,OAAO,KAAMwJ,CAAAA,OAAOG,CAAAA;YAEtC,oCAAoC;YACpC,MAAME,WAAWF,IAAIH;YACrB,MAAMM,eAAe,AAACpB,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK,GAAG,wBAAwB;YACxE,MAAMa,SAASL,cAAeG,WAAW,IAAKC,cAAc,+BAA+B;YAE3FvI,KAAKuH,IAAI,CAAC;gBACRc,MAAMA,KAAK/B,WAAW,GAAGmC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtCD,QAAQrB,KAAKC,KAAK,CAACoB,SAAS,MAAM;gBAClCE,SAASvB,KAAKC,KAAK,CAAC,AAAC,CAAA,KAAKD,KAAKQ,MAAM,KAAK,CAAA,IAAK,MAAM,GAAG,SAAS;YACnE;QACF;QAEA,OAAO3H;IACT;IAEQQ,4BAA4BpC,MAAc,EAAyB;QACzE,MAAM6J,OAAO,IAAI,CAACC,aAAa,CAAC9J;QAChC,MAAM4B,OAA8B,EAAE;QACtC,MAAM2I,eAAe,KAAKxB,KAAKQ,MAAM,KAAK,GAAG,kBAAkB;QAE/D,IAAK,IAAIS,IAAI,GAAGA,IAAIH,MAAMG,IAAK;YAC7B,MAAMC,OAAO,IAAI/J;YACjB+J,KAAK7J,OAAO,CAAC6J,KAAK5J,OAAO,KAAMwJ,CAAAA,OAAOG,CAAAA;YAEtC,MAAME,WAAWF,IAAIH;YACrB,MAAMW,gBAAgB,AAACzB,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK,KAAK,4BAA4B;YAC/E,MAAMe,UAAUC,eAAgBL,WAAW,MAAOM,eAAe,oBAAoB;YAErF5I,KAAKuH,IAAI,CAAC;gBACRc,MAAMA,KAAK/B,WAAW,GAAGmC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtCC,SAASvB,KAAKC,KAAK,CAACsB,UAAU,MAAM;YACtC;QACF;QAEA,OAAO1I;IACT;IAEQU,6BAA6BtC,MAAc,EAA0B;QAC3E,MAAMyK,YAAY;YAAC;YAAU;YAAe;YAAsB;SAAkB;QACpF,MAAM7I,OAA+B,EAAE;QAEvC6I,UAAUC,OAAO,CAACC,CAAAA;YAChB,MAAMC,aAAa,KAAK7B,KAAKQ,MAAM,KAAK,IAAI,gBAAgB;YAC5D,MAAMsB,UAAiC,EAAE;YACzC,MAAMhB,OAAO,IAAI,CAACC,aAAa,CAAC9J;YAEhC,IAAK,IAAIgK,IAAI,GAAGA,IAAIjB,KAAK+B,GAAG,CAACjB,OAAO,GAAG,KAAKG,IAAK;gBAC/C,MAAMC,OAAO,IAAI/J;gBACjB+J,KAAK7J,OAAO,CAAC6J,KAAK5J,OAAO,KAAMwJ,CAAAA,OAAQG,IAAI,CAAC;gBAE5C,MAAME,WAAWF,IAAI;gBACrB,MAAMI,SAASQ,aAAcV,WAAW,KAAM,AAACnB,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK;gBAEtEsB,QAAQ1B,IAAI,CAAC;oBACXc,MAAMA,KAAK/B,WAAW,GAAGmC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACtCD,QAAQrB,KAAKC,KAAK,CAACoB,SAAS,KAAK,EAAE,yBAAyB;gBAC9D;YACF;YAEAxI,KAAKuH,IAAI,CAAC;gBACRwB;gBACAI,SAASF,OAAO,CAACA,QAAQzB,MAAM,GAAG,EAAE,EAAEgB,UAAUQ;gBAChDI,UAAUH,OAAO,CAAC,EAAE,EAAET,UAAUQ;gBAChCK,QAAQJ,QAAQzB,MAAM,GAAG,IACvBL,KAAKC,KAAK,CAAC,AAAC6B,CAAAA,OAAO,CAACA,QAAQzB,MAAM,GAAG,EAAE,CAACgB,MAAM,GAAGS,OAAO,CAAC,EAAE,CAACT,MAAM,AAAD,IAAK,MAAM,KAAK;gBACnFS;YACF;QACF;QAEA,OAAOjJ;IACT;IAEQkI,cAAc9J,MAAc,EAAU;QAC5C,OAAQA;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEQyC,sBAAsBzC,MAAc,EAAuB;QACjE,MAAM6J,OAAO,IAAI,CAACC,aAAa,CAAC9J;QAChC,MAAM4B,OAA4B,EAAE;QACpC,MAAMsJ,iBAAiB;QAEvB,IAAK,IAAIlB,IAAI,GAAGA,IAAIH,MAAMG,IAAK;YAC7B,MAAMC,OAAO,IAAI/J;YACjB+J,KAAK7J,OAAO,CAAC6J,KAAK5J,OAAO,KAAMwJ,CAAAA,OAAOG,CAAAA;YAEtC,MAAMmB,YAAY,AAACpC,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK,KAAK,0BAA0B;YACzE,MAAM6B,WAAWF,iBAAiBC;YAElCvJ,KAAKuH,IAAI,CAAC;gBACRc,MAAMA,KAAK/B,WAAW,GAAGmC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtCe,UAAUrC,KAAKC,KAAK,CAACoC;gBACrBC,QAAQH;gBACRI,WAAWvC,KAAKC,KAAK,CAAC,AAACoC,WAAWF,iBAAkB;YACtD;QACF;QAEA,OAAOtJ;IACT;IAEQe,oBAAoB3C,MAAc,EAAqB;QAC7D,MAAM6J,OAAO,IAAI,CAACC,aAAa,CAAC9J;QAChC,MAAM4B,OAA0B,EAAE;QAElC,IAAK,IAAIoI,IAAI,GAAGA,IAAIH,MAAMG,IAAK;YAC7B,MAAMC,OAAO,IAAI/J;YACjB+J,KAAK7J,OAAO,CAAC6J,KAAK5J,OAAO,KAAMwJ,CAAAA,OAAOG,CAAAA;YAEtCpI,KAAKuH,IAAI,CAAC;gBACRc,MAAMA,KAAK/B,WAAW,GAAGmC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtCkB,SAASxC,KAAKC,KAAK,CAAC,MAAM,AAACD,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK;gBAClDiC,OAAOzC,KAAKC,KAAK,CAAC,MAAM,AAACD,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK;gBAChDkC,KAAK1C,KAAKC,KAAK,CAAC,KAAK,AAACD,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK;gBAC7CmC,OAAO3C,KAAKC,KAAK,CAAC,KAAK,AAACD,CAAAA,KAAKQ,MAAM,KAAK,GAAE,IAAK,IAAI,SAAS;YAC9D;QACF;QAEA,OAAO3H;IACT;IAEQiB,sBAAsB7C,MAAc,EAAS;QACnD,MAAM2L,YAAY;YAAC;YAAiB;YAAU;YAAU;SAAS;QACjE,MAAM9B,OAAO,IAAI,CAACC,aAAa,CAAC9J;QAEhC,OAAO2L,UAAUC,GAAG,CAACC,CAAAA,WAAa,CAAA;gBAChCA;gBACAC,WAAW/C,KAAKC,KAAK,CAAC,AAAC,CAAA,MAAMD,KAAKQ,MAAM,KAAK,GAAE,IAAKM;gBACpDkC,iBAAiBhD,KAAKC,KAAK,CAAC,MAAMD,KAAKQ,MAAM,KAAK,KAAK,mBAAmB;YAC5E,CAAA;IACF;IAEQvG,yBAAyBhD,MAAc,EAA0B;QACvE,MAAM6J,OAAO,IAAI,CAACC,aAAa,CAAC9J;QAChC,MAAM4B,OAA+B,EAAE;QAEvC,IAAK,IAAIoI,IAAI,GAAGA,IAAIH,MAAMG,IAAK;YAC7B,MAAMC,OAAO,IAAI/J;YACjB+J,KAAK7J,OAAO,CAAC6J,KAAK5J,OAAO,KAAMwJ,CAAAA,OAAOG,CAAAA;YAEtC,kDAAkD;YAClD,MAAMgC,YAAY/B,KAAKgC,MAAM;YAC7B,MAAMC,YAAYF,cAAc,KAAKA,cAAc;YACnD,MAAMG,qBAAqBD,YAAY,MAAM;YAC7C,MAAME,aAAarD,KAAKQ,MAAM,KAAK4C;YAEnCvK,KAAKuH,IAAI,CAAC;gBACRc,MAAMA,KAAK/B,WAAW,GAAGmC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtCgC,UAAUD,aAAa,IAAI;gBAC3BE,UAAUF,aAAarD,KAAKC,KAAK,CAAC,KAAKD,KAAKQ,MAAM,KAAK,MAAM;gBAC7D6B,UAAUgB,aAAarD,KAAKC,KAAK,CAAC,MAAMD,KAAKQ,MAAM,KAAK,OAAO,EAAE,mBAAmB;YACtF;QACF;QAEA,OAAO3H;IACT;IAEQsB,wBAA+B;QACrC,MAAMuH,YAAY;YAChB;gBAAEnF,MAAM;gBAAUiH,YAAY;gBAAIC,OAAO;YAAU;YACnD;gBAAElH,MAAM;gBAASiH,YAAY;gBAAIC,OAAO;YAAU;YAClD;gBAAElH,MAAM;gBAAiBiH,YAAY;gBAAIC,OAAO;YAAU;YAC1D;gBAAElH,MAAM;gBAAaiH,YAAY;gBAAIC,OAAO;YAAU;SACvD;QAED,OAAO/B;IACT;IAEQrH,wBAAwBpD,MAAc,EAAwB;QACpE,MAAM6J,OAAO,IAAI,CAACC,aAAa,CAAC9J;QAChC,MAAM4B,OAA6B,EAAE;QAErC,IAAK,IAAIoI,IAAI,GAAGA,IAAIH,MAAMG,IAAK;YAC7B,MAAMC,OAAO,IAAI/J;YACjB+J,KAAK7J,OAAO,CAAC6J,KAAK5J,OAAO,KAAMwJ,CAAAA,OAAOG,CAAAA;YAEtCpI,KAAKuH,IAAI,CAAC;gBACRc,MAAMA,KAAK/B,WAAW,GAAGmC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACtCoC,cAAc1D,KAAKC,KAAK,CAAC,KAAKD,KAAKQ,MAAM,KAAK;gBAC9CmD,mBAAmB3D,KAAKC,KAAK,CAAC,KAAKD,KAAKQ,MAAM,KAAK;gBACnDoD,eAAe5D,KAAKC,KAAK,CAAC,IAAID,KAAKQ,MAAM,KAAK,IAAI,eAAe;YACnE;QACF;QAEA,OAAO3H;IACT;AACF"}