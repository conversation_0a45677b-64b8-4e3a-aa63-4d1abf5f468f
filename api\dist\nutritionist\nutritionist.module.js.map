{"version": 3, "sources": ["../../src/nutritionist/nutritionist.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { NutritionistService } from './nutritionist.service';\r\n\r\n@Module({\r\n  providers: [NutritionistService]\r\n})\r\nexport class NutritionistModule {}\r\n"], "names": ["NutritionistModule", "providers", "NutritionistService"], "mappings": ";;;;+BAMaA;;;eAAAA;;;wBANU;qCACa;;;;;;;AAK7B,IAAA,AAAMA,qBAAN,MAAMA;AAAoB;;;QAF/BC,WAAW;YAACC,wCAAmB;SAAC"}