// src/admin/dto/get-all-exercises-query.dto.ts
import { IsOptional, IsString, IsInt } from 'class-validator';
import { Type } from 'class-transformer';

export class GetAllExercisesQueryDto {
  @IsOptional()
  @IsString()
  name?: string; // Filtro por nome do exercício

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  muscle_group_id?: number; // Filtro por grupo muscular

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  page?: number; // Página atual (padrão: 1)

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  limit?: number; // Limite de itens por página (padrão: 10)
}