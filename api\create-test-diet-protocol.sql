-- Create test diet protocol for demo user (<EMAIL>)
-- This will fix the issue where the frontend shows "no active diet protocol"

-- First, find the user <NAME_EMAIL>
SET @user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- If user doesn't exist, create it
INSERT IGNORE INTO users (email, password, name, created_at, updated_at) 
VALUES ('<EMAIL>', '$2b$10$example_hash', 'Demo User', NOW(), NOW());

-- Get the user ID again
SET @user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- Insert a diet protocol type if it doesn't exist
INSERT IGNORE INTO select_options (value_option, description, created_at, updated_at) 
VALUES ('Emagrecimento', 'Protocolo focado em perda de peso', NOW(), NOW());

-- Get the type_id for Emagrecimento
SET @type_id = (SELECT id FROM select_options WHERE value_option = 'Emagrecimento' LIMIT 1);

-- Create a test diet protocol for the demo user
INSERT INTO nutritionist_protocols (
    client_id,
    user_id,
    name,
    type_id,
    objective,
    goal_calories,
    goal_protein,
    goal_carbs,
    goal_fat,
    goal_water,
    general_notes,
    initial_weight,
    started_at,
    ended_at,
    created_at,
    updated_at
) VALUES (
    @user_id, -- client_id (demo user)
    NULL, -- user_id (nutritionist - can be null for demo)
    'Protocolo de Emagrecimento Demo',
    @type_id,
    'Perda de peso saudável mantendo massa muscular',
    1800, -- calories
    140,  -- protein (g)
    150,  -- carbs (g)
    60,   -- fat (g)
    2500, -- water (ml)
    'Protocolo de demonstração focado em emagrecimento saudável. Inclui refeições balanceadas e suplementação básica.',
    75.0, -- initial_weight (kg)
    '2024-12-01', -- started_at (recent date)
    NULL, -- ended_at (null = active)
    NOW(),
    NOW()
);

-- Get the protocol ID
SET @protocol_id = LAST_INSERT_ID();

-- Create meals for each day of the week
-- Monday meals
INSERT INTO nutritionist_protocols_meals (protocol_id, name, day_of_week, meal_time, created_at, updated_at) VALUES
(@protocol_id, 'Café da Manhã', 'monday', '07:00:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Manhã', 'monday', '10:00:00', NOW(), NOW()),
(@protocol_id, 'Almoço', 'monday', '12:30:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Tarde', 'monday', '15:30:00', NOW(), NOW()),
(@protocol_id, 'Jantar', 'monday', '19:00:00', NOW(), NOW());

-- Tuesday meals (same structure)
INSERT INTO nutritionist_protocols_meals (protocol_id, name, day_of_week, meal_time, created_at, updated_at) VALUES
(@protocol_id, 'Café da Manhã', 'tuesday', '07:00:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Manhã', 'tuesday', '10:00:00', NOW(), NOW()),
(@protocol_id, 'Almoço', 'tuesday', '12:30:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Tarde', 'tuesday', '15:30:00', NOW(), NOW()),
(@protocol_id, 'Jantar', 'tuesday', '19:00:00', NOW(), NOW());

-- Wednesday meals
INSERT INTO nutritionist_protocols_meals (protocol_id, name, day_of_week, meal_time, created_at, updated_at) VALUES
(@protocol_id, 'Café da Manhã', 'wednesday', '07:00:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Manhã', 'wednesday', '10:00:00', NOW(), NOW()),
(@protocol_id, 'Almoço', 'wednesday', '12:30:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Tarde', 'wednesday', '15:30:00', NOW(), NOW()),
(@protocol_id, 'Jantar', 'wednesday', '19:00:00', NOW(), NOW());

-- Thursday meals
INSERT INTO nutritionist_protocols_meals (protocol_id, name, day_of_week, meal_time, created_at, updated_at) VALUES
(@protocol_id, 'Café da Manhã', 'thursday', '07:00:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Manhã', 'thursday', '10:00:00', NOW(), NOW()),
(@protocol_id, 'Almoço', 'thursday', '12:30:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Tarde', 'thursday', '15:30:00', NOW(), NOW()),
(@protocol_id, 'Jantar', 'thursday', '19:00:00', NOW(), NOW());

-- Friday meals
INSERT INTO nutritionist_protocols_meals (protocol_id, name, day_of_week, meal_time, created_at, updated_at) VALUES
(@protocol_id, 'Café da Manhã', 'friday', '07:00:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Manhã', 'friday', '10:00:00', NOW(), NOW()),
(@protocol_id, 'Almoço', 'friday', '12:30:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Tarde', 'friday', '15:30:00', NOW(), NOW()),
(@protocol_id, 'Jantar', 'friday', '19:00:00', NOW(), NOW());

-- Saturday meals
INSERT INTO nutritionist_protocols_meals (protocol_id, name, day_of_week, meal_time, created_at, updated_at) VALUES
(@protocol_id, 'Café da Manhã', 'saturday', '07:00:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Manhã', 'saturday', '10:00:00', NOW(), NOW()),
(@protocol_id, 'Almoço', 'saturday', '12:30:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Tarde', 'saturday', '15:30:00', NOW(), NOW()),
(@protocol_id, 'Jantar', 'saturday', '19:00:00', NOW(), NOW());

-- Sunday meals
INSERT INTO nutritionist_protocols_meals (protocol_id, name, day_of_week, meal_time, created_at, updated_at) VALUES
(@protocol_id, 'Café da Manhã', 'sunday', '07:00:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Manhã', 'sunday', '10:00:00', NOW(), NOW()),
(@protocol_id, 'Almoço', 'sunday', '12:30:00', NOW(), NOW()),
(@protocol_id, 'Lanche da Tarde', 'sunday', '15:30:00', NOW(), NOW()),
(@protocol_id, 'Jantar', 'sunday', '19:00:00', NOW(), NOW());

-- Get meal IDs for adding foods
SET @breakfast_monday_id = (SELECT id FROM nutritionist_protocols_meals WHERE protocol_id = @protocol_id AND name = 'Café da Manhã' AND day_of_week = 'monday' LIMIT 1);
SET @lunch_monday_id = (SELECT id FROM nutritionist_protocols_meals WHERE protocol_id = @protocol_id AND name = 'Almoço' AND day_of_week = 'monday' LIMIT 1);

-- Add foods to breakfast (Monday)
INSERT INTO nutritionist_protocols_meals_foods (meal_id, name, quantity, unit, calories, protein, carbs, fat, fiber, created_at, updated_at) VALUES
(@breakfast_monday_id, 'Aveia', 50, 'g', 190, 6.9, 32.8, 3.4, 5.0, NOW(), NOW()),
(@breakfast_monday_id, 'Banana', 100, 'g', 89, 1.1, 22.8, 0.3, 2.6, NOW(), NOW()),
(@breakfast_monday_id, 'Leite Desnatado', 200, 'ml', 68, 6.8, 9.6, 0.2, 0, NOW(), NOW());

-- Add foods to lunch (Monday)
INSERT INTO nutritionist_protocols_meals_foods (meal_id, name, quantity, unit, calories, protein, carbs, fat, fiber, created_at, updated_at) VALUES
(@lunch_monday_id, 'Peito de Frango', 150, 'g', 248, 46.2, 0, 5.4, 0, NOW(), NOW()),
(@lunch_monday_id, 'Arroz Integral', 100, 'g', 111, 2.6, 22.9, 0.9, 1.8, NOW(), NOW()),
(@lunch_monday_id, 'Brócolis', 100, 'g', 34, 2.8, 6.6, 0.4, 2.6, NOW(), NOW());

-- Add supplements
INSERT INTO nutritionist_protocols_supplements (protocol_id, name, dosage, supplement_time, notes, created_at, updated_at) VALUES
(@protocol_id, 'Whey Protein', '30g', 'Pós-treino', 'Misturar com 300ml de água', NOW(), NOW()),
(@protocol_id, 'Multivitamínico', '1 cápsula', 'Café da manhã', 'Tomar com o café da manhã', NOW(), NOW()),
(@protocol_id, 'Ômega 3', '1g', 'Almoço', 'Tomar junto com o almoço', NOW(), NOW());

-- Show the created protocol
SELECT 
    np.id,
    np.name,
    np.objective,
    np.goal_calories,
    np.goal_protein,
    np.goal_carbs,
    np.goal_fat,
    np.goal_water,
    np.started_at,
    np.ended_at,
    so.value_option as type,
    u.email as client_email
FROM nutritionist_protocols np
LEFT JOIN select_options so ON so.id = np.type_id
LEFT JOIN users u ON u.id = np.client_id
WHERE np.client_id = @user_id;

-- Show the meals for today (adjust day_of_week as needed)
SELECT 
    npm.id,
    npm.name,
    npm.day_of_week,
    npm.meal_time,
    np.name as protocol_name
FROM nutritionist_protocols_meals npm
JOIN nutritionist_protocols np ON np.id = npm.protocol_id
WHERE np.client_id = @user_id
ORDER BY npm.day_of_week, npm.meal_time;

-- Show meal foods
SELECT 
    npmf.meal_id,
    npm.name as meal_name,
    npmf.name as food_name,
    npmf.quantity,
    npmf.unit,
    npmf.calories,
    npmf.protein,
    npmf.carbs,
    npmf.fat
FROM nutritionist_protocols_meals_foods npmf
JOIN nutritionist_protocols_meals npm ON npm.id = npmf.meal_id
JOIN nutritionist_protocols np ON np.id = npm.protocol_id
WHERE np.client_id = @user_id
ORDER BY npm.meal_time;
