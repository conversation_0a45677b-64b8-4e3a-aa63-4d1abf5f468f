{"version": 3, "sources": ["../../src/http-exception/http-exception.filter.ts"], "sourcesContent": ["import {\r\n    ExceptionFilter,\r\n    Catch,\r\n    ArgumentsHost,\r\n    HttpException,\r\n    HttpStatus,\r\n  } from '@nestjs/common';\r\n  import { Response } from 'express';\r\n  \r\n  @Catch(HttpException)\r\n  export class HttpExceptionFilter implements ExceptionFilter {\r\n    catch(exception: HttpException, host: ArgumentsHost) {\r\n      const ctx = host.switchToHttp();\r\n      const response = ctx.getResponse<Response>();\r\n      const status = exception.getStatus() ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;\r\n  \r\n      const responseBody = {\r\n        status: 'error',\r\n        statusCode: status,\r\n        data: null,\r\n        errors: exception.getResponse()['message'] || ['Erro desconhecido'],\r\n      };\r\n  \r\n      response.status(status).json(responseBody);\r\n    }\r\n  }"], "names": ["HttpExceptionFilter", "catch", "exception", "host", "ctx", "switchToHttp", "response", "getResponse", "status", "getStatus", "HttpStatus", "INTERNAL_SERVER_ERROR", "responseBody", "statusCode", "data", "errors", "json"], "mappings": ";;;;+BAUeA;;;eAAAA;;;wBAJN;;;;;;;AAIA,IAAA,AAAMA,sBAAN,MAAMA;IACXC,MAAMC,SAAwB,EAAEC,IAAmB,EAAE;QACnD,MAAMC,MAAMD,KAAKE,YAAY;QAC7B,MAAMC,WAAWF,IAAIG,WAAW;QAChC,MAAMC,SAASN,UAAUO,SAAS,KAAKP,UAAUO,SAAS,KAAKC,kBAAU,CAACC,qBAAqB;QAE/F,MAAMC,eAAe;YACnBJ,QAAQ;YACRK,YAAYL;YACZM,MAAM;YACNC,QAAQb,UAAUK,WAAW,EAAE,CAAC,UAAU,IAAI;gBAAC;aAAoB;QACrE;QAEAD,SAASE,MAAM,CAACA,QAAQQ,IAAI,CAACJ;IAC/B;AACF"}