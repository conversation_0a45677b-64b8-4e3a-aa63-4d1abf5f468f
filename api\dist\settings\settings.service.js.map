{"version": 3, "sources": ["../../src/settings/settings.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\nimport { db } from '../database';\nimport * as dayjs from 'dayjs';\n\n@Injectable()\nexport class SettingsService {\n  \n  async getAllSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        profile: {},\n        privacy: {},\n        goals: {},\n        units: {},\n        integrations: {},\n        reminders: {},\n        themes: {},\n        dataRetention: {}\n      }\n    };\n  }\n\n  async getProfileSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        displayName: '',\n        bio: '',\n        profileVisibility: 'public',\n        showAchievements: true,\n        showProgress: true\n      }\n    };\n  }\n\n  async updateProfileSettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async getPrivacySettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        profileVisibility: 'public',\n        dataSharing: false,\n        analyticsOptOut: false,\n        marketingOptOut: false\n      }\n    };\n  }\n\n  async updatePrivacySettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async getGoalSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        primaryGoal: '',\n        targetWeight: 0,\n        targetBodyFat: 0,\n        weeklyGoal: '',\n        reminderFrequency: 'daily'\n      }\n    };\n  }\n\n  async updateGoalSettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async getUnitSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        weightUnit: 'kg',\n        heightUnit: 'cm',\n        distanceUnit: 'km',\n        temperatureUnit: 'celsius'\n      }\n    };\n  }\n\n  async updateUnitSettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async getIntegrationSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        googleFit: false,\n        appleHealth: false,\n        fitbit: false,\n        strava: false,\n        myFitnessPal: false\n      }\n    };\n  }\n\n  async updateIntegrationSettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async getReminderSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        workoutReminders: true,\n        mealReminders: true,\n        waterReminders: true,\n        sleepReminders: true,\n        reminderTimes: []\n      }\n    };\n  }\n\n  async updateReminderSettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async testReminder(userId: number, reminderData: any) {\n    return {\n      status: 'success',\n      data: {\n        sent: true,\n        type: reminderData.type || 'test'\n      }\n    };\n  }\n\n  async getThemeSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        theme: 'light',\n        accentColor: '#007bff',\n        fontSize: 'medium',\n        animations: true\n      }\n    };\n  }\n\n  async updateThemeSettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async getDataRetentionSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        retentionPeriod: '2years',\n        autoDelete: false,\n        backupBeforeDelete: true\n      }\n    };\n  }\n\n  async updateDataRetentionSettings(userId: number, settings: any) {\n    return {\n      status: 'success',\n      data: {\n        updated: true,\n        settings\n      }\n    };\n  }\n\n  async resetSettings(userId: number, resetOptions: any) {\n    return {\n      status: 'success',\n      data: {\n        reset: true,\n        categories: resetOptions.categories || []\n      }\n    };\n  }\n\n  async exportSettings(userId: number) {\n    return {\n      status: 'success',\n      data: {\n        exportId: 'settings_export_' + Date.now(),\n        downloadUrl: null,\n        expiresAt: dayjs().add(24, 'hours').toISOString()\n      }\n    };\n  }\n\n  async importSettings(userId: number, settingsData: any) {\n    return {\n      status: 'success',\n      data: {\n        imported: true,\n        categoriesImported: Object.keys(settingsData).length\n      }\n    };\n  }\n}\n"], "names": ["SettingsService", "getAllSettings", "userId", "status", "data", "profile", "privacy", "goals", "units", "integrations", "reminders", "themes", "dataRetention", "getProfileSettings", "displayName", "bio", "profileVisibility", "showAchievements", "showProgress", "updateProfileSettings", "settings", "updated", "getPrivacySettings", "dataSharing", "analyticsOptOut", "marketingOptOut", "updatePrivacySettings", "getGoalSettings", "primaryGoal", "targetWeight", "targetBodyFat", "weeklyGoal", "reminderFrequency", "updateGoalSettings", "getUnitSettings", "weightUnit", "heightUnit", "distanceUnit", "temperatureUnit", "updateUnitSettings", "getIntegrationSettings", "googleFit", "appleHealth", "fitbit", "strava", "myFitnessPal", "updateIntegrationSettings", "getReminderSettings", "workoutReminders", "meal<PERSON><PERSON><PERSON>s", "waterReminders", "sleepReminders", "reminderTimes", "updateReminderSettings", "testReminder", "reminderData", "sent", "type", "getThemeSettings", "theme", "accentColor", "fontSize", "animations", "updateThemeSettings", "getDataRetentionSettings", "retentionPeriod", "autoDelete", "backupBeforeDelete", "updateDataRetentionSettings", "resetSettings", "resetOptions", "reset", "categories", "exportSettings", "exportId", "Date", "now", "downloadUrl", "expiresAt", "dayjs", "add", "toISOString", "importSettings", "settingsData", "imported", "categoriesImported", "Object", "keys", "length"], "mappings": ";;;;+BAKaA;;;eAAAA;;;wBALc;+DAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGhB,IAAA,AAAMA,kBAAN,MAAMA;IAEX,MAAMC,eAAeC,MAAc,EAAE;QACnC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJC,SAAS,CAAC;gBACVC,SAAS,CAAC;gBACVC,OAAO,CAAC;gBACRC,OAAO,CAAC;gBACRC,cAAc,CAAC;gBACfC,WAAW,CAAC;gBACZC,QAAQ,CAAC;gBACTC,eAAe,CAAC;YAClB;QACF;IACF;IAEA,MAAMC,mBAAmBX,MAAc,EAAE;QACvC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJU,aAAa;gBACbC,KAAK;gBACLC,mBAAmB;gBACnBC,kBAAkB;gBAClBC,cAAc;YAChB;QACF;IACF;IAEA,MAAMC,sBAAsBjB,MAAc,EAAEkB,QAAa,EAAE;QACzD,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAME,mBAAmBpB,MAAc,EAAE;QACvC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJY,mBAAmB;gBACnBO,aAAa;gBACbC,iBAAiB;gBACjBC,iBAAiB;YACnB;QACF;IACF;IAEA,MAAMC,sBAAsBxB,MAAc,EAAEkB,QAAa,EAAE;QACzD,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAMO,gBAAgBzB,MAAc,EAAE;QACpC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJwB,aAAa;gBACbC,cAAc;gBACdC,eAAe;gBACfC,YAAY;gBACZC,mBAAmB;YACrB;QACF;IACF;IAEA,MAAMC,mBAAmB/B,MAAc,EAAEkB,QAAa,EAAE;QACtD,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAMc,gBAAgBhC,MAAc,EAAE;QACpC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJ+B,YAAY;gBACZC,YAAY;gBACZC,cAAc;gBACdC,iBAAiB;YACnB;QACF;IACF;IAEA,MAAMC,mBAAmBrC,MAAc,EAAEkB,QAAa,EAAE;QACtD,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAMoB,uBAAuBtC,MAAc,EAAE;QAC3C,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJqC,WAAW;gBACXC,aAAa;gBACbC,QAAQ;gBACRC,QAAQ;gBACRC,cAAc;YAChB;QACF;IACF;IAEA,MAAMC,0BAA0B5C,MAAc,EAAEkB,QAAa,EAAE;QAC7D,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAM2B,oBAAoB7C,MAAc,EAAE;QACxC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJ4C,kBAAkB;gBAClBC,eAAe;gBACfC,gBAAgB;gBAChBC,gBAAgB;gBAChBC,eAAe,EAAE;YACnB;QACF;IACF;IAEA,MAAMC,uBAAuBnD,MAAc,EAAEkB,QAAa,EAAE;QAC1D,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAMkC,aAAapD,MAAc,EAAEqD,YAAiB,EAAE;QACpD,OAAO;YACLpD,QAAQ;YACRC,MAAM;gBACJoD,MAAM;gBACNC,MAAMF,aAAaE,IAAI,IAAI;YAC7B;QACF;IACF;IAEA,MAAMC,iBAAiBxD,MAAc,EAAE;QACrC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJuD,OAAO;gBACPC,aAAa;gBACbC,UAAU;gBACVC,YAAY;YACd;QACF;IACF;IAEA,MAAMC,oBAAoB7D,MAAc,EAAEkB,QAAa,EAAE;QACvD,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAM4C,yBAAyB9D,MAAc,EAAE;QAC7C,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJ6D,iBAAiB;gBACjBC,YAAY;gBACZC,oBAAoB;YACtB;QACF;IACF;IAEA,MAAMC,4BAA4BlE,MAAc,EAAEkB,QAAa,EAAE;QAC/D,OAAO;YACLjB,QAAQ;YACRC,MAAM;gBACJiB,SAAS;gBACTD;YACF;QACF;IACF;IAEA,MAAMiD,cAAcnE,MAAc,EAAEoE,YAAiB,EAAE;QACrD,OAAO;YACLnE,QAAQ;YACRC,MAAM;gBACJmE,OAAO;gBACPC,YAAYF,aAAaE,UAAU,IAAI,EAAE;YAC3C;QACF;IACF;IAEA,MAAMC,eAAevE,MAAc,EAAE;QACnC,OAAO;YACLC,QAAQ;YACRC,MAAM;gBACJsE,UAAU,qBAAqBC,KAAKC,GAAG;gBACvCC,aAAa;gBACbC,WAAWC,SAAQC,GAAG,CAAC,IAAI,SAASC,WAAW;YACjD;QACF;IACF;IAEA,MAAMC,eAAehF,MAAc,EAAEiF,YAAiB,EAAE;QACtD,OAAO;YACLhF,QAAQ;YACRC,MAAM;gBACJgF,UAAU;gBACVC,oBAAoBC,OAAOC,IAAI,CAACJ,cAAcK,MAAM;YACtD;QACF;IACF;AACF"}