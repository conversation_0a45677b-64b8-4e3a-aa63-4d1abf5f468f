import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { HttpExceptionFilter } from './http-exception/http-exception.filter';
import * as dotenv from 'dotenv';
import { join } from 'path';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as bodyParser from 'body-parser';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  
  // app.use(express.static(join(__dirname, 'storage')));
  // app.use(express.static(join(__dirname, '__temp')));

  // Aumentar o limite para ~50MB
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

  // Configurar CORS
  app.enableCors({
    // origin: 'http://localhost:3000',
    origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:5177', 'http://localhost:5178', 'http://localhost:5179', 'https://api.mysnapfit.com.br', 'https://api2.mysnapfit.com.br', 'https://app.mysnapfit.com.br'],
    // origin: ['*'],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));

  app.useGlobalFilters(new HttpExceptionFilter());

  // Serve arquivos estáticos media/...
  app.useStaticAssets(join(__dirname, '..', 'media'), {
    prefix: '/media/',
  });

  /*
  // Serve arquivos estáticos storage/...
  app.useStaticAssets(join(__dirname, '..', 'storage'), {
    prefix: '/storage/',
  });

  // Serve aquivos estáticos __temp/...
  app.useStaticAssets(join(__dirname, '..', '__temp'), {
    prefix: '/__temp/',
  });
  */


  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
