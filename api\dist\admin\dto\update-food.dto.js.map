{"version": 3, "sources": ["../../../src/admin/dto/update-food.dto.ts"], "sourcesContent": ["// src/admin/dto/update-food.dto.ts\r\nimport { IsString, IsNotEmpty, IsInt, IsNumber, Min } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class UpdateFoodDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string; // Nome do alimento\r\n\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  category_id: number; // ID da categoria\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  quantity: number; // Porção\r\n\r\n  @IsString()\r\n  unit: string; // ID da unidade de porção\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  calories: number; // Calorias\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  protein: number; // Proteína\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  carbs: number; // Carboidratos\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  fat: number; // Gorduras\r\n\r\n  @IsNumber()\r\n  @Min(0)\r\n  fiber: number; // Fibras\r\n}"], "names": ["UpdateFoodDto", "Number"], "mappings": "AAAA,mCAAmC;;;;;+BAItBA;;;eAAAA;;;gCAH8C;kCACtC;;;;;;;;;;AAEd,IAAA,AAAMA,gBAAN,MAAMA;AAmCb;;;;;;;;oCA7BcC"}