{"version": 3, "sources": ["../../src/dto/create-nutritionist-client-protocol.dto.ts"], "sourcesContent": ["import { IsString, IsNotEmpty, IsInt, IsArray, ArrayNotEmpty, IsOptional, IsNumber, IsObject } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\nexport class CreateNutritionistClientProtocolDto {\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @IsInt()\r\n  @Type(() => Number)\r\n  type_id: number;\r\n\r\n  @IsString()\r\n  objective: string;\r\n\r\n  @IsObject()\r\n  nutritional_goals: {\r\n    calories: number,\r\n    protein: number,\r\n    carbs: number,\r\n    fat: number,\r\n    water: number\r\n  }\r\n  \r\n  @IsArray()\r\n  @ArrayNotEmpty()\r\n  meals: {\r\n    name: string,\r\n    week_day: number,\r\n    meal_time: string,\r\n    foods: {\r\n      food_id: number,\r\n      quantity: number\r\n    }[],\r\n  }[];\r\n\r\n  @IsOptional()\r\n  @IsArray()\r\n  supplements?: {\r\n    name: string,\r\n    dosage: string,\r\n    supplement_time: string,\r\n    notes?: string\r\n  }[];\r\n\r\n  @IsString()\r\n  @IsOptional()\r\n  general_notes?: string;\r\n}"], "names": ["CreateNutritionistClientProtocolDto", "Number"], "mappings": ";;;;+BAGaA;;;eAAAA;;;gCAHuF;kCAC/E;;;;;;;;;;AAEd,IAAA,AAAMA,sCAAN,MAAMA;AA6Cb;;;;;;;;oCAvCcC"}