-- Create test user for SnapFit
-- Password: snapfit (hashed with bcrypt)

INSERT INTO users (
    name,
    email,
    password,
    height,
    weight,
    bodyfat,
    date_of_birth,
    created_at,
    updated_at
) VALUES (
    'Demo User',
    '<EMAIL>',
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: snapfit
    175,
    70.5,
    15.2,
    '1990-01-01',
    NOW(),
    NOW()
);

-- Get the user ID for wallet setup
SET @user_id = LAST_INSERT_ID();

-- Note: Wallet data will be created automatically by the backend when needed
-- The wallet service uses existing tables and calculates values dynamically

-- Show created user
SELECT
    id,
    name,
    email,
    height,
    weight,
    bodyfat,
    date_of_birth,
    created_at
FROM users
WHERE email = '<EMAIL>';
