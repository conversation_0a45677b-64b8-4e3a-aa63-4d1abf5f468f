"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "RewardsController", {
    enumerable: true,
    get: function() {
        return RewardsController;
    }
});
const _common = require("@nestjs/common");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _rewardsservice = require("./rewards.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let RewardsController = class RewardsController {
    async getAvailableRewards(req, query) {
        const userId = req.user.userId;
        return this.rewardsService.getAvailableRewards(userId, query);
    }
    async getMyRewards(req) {
        const userId = req.user.userId;
        return this.rewardsService.getMyRewards(userId);
    }
    async redeemReward(id, req) {
        const userId = req.user.userId;
        return this.rewardsService.redeemReward(Number(id), userId);
    }
    async getRewardCategories() {
        return this.rewardsService.getRewardCategories();
    }
    constructor(rewardsService){
        this.rewardsService = rewardsService;
    }
};
_ts_decorate([
    (0, _common.Get)('available'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], RewardsController.prototype, "getAvailableRewards", null);
_ts_decorate([
    (0, _common.Get)('my'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], RewardsController.prototype, "getMyRewards", null);
_ts_decorate([
    (0, _common.Post)(':id/redeem'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], RewardsController.prototype, "redeemReward", null);
_ts_decorate([
    (0, _common.Get)('categories'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", Promise)
], RewardsController.prototype, "getRewardCategories", null);
RewardsController = _ts_decorate([
    (0, _common.Controller)('rewards'),
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _rewardsservice.RewardsService === "undefined" ? Object : _rewardsservice.RewardsService
    ])
], RewardsController);

//# sourceMappingURL=rewards.controller.js.map