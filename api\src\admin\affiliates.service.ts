import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { db } from '../database';
// import * as dayjs from 'dayjs';
const dayjs = require('dayjs');
import { sql } from 'kysely';
import { Stripe } from 'stripe';

@Injectable()
export class AffiliatesService {
    private stripe: Stripe;

    constructor() {
    // @ts-ignore
    this.stripe = new Stripe(process.env.STRIPE_SK, {
      // apiVersion: '2025-04-30',
    });
    }

    async getAffiliateById(id: number) {
        const affiliate = await db.selectFrom('affiliates')
            .leftJoin('users', 'users.id', 'affiliates.user_id')
            .select(['users.name', 'users.email', 'affiliates.is_master'])
            .where('user_id', '=', id)
            .where('affiliates.deleted_at', 'is', null)
            .where('affiliates.status', '=', 'active')
            .executeTakeFirst();

        if (!affiliate) {
            throw new NotFoundException('Affiliate not found');
        }

        return {
            status: 'success',
            data: {
                name: affiliate.name,
                email: affiliate.email,
                is_master: affiliate.is_master,
            },
        };
    }


  async findAccountByAffiliateId(affiliateId: string): Promise<Stripe.Account | null> {
    const affiliate = await db.selectFrom('affiliates')
    .where('user_id', '=', Number(affiliateId))
    .select(['stripeId'])
    .executeTakeFirst();

    if (!affiliate) {
      throw new NotFoundException('Affiliate not found');
    }

    if (!affiliate.stripeId) {
      return null;
    }

    const account = await this.stripe.accounts.retrieve(affiliate.stripeId);

    return account;
  }

  async checkPaymentEligibilityByAffiliateId(affiliateId: string): Promise<any> {
    const account = await this.findAccountByAffiliateId(affiliateId);
    if (!account) {
      return null;
    }

    const canReceivePayments =
      account.capabilities?.card_payments === 'active' &&
      account.capabilities?.transfers === 'active';
    const detailsSubmitted = account.details_submitted;

    return {
      status: 'success',
      data: {
      onboarding: canReceivePayments
        ? 'ready'
        : detailsSubmitted
          ? 'pending'
          : 'onboarding'
      },
    };
  }

  async startOnboarding(affiliateId: string): Promise<any> {
    const account = await this.findAccountByAffiliateId(affiliateId);
    if (!account) {
      return null;
    }

    const accountLink = await this.stripe.accountLinks.create({
      account: account.id,
      refresh_url: process.env.STRIPE_REFRESH_URL,
      return_url: process.env.STRIPE_RETURN_URL,
      type: 'account_onboarding',
    });

    return {
      status: 'success',
      data: {
        accountLink: accountLink.url,
      },
    };
  }

  async getLinks(userId: number) {
    const links = await db.selectFrom('affiliate_links')
    .where('user_id', '=', userId)
    .where('deleted_at', 'is', null)
    .select(['id', 'name', 'invite', 'link_type', 'created_at', 'updated_at'])
    .execute();

    return {
      status: 'success',
      data: links,
    };
  }


  async createLink(userId: number, body: any) {
    const { name, invite, link_type } = body;

    const existingLink = await db.selectFrom('affiliate_links')
    .where('invite', '=', invite)
    .select('id')
    .executeTakeFirst();

    if (existingLink) {
      throw new ConflictException('Esse cupom já está em uso');
    }

    const newLink = await db.insertInto('affiliate_links')
    .values({
      user_id: userId,
      name,
      invite,
      link_type,
      created_at: new Date(),
      updated_at: new Date(),
    })
    .executeTakeFirstOrThrow();

    return {
      status: 'success',
      data: [],
    };
  }

  async registerLinkVisit(id: string) {
    const link = await db.selectFrom('affiliate_links')
    .where('invite', '=', id)
    .select(['id', 'user_id', 'link_type'])
    .executeTakeFirst();

    if (!link) {
      throw new NotFoundException('Link not found');
    }

    const newVisit = await db.insertInto('affiliate_links_visits')
    .values({
      link_id: link.id,
      created_at: new Date(),
    })
    .executeTakeFirstOrThrow();

    return {
      status: 'success',
      data: [],
    };
  }

  async getDashboardStats(userId: number) {
    try {
      console.log(`Buscando estatísticas do dashboard para o afiliado ${userId}`);

      // Verificar se o usuário é um afiliado ativo
      const affiliate = await db.selectFrom('affiliates')
        .where('user_id', '=', userId)
        .where('status', '=', 'active')
        .where('deleted_at', 'is', null)
        .select(['id', 'user_id', 'invite'])
        .executeTakeFirst();

      if (!affiliate) {
        throw new NotFoundException('Afiliado não encontrado ou não está ativo');
      }

      console.log(`Afiliado encontrado: ${JSON.stringify(affiliate)}`);

      // 1. Total de Cliques - buscar visitas aos links do afiliado
      const totalClicks = await db
        .selectFrom('affiliate_links_visits as alv')
        .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')
        .where('al.user_id', '=', userId)
        .where('al.deleted_at', 'is', null)
        .select(sql<number>`COUNT(*)`.as('total'))
        .executeTakeFirst();

      console.log(`Total de cliques: ${totalClicks?.total || 0}`);

      // 2. Ganhos Totais - buscar comissões do afiliado
      const totalEarnings = await db
        .selectFrom('affiliate_commissions')
        .where('aff_user_id', '=', userId)
        .select([
          sql<number>`SUM(commission_value)`.as('total_earnings'),
          sql<number>`COUNT(*)`.as('total_commissions')
        ])
        .executeTakeFirst();

      console.log(`Ganhos totais: ${totalEarnings?.total_earnings || 0}, Total de comissões: ${totalEarnings?.total_commissions || 0}`);

      // 3. Conversões - total de usuários que têm este afiliado como referência
      const conversions = await db
        .selectFrom('users')
        .where('aff_id', '=', userId)
        .select(sql<number>`COUNT(*)`.as('total'))
        .executeTakeFirst();

      console.log(`Total de conversões: ${conversions?.total || 0}`);

      // 4. Comissões por status
      const commissionsByStatus = await db
        .selectFrom('affiliate_commissions')
        .where('aff_user_id', '=', userId)
        .select([
          'status',
          sql<number>`COUNT(*)`.as('count'),
          sql<number>`SUM(commission_value)`.as('total_value')
        ])
        .groupBy('status')
        .execute();

      console.log(`Comissões por status: ${JSON.stringify(commissionsByStatus)}`);

      // 5. Estatísticas dos últimos 30 dias
      const thirtyDaysAgo = dayjs().subtract(30, 'day').toDate();

      const recentStats = await db
        .selectFrom('affiliate_commissions')
        .where('aff_user_id', '=', userId)
        .where('created_at', '>=', thirtyDaysAgo)
        .select([
          sql<number>`COUNT(*)`.as('recent_commissions'),
          sql<number>`SUM(commission_value)`.as('recent_earnings')
        ])
        .executeTakeFirst();

      console.log(`Estatísticas dos últimos 30 dias: ${JSON.stringify(recentStats)}`);

      // 6. Conversões dos últimos 30 dias
      const recentConversions = await db
        .selectFrom('users')
        .where('aff_id', '=', userId)
        .where('created_at', '>=', thirtyDaysAgo)
        .select(sql<number>`COUNT(*)`.as('total'))
        .executeTakeFirst();

      console.log(`Conversões dos últimos 30 dias: ${recentConversions?.total || 0}`);

      // 7. Cliques dos últimos 30 dias
      const recentClicks = await db
        .selectFrom('affiliate_links_visits as alv')
        .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')
        .where('al.user_id', '=', userId)
        .where('al.deleted_at', 'is', null)
        .where('alv.created_at', '>=', thirtyDaysAgo)
        .select(sql<number>`COUNT(*)`.as('total'))
        .executeTakeFirst();

      console.log(`Cliques dos últimos 30 dias: ${recentClicks?.total || 0}`);

      // 8. Último Pagamento - buscar a comissão mais recente com status 'paid'
      const lastPayment = await db
        .selectFrom('affiliate_commissions')
        .where('aff_user_id', '=', userId)
        .where('status', '=', 'paid')
        .select([
          'commission_value',
          'created_at',
          'commission_percent',
          'metadata'
        ])
        .orderBy('created_at', 'desc')
        .executeTakeFirst();

      console.log(`Último pagamento: ${JSON.stringify(lastPayment)}`);

      // Processar informações do último pagamento
      let lastPaymentInfo: any = null;
      if (lastPayment) {
        let metadata: any = null;
        try {
          metadata = lastPayment.metadata ? JSON.parse(lastPayment.metadata as string) : null;
        } catch (error) {
          console.error('Erro ao processar metadata do último pagamento:', error);
        }

        lastPaymentInfo = {
          value: Number(lastPayment.commission_value || 0),
          date: lastPayment.created_at,
          percent: Number(lastPayment.commission_percent || 0),
          transaction_amount: metadata?.transaction_amount || null,
          currency: metadata?.currency || 'BRL',
          stripe_transfer_id: metadata?.stripe_transfer_id || null
        };
      }

      const dashboardData = {
        // Estatísticas principais
        total_clicks: Number(totalClicks?.total || 0),
        total_earnings: Number(totalEarnings?.total_earnings || 0),
        total_conversions: Number(conversions?.total || 0),
        total_commissions: Number(totalEarnings?.total_commissions || 0),

        // Estatísticas dos últimos 30 dias
        recent_stats: {
          clicks: Number(recentClicks?.total || 0),
          earnings: Number(recentStats?.recent_earnings || 0),
          conversions: Number(recentConversions?.total || 0),
          commissions: Number(recentStats?.recent_commissions || 0)
        },

        // Comissões por status
        commissions_by_status: commissionsByStatus.map(item => ({
          status: item.status,
          count: Number(item.count),
          total_value: Number(item.total_value || 0)
        })),

        // Taxa de conversão
        conversion_rate: Number(totalClicks?.total || 0) > 0
          ? ((Number(conversions?.total || 0) / Number(totalClicks?.total || 0)) * 100).toFixed(2)
          : '0.00',

        // Valor médio por comissão
        average_commission: Number(totalEarnings?.total_commissions || 0) > 0
          ? (Number(totalEarnings?.total_earnings || 0) / Number(totalEarnings?.total_commissions || 0)).toFixed(2)
          : '0.00',

        // Último pagamento
        last_payment: lastPaymentInfo
      };

      console.log(`Dashboard data: ${JSON.stringify(dashboardData)}`);

      return {
        status: 'success',
        data: dashboardData
      };

    } catch (error) {
      console.error('Erro ao buscar estatísticas do dashboard:', error);
      throw error;
    }
  }

  async getDetailedStats(userId: number, startDate?: string, endDate?: string) {
    try {
      console.log(`Buscando estatísticas detalhadas para o afiliado ${userId}, período: ${startDate} - ${endDate}`);

      // Verificar se o usuário é um afiliado ativo
      const affiliate = await db.selectFrom('affiliates')
        .where('user_id', '=', userId)
        .where('status', '=', 'active')
        .where('deleted_at', 'is', null)
        .select(['id', 'user_id', 'invite'])
        .executeTakeFirst();

      if (!affiliate) {
        throw new NotFoundException('Afiliado não encontrado ou não está ativo');
      }

      // Definir período de consulta
      let dateFilter: { start?: Date; end?: Date } = {};
      if (startDate && endDate) {
        const start = dayjs(startDate).startOf('day').toDate();
        const end = dayjs(endDate).endOf('day').toDate();
        dateFilter = {
          start,
          end
        };
      }

      // Buscar comissões detalhadas
      let commissionsQuery = db
        .selectFrom('affiliate_commissions as ac')
        .leftJoin('users as u', 'ac.user_id', 'u.id')
        .leftJoin('plans as p', 'ac.plan_id', 'p.id')
        .leftJoin('transactions as t', 'ac.transaction_id', 't.id')
        .where('ac.aff_user_id', '=', userId)
        .select([
          'ac.id',
          'ac.commission_value',
          'ac.commission_percent',
          'ac.status',
          'ac.created_at',
          'u.name as user_name',
          'u.email as user_email',
          'p.name as plan_name',
          't.amount as transaction_amount',
          't.currency as transaction_currency'
        ])
        .orderBy('ac.created_at', 'desc');

      if (dateFilter.start && dateFilter.end) {
        commissionsQuery = commissionsQuery
          .where('ac.created_at', '>=', dateFilter.start)
          .where('ac.created_at', '<=', dateFilter.end);
      }

      const commissions = await commissionsQuery.execute();

      // Buscar conversões detalhadas
      let conversionsQuery = db
        .selectFrom('users as u')
        .leftJoin('users_subscriptions as us', 'u.id', 'us.user_id')
        .leftJoin('plans as p', 'us.plan_id', 'p.id')
        .where('u.aff_id', '=', userId)
        .select([
          'u.id',
          'u.name',
          'u.email',
          'u.created_at as conversion_date',
          'us.status as subscription_status',
          'p.name as plan_name',
          'us.price as subscription_price'
        ])
        .orderBy('u.created_at', 'desc');

      if (dateFilter.start && dateFilter.end) {
        conversionsQuery = conversionsQuery
          .where('u.created_at', '>=', dateFilter.start)
          .where('u.created_at', '<=', dateFilter.end);
      }

      const conversions = await conversionsQuery.execute();

      // Buscar cliques detalhados
      let clicksQuery = db
        .selectFrom('affiliate_links_visits as alv')
        .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')
        .where('al.user_id', '=', userId)
        .where('al.deleted_at', 'is', null)
        .select([
          'alv.id',
          'alv.created_at as click_date',
          'al.name as link_name',
          'al.invite as link_code',
          'al.link_type'
        ])
        .orderBy('alv.created_at', 'desc');

      if (dateFilter.start && dateFilter.end) {
        clicksQuery = clicksQuery
          .where('alv.created_at', '>=', dateFilter.start)
          .where('alv.created_at', '<=', dateFilter.end);
      }

      const clicks = await clicksQuery.execute();

      // Estatísticas por período (últimos 12 meses)
      const monthlyStats: any[] = [];
      for (let i = 11; i >= 0; i--) {
        const monthStart = dayjs().subtract(i, 'month').startOf('month').toDate();
        const monthEnd = dayjs().subtract(i, 'month').endOf('month').toDate();
        const monthName = dayjs().subtract(i, 'month').format('YYYY-MM');

        const monthCommissions = await db
          .selectFrom('affiliate_commissions')
          .where('aff_user_id', '=', userId)
          .where('created_at', '>=', monthStart)
          .where('created_at', '<=', monthEnd)
          .select([
            sql<number>`COUNT(*)`.as('count'),
            sql<number>`SUM(commission_value)`.as('total_value')
          ])
          .executeTakeFirst();

        const monthConversions = await db
          .selectFrom('users')
          .where('aff_id', '=', userId)
          .where('created_at', '>=', monthStart)
          .where('created_at', '<=', monthEnd)
          .select(sql<number>`COUNT(*)`.as('count'))
          .executeTakeFirst();

        const monthClicks = await db
          .selectFrom('affiliate_links_visits as alv')
          .innerJoin('affiliate_links as al', 'alv.link_id', 'al.id')
          .where('al.user_id', '=', userId)
          .where('al.deleted_at', 'is', null)
          .where('alv.created_at', '>=', monthStart)
          .where('alv.created_at', '<=', monthEnd)
          .select(sql<number>`COUNT(*)`.as('count'))
          .executeTakeFirst();

        monthlyStats.push({
          month: monthName,
          commissions: {
            count: Number(monthCommissions?.count || 0),
            total_value: Number(monthCommissions?.total_value || 0)
          },
          conversions: Number(monthConversions?.count || 0),
          clicks: Number(monthClicks?.count || 0)
        });
      }

      const detailedData = {
        // Dados detalhados
        commissions: commissions.map(commission => ({
          id: commission.id,
          value: Number(commission.commission_value || 0),
          percent: Number(commission.commission_percent || 0),
          status: commission.status,
          date: commission.created_at,
          user: {
            name: commission.user_name,
            email: commission.user_email
          },
          plan: commission.plan_name,
          transaction: {
            amount: Number(commission.transaction_amount || 0),
            currency: commission.transaction_currency
          }
        })),

        conversions: conversions.map(conversion => ({
          id: conversion.id,
          user: {
            name: conversion.name,
            email: conversion.email
          },
          conversion_date: conversion.conversion_date,
          subscription: {
            status: conversion.subscription_status,
            plan: conversion.plan_name,
            price: Number(conversion.subscription_price || 0)
          }
        })),

        clicks: clicks.map(click => ({
          id: click.id,
          date: click.click_date,
          link: {
            name: click.link_name,
            code: click.link_code,
            type: click.link_type
          }
        })),

        // Estatísticas mensais
        monthly_stats: monthlyStats,

        // Resumo do período
        period_summary: {
          total_commissions: commissions.length,
          total_earnings: commissions.reduce((sum, c) => sum + Number(c.commission_value || 0), 0),
          total_conversions: conversions.length,
          total_clicks: clicks.length,
          conversion_rate: clicks.length > 0
            ? ((conversions.length / clicks.length) * 100).toFixed(2)
            : '0.00'
        }
      };

      return {
        status: 'success',
        data: detailedData
      };

    } catch (error) {
      console.error('Erro ao buscar estatísticas detalhadas:', error);
      throw error;
    }
  }

  async getPaymentHistory(userId: number, page: number = 1, limit: number = 10) {
    try {
      console.log(`Buscando histórico de pagamentos para o afiliado ${userId}, página ${page}, limite ${limit}`);

      // Verificar se o usuário é um afiliado ativo
      const affiliate = await db.selectFrom('affiliates')
        .where('user_id', '=', userId)
        .where('status', '=', 'active')
        .where('deleted_at', 'is', null)
        .select(['id', 'user_id'])
        .executeTakeFirst();

      if (!affiliate) {
        throw new NotFoundException('Afiliado não encontrado ou não está ativo');
      }

      const offset = (page - 1) * limit;

      // Buscar histórico de pagamentos (comissões pagas)
      const payments = await db
        .selectFrom('affiliate_commissions as ac')
        .leftJoin('users as u', 'ac.user_id', 'u.id')
        .leftJoin('plans as p', 'ac.plan_id', 'p.id')
        .leftJoin('transactions as t', 'ac.transaction_id', 't.id')
        .where('ac.aff_user_id', '=', userId)
        .where('ac.status', '=', 'paid')
        .select([
          'ac.id',
          'ac.commission_value',
          'ac.commission_percent',
          'ac.created_at as payment_date',
          'ac.metadata',
          'u.name as customer_name',
          'u.email as customer_email',
          'p.name as plan_name',
          't.amount as transaction_amount',
          't.currency as transaction_currency'
        ])
        .orderBy('ac.created_at', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      // Contar total de pagamentos
      const totalPayments = await db
        .selectFrom('affiliate_commissions')
        .where('aff_user_id', '=', userId)
        .where('status', '=', 'paid')
        .select(sql<number>`COUNT(*)`.as('total'))
        .executeTakeFirst();

      // Processar dados dos pagamentos
      const processedPayments = payments.map(payment => {
        let metadata: any = null;
        try {
          metadata = payment.metadata ? JSON.parse(payment.metadata as string) : null;
        } catch (error) {
          console.error('Erro ao processar metadata do pagamento:', error);
        }

        return {
          id: payment.id,
          value: Number(payment.commission_value || 0),
          percent: Number(payment.commission_percent || 0),
          date: payment.payment_date,
          customer: {
            name: payment.customer_name,
            email: payment.customer_email
          },
          plan: payment.plan_name,
          transaction: {
            amount: Number(payment.transaction_amount || 0),
            currency: payment.transaction_currency || 'BRL'
          },
          stripe_transfer_id: metadata?.stripe_transfer_id || null
        };
      });

      const totalPages = Math.ceil(Number(totalPayments?.total || 0) / limit);

      return {
        status: 'success',
        data: {
          payments: processedPayments,
          pagination: {
            current_page: page,
            total_pages: totalPages,
            total_items: Number(totalPayments?.total || 0),
            items_per_page: limit,
            has_next: page < totalPages,
            has_previous: page > 1
          }
        }
      };

    } catch (error) {
      console.error('Erro ao buscar histórico de pagamentos:', error);
      throw error;
    }
  }

  async getRecentTransactions(userId: number, page: number = 1, limit: number = 10) {
  try {
    console.log(`Buscando transações recentes para o afiliado ${userId}, página ${page}, limite ${limit}`);

    // Verificar se o usuário é um afiliado ativo
    const affiliate = await db.selectFrom('affiliates')
      .where('user_id', '=', userId)
      .where('status', '=', 'active')
      .where('deleted_at', 'is', null)
      .select(['id', 'user_id'])
      .executeTakeFirst();

    if (!affiliate) {
      throw new NotFoundException('Afiliado não encontrado ou não está ativo');
    }

    const offset = (page - 1) * limit;

    // Buscar transações recentes relacionadas às comissões do afiliado
    // Removido o LEFT JOIN com users_subscriptions que estava causando duplicatas
    const transactions = await db
      .selectFrom('affiliate_commissions as ac')
      .innerJoin('transactions as t', 'ac.transaction_id', 't.id')
      .leftJoin('users as u', 'ac.user_id', 'u.id')
      .leftJoin('plans as p', 'ac.plan_id', 'p.id')
      .where('ac.aff_user_id', '=', userId)
      .select([
        'ac.id as commission_id',
        'ac.commission_value',
        'ac.commission_percent',
        'ac.status as commission_status',
        'ac.created_at as commission_date',
        't.id as transaction_id',
        't.amount as transaction_amount',
        't.currency as transaction_currency',
        't.status as transaction_status',
        't.created_at as transaction_date',
        't.provider_transaction_id',
        't.source_id', // Adicionado para buscar a subscription depois
        't.source_type', // Adicionado para confirmar se é subscription
        'u.id as user_id',
        'u.name as user_name',
        'u.email as user_email',
        'p.id as plan_id',
        'p.name as plan_name',
        'p.price as plan_price'
      ])
      .orderBy('t.created_at', 'desc')
      .limit(limit)
      .offset(offset)
      .execute();

    // Contar total de transações (sem o LEFT JOIN problemático)
    const totalTransactions = await db
      .selectFrom('affiliate_commissions as ac')
      .innerJoin('transactions as t', 'ac.transaction_id', 't.id')
      .where('ac.aff_user_id', '=', userId)
      .select(sql<number>`COUNT(*)`.as('total'))
      .executeTakeFirst();

    // Buscar informações de subscription para cada transação (apenas se for source_type = 'subscription')
    const subscriptionIds = transactions
      .filter(t => t.source_type === 'subscription' && t.source_id)
      .map(t => t.source_id);

    let subscriptions: Record<number, string> = {};
    
    if (subscriptionIds.length > 0) {
      const subscriptionData = await db
        .selectFrom('users_subscriptions')
        .where('id', 'in', subscriptionIds)
        .select(['id', 'status'])
        .execute();

      subscriptions = subscriptionData.reduce((acc, sub) => {
        if (sub.id && sub.status) {
          acc[sub.id] = sub.status;
        }
        return acc;
      }, {} as Record<number, string>);
    }

    // Processar dados das transações
    const processedTransactions = transactions.map(transaction => {
      // Buscar status da subscription se disponível
      let subscriptionStatus: string | null = null;
      if (transaction.source_type === 'subscription' && transaction.source_id) {
        subscriptionStatus = subscriptions[transaction.source_id] || null;
      }

      return {
        commission: {
          id: transaction.commission_id,
          value: Number(transaction.commission_value || 0),
          percent: Number(transaction.commission_percent || 0),
          status: transaction.commission_status,
          date: transaction.commission_date
        },
        transaction: {
          id: transaction.transaction_id,
          amount: Number(transaction.transaction_amount || 0),
          currency: transaction.transaction_currency || 'BRL',
          status: transaction.transaction_status,
          date: transaction.transaction_date,
          provider_id: transaction.provider_transaction_id,
          source_type: transaction.source_type,
          source_id: transaction.source_id
        },
        customer: {
          id: transaction.user_id,
          name: transaction.user_name,
          email: transaction.user_email
        },
        plan: {
          id: transaction.plan_id,
          name: transaction.plan_name,
          price: Number(transaction.plan_price || 0)
        },
        subscription_status: subscriptionStatus
      };
    });

    const totalPages = Math.ceil(Number(totalTransactions?.total || 0) / limit);

    // Calcular estatísticas das transações da página atual
    const pageStats = {
      total_commission_value: processedTransactions.reduce((sum, t) => sum + t.commission.value, 0),
      total_transaction_value: processedTransactions.reduce((sum, t) => sum + t.transaction.amount, 0),
      paid_commissions: processedTransactions.filter(t => t.commission.status === 'paid').length,
      pending_commissions: processedTransactions.filter(t => t.commission.status === 'pending').length,
      successful_transactions: processedTransactions.filter(t => t.transaction.status === 'paid').length
    };

    console.log(`Encontradas ${processedTransactions.length} transações para a página ${page}`);
    console.log(`Total de transações no banco: ${totalTransactions?.total}`);

    return {
      status: 'success',
      data: {
        transactions: processedTransactions,
        page_stats: pageStats,
        pagination: {
          current_page: page,
          total_pages: totalPages,
          total_items: Number(totalTransactions?.total || 0),
          items_per_page: limit,
          has_next: page < totalPages,
          has_previous: page > 1
        }
      }
    };

  } catch (error) {
    console.error('Erro ao buscar transações recentes:', error);
    throw error;
  }
}

  async getRecentTransactionsOld(userId: number, page: number = 1, limit: number = 10) {
    try {
      console.log(`Buscando transações recentes para o afiliado ${userId}, página ${page}, limite ${limit}`);

      // Verificar se o usuário é um afiliado ativo
      const affiliate = await db.selectFrom('affiliates')
        .where('user_id', '=', userId)
        .where('status', '=', 'active')
        .where('deleted_at', 'is', null)
        .select(['id', 'user_id'])
        .executeTakeFirst();

      if (!affiliate) {
        throw new NotFoundException('Afiliado não encontrado ou não está ativo');
      }

      const offset = (page - 1) * limit;

      // Buscar transações recentes relacionadas às comissões do afiliado
      const transactions = await db
        .selectFrom('affiliate_commissions as ac')
        .innerJoin('transactions as t', 'ac.transaction_id', 't.id')
        .leftJoin('users as u', 'ac.user_id', 'u.id')
        .leftJoin('plans as p', 'ac.plan_id', 'p.id')
        .leftJoin('users_subscriptions as us', 'us.user_id', 'u.id')
        .where('ac.aff_user_id', '=', userId)
        .select([
          'ac.id as commission_id',
          'ac.commission_value',
          'ac.commission_percent',
          'ac.status as commission_status',
          'ac.created_at as commission_date',
          't.id as transaction_id',
          't.amount as transaction_amount',
          't.currency as transaction_currency',
          't.status as transaction_status',
          't.created_at as transaction_date',
          't.provider_transaction_id',
          'u.id as user_id',
          'u.name as user_name',
          'u.email as user_email',
          'p.id as plan_id',
          'p.name as plan_name',
          'p.price as plan_price',
          'us.status as subscription_status'
        ])
        .orderBy('t.created_at', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      // Contar total de transações
      const totalTransactions = await db
        .selectFrom('affiliate_commissions as ac')
        .innerJoin('transactions as t', 'ac.transaction_id', 't.id')
        .where('ac.aff_user_id', '=', userId)
        .select(sql<number>`COUNT(*)`.as('total'))
        .executeTakeFirst();

      // Processar dados das transações
      const processedTransactions = transactions.map(transaction => {
        return {
          commission: {
            id: transaction.commission_id,
            value: Number(transaction.commission_value || 0),
            percent: Number(transaction.commission_percent || 0),
            status: transaction.commission_status,
            date: transaction.commission_date
          },
          transaction: {
            id: transaction.transaction_id,
            amount: Number(transaction.transaction_amount || 0),
            currency: transaction.transaction_currency || 'BRL',
            status: transaction.transaction_status,
            date: transaction.transaction_date,
            provider_id: transaction.provider_transaction_id
          },
          customer: {
            id: transaction.user_id,
            name: transaction.user_name,
            email: transaction.user_email
          },
          plan: {
            id: transaction.plan_id,
            name: transaction.plan_name,
            price: Number(transaction.plan_price || 0)
          },
          subscription_status: transaction.subscription_status
        };
      });

      const totalPages = Math.ceil(Number(totalTransactions?.total || 0) / limit);

      // Calcular estatísticas das transações da página atual
      const pageStats = {
        total_commission_value: processedTransactions.reduce((sum, t) => sum + t.commission.value, 0),
        total_transaction_value: processedTransactions.reduce((sum, t) => sum + t.transaction.amount, 0),
        paid_commissions: processedTransactions.filter(t => t.commission.status === 'paid').length,
        pending_commissions: processedTransactions.filter(t => t.commission.status === 'pending').length,
        successful_transactions: processedTransactions.filter(t => t.transaction.status === 'paid').length
      };

      return {
        status: 'success',
        data: {
          transactions: processedTransactions,
          page_stats: pageStats,
          pagination: {
            current_page: page,
            total_pages: totalPages,
            total_items: Number(totalTransactions?.total || 0),
            items_per_page: limit,
            has_next: page < totalPages,
            has_previous: page > 1
          }
        }
      };

    } catch (error) {
      console.error('Erro ao buscar transações recentes:', error);
      throw error;
    }
  }

}