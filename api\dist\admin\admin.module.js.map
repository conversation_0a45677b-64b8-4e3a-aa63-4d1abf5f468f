{"version": 3, "sources": ["../../src/admin/admin.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { AdminService } from './admin.service';\r\nimport { AdminController } from './admin.controller';\r\n\r\n@Module({\r\n  imports: [\r\n  ],\r\n  controllers: [AdminController],\r\n  providers: [AdminService],\r\n})\r\nexport class AdminModule {}\r\n"], "names": ["AdminModule", "imports", "controllers", "AdminController", "providers", "AdminService"], "mappings": ";;;;+BAUaA;;;eAAAA;;;wBAVU;8BACM;iCACG;;;;;;;AAQzB,IAAA,AAAMA,cAAN,MAAMA;AAAa;;;QALxBC,SAAS,EACR;QACDC,aAAa;YAACC,gCAAe;SAAC;QAC9BC,WAAW;YAACC,0BAAY;SAAC"}