require('dotenv').config();

console.log('🔍 Verificando configuração OAuth...\n');

// Verificar variáveis de ambiente
console.log('📋 Variáveis de Ambiente:');
console.log(`API_URL: ${process.env.API_URL}`);
console.log(`GOOGLE_REDIRECT_URL: ${process.env.GOOGLE_REDIRECT_URL}`);
console.log(`APPLE_REDIRECT_URL: ${process.env.APPLE_REDIRECT_URL}`);
console.log(`GOOGLE_CLIENT_ID: ${process.env.GOOGLE_CLIENT_ID ? 'Configurado' : 'NÃO CONFIGURADO'}`);
console.log(`GOOGLE_CLIENT_SECRET: ${process.env.GOOGLE_CLIENT_SECRET ? 'Configurado' : 'NÃO CONFIGURADO'}`);
console.log(`APPLE_CLIENT_ID: ${process.env.APPLE_CLIENT_ID ? 'Configurado' : 'NÃO CONFIGURADO'}`);
console.log(`APPLE_TEAM_ID: ${process.env.APPLE_TEAM_ID ? 'Configurado' : 'NÃO CONFIGURADO'}`);
console.log(`APPLE_KEY_ID: ${process.env.APPLE_KEY_ID ? 'Configurado' : 'NÃO CONFIGURADO'}`);
console.log(`APPLE_PRIVATE_KEY: ${process.env.APPLE_PRIVATE_KEY ? 'Configurado' : 'NÃO CONFIGURADO'}`);

console.log('\n🔗 URLs OAuth:');
console.log(`Google OAuth URL: ${process.env.API_URL}/auth/google`);
console.log(`Apple OAuth URL: ${process.env.API_URL}/auth/apple`);
console.log(`Google Callback URL: ${process.env.API_URL}/auth/google/callback`);
console.log(`Apple Callback URL: ${process.env.API_URL}/auth/apple/callback`);
console.log(`Google Frontend Redirect: ${process.env.GOOGLE_REDIRECT_URL}`);
console.log(`Apple Frontend Redirect: ${process.env.APPLE_REDIRECT_URL}`);

console.log('\n✅ Configurações necessárias para produção:');
console.log('1. API_URL deve ser: https://api.mysnapfit.com.br');
console.log('2. GOOGLE_REDIRECT_URL deve ser: https://mysnapfit.com.br/auth/callback');
console.log('3. APPLE_REDIRECT_URL deve ser: https://mysnapfit.com.br/auth/callback');
console.log('4. Google OAuth deve ter redirect URI: https://api.mysnapfit.com.br/auth/google/callback');
console.log('5. Apple OAuth deve ter redirect URI: https://api.mysnapfit.com.br/auth/apple/callback');

console.log('\n🚨 Problemas encontrados:');
const issues = [];

if (process.env.API_URL === 'http://localhost:3000') {
  issues.push('API_URL está configurado para localhost em vez de produção');
}

if (process.env.GOOGLE_REDIRECT_URL && process.env.GOOGLE_REDIRECT_URL.includes('localhost')) {
  issues.push('GOOGLE_REDIRECT_URL está configurado para localhost em vez de produção');
}

if (process.env.APPLE_REDIRECT_URL && process.env.APPLE_REDIRECT_URL.includes('localhost')) {
  issues.push('APPLE_REDIRECT_URL está configurado para localhost em vez de produção');
}

if (!process.env.GOOGLE_CLIENT_ID || process.env.GOOGLE_CLIENT_ID === 'your_google_client_id_here') {
  issues.push('Google Client ID não está configurado');
}

if (!process.env.GOOGLE_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET === 'your_google_client_secret_here') {
  issues.push('Google Client Secret não está configurado');
}

if (issues.length === 0) {
  console.log('✅ Nenhum problema encontrado!');
} else {
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ❌ ${issue}`);
  });
}

console.log('\n📝 Próximos passos:');
console.log('1. Configure as credenciais reais do Google OAuth');
console.log('2. Verifique se as URLs de redirect estão corretas nos consoles OAuth');
console.log('3. Teste o fluxo OAuth completo');
console.log('4. Reinicie o servidor após as alterações');
