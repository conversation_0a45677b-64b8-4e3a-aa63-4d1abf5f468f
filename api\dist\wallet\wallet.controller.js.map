{"version": 3, "sources": ["../../src/wallet/wallet.controller.ts"], "sourcesContent": ["import { Controller, Get, Post, Body, UseGuards, Request } from '@nestjs/common';\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\nimport { WalletService } from './wallet.service';\n\n@Controller('wallet')\n@UseGuards(JwtAuthGuard)\nexport class WalletController {\n  constructor(private readonly walletService: WalletService) {}\n\n  @Get()\n  async getUserWallet(@Request() req: any) {\n    const userId = req.user.userId;\n    return this.walletService.getUserWallet(userId);\n  }\n\n  @Post('earn-coins')\n  async earnSnapCoins(\n    @Body() body: { amount: number; reason: string; category?: string },\n    @Request() req: any\n  ) {\n    const userId = req.user.userId;\n    return this.walletService.earnSnapCoins(userId, body.amount, body.reason, body.category);\n  }\n\n  @Post('spend-coins')\n  async spendSnapCoins(\n    @Body() body: { amount: number; reason: string; category?: string },\n    @Request() req: any\n  ) {\n    const userId = req.user.userId;\n    return this.walletService.spendSnapCoins(userId, body.amount, body.reason, body.category);\n  }\n\n  @Post('use-tokens')\n  async useSnapTokens(\n    @Body() body: { amount: number; feature: string },\n    @Request() req: any\n  ) {\n    const userId = req.user.userId;\n    return this.walletService.useSnapTokens(userId, body.amount, body.feature);\n  }\n\n  @Get('can-use-feature/:feature')\n  async canUseFeature(\n    @Request() req: any,\n    @Body() body: { feature: string }\n  ) {\n    const userId = req.user.userId;\n    return this.walletService.canUseFeature(userId, body.feature);\n  }\n}\n"], "names": ["WalletController", "getUserWallet", "req", "userId", "user", "walletService", "earnSnapCoins", "body", "amount", "reason", "category", "spendSnapCoins", "useSnapTokens", "feature", "canUseFeature", "constructor"], "mappings": ";;;;+BAMaA;;;eAAAA;;;wBANmD;8BACnC;+BACC;;;;;;;;;;;;;;;AAIvB,IAAA,AAAMA,mBAAN,MAAMA;IAGX,MACMC,cAAc,AAAWC,GAAQ,EAAE;QACvC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACJ,aAAa,CAACE;IAC1C;IAEA,MACMG,cACJ,AAAQC,IAA2D,EACnE,AAAWL,GAAQ,EACnB;QACA,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACC,aAAa,CAACH,QAAQI,KAAKC,MAAM,EAAED,KAAKE,MAAM,EAAEF,KAAKG,QAAQ;IACzF;IAEA,MACMC,eACJ,AAAQJ,IAA2D,EACnE,AAAWL,GAAQ,EACnB;QACA,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACM,cAAc,CAACR,QAAQI,KAAKC,MAAM,EAAED,KAAKE,MAAM,EAAEF,KAAKG,QAAQ;IAC1F;IAEA,MACME,cACJ,AAAQL,IAAyC,EACjD,AAAWL,GAAQ,EACnB;QACA,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACO,aAAa,CAACT,QAAQI,KAAKC,MAAM,EAAED,KAAKM,OAAO;IAC3E;IAEA,MACMC,cACJ,AAAWZ,GAAQ,EACnB,AAAQK,IAAyB,EACjC;QACA,MAAMJ,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,OAAO,IAAI,CAACE,aAAa,CAACS,aAAa,CAACX,QAAQI,KAAKM,OAAO;IAC9D;IA1CAE,YAAY,AAAiBV,aAA4B,CAAE;aAA9BA,gBAAAA;IAA+B;AA2C9D"}