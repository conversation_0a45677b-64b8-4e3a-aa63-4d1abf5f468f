import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { db } from '../database';

@Injectable()
export class NotificationsService {

  async getNotifications(userId: number, query: any) {
    try {
      const { page = 1, limit = 20, type, read } = query;
      const limitNum = parseInt(limit, 10) || 20;
      const pageNum = parseInt(page, 10) || 1;
      const offset = (pageNum - 1) * limitNum;

      let sql = `
        SELECT 
          id,
          type,
          title,
          message,
          data,
          read_at as readAt,
          created_at as createdAt
        FROM notifications 
        WHERE user_id = ?
      `;

      const params = [userId];

      if (type) {
        sql += ` AND type = ?`;
        params.push(type);
      }

      if (read !== undefined) {
        if (read === 'true') {
          sql += ` AND read_at IS NOT NULL`;
        } else {
          sql += ` AND read_at IS NULL`;
        }
      }

      sql += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
      params.push(limitNum, offset);

      const [notifications] = await db.execute(sql, params);

      // Check if notifications is valid
      if (!notifications || !Array.isArray(notifications)) {
        console.log('No notifications or invalid notifications format:', notifications);
        return {
          status: 'success',
          data: {
            notifications: [],
            pagination: {
              page: pageNum,
              limit: limitNum,
              total: 0,
              totalPages: 0
            }
          }
        };
      }

      // Get total count
      let countSql = `SELECT COUNT(*) as total FROM notifications WHERE user_id = ?`;
      const countParams = [userId];

      if (type) {
        countSql += ` AND type = ?`;
        countParams.push(type);
      }

      if (read !== undefined) {
        if (read === 'true') {
          countSql += ` AND read_at IS NOT NULL`;
        } else {
          countSql += ` AND read_at IS NULL`;
        }
      }

      const [countResult] = await db.execute(countSql, countParams);
      const total = countResult[0]?.total || 0;

      return {
        status: 'success',
        data: {
          notifications: notifications.map(notification => ({
            ...notification,
            data: notification.data ? JSON.parse(notification.data) : null,
            isRead: !!notification.readAt
          })),
          pagination: {
            page: pageNum,
            limit: limitNum,
            total,
            totalPages: Math.ceil(total / limitNum)
          }
        }
      };
    } catch (error) {
      console.error('Error getting notifications:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get notifications'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getUnreadCount(userId: number) {
    try {
      const [result] = await db.execute(
        'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND read_at IS NULL',
        [userId]
      );

      return {
        status: 'success',
        data: {
          unreadCount: result[0]?.count || 0
        }
      };
    } catch (error) {
      console.error('Error getting unread count:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get unread count'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async markAsRead(notificationIds: number[], userId: number) {
    try {
      if (!notificationIds || notificationIds.length === 0) {
        throw new HttpException({
          status: 'error',
          message: 'No notification IDs provided'
        }, HttpStatus.BAD_REQUEST);
      }

      const placeholders = notificationIds.map(() => '?').join(',');
      await db.execute(
        `UPDATE notifications 
         SET read_at = NOW() 
         WHERE id IN (${placeholders}) AND user_id = ? AND read_at IS NULL`,
        [...notificationIds, userId]
      );

      return {
        status: 'success',
        message: 'Notifications marked as read'
      };
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      if (error instanceof HttpException) throw error;
      
      throw new HttpException({
        status: 'error',
        message: 'Failed to mark notifications as read'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async markAllAsRead(userId: number) {
    try {
      await db.execute(
        'UPDATE notifications SET read_at = NOW() WHERE user_id = ? AND read_at IS NULL',
        [userId]
      );

      return {
        status: 'success',
        message: 'All notifications marked as read'
      };
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to mark all notifications as read'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getNotificationSettings(userId: number) {
    try {
      const [settings] = await db.execute(
        `SELECT 
          push_enabled,
          email_enabled,
          workout_reminders,
          meal_reminders,
          progress_updates,
          social_notifications,
          marketing_emails
         FROM notification_settings 
         WHERE user_id = ?`,
        [userId]
      );

      // Return default settings if none exist
      const defaultSettings = {
        pushEnabled: true,
        emailEnabled: true,
        workoutReminders: true,
        mealReminders: true,
        progressUpdates: true,
        socialNotifications: true,
        marketingEmails: false
      };

      const userSettings = settings[0] ? {
        pushEnabled: !!settings[0].push_enabled,
        emailEnabled: !!settings[0].email_enabled,
        workoutReminders: !!settings[0].workout_reminders,
        mealReminders: !!settings[0].meal_reminders,
        progressUpdates: !!settings[0].progress_updates,
        socialNotifications: !!settings[0].social_notifications,
        marketingEmails: !!settings[0].marketing_emails
      } : defaultSettings;

      return {
        status: 'success',
        data: userSettings
      };
    } catch (error) {
      console.error('Error getting notification settings:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to get notification settings'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateNotificationSettings(userId: number, settings: any) {
    try {
      const {
        pushEnabled,
        emailEnabled,
        workoutReminders,
        mealReminders,
        progressUpdates,
        socialNotifications,
        marketingEmails
      } = settings;

      // Check if settings exist
      const [existing] = await db.execute(
        'SELECT id FROM notification_settings WHERE user_id = ?',
        [userId]
      );

      if (existing[0]) {
        // Update existing settings
        await db.execute(
          `UPDATE notification_settings SET
           push_enabled = ?,
           email_enabled = ?,
           workout_reminders = ?,
           meal_reminders = ?,
           progress_updates = ?,
           social_notifications = ?,
           marketing_emails = ?,
           updated_at = NOW()
           WHERE user_id = ?`,
          [
            pushEnabled, emailEnabled, workoutReminders, mealReminders,
            progressUpdates, socialNotifications, marketingEmails, userId
          ]
        );
      } else {
        // Create new settings
        await db.execute(
          `INSERT INTO notification_settings (
           user_id, push_enabled, email_enabled, workout_reminders,
           meal_reminders, progress_updates, social_notifications,
           marketing_emails, created_at, updated_at
           ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
          [
            userId, pushEnabled, emailEnabled, workoutReminders,
            mealReminders, progressUpdates, socialNotifications, marketingEmails
          ]
        );
      }

      return {
        status: 'success',
        message: 'Notification settings updated successfully'
      };
    } catch (error) {
      console.error('Error updating notification settings:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to update notification settings'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async subscribeToPushNotifications(userId: number, subscription: any) {
    try {
      const { endpoint, keys } = subscription;

      // Check if subscription already exists
      const [existing] = await db.execute(
        'SELECT id FROM push_subscriptions WHERE user_id = ? AND endpoint = ?',
        [userId, endpoint]
      );

      if (!existing[0]) {
        await db.execute(
          `INSERT INTO push_subscriptions (user_id, endpoint, p256dh_key, auth_key, created_at, updated_at)
           VALUES (?, ?, ?, ?, NOW(), NOW())`,
          [userId, endpoint, keys.p256dh, keys.auth]
        );
      }

      return {
        status: 'success',
        message: 'Successfully subscribed to push notifications'
      };
    } catch (error) {
      console.error('Error subscribing to push notifications:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to subscribe to push notifications'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async unsubscribeFromPushNotifications(userId: number, endpoint: string) {
    try {
      await db.execute(
        'DELETE FROM push_subscriptions WHERE user_id = ? AND endpoint = ?',
        [userId, endpoint]
      );

      return {
        status: 'success',
        message: 'Successfully unsubscribed from push notifications'
      };
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to unsubscribe from push notifications'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async sendTestNotification(userId: number) {
    try {
      await this.createNotification(userId, {
        type: 'test',
        title: 'Notificação de Teste',
        message: 'Esta é uma notificação de teste para verificar se o sistema está funcionando corretamente.',
        data: {
          testData: true,
          timestamp: new Date().toISOString()
        }
      });

      return {
        status: 'success',
        message: 'Test notification sent successfully'
      };
    } catch (error) {
      console.error('Error sending test notification:', error);
      throw new HttpException({
        status: 'error',
        message: 'Failed to send test notification'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // Helper method to create notifications
  async createNotification(userId: number, notification: {
    type: string;
    title: string;
    message: string;
    data?: any;
  }) {
    try {
      await db.execute(
        `INSERT INTO notifications (user_id, type, title, message, data, created_at)
         VALUES (?, ?, ?, ?, ?, NOW())`,
        [
          userId,
          notification.type,
          notification.title,
          notification.message,
          notification.data ? JSON.stringify(notification.data) : null
        ]
      );

      // Here you would also trigger push notifications if enabled
      // await this.sendPushNotification(userId, notification);

      return true;
    } catch (error) {
      console.error('Error creating notification:', error);
      return false;
    }
  }

  // Helper method for sending push notifications (to be implemented)
  private async sendPushNotification(userId: number, notification: any) {
    // Implementation for sending actual push notifications
    // This would use web-push library or similar
    console.log('Push notification would be sent:', { userId, notification });
  }
}
