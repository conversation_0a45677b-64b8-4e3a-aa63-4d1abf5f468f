const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkNotificationsTable() {
  let connection;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });

    console.log('Connected to database successfully');

    // Show all tables first
    const [allTables] = await connection.execute('SHOW TABLES');
    console.log('Available tables:');
    allTables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });

    // Check if notifications table exists
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'notifications'"
    );

    if (tables.length === 0) {
      console.log('\n❌ Table "notifications" does NOT exist');

      // Create the notifications table
      console.log('\n🔧 Creating notifications table...');
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS notifications (
          id INT PRIMARY KEY AUTO_INCREMENT,
          user_id INT NOT NULL,
          type VARCHAR(100) NOT NULL,
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          data JSON,
          read_at TIMESTAMP NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_user_unread (user_id, read_at),
          INDEX idx_type (type),
          INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      console.log('✅ Notifications table created successfully');

    } else {
      console.log('\n✅ Table "notifications" exists');

      // Check table structure
      const [columns] = await connection.execute(
        "DESCRIBE notifications"
      );

      console.log('Table structure:');
      columns.forEach(column => {
        console.log(`  - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(nullable)' : '(not null)'}`);
      });

      // Check if there are any records
      const [count] = await connection.execute(
        'SELECT COUNT(*) as total FROM notifications'
      );

      console.log(`Total notifications: ${count[0].total}`);
    }

    // Check users table
    console.log('\n👤 Checking users table...');
    const [userTables] = await connection.execute("SHOW TABLES LIKE 'users'");

    if (userTables.length > 0) {
      const [userCount] = await connection.execute('SELECT COUNT(*) as total FROM users');
      console.log(`Total users: ${userCount[0].total}`);

      if (userCount[0].total > 0) {
        const [sampleUsers] = await connection.execute('SELECT id, email, name FROM users LIMIT 3');
        console.log('Sample users:');
        sampleUsers.forEach(user => {
          console.log(`  - ID: ${user.id}, Email: ${user.email}, Name: ${user.name || 'N/A'}`);
        });
      }
    } else {
      console.log('❌ Users table does not exist');
    }

  } catch (error) {
    console.error('❌ Database error:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

checkNotificationsTable();
