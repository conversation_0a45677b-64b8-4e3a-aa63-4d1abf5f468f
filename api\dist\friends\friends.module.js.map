{"version": 3, "sources": ["../../src/friends/friends.module.ts"], "sourcesContent": ["import { <PERSON>du<PERSON> } from '@nestjs/common';\nimport { FriendsController } from './friends.controller';\nimport { FriendsService } from './friends.service';\n\n@Module({\n  controllers: [FriendsController],\n  providers: [FriendsService],\n  exports: [FriendsService],\n})\nexport class FriendsModule {}\n"], "names": ["FriendsModule", "controllers", "FriendsController", "providers", "FriendsService", "exports"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATU;mCACW;gCACH;;;;;;;AAOxB,IAAA,AAAMA,gBAAN,MAAMA;AAAe;;;QAJ1BC,aAAa;YAACC,oCAAiB;SAAC;QAChCC,WAAW;YAACC,8BAAc;SAAC;QAC3BC,SAAS;YAACD,8BAAc;SAAC"}