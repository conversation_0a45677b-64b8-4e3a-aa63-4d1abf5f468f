-- Create test workout protocol for demo user (ID: 72)
-- This will fix the issue where the frontend shows "no active protocol"

-- First, let's check if we need to create a type_id for workout protocols
-- Insert a workout protocol type if it doesn't exist
INSERT IGNORE INTO select_options (value_option, description, created_at, updated_at) 
VALUES ('Hipertrofia', 'Protocolo focado em ganho de massa muscular', NOW(), NOW());

-- Get the type_id for Hipertrofia
SET @type_id = (SELECT id FROM select_options WHERE value_option = 'Hipertrofia' LIMIT 1);

-- Create a test workout protocol for user ID 72 (demo user)
INSERT INTO coach_protocols (
    client_id,
    user_id,
    name,
    type_id,
    split,
    frequency,
    objective,
    general_notes,
    started_at,
    ended_at,
    created_at,
    updated_at
) VALUES (
    72, -- client_id (demo user)
    NULL, -- user_id (coach - can be null for demo)
    'Protocolo de Hipertrofia Demo',
    @type_id,
    'ABCDE',
    5,
    'Gan<PERSON> de <PERSON>a muscular e força',
    'Protocolo de demonstração focado em hipertrofia com divisão ABCDE. Foco em exercícios compostos e tempo sob tensão.',
    '2024-12-01', -- started_at (recent date)
    NULL, -- ended_at (null = active)
    NOW(),
    NOW()
);

-- Get the protocol ID
SET @protocol_id = LAST_INSERT_ID();

-- Create workout days for the protocol
INSERT INTO coach_protocols_workouts (protocol_id, name, general_notes, created_at, updated_at) VALUES
(@protocol_id, 'Treino A - Peito e Tríceps', 'Foco em exercícios de empurrar para peito e tríceps', NOW(), NOW()),
(@protocol_id, 'Treino B - Costas e Bíceps', 'Foco em exercícios de puxar para costas e bíceps', NOW(), NOW()),
(@protocol_id, 'Treino C - Pernas', 'Treino completo de membros inferiores', NOW(), NOW()),
(@protocol_id, 'Treino D - Ombros', 'Foco em desenvolvimento dos deltoides', NOW(), NOW()),
(@protocol_id, 'Treino E - Braços', 'Treino específico para bíceps e tríceps', NOW(), NOW());

-- Get workout IDs
SET @workout_a_id = (SELECT id FROM coach_protocols_workouts WHERE protocol_id = @protocol_id AND name LIKE 'Treino A%' LIMIT 1);
SET @workout_b_id = (SELECT id FROM coach_protocols_workouts WHERE protocol_id = @protocol_id AND name LIKE 'Treino B%' LIMIT 1);
SET @workout_c_id = (SELECT id FROM coach_protocols_workouts WHERE protocol_id = @protocol_id AND name LIKE 'Treino C%' LIMIT 1);

-- Create some sample exercises (we'll need to check if exercises exist first)
-- Let's assume some basic exercise IDs exist, or create them

-- Insert basic exercises if they don't exist
INSERT IGNORE INTO exercises (name, muscle_group, equipment, instructions, created_at, updated_at) VALUES
('Supino Reto', 'Peito', 'Barra e Banco', 'Exercício básico para desenvolvimento do peitoral', NOW(), NOW()),
('Remada Curvada', 'Costas', 'Barra', 'Exercício para desenvolvimento das costas', NOW(), NOW()),
('Agachamento', 'Pernas', 'Barra', 'Exercício fundamental para pernas', NOW(), NOW()),
('Desenvolvimento Militar', 'Ombros', 'Barra', 'Exercício para ombros', NOW(), NOW()),
('Rosca Direta', 'Bíceps', 'Barra', 'Exercício para bíceps', NOW(), NOW());

-- Get exercise IDs
SET @supino_id = (SELECT id FROM exercises WHERE name = 'Supino Reto' LIMIT 1);
SET @remada_id = (SELECT id FROM exercises WHERE name = 'Remada Curvada' LIMIT 1);
SET @agachamento_id = (SELECT id FROM exercises WHERE name = 'Agachamento' LIMIT 1);
SET @desenvolvimento_id = (SELECT id FROM exercises WHERE name = 'Desenvolvimento Militar' LIMIT 1);
SET @rosca_id = (SELECT id FROM exercises WHERE name = 'Rosca Direta' LIMIT 1);

-- Create workout exercises for Treino A (Peito e Tríceps)
INSERT INTO coach_protocols_workouts_exercises (
    workout_id,
    exercise_id,
    sets,
    reps,
    rpe,
    rest_seconds,
    notes,
    created_at,
    updated_at
) VALUES
(@workout_a_id, @supino_id, 4, 12, 8, 90, 'Foco na contração e tempo sob tensão', NOW(), NOW()),
(@workout_a_id, @supino_id, 3, 10, 7, 90, 'Supino inclinado com halteres', NOW(), NOW());

-- Create workout exercises for Treino B (Costas e Bíceps)
INSERT INTO coach_protocols_workouts_exercises (
    workout_id,
    exercise_id,
    sets,
    reps,
    rpe,
    rest_seconds,
    notes,
    created_at,
    updated_at
) VALUES
(@workout_b_id, @remada_id, 4, 10, 8, 90, 'Manter postura ereta', NOW(), NOW()),
(@workout_b_id, @rosca_id, 3, 12, 7, 60, 'Movimento controlado', NOW(), NOW());

-- Create workout exercises for Treino C (Pernas)
INSERT INTO coach_protocols_workouts_exercises (
    workout_id,
    exercise_id,
    sets,
    reps,
    rpe,
    rest_seconds,
    notes,
    created_at,
    updated_at
) VALUES
(@workout_c_id, @agachamento_id, 4, 15, 8, 120, 'Descer até paralelo', NOW(), NOW());

-- Show the created protocol
SELECT 
    cp.id,
    cp.name,
    cp.split,
    cp.frequency,
    cp.objective,
    cp.started_at,
    cp.ended_at,
    so.value_option as type
FROM coach_protocols cp
LEFT JOIN select_options so ON so.id = cp.type_id
WHERE cp.client_id = 72;

-- Show the workouts
SELECT 
    cpw.id,
    cpw.name,
    cpw.general_notes,
    cp.name as protocol_name
FROM coach_protocols_workouts cpw
JOIN coach_protocols cp ON cp.id = cpw.protocol_id
WHERE cp.client_id = 72;

-- Show the exercises
SELECT 
    cpwe.id,
    e.name as exercise_name,
    cpwe.sets,
    cpwe.reps,
    cpwe.rpe,
    cpwe.rest_seconds,
    cpwe.notes,
    cpw.name as workout_name
FROM coach_protocols_workouts_exercises cpwe
JOIN coach_protocols_workouts cpw ON cpw.id = cpwe.workout_id
JOIN coach_protocols cp ON cp.id = cpw.protocol_id
JOIN exercises e ON e.id = cpwe.exercise_id
WHERE cp.client_id = 72;
