import { IsString, <PERSON>NotEmpty, <PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ber, Min, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class ConfigPlanDto {
  /*
  @IsInt()
  @Type(() => Number)
  plan_id: number; // ID do plano
  */

  /*
  @IsInt()
  @Type(() => Number)
  payment_provider_id: number; // ID do provedor de pagamento
  */

  @IsString()
  payment_provider: string; // Nome do provedor de pagamento

  @IsString()
  @IsOptional()
  platform: 'web' | 'android' | 'ios'; // Plataforma, com valor padrão 'web'

  @IsNumber()
  @Min(0)
  @IsOptional()
  price?: number; // Preço customizado

  @IsString()
  @IsOptional()
  currency?: string; // Moeda

  @IsNumber()
  @Min(0)
  @IsOptional()
  snaptokens?: number; // Snaptokens customizados

  @IsString()
  @IsOptional()
  payment_provider_external_id?: string; // Identificadores externos
}
