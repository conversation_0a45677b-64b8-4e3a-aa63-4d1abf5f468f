{"version": 3, "sources": ["../../src/wearables/wearables.service.ts"], "sourcesContent": ["import { Injectable, HttpException, HttpStatus } from '@nestjs/common';\nimport { db } from '../database';\nimport axios from 'axios';\n\n@Injectable()\nexport class WearablesService {\n\n  async getConnectedDevices(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          id,\n          device_type as deviceType,\n          device_name as deviceName,\n          brand,\n          model,\n          status,\n          last_sync as lastSync,\n          sync_frequency as syncFrequency,\n          connected_at as connectedAt,\n          access_token_expires as accessTokenExpires\n        FROM user_wearables \n        WHERE user_id = ? AND status = 'connected'\n        ORDER BY connected_at DESC\n      `;\n\n      const [devices] = await db.execute(sql, [userId]);\n\n      return {\n        status: 'success',\n        data: devices\n      };\n    } catch (error) {\n      console.error('Error getting connected devices:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get connected devices'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getSupportedDevices() {\n    try {\n      const supportedDevices = [\n        {\n          id: 'fitbit',\n          name: 'Fitbit',\n          brand: 'Fitbit',\n          types: ['fitness_tracker', 'smartwatch'],\n          features: ['steps', 'heart_rate', 'sleep', 'calories', 'distance'],\n          oauth_required: true,\n          icon: 'fitbit-icon.png'\n        },\n        {\n          id: 'garmin',\n          name: 'Garmin',\n          brand: 'Garmin',\n          types: ['fitness_tracker', 'smartwatch', 'gps_watch'],\n          features: ['steps', 'heart_rate', 'sleep', 'calories', 'distance', 'gps'],\n          oauth_required: true,\n          icon: 'garmin-icon.png'\n        },\n        {\n          id: 'apple_health',\n          name: 'Apple Health',\n          brand: 'Apple',\n          types: ['health_app'],\n          features: ['steps', 'heart_rate', 'sleep', 'calories', 'workouts'],\n          oauth_required: true,\n          icon: 'apple-health-icon.png'\n        },\n        {\n          id: 'google_fit',\n          name: 'Google Fit',\n          brand: 'Google',\n          types: ['health_app'],\n          features: ['steps', 'heart_rate', 'calories', 'workouts'],\n          oauth_required: true,\n          icon: 'google-fit-icon.png'\n        },\n        {\n          id: 'samsung_health',\n          name: 'Samsung Health',\n          brand: 'Samsung',\n          types: ['health_app'],\n          features: ['steps', 'heart_rate', 'sleep', 'calories'],\n          oauth_required: true,\n          icon: 'samsung-health-icon.png'\n        },\n        {\n          id: 'polar',\n          name: 'Polar',\n          brand: 'Polar',\n          types: ['fitness_tracker', 'heart_rate_monitor'],\n          features: ['heart_rate', 'calories', 'workouts'],\n          oauth_required: true,\n          icon: 'polar-icon.png'\n        }\n      ];\n\n      return {\n        status: 'success',\n        data: supportedDevices\n      };\n    } catch (error) {\n      console.error('Error getting supported devices:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get supported devices'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async connectDevice(userId: number, deviceData: any) {\n    try {\n      const {\n        deviceType,\n        deviceName,\n        brand,\n        model,\n        accessToken,\n        refreshToken,\n        expiresIn,\n        syncFrequency = 'hourly'\n      } = deviceData;\n\n      // Check if device is already connected\n      const [existing] = await db.execute(\n        'SELECT id FROM user_wearables WHERE user_id = ? AND device_type = ? AND status = \"connected\"',\n        [userId, deviceType]\n      );\n\n      if (existing[0]) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Device type already connected'\n        }, HttpStatus.CONFLICT);\n      }\n\n      const expiresAt = expiresIn ? \n        new Date(Date.now() + (expiresIn * 1000)).toISOString() : null;\n\n      const [result] = await db.execute(\n        `INSERT INTO user_wearables (\n          user_id, device_type, device_name, brand, model, status,\n          access_token, refresh_token, access_token_expires,\n          sync_frequency, connected_at, created_at, updated_at\n        ) VALUES (?, ?, ?, ?, ?, 'connected', ?, ?, ?, ?, NOW(), NOW(), NOW())`,\n        [\n          userId, deviceType, deviceName, brand, model,\n          accessToken, refreshToken, expiresAt, syncFrequency\n        ]\n      );\n\n      // Trigger initial sync\n      await this.syncData(userId, { deviceId: result.insertId });\n\n      return {\n        status: 'success',\n        message: 'Device connected successfully',\n        data: {\n          id: result.insertId,\n          deviceType,\n          deviceName,\n          status: 'connected'\n        }\n      };\n    } catch (error) {\n      console.error('Error connecting device:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to connect device'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async disconnectDevice(userId: number, deviceId: number) {\n    try {\n      const result = await db.execute(\n        'UPDATE user_wearables SET status = \"disconnected\", updated_at = NOW() WHERE id = ? AND user_id = ?',\n        [deviceId, userId]\n      );\n\n      if (result.affectedRows === 0) {\n        throw new HttpException({\n          status: 'error',\n          message: 'Device not found or not accessible'\n        }, HttpStatus.NOT_FOUND);\n      }\n\n      return {\n        status: 'success',\n        message: 'Device disconnected successfully'\n      };\n    } catch (error) {\n      console.error('Error disconnecting device:', error);\n      if (error instanceof HttpException) throw error;\n      \n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to disconnect device'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getSyncedData(userId: number, query: any) {\n    try {\n      const {\n        startDate,\n        endDate,\n        dataType,\n        deviceId,\n        page = 1,\n        limit = 100,\n        type // New parameter for recovery-specific data\n      } = query;\n\n      // Handle recovery-specific data request\n      if (type === 'recovery') {\n        return this.getRecoveryData(userId, query);\n      }\n\n      const offset = (page - 1) * limit;\n\n      let sql = `\n        SELECT\n          wd.id,\n          wd.data_type as dataType,\n          wd.value,\n          wd.unit,\n          wd.recorded_at as recordedAt,\n          wd.synced_at as syncedAt,\n          uw.device_name as deviceName,\n          uw.brand\n        FROM wearable_data wd\n        INNER JOIN user_wearables uw ON wd.device_id = uw.id\n        WHERE wd.user_id = ?\n      `;\n\n      const params = [userId];\n\n      if (startDate) {\n        sql += ` AND wd.recorded_at >= ?`;\n        params.push(startDate);\n      }\n\n      if (endDate) {\n        sql += ` AND wd.recorded_at <= ?`;\n        params.push(endDate);\n      }\n\n      if (dataType) {\n        sql += ` AND wd.data_type = ?`;\n        params.push(dataType);\n      }\n\n      if (deviceId) {\n        sql += ` AND wd.device_id = ?`;\n        params.push(deviceId);\n      }\n\n      sql += ` ORDER BY wd.recorded_at DESC LIMIT ? OFFSET ?`;\n      params.push(limit, offset);\n\n      const [data] = await db.execute(sql, params);\n\n      return {\n        status: 'success',\n        data: {\n          records: data,\n          pagination: {\n            page: Number(page),\n            limit: Number(limit)\n          }\n        }\n      };\n    } catch (error) {\n      console.error('Error getting synced data:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get synced data'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getRecoveryData(userId: number, query: any) {\n    try {\n      console.log('🔄 Getting recovery data for user:', userId);\n\n      const { period = 'week' } = query;\n\n      // Calculate date range\n      const endDate = new Date();\n      const startDate = new Date();\n\n      switch (period) {\n        case 'week':\n          startDate.setDate(endDate.getDate() - 7);\n          break;\n        case 'month':\n          startDate.setMonth(endDate.getMonth() - 1);\n          break;\n        default:\n          startDate.setDate(endDate.getDate() - 7);\n      }\n\n      // For now, return enhanced mock data that would come from real wearables\n      // In production, this would query actual wearable data from the database\n      const recoveryData = {\n        sleep: {\n          average_duration: 7.2 + (Math.random() - 0.5) * 1.5, // 6.5-8.0 hours\n          quality_score: 75 + Math.random() * 20, // 75-95%\n          deep_sleep_percentage: 20 + Math.random() * 10, // 20-30%\n          rem_sleep_percentage: 15 + Math.random() * 10, // 15-25%\n          efficiency: 80 + Math.random() * 15, // 80-95%\n          trend: Math.random() > 0.5 ? 'improving' : 'stable'\n        },\n        hrv: {\n          average: 35 + Math.random() * 20, // 35-55ms\n          trend: Math.random() > 0.6 ? 'improving' : 'stable',\n          recovery_score: 65 + Math.random() * 25 // 65-90\n        },\n        stress: {\n          average_level: 20 + Math.random() * 40, // 20-60\n          trend: Math.random() > 0.7 ? 'decreasing' : 'stable'\n        },\n        energy: {\n          average_level: 60 + Math.random() * 30, // 60-90\n          trend: Math.random() > 0.6 ? 'improving' : 'stable'\n        },\n        heart_rate: {\n          resting_average: 58 + Math.random() * 12, // 58-70 bpm\n          max_recorded: 175 + Math.random() * 20, // 175-195 bpm\n          zones: {\n            zone1: 20 + Math.random() * 10,\n            zone2: 30 + Math.random() * 10,\n            zone3: 25 + Math.random() * 10,\n            zone4: 10 + Math.random() * 10,\n            zone5: 5 + Math.random() * 5\n          }\n        },\n        period,\n        last_updated: new Date().toISOString()\n      };\n\n      return {\n        status: 'success',\n        data: recoveryData\n      };\n\n    } catch (error) {\n      console.error('Error getting recovery data:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get recovery data'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async syncData(userId: number, syncOptions?: any) {\n    try {\n      const { deviceId, dataTypes } = syncOptions || {};\n\n      // Get connected devices\n      let sql = `\n        SELECT id, device_type, access_token, refresh_token, last_sync\n        FROM user_wearables \n        WHERE user_id = ? AND status = 'connected'\n      `;\n      const params = [userId];\n\n      if (deviceId) {\n        sql += ` AND id = ?`;\n        params.push(deviceId);\n      }\n\n      const [devices] = await db.execute(sql, params);\n\n      if (!devices.length) {\n        return {\n          status: 'success',\n          message: 'No devices to sync',\n          data: { synced: 0 }\n        };\n      }\n\n      let totalSynced = 0;\n\n      for (const device of devices) {\n        try {\n          const syncResult = await this.syncDeviceData(device, dataTypes);\n          totalSynced += syncResult.count;\n\n          // Update last sync time\n          await db.execute(\n            'UPDATE user_wearables SET last_sync = NOW() WHERE id = ?',\n            [device.id]\n          );\n        } catch (error) {\n          console.error(`Error syncing device ${device.id}:`, error);\n        }\n      }\n\n      return {\n        status: 'success',\n        message: `Synced ${totalSynced} records from ${devices.length} device(s)`,\n        data: { synced: totalSynced }\n      };\n    } catch (error) {\n      console.error('Error syncing data:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to sync data'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  private async syncDeviceData(device: any, dataTypes?: string[]) {\n    // This would implement actual API calls to device providers\n    // For now, return mock sync result\n    const mockData = [\n      { type: 'steps', value: 8500, unit: 'steps' },\n      { type: 'heart_rate', value: 72, unit: 'bpm' },\n      { type: 'calories', value: 2100, unit: 'kcal' }\n    ];\n\n    let syncedCount = 0;\n\n    for (const data of mockData) {\n      if (!dataTypes || dataTypes.includes(data.type)) {\n        // Insert mock data\n        await db.execute(\n          `INSERT INTO wearable_data (\n            user_id, device_id, data_type, value, unit, \n            recorded_at, synced_at, created_at\n          ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), NOW())`,\n          [device.user_id, device.id, data.type, data.value, data.unit]\n        );\n        syncedCount++;\n      }\n    }\n\n    return { count: syncedCount };\n  }\n\n  async getStepsData(userId: number, query: any) {\n    return this.getDataByType(userId, 'steps', query);\n  }\n\n  async getHeartRateData(userId: number, query: any) {\n    return this.getDataByType(userId, 'heart_rate', query);\n  }\n\n  async getSleepData(userId: number, query: any) {\n    return this.getDataByType(userId, 'sleep', query);\n  }\n\n  async getCaloriesData(userId: number, query: any) {\n    return this.getDataByType(userId, 'calories', query);\n  }\n\n  private async getDataByType(userId: number, dataType: string, query: any) {\n    try {\n      const { startDate, endDate, aggregation = 'daily' } = query;\n\n      let sql = `\n        SELECT \n          DATE(recorded_at) as date,\n          AVG(value) as avgValue,\n          MIN(value) as minValue,\n          MAX(value) as maxValue,\n          COUNT(*) as recordCount,\n          unit\n        FROM wearable_data \n        WHERE user_id = ? AND data_type = ?\n      `;\n\n      const params = [userId, dataType];\n\n      if (startDate) {\n        sql += ` AND recorded_at >= ?`;\n        params.push(startDate);\n      }\n\n      if (endDate) {\n        sql += ` AND recorded_at <= ?`;\n        params.push(endDate);\n      }\n\n      sql += ` GROUP BY DATE(recorded_at), unit ORDER BY date DESC`;\n\n      const [data] = await db.execute(sql, params);\n\n      return {\n        status: 'success',\n        data: {\n          dataType,\n          aggregation,\n          records: data\n        }\n      };\n    } catch (error) {\n      console.error(`Error getting ${dataType} data:`, error);\n      throw new HttpException({\n        status: 'error',\n        message: `Failed to get ${dataType} data`\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async addManualData(userId: number, data: any) {\n    try {\n      const { dataType, value, unit, recordedAt } = data;\n\n      await db.execute(\n        `INSERT INTO wearable_data (\n          user_id, device_id, data_type, value, unit, \n          recorded_at, synced_at, created_at, source\n        ) VALUES (?, NULL, ?, ?, ?, ?, NOW(), NOW(), 'manual')`,\n        [userId, dataType, value, unit, recordedAt || new Date().toISOString()]\n      );\n\n      return {\n        status: 'success',\n        message: 'Manual data added successfully'\n      };\n    } catch (error) {\n      console.error('Error adding manual data:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to add manual data'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  async getSyncStatus(userId: number) {\n    try {\n      const sql = `\n        SELECT \n          device_type as deviceType,\n          device_name as deviceName,\n          status,\n          last_sync as lastSync,\n          sync_frequency as syncFrequency,\n          CASE \n            WHEN last_sync IS NULL THEN 'never'\n            WHEN last_sync < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'outdated'\n            ELSE 'recent'\n          END as syncStatus\n        FROM user_wearables \n        WHERE user_id = ?\n        ORDER BY last_sync DESC\n      `;\n\n      const [devices] = await db.execute(sql, [userId]);\n\n      return {\n        status: 'success',\n        data: devices\n      };\n    } catch (error) {\n      console.error('Error getting sync status:', error);\n      throw new HttpException({\n        status: 'error',\n        message: 'Failed to get sync status'\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n\n  // OAuth integration methods (to be implemented with actual APIs)\n  async connectFitbit(userId: number, authData: any) {\n    // Implementation for Fitbit OAuth\n    return this.connectOAuthDevice(userId, 'fitbit', authData);\n  }\n\n  async connectGarmin(userId: number, authData: any) {\n    // Implementation for Garmin OAuth\n    return this.connectOAuthDevice(userId, 'garmin', authData);\n  }\n\n  async connectAppleHealth(userId: number, authData: any) {\n    // Implementation for Apple Health OAuth\n    return this.connectOAuthDevice(userId, 'apple_health', authData);\n  }\n\n  async connectGoogleFit(userId: number, authData: any) {\n    // Implementation for Google Fit OAuth\n    return this.connectOAuthDevice(userId, 'google_fit', authData);\n  }\n\n  private async connectOAuthDevice(userId: number, deviceType: string, authData: any) {\n    try {\n      const { code, redirectUri } = authData;\n\n      // This would implement actual OAuth flow\n      // For now, return success with mock data\n      return {\n        status: 'success',\n        message: `${deviceType} connected successfully`,\n        data: {\n          deviceType,\n          status: 'connected',\n          features: ['steps', 'heart_rate', 'calories']\n        }\n      };\n    } catch (error) {\n      console.error(`Error connecting ${deviceType}:`, error);\n      throw new HttpException({\n        status: 'error',\n        message: `Failed to connect ${deviceType}`\n      }, HttpStatus.INTERNAL_SERVER_ERROR);\n    }\n  }\n}\n"], "names": ["WearablesService", "getConnectedDevices", "userId", "sql", "devices", "db", "execute", "status", "data", "error", "console", "HttpException", "message", "HttpStatus", "INTERNAL_SERVER_ERROR", "getSupportedDevices", "supportedDevices", "id", "name", "brand", "types", "features", "oauth_required", "icon", "connectDevice", "deviceData", "deviceType", "deviceName", "model", "accessToken", "refreshToken", "expiresIn", "syncFrequency", "existing", "CONFLICT", "expiresAt", "Date", "now", "toISOString", "result", "syncData", "deviceId", "insertId", "disconnectDevice", "affectedRows", "NOT_FOUND", "getSyncedData", "query", "startDate", "endDate", "dataType", "page", "limit", "type", "getRecoveryData", "offset", "params", "push", "records", "pagination", "Number", "log", "period", "setDate", "getDate", "setMonth", "getMonth", "recoveryData", "sleep", "average_duration", "Math", "random", "quality_score", "deep_sleep_percentage", "rem_sleep_percentage", "efficiency", "trend", "hrv", "average", "recovery_score", "stress", "average_level", "energy", "heart_rate", "resting_average", "max_recorded", "zones", "zone1", "zone2", "zone3", "zone4", "zone5", "last_updated", "syncOptions", "dataTypes", "length", "synced", "totalSynced", "device", "syncResult", "syncDeviceData", "count", "mockData", "value", "unit", "syncedCount", "includes", "user_id", "getStepsData", "getDataByType", "getHeartRateData", "getSleepData", "getCaloriesData", "aggregation", "addManualData", "recordedAt", "getSyncStatus", "connectFitbit", "authData", "connectOAuthDevice", "connectGarmin", "connectAppleHealth", "connectGoogleFit", "code", "redirectUri"], "mappings": ";;;;+BAKaA;;;eAAAA;;;wBALyC;0BACnC;;;;;;;AAIZ,IAAA,AAAMA,mBAAN,MAAMA;IAEX,MAAMC,oBAAoBC,MAAc,EAAE;QACxC,IAAI;YACF,MAAMC,MAAM,CAAC;;;;;;;;;;;;;;;MAeb,CAAC;YAED,MAAM,CAACC,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACH,KAAK;gBAACD;aAAO;YAEhD,OAAO;gBACLK,QAAQ;gBACRC,MAAMJ;YACR;QACF,EAAE,OAAOK,OAAO;YACdC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMC,sBAAsB;QAC1B,IAAI;YACF,MAAMC,mBAAmB;gBACvB;oBACEC,IAAI;oBACJC,MAAM;oBACNC,OAAO;oBACPC,OAAO;wBAAC;wBAAmB;qBAAa;oBACxCC,UAAU;wBAAC;wBAAS;wBAAc;wBAAS;wBAAY;qBAAW;oBAClEC,gBAAgB;oBAChBC,MAAM;gBACR;gBACA;oBACEN,IAAI;oBACJC,MAAM;oBACNC,OAAO;oBACPC,OAAO;wBAAC;wBAAmB;wBAAc;qBAAY;oBACrDC,UAAU;wBAAC;wBAAS;wBAAc;wBAAS;wBAAY;wBAAY;qBAAM;oBACzEC,gBAAgB;oBAChBC,MAAM;gBACR;gBACA;oBACEN,IAAI;oBACJC,MAAM;oBACNC,OAAO;oBACPC,OAAO;wBAAC;qBAAa;oBACrBC,UAAU;wBAAC;wBAAS;wBAAc;wBAAS;wBAAY;qBAAW;oBAClEC,gBAAgB;oBAChBC,MAAM;gBACR;gBACA;oBACEN,IAAI;oBACJC,MAAM;oBACNC,OAAO;oBACPC,OAAO;wBAAC;qBAAa;oBACrBC,UAAU;wBAAC;wBAAS;wBAAc;wBAAY;qBAAW;oBACzDC,gBAAgB;oBAChBC,MAAM;gBACR;gBACA;oBACEN,IAAI;oBACJC,MAAM;oBACNC,OAAO;oBACPC,OAAO;wBAAC;qBAAa;oBACrBC,UAAU;wBAAC;wBAAS;wBAAc;wBAAS;qBAAW;oBACtDC,gBAAgB;oBAChBC,MAAM;gBACR;gBACA;oBACEN,IAAI;oBACJC,MAAM;oBACNC,OAAO;oBACPC,OAAO;wBAAC;wBAAmB;qBAAqB;oBAChDC,UAAU;wBAAC;wBAAc;wBAAY;qBAAW;oBAChDC,gBAAgB;oBAChBC,MAAM;gBACR;aACD;YAED,OAAO;gBACLhB,QAAQ;gBACRC,MAAMQ;YACR;QACF,EAAE,OAAOP,OAAO;YACdC,QAAQD,KAAK,CAAC,oCAAoCA;YAClD,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMU,cAActB,MAAc,EAAEuB,UAAe,EAAE;QACnD,IAAI;YACF,MAAM,EACJC,UAAU,EACVC,UAAU,EACVR,KAAK,EACLS,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,SAAS,EACTC,gBAAgB,QAAQ,EACzB,GAAGP;YAEJ,uCAAuC;YACvC,MAAM,CAACQ,SAAS,GAAG,MAAM5B,YAAE,CAACC,OAAO,CACjC,gGACA;gBAACJ;gBAAQwB;aAAW;YAGtB,IAAIO,QAAQ,CAAC,EAAE,EAAE;gBACf,MAAM,IAAItB,qBAAa,CAAC;oBACtBJ,QAAQ;oBACRK,SAAS;gBACX,GAAGC,kBAAU,CAACqB,QAAQ;YACxB;YAEA,MAAMC,YAAYJ,YAChB,IAAIK,KAAKA,KAAKC,GAAG,KAAMN,YAAY,MAAOO,WAAW,KAAK;YAE5D,MAAM,CAACC,OAAO,GAAG,MAAMlC,YAAE,CAACC,OAAO,CAC/B,CAAC;;;;8EAIqE,CAAC,EACvE;gBACEJ;gBAAQwB;gBAAYC;gBAAYR;gBAAOS;gBACvCC;gBAAaC;gBAAcK;gBAAWH;aACvC;YAGH,uBAAuB;YACvB,MAAM,IAAI,CAACQ,QAAQ,CAACtC,QAAQ;gBAAEuC,UAAUF,OAAOG,QAAQ;YAAC;YAExD,OAAO;gBACLnC,QAAQ;gBACRK,SAAS;gBACTJ,MAAM;oBACJS,IAAIsB,OAAOG,QAAQ;oBACnBhB;oBACAC;oBACApB,QAAQ;gBACV;YACF;QACF,EAAE,OAAOE,OAAO;YACdC,QAAQD,KAAK,CAAC,4BAA4BA;YAC1C,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM6B,iBAAiBzC,MAAc,EAAEuC,QAAgB,EAAE;QACvD,IAAI;YACF,MAAMF,SAAS,MAAMlC,YAAE,CAACC,OAAO,CAC7B,sGACA;gBAACmC;gBAAUvC;aAAO;YAGpB,IAAIqC,OAAOK,YAAY,KAAK,GAAG;gBAC7B,MAAM,IAAIjC,qBAAa,CAAC;oBACtBJ,QAAQ;oBACRK,SAAS;gBACX,GAAGC,kBAAU,CAACgC,SAAS;YACzB;YAEA,OAAO;gBACLtC,QAAQ;gBACRK,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,+BAA+BA;YAC7C,IAAIA,iBAAiBE,qBAAa,EAAE,MAAMF;YAE1C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMgC,cAAc5C,MAAc,EAAE6C,KAAU,EAAE;QAC9C,IAAI;YACF,MAAM,EACJC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRT,QAAQ,EACRU,OAAO,CAAC,EACRC,QAAQ,GAAG,EACXC,IAAI,AAAC,2CAA2C;cACjD,GAAGN;YAEJ,wCAAwC;YACxC,IAAIM,SAAS,YAAY;gBACvB,OAAO,IAAI,CAACC,eAAe,CAACpD,QAAQ6C;YACtC;YAEA,MAAMQ,SAAS,AAACJ,CAAAA,OAAO,CAAA,IAAKC;YAE5B,IAAIjD,MAAM,CAAC;;;;;;;;;;;;;MAaX,CAAC;YAED,MAAMqD,SAAS;gBAACtD;aAAO;YAEvB,IAAI8C,WAAW;gBACb7C,OAAO,CAAC,wBAAwB,CAAC;gBACjCqD,OAAOC,IAAI,CAACT;YACd;YAEA,IAAIC,SAAS;gBACX9C,OAAO,CAAC,wBAAwB,CAAC;gBACjCqD,OAAOC,IAAI,CAACR;YACd;YAEA,IAAIC,UAAU;gBACZ/C,OAAO,CAAC,qBAAqB,CAAC;gBAC9BqD,OAAOC,IAAI,CAACP;YACd;YAEA,IAAIT,UAAU;gBACZtC,OAAO,CAAC,qBAAqB,CAAC;gBAC9BqD,OAAOC,IAAI,CAAChB;YACd;YAEAtC,OAAO,CAAC,8CAA8C,CAAC;YACvDqD,OAAOC,IAAI,CAACL,OAAOG;YAEnB,MAAM,CAAC/C,KAAK,GAAG,MAAMH,YAAE,CAACC,OAAO,CAACH,KAAKqD;YAErC,OAAO;gBACLjD,QAAQ;gBACRC,MAAM;oBACJkD,SAASlD;oBACTmD,YAAY;wBACVR,MAAMS,OAAOT;wBACbC,OAAOQ,OAAOR;oBAChB;gBACF;YACF;QACF,EAAE,OAAO3C,OAAO;YACdC,QAAQD,KAAK,CAAC,8BAA8BA;YAC5C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMwC,gBAAgBpD,MAAc,EAAE6C,KAAU,EAAE;QAChD,IAAI;YACFrC,QAAQmD,GAAG,CAAC,sCAAsC3D;YAElD,MAAM,EAAE4D,SAAS,MAAM,EAAE,GAAGf;YAE5B,uBAAuB;YACvB,MAAME,UAAU,IAAIb;YACpB,MAAMY,YAAY,IAAIZ;YAEtB,OAAQ0B;gBACN,KAAK;oBACHd,UAAUe,OAAO,CAACd,QAAQe,OAAO,KAAK;oBACtC;gBACF,KAAK;oBACHhB,UAAUiB,QAAQ,CAAChB,QAAQiB,QAAQ,KAAK;oBACxC;gBACF;oBACElB,UAAUe,OAAO,CAACd,QAAQe,OAAO,KAAK;YAC1C;YAEA,yEAAyE;YACzE,yEAAyE;YACzE,MAAMG,eAAe;gBACnBC,OAAO;oBACLC,kBAAkB,MAAM,AAACC,CAAAA,KAAKC,MAAM,KAAK,GAAE,IAAK;oBAChDC,eAAe,KAAKF,KAAKC,MAAM,KAAK;oBACpCE,uBAAuB,KAAKH,KAAKC,MAAM,KAAK;oBAC5CG,sBAAsB,KAAKJ,KAAKC,MAAM,KAAK;oBAC3CI,YAAY,KAAKL,KAAKC,MAAM,KAAK;oBACjCK,OAAON,KAAKC,MAAM,KAAK,MAAM,cAAc;gBAC7C;gBACAM,KAAK;oBACHC,SAAS,KAAKR,KAAKC,MAAM,KAAK;oBAC9BK,OAAON,KAAKC,MAAM,KAAK,MAAM,cAAc;oBAC3CQ,gBAAgB,KAAKT,KAAKC,MAAM,KAAK,GAAG,QAAQ;gBAClD;gBACAS,QAAQ;oBACNC,eAAe,KAAKX,KAAKC,MAAM,KAAK;oBACpCK,OAAON,KAAKC,MAAM,KAAK,MAAM,eAAe;gBAC9C;gBACAW,QAAQ;oBACND,eAAe,KAAKX,KAAKC,MAAM,KAAK;oBACpCK,OAAON,KAAKC,MAAM,KAAK,MAAM,cAAc;gBAC7C;gBACAY,YAAY;oBACVC,iBAAiB,KAAKd,KAAKC,MAAM,KAAK;oBACtCc,cAAc,MAAMf,KAAKC,MAAM,KAAK;oBACpCe,OAAO;wBACLC,OAAO,KAAKjB,KAAKC,MAAM,KAAK;wBAC5BiB,OAAO,KAAKlB,KAAKC,MAAM,KAAK;wBAC5BkB,OAAO,KAAKnB,KAAKC,MAAM,KAAK;wBAC5BmB,OAAO,KAAKpB,KAAKC,MAAM,KAAK;wBAC5BoB,OAAO,IAAIrB,KAAKC,MAAM,KAAK;oBAC7B;gBACF;gBACAT;gBACA8B,cAAc,IAAIxD,OAAOE,WAAW;YACtC;YAEA,OAAO;gBACL/B,QAAQ;gBACRC,MAAM2D;YACR;QAEF,EAAE,OAAO1D,OAAO;YACdC,QAAQD,KAAK,CAAC,gCAAgCA;YAC9C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAM0B,SAAStC,MAAc,EAAE2F,WAAiB,EAAE;QAChD,IAAI;YACF,MAAM,EAAEpD,QAAQ,EAAEqD,SAAS,EAAE,GAAGD,eAAe,CAAC;YAEhD,wBAAwB;YACxB,IAAI1F,MAAM,CAAC;;;;MAIX,CAAC;YACD,MAAMqD,SAAS;gBAACtD;aAAO;YAEvB,IAAIuC,UAAU;gBACZtC,OAAO,CAAC,WAAW,CAAC;gBACpBqD,OAAOC,IAAI,CAAChB;YACd;YAEA,MAAM,CAACrC,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACH,KAAKqD;YAExC,IAAI,CAACpD,QAAQ2F,MAAM,EAAE;gBACnB,OAAO;oBACLxF,QAAQ;oBACRK,SAAS;oBACTJ,MAAM;wBAAEwF,QAAQ;oBAAE;gBACpB;YACF;YAEA,IAAIC,cAAc;YAElB,KAAK,MAAMC,UAAU9F,QAAS;gBAC5B,IAAI;oBACF,MAAM+F,aAAa,MAAM,IAAI,CAACC,cAAc,CAACF,QAAQJ;oBACrDG,eAAeE,WAAWE,KAAK;oBAE/B,wBAAwB;oBACxB,MAAMhG,YAAE,CAACC,OAAO,CACd,4DACA;wBAAC4F,OAAOjF,EAAE;qBAAC;gBAEf,EAAE,OAAOR,OAAO;oBACdC,QAAQD,KAAK,CAAC,CAAC,qBAAqB,EAAEyF,OAAOjF,EAAE,CAAC,CAAC,CAAC,EAAER;gBACtD;YACF;YAEA,OAAO;gBACLF,QAAQ;gBACRK,SAAS,CAAC,OAAO,EAAEqF,YAAY,cAAc,EAAE7F,QAAQ2F,MAAM,CAAC,UAAU,CAAC;gBACzEvF,MAAM;oBAAEwF,QAAQC;gBAAY;YAC9B;QACF,EAAE,OAAOxF,OAAO;YACdC,QAAQD,KAAK,CAAC,uBAAuBA;YACrC,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAcsF,eAAeF,MAAW,EAAEJ,SAAoB,EAAE;QAC9D,4DAA4D;QAC5D,mCAAmC;QACnC,MAAMQ,WAAW;YACf;gBAAEjD,MAAM;gBAASkD,OAAO;gBAAMC,MAAM;YAAQ;YAC5C;gBAAEnD,MAAM;gBAAckD,OAAO;gBAAIC,MAAM;YAAM;YAC7C;gBAAEnD,MAAM;gBAAYkD,OAAO;gBAAMC,MAAM;YAAO;SAC/C;QAED,IAAIC,cAAc;QAElB,KAAK,MAAMjG,QAAQ8F,SAAU;YAC3B,IAAI,CAACR,aAAaA,UAAUY,QAAQ,CAAClG,KAAK6C,IAAI,GAAG;gBAC/C,mBAAmB;gBACnB,MAAMhD,YAAE,CAACC,OAAO,CACd,CAAC;;;uDAG4C,CAAC,EAC9C;oBAAC4F,OAAOS,OAAO;oBAAET,OAAOjF,EAAE;oBAAET,KAAK6C,IAAI;oBAAE7C,KAAK+F,KAAK;oBAAE/F,KAAKgG,IAAI;iBAAC;gBAE/DC;YACF;QACF;QAEA,OAAO;YAAEJ,OAAOI;QAAY;IAC9B;IAEA,MAAMG,aAAa1G,MAAc,EAAE6C,KAAU,EAAE;QAC7C,OAAO,IAAI,CAAC8D,aAAa,CAAC3G,QAAQ,SAAS6C;IAC7C;IAEA,MAAM+D,iBAAiB5G,MAAc,EAAE6C,KAAU,EAAE;QACjD,OAAO,IAAI,CAAC8D,aAAa,CAAC3G,QAAQ,cAAc6C;IAClD;IAEA,MAAMgE,aAAa7G,MAAc,EAAE6C,KAAU,EAAE;QAC7C,OAAO,IAAI,CAAC8D,aAAa,CAAC3G,QAAQ,SAAS6C;IAC7C;IAEA,MAAMiE,gBAAgB9G,MAAc,EAAE6C,KAAU,EAAE;QAChD,OAAO,IAAI,CAAC8D,aAAa,CAAC3G,QAAQ,YAAY6C;IAChD;IAEA,MAAc8D,cAAc3G,MAAc,EAAEgD,QAAgB,EAAEH,KAAU,EAAE;QACxE,IAAI;YACF,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEgE,cAAc,OAAO,EAAE,GAAGlE;YAEtD,IAAI5C,MAAM,CAAC;;;;;;;;;;MAUX,CAAC;YAED,MAAMqD,SAAS;gBAACtD;gBAAQgD;aAAS;YAEjC,IAAIF,WAAW;gBACb7C,OAAO,CAAC,qBAAqB,CAAC;gBAC9BqD,OAAOC,IAAI,CAACT;YACd;YAEA,IAAIC,SAAS;gBACX9C,OAAO,CAAC,qBAAqB,CAAC;gBAC9BqD,OAAOC,IAAI,CAACR;YACd;YAEA9C,OAAO,CAAC,oDAAoD,CAAC;YAE7D,MAAM,CAACK,KAAK,GAAG,MAAMH,YAAE,CAACC,OAAO,CAACH,KAAKqD;YAErC,OAAO;gBACLjD,QAAQ;gBACRC,MAAM;oBACJ0C;oBACA+D;oBACAvD,SAASlD;gBACX;YACF;QACF,EAAE,OAAOC,OAAO;YACdC,QAAQD,KAAK,CAAC,CAAC,cAAc,EAAEyC,SAAS,MAAM,CAAC,EAAEzC;YACjD,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS,CAAC,cAAc,EAAEsC,SAAS,KAAK,CAAC;YAC3C,GAAGrC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMoG,cAAchH,MAAc,EAAEM,IAAS,EAAE;QAC7C,IAAI;YACF,MAAM,EAAE0C,QAAQ,EAAEqD,KAAK,EAAEC,IAAI,EAAEW,UAAU,EAAE,GAAG3G;YAE9C,MAAMH,YAAE,CAACC,OAAO,CACd,CAAC;;;8DAGqD,CAAC,EACvD;gBAACJ;gBAAQgD;gBAAUqD;gBAAOC;gBAAMW,cAAc,IAAI/E,OAAOE,WAAW;aAAG;YAGzE,OAAO;gBACL/B,QAAQ;gBACRK,SAAS;YACX;QACF,EAAE,OAAOH,OAAO;YACdC,QAAQD,KAAK,CAAC,6BAA6BA;YAC3C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,MAAMsG,cAAclH,MAAc,EAAE;QAClC,IAAI;YACF,MAAMC,MAAM,CAAC;;;;;;;;;;;;;;;MAeb,CAAC;YAED,MAAM,CAACC,QAAQ,GAAG,MAAMC,YAAE,CAACC,OAAO,CAACH,KAAK;gBAACD;aAAO;YAEhD,OAAO;gBACLK,QAAQ;gBACRC,MAAMJ;YACR;QACF,EAAE,OAAOK,OAAO;YACdC,QAAQD,KAAK,CAAC,8BAA8BA;YAC5C,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS;YACX,GAAGC,kBAAU,CAACC,qBAAqB;QACrC;IACF;IAEA,iEAAiE;IACjE,MAAMuG,cAAcnH,MAAc,EAAEoH,QAAa,EAAE;QACjD,kCAAkC;QAClC,OAAO,IAAI,CAACC,kBAAkB,CAACrH,QAAQ,UAAUoH;IACnD;IAEA,MAAME,cAActH,MAAc,EAAEoH,QAAa,EAAE;QACjD,kCAAkC;QAClC,OAAO,IAAI,CAACC,kBAAkB,CAACrH,QAAQ,UAAUoH;IACnD;IAEA,MAAMG,mBAAmBvH,MAAc,EAAEoH,QAAa,EAAE;QACtD,wCAAwC;QACxC,OAAO,IAAI,CAACC,kBAAkB,CAACrH,QAAQ,gBAAgBoH;IACzD;IAEA,MAAMI,iBAAiBxH,MAAc,EAAEoH,QAAa,EAAE;QACpD,sCAAsC;QACtC,OAAO,IAAI,CAACC,kBAAkB,CAACrH,QAAQ,cAAcoH;IACvD;IAEA,MAAcC,mBAAmBrH,MAAc,EAAEwB,UAAkB,EAAE4F,QAAa,EAAE;QAClF,IAAI;YACF,MAAM,EAAEK,IAAI,EAAEC,WAAW,EAAE,GAAGN;YAE9B,yCAAyC;YACzC,yCAAyC;YACzC,OAAO;gBACL/G,QAAQ;gBACRK,SAAS,GAAGc,WAAW,uBAAuB,CAAC;gBAC/ClB,MAAM;oBACJkB;oBACAnB,QAAQ;oBACRc,UAAU;wBAAC;wBAAS;wBAAc;qBAAW;gBAC/C;YACF;QACF,EAAE,OAAOZ,OAAO;YACdC,QAAQD,KAAK,CAAC,CAAC,iBAAiB,EAAEiB,WAAW,CAAC,CAAC,EAAEjB;YACjD,MAAM,IAAIE,qBAAa,CAAC;gBACtBJ,QAAQ;gBACRK,SAAS,CAAC,kBAAkB,EAAEc,YAAY;YAC5C,GAAGb,kBAAU,CAACC,qBAAqB;QACrC;IACF;AACF"}