import { Controller, Get, UseGuards, Request, Query, HttpException, HttpStatus } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { DashboardService } from './dashboard.service';

@Controller('dashboard')
export class DashboardController {
    constructor(private readonly dashboardService: DashboardService) {}

    private validateRequest(userId: number, period: string) {
        if (!userId) {
            throw new HttpException('User ID not found', HttpStatus.UNAUTHORIZED);
        }

        const validPeriods = ['week', 'month', 'semester', 'year', 'all'];
        if (!validPeriods.includes(period)) {
            throw new HttpException('Invalid period parameter', HttpStatus.BAD_REQUEST);
        }
    }
    @Get('stats')
    getStats() {
        return {
            totalUsers: 0,
            totalProfessionals: 0,
            activeSubscriptions: 0,
            monthlyRevenue: 0
          };
    }

    @UseGuards(JwtAuthGuard)
    @Get('workout')
    async getWorkoutData(@Request() req: any) {
        const userId = req.user.userId;
        return await this.dashboardService.getWorkoutDashboardData(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('progress/weight')
    async getProgressWeight(@Request() req: any, @Query() query: any) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getWeightProgress(userId, period);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('progress/analytics')
    async getProgressAnalytics(@Request() req: any, @Query() query: any) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getAnalyticsOverview(userId, period);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('nutritional-summary')
    async getNutritionalSummary(@Request() req: any, @Query() query: any) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }

        return this.dashboardService.getNutritionalSummary(userId, query);
    }

    @UseGuards(JwtAuthGuard)
    @Get('ai-suggestions')
    getAISuggestions(@Request() req: any, @Query() query: any) {
        const userId = req.user.userId;

        // Mock AI suggestions
        const suggestions = [
            'Considere adicionar mais proteína na próxima refeição',
            'Você está próximo da sua meta de hidratação hoje!',
            'Que tal um treino de força hoje?',
            'Tente incluir mais vegetais na sua dieta',
            'Mantenha a consistência nos treinos'
        ];

        return {
            status: 'success',
            data: suggestions.slice(0, 3) // Return 3 random suggestions
        };
    }


    @UseGuards(JwtAuthGuard)
    @Get('workout-analytics')
    async getWorkoutAnalytics(@Request() req: any, @Query() query: any) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'week';
            this.validateRequest(userId, period);
            return await this.dashboardService.getWorkoutAnalytics(userId, period);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('nutrition-analytics')
    async getNutritionAnalytics(@Request() req: any, @Query() query: any) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'week';
            this.validateRequest(userId, period);
            return await this.dashboardService.getNutritionAnalytics(userId, period);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('caloric-balance')
    async getCaloricBalance(@Request() req: any, @Query() query: any) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'week';
            this.validateRequest(userId, period);
            return await this.dashboardService.getCaloricBalance(userId, period);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('strength-progress')
    async getStrengthProgress(@Request() req: any, @Query() query: any) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getStrengthProgress(userId, period);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @UseGuards(JwtAuthGuard)
    @Get('body-composition')
    async getBodyComposition(@Request() req: any, @Query() query: any) {
        try {
            const userId = req.user.userId;
            const period = query.period || 'month';
            this.validateRequest(userId, period);
            return await this.dashboardService.getBodyComposition(userId, period);
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
